"""带窥孔连接的长短期记忆网络 (PeepholeLSTM)"""


import pickle
import requests
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow import keras
from sklearn.preprocessing import StandardScaler, LabelEncoder, OrdinalEncoder
from sklearn.model_selection import train_test_split

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class PeepholeLSTMCell(keras.layers.Layer):
    """自定义的窥视孔LSTM单元"""
    def __init__(self, units, **kwargs):
        super(PeepholeLSTMCell, self).__init__(**kwargs)
        self.units = units
        self.state_size = [units, units]  # [cell_state, hidden_state]
        
    def build(self, input_shape):
        input_dim = input_shape[-1]
        
        # 输入权重
        self.kernel = self.add_weight(
            shape=(input_dim, self.units * 4),
            initializer='glorot_uniform',
            name='kernel'
        )
        
        # 循环权重
        self.recurrent_kernel = self.add_weight(
            shape=(self.units, self.units * 4),
            initializer='orthogonal',
            name='recurrent_kernel'
        )
        
        # 窥视孔连接权重
        self.peephole_i = self.add_weight(
            shape=(self.units,),
            initializer='zeros',
            name='peephole_i'
        )
        self.peephole_f = self.add_weight(
            shape=(self.units,),
            initializer='zeros',
            name='peephole_f'
        )
        self.peephole_o = self.add_weight(
            shape=(self.units,),
            initializer='zeros',
            name='peephole_o'
        )
        
        # 偏置
        self.bias = self.add_weight(
            shape=(self.units * 4,),
            initializer='zeros',
            name='bias'
        )
        
        self.built = True

    def call(self, inputs, states):
        cell_state_prev, hidden_state_prev = states
        
        # 计算输入和循环连接
        z = tf.matmul(inputs, self.kernel) + tf.matmul(hidden_state_prev, self.recurrent_kernel) + self.bias
        
        # 分割门控单元
        i, f, c, o = tf.split(z, 4, axis=1)
        
        # 添加窥视孔连接
        i += self.peephole_i * cell_state_prev
        f += self.peephole_f * cell_state_prev
        
        # 应用激活函数
        i = tf.sigmoid(i)
        f = tf.sigmoid(f)
        c = tf.tanh(c)
        
        # 更新cell state
        cell_state = f * cell_state_prev + i * c
        
        # 输出门添加窥视孔连接
        o += self.peephole_o * cell_state
        o = tf.sigmoid(o)
        
        # 计算hidden state
        hidden_state = o * tf.tanh(cell_state)
        
        return hidden_state, [cell_state, hidden_state]

class PeepholeLSTM(keras.Model):
    """窥视孔长短期记忆网络模型"""
    def __init__(self, input_size, hidden_size, num_layers, num_classes):
        super(PeepholeLSTM, self).__init__()
        
        # 转换并验证参数
        self.input_size = int(input_size)
        self.hidden_size = int(hidden_size)
        self.num_layers = int(num_layers)
        self.num_classes = int(num_classes)
        
        # 验证参数
        if self.input_size <= 0 or self.hidden_size <= 0 or self.num_layers <= 0 or self.num_classes <= 0:
            raise ValueError("All parameters must be positive integers")
        
        # 创建LSTM层
        self.lstm_layers = []
        for i in range(self.num_layers):
            lstm_cell = PeepholeLSTMCell(units=self.hidden_size)
            self.lstm_layers.append(
                keras.layers.RNN(
                    lstm_cell,
                    return_sequences=(i < self.num_layers - 1)
                )
            )
        
        # 输出层
        self.fc = keras.layers.Dense(self.num_classes, activation='softmax')
    
    def call(self, x):
        # 确保输入维度正确
        if len(x.shape) == 2:
            x = tf.expand_dims(x, axis=1)
        
        # 通过LSTM层
        for lstm_layer in self.lstm_layers:
            x = lstm_layer(x)
        
        # 通过输出层
        out = self.fc(x)
        return out

def validate_params(job_params):
    required_params = ["input_size", "hidden_size", "output_size", "num_layers", 
                      "num_epochs", "learning_rate", "batch_size"]
    
    # 检查所有必需参数
    for param in required_params:
        if param not in job_params:
            raise ValueError(f"Missing required parameter: {param}")
    
    # 转换和验证数值参数
    try:
        job_params["input_size"] = int(job_params["input_size"])
        job_params["hidden_size"] = int(job_params["hidden_size"])
        job_params["output_size"] = int(job_params["output_size"])
        job_params["num_layers"] = int(job_params["num_layers"])
        job_params["num_epochs"] = int(job_params["num_epochs"])
        job_params["batch_size"] = int(job_params["batch_size"])
        job_params["learning_rate"] = float(job_params["learning_rate"])
    except (ValueError, TypeError) as e:
        raise ValueError(f"Invalid parameter value: {str(e)}")
    
    # 验证参数值
    if job_params["input_size"] <= 0:
        raise ValueError("input_size must be positive")
    if job_params["hidden_size"] <= 0:
        raise ValueError("hidden_size must be positive")
    if job_params["output_size"] <= 0:
        raise ValueError("output_size must be positive")
    if job_params["num_layers"] <= 0:
        raise ValueError("num_layers must be positive")
    if job_params["num_epochs"] <= 0:
        raise ValueError("num_epochs must be positive")
    if job_params["batch_size"] <= 0:
        raise ValueError("batch_size must be positive")
    if job_params["learning_rate"] <= 0:
        raise ValueError("learning_rate must be positive")
    
    return job_params

def load_data_preprocess(pkl_file):
    # 从pkl文件加载数据
    with open(pkl_file, 'rb') as file:
        data = pickle.load(file)
    
    # 清理列名
    data.columns = data.columns.str.strip()

    print(type(data))
    print(data.head())
    print(data.info())

    if isinstance(data, pd.DataFrame):
        print(data.columns)
        X = data.drop(['Label'], axis=1)
        y = data['Label']
    elif isinstance(data, dict):
        X = data['features']
        y = data['labels']
    
    return X, y

def preprocess_data(X, y):
    # 处理缺失值
    if X.isnull().any().any() or y.isnull().any():
        X, y = X.align(y, join='inner', axis=0)
        X = X.dropna()
        y = y[X.index]

    # 标签编码
    le = LabelEncoder()
    y = le.fit_transform(y)

    # 分类特征编码
    categorical_cols = X.select_dtypes(include=['object']).columns
    encoder = OrdinalEncoder()
    if not categorical_cols.empty:
        X[categorical_cols] = encoder.fit_transform(X[categorical_cols])
    
    # 处理无限值
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.mean(), inplace=True)
    
    # 数据标准化
    scaler = StandardScaler()
    X = scaler.fit_transform(X)

    # 划分数据集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    return X_train, X_test, y_train, y_test

def peephole_lstm_train(dataset, job_params, model_name, result_dir, fit_params=None):
    try:
        # 验证参数
        job_params = validate_params(job_params)
        
        print("Model parameters:")
        print(f"Input size: {job_params['input_size']}")
        print(f"Hidden size: {job_params['hidden_size']}")
        print(f"Output size: {job_params['output_size']}")
        print(f"Number of layers: {job_params['num_layers']}")
        
        # 准备数据
        training_data_path = "/workspace/" + dataset["training_data_path"]
        X, y = load_data_preprocess(training_data_path)
        
        if X.shape[1] != job_params["input_size"]:
            raise ValueError(f"Input size mismatch. Expected {job_params['input_size']}, got {X.shape[1]}")
        
        # 数据预处理
        train_x, test_x, train_y, test_y = preprocess_data(X, y)

        # 重塑数据
        train_x = train_x.reshape(train_x.shape[0], 1, train_x.shape[1])
        test_x = test_x.reshape(test_x.shape[0], 1, test_x.shape[1])

        # 创建数据集
        train_dataset = tf.data.Dataset.from_tensor_slices(
            (train_x, train_y)
        ).batch(job_params["batch_size"])

        test_dataset = tf.data.Dataset.from_tensor_slices(
            (test_x, test_y)
        ).batch(job_params["batch_size"])

        # 初始化模型
        model = PeepholeLSTM(
            input_size=job_params["input_size"],
            hidden_size=job_params["hidden_size"],
            num_layers=job_params["num_layers"],
            num_classes=job_params["output_size"]
        )

        # 编译模型
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=job_params["learning_rate"]),
            loss=keras.losses.SparseCategoricalCrossentropy(),
            metrics=['accuracy']
        )

        # 训练模型
        history = model.fit(
            train_dataset, 
            epochs=job_params["num_epochs"], 
            validation_data=test_dataset,
            verbose=1
        )

        # 评估模型
        test_loss, test_accuracy = model.evaluate(test_dataset, verbose=0)
        print(f'Test Accuracy: {test_accuracy * 100:.2f}%')

        # 保存模型
        model_path = os.path.join(result_dir, f"{model_name}.h5")
        model.save_weights(model_path)
        print(f'PeepholeLSTM训练完成，模型保存到 {model_path}')

        # 保存配置
        model_config = {
            "input_size": job_params["input_size"],
            "hidden_size": job_params["hidden_size"],
            "num_layers": job_params["num_layers"],
            "num_classes": job_params["output_size"]
        }
        config_path = os.path.join(result_dir, f"{model_name}_config.json")
        with open(config_path, 'w') as f:
            json.dump(model_config, f)
        print(f'模型配置保存到 {config_path}')

        return None, 0
        
    except Exception as e:
        print(f"Error in PeepholeLSTM training: {str(e)}")
        return str(e), -1

# 模型加载函数
def load_peephole_lstm_model(model_path, config_path):
    # 读取配置
    with open(config_path, 'r') as f:
        model_config = json.load(f)
    
    # 重建模型
    model = PeepholeLSTM(
        input_size=model_config["input_size"],
        hidden_size=model_config["hidden_size"],
        num_layers=model_config["num_layers"],
        num_classes=model_config["num_classes"]
    )
    
    # 编译模型
    model.compile(
        optimizer=keras.optimizers.Adam(learning_rate=0.001),
        loss=keras.losses.SparseCategoricalCrossentropy(),
        metrics=['accuracy']
    )
    
    # 加载权重
    model.load_weights(model_path)
    
    return model

def model_upload(result_dir, model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".h5"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, os.path.join(result_dir, f"{model_name}.h5"))
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "TENSORFLOW",
          "file_name": model_name + ".h5",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "RNN",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='tensorflow PeepholeLSTM Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='PeepholeLSTM Job Params, set all params in dict')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='PeepholeLSTM DataSet, set as a dict')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,
                    default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost', nargs='?', const=None, dest='nodehost', 
                    type=str, default="**************",
                    help='nodehost params')     
    
    print("Start PeepholeLSTM training job, params:\n" + str(sys.argv) + "\n")
    args = parser.parse_args()
    
    # Get all parameters
    job_params = args.job_params
    print("PeepholeLSTM job params:" + str(job_params) + "\n")
    dataset = args.dataset
    print("PeepholeLSTM dataSet:" + str(dataset) + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("PeepholeLSTM result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("PeepholeLSTM factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("PeepholeLSTM fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("PeepholeLSTM sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    
    if fit_params is None:
        fit_params = {}
     
    print("Step 1 PeepholeLSTM training:\n")
    result, ret_code = peephole_lstm_train(dataset, job_params, model["model_name"], 
                                         result_dir, fit_params)
    if ret_code != 0:
        print("PeepholeLSTM train err, stop job....\n")
        print("Error Msg:" + result + "\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    print("Step 2 Model Upload to MinIO: \n")
    result, ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model register...\n")
    
    print("Step 3 Model Register:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result, ret_code = model_register(model["model_name"], source, 
                                    model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: " + result)
        sys.exit(-1)
    print("Register model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()