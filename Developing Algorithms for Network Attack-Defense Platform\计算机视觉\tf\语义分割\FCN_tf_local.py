import tensorflow as tf
import numpy as np
import os
import pickle
import requests
import sys
import argparse
import json
from minio import Minio
import uuid
from PIL import Image
import xml.etree.ElementTree as ET
import pandas as pd
from tqdm.auto import tqdm

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class FCNSegmentation(tf.keras.Model):
    def __init__(self, num_classes, pretrained_model_path):
        super(FCNSegmentation, self).__init__()
        # 使用预训练的VGG16作为基础网络
        base_model = tf.keras.applications.VGG16(weights=None, include_top=False)
        base_model.load_weights(pretrained_model_path)
        
        # 获取特定层的输出用于跳跃连接
        self.pool3_layer = base_model.get_layer('block3_pool').output
        self.pool4_layer = base_model.get_layer('block4_pool').output
        self.pool5_layer = base_model.get_layer('block5_pool').output
        
        # FCN特定层
        self.fcn_conv = tf.keras.Sequential([
            tf.keras.layers.Conv2D(4096, 7, padding='same', activation='relu'),
            tf.keras.layers.Dropout(0.5),
            tf.keras.layers.Conv2D(4096, 1, activation='relu'),
            tf.keras.layers.Dropout(0.5),
            tf.keras.layers.Conv2D(num_classes, 1)
        ])
        
        # 上采样层
        self.up_pool4 = tf.keras.layers.Conv2DTranspose(num_classes, 4, strides=2, padding='same')
        self.up_pool3 = tf.keras.layers.Conv2DTranspose(num_classes, 4, strides=2, padding='same')
        self.up_final = tf.keras.layers.Conv2DTranspose(num_classes, 16, strides=8, padding='same')
        
        # 跳跃连接的卷积层
        self.conv_pool4 = tf.keras.layers.Conv2D(num_classes, 1)
        self.conv_pool3 = tf.keras.layers.Conv2D(num_classes, 1)
        
        # 构建完整模型
        inputs = base_model.input
        x = self.fcn_conv(self.pool5_layer)
        
        # 上采样和跳跃连接
        score_pool4 = self.conv_pool4(self.pool4_layer)
        x = self.up_pool4(x)
        x = tf.keras.layers.Add()([x, score_pool4])
        
        score_pool3 = self.conv_pool3(self.pool3_layer)
        x = self.up_pool3(x)
        x = tf.keras.layers.Add()([x, score_pool3])
        
        outputs = self.up_final(x)
        
        self.fcn_model = tf.keras.Model(inputs=inputs, outputs=outputs)

    def call(self, inputs):
        return self.fcn_model(inputs)

class UniversalImageDataset(tf.keras.utils.Sequence):
    def __init__(self, data_dir, batch_size=32, target_size=(224, 224), dataset_type='folder', annotations_file=None):
        self.data_dir = data_dir
        self.batch_size = batch_size
        self.target_size = target_size
        self.dataset_type = dataset_type
        self.annotations_file = annotations_file

        self.classes = ['background'] + [f'class_{i}' for i in range(1, 21)]
        self.class_to_idx = {cls: idx for idx, cls in enumerate(self.classes)}

        if dataset_type == 'pickle':
            self.image_data, self.mask_data = self.load_pickle_data()
        elif dataset_type == 'coco':
            self.image_paths, self.mask_data = self.load_coco_data()
        elif dataset_type == 'yolo':
            self.image_paths, self.mask_data = self.load_yolo_data()
        else:  # folder or voc
            self.image_paths, self.mask_paths = self.load_segmentation_data()

    def load_segmentation_data(self):
        image_paths = []
        mask_paths = []
        
        images_dir = os.path.join(self.data_dir, 'JPEGImages')
        masks_dir = os.path.join(self.data_dir, 'SegmentationClass')
        
        for img_name in os.listdir(images_dir):
            if img_name.endswith(('.jpg', '.jpeg', '.png')):
                img_path = os.path.join(images_dir, img_name)
                mask_name = img_name.replace('.jpg', '.png').replace('.jpeg', '.png')
                mask_path = os.path.join(masks_dir, mask_name)
                
                if os.path.exists(mask_path):
                    image_paths.append(img_path)
                    mask_paths.append(mask_path)

        return image_paths, mask_paths

    def __len__(self):
        if self.dataset_type == 'pickle':
            return len(self.image_data) // self.batch_size
        else:
            return len(self.image_paths) // self.batch_size

    def __getitem__(self, idx):
        batch_x = np.zeros((self.batch_size,) + self.target_size + (3,), dtype=np.float32)
        batch_y = np.zeros((self.batch_size,) + self.target_size, dtype=np.int32)

        if self.dataset_type == 'pickle':
            for i in range(self.batch_size):
                index = idx * self.batch_size + i
                if index >= len(self.image_data):
                    break
                    
                image = self.image_data[index]
                mask = self.mask_data[index]
                
                if isinstance(image, np.ndarray):
                    image = Image.fromarray(image)
                
                # 预处理
                image = self.preprocess_image(image)
                mask = self.preprocess_mask(mask)
                
                batch_x[i] = image
                batch_y[i] = mask
        else:
            for i in range(self.batch_size):
                index = idx * self.batch_size + i
                if index >= len(self.image_paths):
                    break
                    
                image = Image.open(self.image_paths[index]).convert('RGB')
                if self.dataset_type in ['coco', 'yolo']:
                    mask = self.mask_data[index]
                    mask = Image.fromarray(mask)
                else:
                    mask = Image.open(self.mask_paths[index])
                
                # 预处理
                image = self.preprocess_image(image)
                mask = self.preprocess_mask(mask)
                
                batch_x[i] = image
                batch_y[i] = mask

        return batch_x, batch_y

    def preprocess_image(self, image):
        # 调整大小
        image = image.resize(self.target_size, Image.BILINEAR)
        # 转换为numpy数组并归一化
        image = np.array(image, dtype=np.float32) / 255.0
        # VGG16预处理
        image = tf.keras.applications.vgg16.preprocess_input(image)
        return image

    def preprocess_mask(self, mask):
        # 调整大小
        mask = mask.resize(self.target_size, Image.NEAREST)
        # 转换为numpy数组
        mask = np.array(mask)
        return mask

def prepare_data(data_dir, dataset_type, batch_size, image_size, annotations_file=None):
    dataset = UniversalImageDataset(
        data_dir,
        batch_size=batch_size,
        target_size=(image_size, image_size),
        dataset_type=dataset_type,
        annotations_file=annotations_file
    )
    
    # 分割训练集和测试集
    total_size = len(dataset)
    train_size = int(0.8 * total_size)
    test_size = total_size - train_size
    
    train_dataset = tf.data.Dataset.from_tensor_slices(
        range(train_size)).shuffle(train_size).batch(batch_size)
    test_dataset = tf.data.Dataset.from_tensor_slices(
        range(test_size)).batch(batch_size)
    
    return train_dataset, test_dataset, dataset

@tf.function
def train_step(model, images, masks, optimizer):
    with tf.GradientTape() as tape:
        predictions = model(images, training=True)
        loss = tf.keras.losses.sparse_categorical_crossentropy(
            masks, predictions, from_logits=True)
        loss = tf.reduce_mean(loss)
    
    gradients = tape.gradient(loss, model.trainable_variables)
    optimizer.apply_gradients(zip(gradients, model.trainable_variables))
    
    return loss

def train_model(train_dataset, dataset, model, optimizer, num_epochs):
    for epoch in range(num_epochs):
        print(f'\nEpoch {epoch+1}/{num_epochs}')
        progbar = tf.keras.utils.Progbar(len(train_dataset))
        
        for batch_idx in train_dataset:
            images, masks = dataset[int(batch_idx.numpy())]
            loss = train_step(model, images, masks, optimizer)
            progbar.add(1, values=[('loss', float(loss))])

def compute_iou(y_true, y_pred):
    intersection = tf.reduce_sum(y_true * y_pred)
    union = tf.reduce_sum(y_true) + tf.reduce_sum(y_pred) - intersection
    iou = intersection / (union + tf.keras.backend.epsilon())
    return iou

def test_model(test_dataset, dataset, model):
    total_iou = 0.0
    num_batches = 0
    
    for batch_idx in test_dataset:
        images, masks = dataset[int(batch_idx.numpy())]
        predictions = model(images, training=False)
        predictions = tf.argmax(predictions, axis=-1)
        
        # 计算IoU
        iou = compute_iou(tf.cast(masks, tf.float32), 
                         tf.cast(predictions, tf.float32))
        total_iou += float(iou)
        num_batches += 1
    
    mean_iou = total_iou / num_batches
    print(f'Mean IoU: {mean_iou:.4f}')

def fcn_train(dataset_config, job_params, model_name, result_dir, fit_params=None):
    """
    主训练函数
    """
    input_size = job_params["input_size"]
    num_classes = job_params["num_classes"]
    dataset_type = job_params["dataset_type"]
    num_epochs = job_params["num_epochs"]
    learning_rate = job_params["learning_rate"]
    batch_size = job_params["batch_size"]
    
    training_data_path = "/workspace/" + dataset_config["training_data_path"]
    
    # 准备数据
    train_dataset, test_dataset, dataset = prepare_data(
        training_data_path, dataset_type, batch_size, input_size)

    # 初始化模型
    pretrained_model_path = "/workspace/pretrained_model/vgg16_weights_tf_dim_ordering_tf_kernels_notop.h5"
    model = FCNSegmentation(num_classes, pretrained_model_path)
    
    # 优化器
    optimizer = tf.keras.optimizers.Adam(learning_rate=learning_rate)

    try:
        # 训练模型
        train_model(train_dataset, dataset, model, optimizer, num_epochs)
        
        # 测试模型
        test_model(test_dataset, dataset, model)
        
        # 保存模型
        model_path = os.path.join(result_dir, f"{model_name}.h5")
        model.save(model_path)
        print(f'训练完成，模型保存到 {model_path}')
    except Exception as e:
        print(f"训练过程中发生错误: {e}")
        return None, -1

    return None, 0

def model_upload(result_dir, model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".h5"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, 
                                    result_dir+"/"+model_name+".h5")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1

def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "TENSORFLOW",
          "file_name": model_name+".h5",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,
                     data=json.dumps(params),
                     headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    
def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "FCN",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

    
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='tensorflow FCN Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='FCN Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='FCN DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start FCN training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("FCN job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("FCN dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("FCN result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("FCN factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("FCN fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("FCN sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    
    device = '/GPU:0' if tf.config.list_physical_devices('GPU') else '/CPU:0'
    print("Step 1 FCN training:\n")
    result,ret_code = fcn_train(dataset,job_params, model["model_name"],result_dir,fit_params)
    if ret_code != 0:
        print("FCN train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()
