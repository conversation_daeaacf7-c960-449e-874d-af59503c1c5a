{"missing_values": {"before": 1008, "after": 0}, "label_encoder": "LabelEncoder()", "classes": ["BENIGN", "DoS GoldenEye", "DoS Hulk", "DoS Slowhttptest", "DoS slowloris", "Heartbleed"], "categorical_encoder": "OrdinalEncoder(handle_unknown='use_encoded_value', unknown_value=-1)", "categorical_columns": ["Flow ID", "Source IP", "Destination IP", "Timestamp"], "inf_values_handled": true, "feature_scaler": "StandardScaler()"}