2025-05-21 14:33:36,979 - GAT_TF_Local - INFO - Logging to console and file: ./results/results_gat_tf_local\gat_tf_local_training_20250521_143336.log
2025-05-21 14:33:36,979 - GAT_TF_Local - INFO - GAT TensorFlow Local Mode Training Script Started.
2025-05-21 14:33:36,980 - GAT_TF_Local - INFO - TensorFlow Version: 2.13.0
2025-05-21 14:33:36,980 - GAT_TF_Local - INFO - Provided arguments: Namespace(adj_type='identity', categorical_impute_strategy='most_frequent', cv_folds=5, data_format='pkl', dropout=0.5, final_dropout=0.5, handle_imbalance=False, hidden_size=64, input_file='E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', k_neighbors=5, l2_reg=0.0001, label_column='Label', learning_rate=0.005, log_level='INFO', model_name='GAT_TF_Local_Model', normalize=True, num_epochs=3, num_heads=4, numeric_impute_strategy='mean', random_state=42, result_dir='./results/results_gat_tf_local', scaler_type='standard', stratify_split=True, test_split_size=0.2, use_cv=False)
2025-05-21 14:33:36,981 - GAT_TF_Local - INFO - Loading data from: E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl (Format: pkl, Label: 'Label')
2025-05-21 14:33:37,602 - GAT_TF_Local - INFO - Data loaded: X shape (692703, 84), y shape (692703,). Label dist: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-21 14:33:37,630 - GAT_TF_Local - INFO - Standard train/test split mode selected for GAT (TF).
2025-05-21 14:33:38,154 - GAT_TF_Local - INFO - Replaced infinite values with NaN.
2025-05-21 14:33:38,253 - GAT_TF_Local - INFO - Handling missing values.
2025-05-21 14:33:40,423 - GAT_TF_Local - INFO - Imputed numeric missing values using 'mean'.
2025-05-21 14:33:40,925 - GAT_TF_Local - INFO - Imputed categorical missing values using 'most_frequent'.
2025-05-21 14:33:41,038 - GAT_TF_Local - INFO - Encoded labels. Classes: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-21 14:33:41,122 - GAT_TF_Local - INFO - Encoding categorical features: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-21 14:33:45,151 - GAT_TF_Local - INFO - Normalizing features using standard scaler.
2025-05-21 14:33:46,921 - GAT_TF_Local - INFO - Split data: X_train (554162, 84), y_train (554162,), X_test (138541, 84), y_test (138541,)
2025-05-21 14:33:47,076 - GAT_TF_Local - INFO - Preprocessing info saved to ./results/results_gat_tf_local\GAT_TF_Local_Model_preprocessing_info.json
2025-05-21 14:33:47,077 - GAT_TF_Local - INFO - Data after preprocessing: Input size=84, Num classes=6
2025-05-21 14:33:47,153 - GAT_TF_Local - INFO - Combined train/test for graph: Total nodes 692703, Train 554162, Val/Test 138541
2025-05-21 14:33:47,154 - GAT_TF_Local - INFO - Using special 'IDENTITY_ADJACENCY' marker for 692703 nodes (self-loops only).
2025-05-21 14:33:47,388 - GAT_TF_Local - INFO - Starting GAT (TF) model training (standard split).
2025-05-21 14:33:47,388 - GAT_TF_Local - INFO - Starting GAT (TF) model training for 3 epochs.
2025-05-21 14:34:07,906 - GAT_TF_Local - INFO - Epoch [1/3] Train Loss: 3.0906, Train Acc: 13.12%, Val Loss: 1.0275, Val Acc: 69.09%
2025-05-21 14:34:37,220 - GAT_TF_Local - INFO - Epoch [3/3] Train Loss: 0.9192, Train Acc: 73.40%, Val Loss: 0.4120, Val Acc: 87.68%
2025-05-21 14:34:37,455 - GAT_TF_Local - INFO - Model config saved to ./results/results_gat_tf_local\models\GAT_TF_Local_Model_final_config.json, weights to ./results/results_gat_tf_local\models\GAT_TF_Local_Model_final_weights.h5
2025-05-21 14:34:37,456 - GAT_TF_Local - INFO - Saved final GAT (TF) model components to prefix: ./results/results_gat_tf_local\models\GAT_TF_Local_Model_final
2025-05-21 14:34:37,460 - GAT_TF_Local - INFO - Evaluating GAT (TF) model on test set.
2025-05-21 14:34:37,531 - GAT_TF_Local - ERROR - Value Error: Unable to load weights saved in HDF5 format into a subclassed Model which has not created its variables yet. Call the Model first, then load the weights.
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GAT_tf_local_mode.py", line 1188, in gat_train_tf_local_entrypoint
    test_model_instance = GATNetwork.load_model_local(final_model_prefix)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GAT_tf_local_mode.py", line 332, in load_model_local
    model.load_weights(weights_path)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\saving\legacy\save.py", line 480, in load_weights
    raise ValueError(
ValueError: Unable to load weights saved in HDF5 format into a subclassed Model which has not created its variables yet. Call the Model first, then load the weights.
