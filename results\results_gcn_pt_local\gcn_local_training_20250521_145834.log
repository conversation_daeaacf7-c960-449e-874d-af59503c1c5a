2025-05-21 14:58:34,350 - GCN_Local - INFO - Logging to console and file: results\results_gcn_pt_local\gcn_local_training_20250521_145834.log
2025-05-21 14:58:34,350 - GCN_Local - INFO - GCN PyTorch Local Mode Training Script Initialized.
2025-05-21 14:58:34,351 - GCN_Local - INFO - PyTorch Version: 2.4.1+cpu
2025-05-21 14:58:34,351 - GCN_Local - INFO - Arguments: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'result_dir': 'results\\results_gcn_pt_local', 'model_name': 'GCN_PT_Local_Model', 'label_column': 'Label', 'data_format': 'pkl', 'hidden_size': 128, 'num_layers': 2, 'dropout_rate': 0.5, 'num_epochs': 3, 'learning_rate': 0.01, 'edge_strategy': 'fully_connected', 'k_neighbors': 5, 'radius': 1.0, 'test_split_size': 0.2, 'random_state': 42, 'normalize': True, 'scaler_type': 'standard', 'handle_imbalance': False, 'stratify_split': True, 'numeric_impute_strategy': 'mean', 'categorical_impute_strategy': 'most_frequent', 'use_cv': False, 'cv_folds': 5, 'log_level': 'INFO', 'force_cpu': False}
2025-05-21 14:58:34,352 - GCN_Local - INFO - Using device: cpu
2025-05-21 14:58:34,357 - GCN_Local - INFO - Loading data from E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl with format pkl
2025-05-21 14:58:34,761 - GCN_Local - INFO - Data loaded as DataFrame with shape (692703, 85)
2025-05-21 14:58:34,888 - GCN_Local - INFO - Features shape: (692703, 84), Labels shape: (692703,)
2025-05-21 14:58:34,919 - GCN_Local - INFO - Label distribution: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-21 14:58:34,947 - GCN_Local - INFO - Standard train/test split mode selected.
2025-05-21 14:58:35,447 - GCN_Local - INFO - Replaced infinite values in features with NaN.
2025-05-21 14:58:35,538 - GCN_Local - INFO - Handling missing values in features.
2025-05-21 14:58:37,566 - GCN_Local - INFO - Imputed numeric missing values using 'mean'.
2025-05-21 14:58:38,068 - GCN_Local - INFO - Imputed categorical missing values using 'most_frequent'.
2025-05-21 14:58:38,170 - GCN_Local - INFO - Encoded labels. 6 classes: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-21 14:58:38,249 - GCN_Local - INFO - Encoding categorical features: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-21 14:58:42,219 - GCN_Local - INFO - Normalizing features using standard scaler.
2025-05-21 14:58:44,471 - GCN_Local - INFO - Split data: X_train (554162, 84), y_train (554162,), X_test (138541, 84), y_test (138541,)
2025-05-21 14:58:44,519 - GCN_Local - INFO - Preprocessing info for train/test split saved to: results\results_gcn_pt_local\GCN_PT_Local_Model_preprocessing_info.json
2025-05-21 14:58:44,519 - GCN_Local - INFO - Data after preprocessing (train/test split): Input size=84, Num classes=6
2025-05-21 14:58:44,520 - GCN_Local - INFO - Preparing graph datasets with edge strategy: fully_connected
2025-05-21 14:58:44,569 - GCN_Local - WARNING - Number of nodes (554162) exceeds threshold (20000) for creating a dense fully connected graph. Creating graph with no explicit inter-node edges. GCNConv will use self-loops.
2025-05-21 14:58:44,571 - GCN_Local - INFO - Created graph data: Nodes=554162, Edges=0 (Strategy: fully_connected)
2025-05-21 14:58:44,584 - GCN_Local - WARNING - Number of nodes (138541) exceeds threshold (20000) for creating a dense fully connected graph. Creating graph with no explicit inter-node edges. GCNConv will use self-loops.
2025-05-21 14:58:44,585 - GCN_Local - INFO - Created graph data: Nodes=138541, Edges=0 (Strategy: fully_connected)
2025-05-21 14:58:44,585 - GCN_Local - INFO - Test graph created.
2025-05-21 14:58:44,746 - GCN_Local - INFO - Starting GCN model training (standard split).
2025-05-21 14:58:44,749 - GCN_Local - INFO - Starting GCN model training for 3 epochs on device: cpu
2025-05-21 14:58:49,567 - GCN_Local - INFO - 模型配置保存至: results\results_gcn_pt_local\models_gcn_local\GCN_PT_Local_Model_best_config.json
2025-05-21 14:58:49,567 - GCN_Local - INFO - 模型权重保存至: results\results_gcn_pt_local\models_gcn_local\GCN_PT_Local_Model_best_weights.pth
2025-05-21 14:58:49,569 - GCN_Local - INFO - Epoch 1/3 - train_loss: 1.8344, train_acc: 20.62%, val_loss: 1.1363, val_acc: 85.20%
2025-05-21 14:58:54,141 - GCN_Local - INFO - 模型配置保存至: results\results_gcn_pt_local\models_gcn_local\GCN_PT_Local_Model_best_config.json
2025-05-21 14:58:54,141 - GCN_Local - INFO - 模型权重保存至: results\results_gcn_pt_local\models_gcn_local\GCN_PT_Local_Model_best_weights.pth
2025-05-21 14:58:58,886 - GCN_Local - INFO - 模型配置保存至: results\results_gcn_pt_local\models_gcn_local\GCN_PT_Local_Model_best_config.json
2025-05-21 14:58:58,886 - GCN_Local - INFO - 模型权重保存至: results\results_gcn_pt_local\models_gcn_local\GCN_PT_Local_Model_best_weights.pth
2025-05-21 14:58:58,887 - GCN_Local - INFO - Epoch 3/3 - train_loss: 0.6119, train_acc: 82.33%, val_loss: 0.4165, val_acc: 85.62%
2025-05-21 14:58:58,893 - GCN_Local - INFO - 模型配置保存至: results\results_gcn_pt_local\models_gcn_local\GCN_PT_Local_Model_final_config.json
2025-05-21 14:58:58,893 - GCN_Local - INFO - 模型权重保存至: results\results_gcn_pt_local\models_gcn_local\GCN_PT_Local_Model_final_weights.pth
2025-05-21 14:58:58,894 - GCN_Local - INFO - Saved final GCN model components to prefix: results\results_gcn_pt_local\models_gcn_local\GCN_PT_Local_Model_final
2025-05-21 14:59:00,130 - GCN_Local - INFO - Training history plot saved to results\results_gcn_pt_local\plots\GCN_Local_training_history.png
2025-05-21 14:59:00,155 - GCN_Local - INFO - Loading best saved model for testing from: results\results_gcn_pt_local\models_gcn_local\GCN_PT_Local_Model_best_config.json
2025-05-21 14:59:00,190 - GCN_Local - INFO - 从以下路径加载模型成功: results\results_gcn_pt_local\models_gcn_local\GCN_PT_Local_Model_best_config.json, results\results_gcn_pt_local\models_gcn_local\GCN_PT_Local_Model_best_weights.pth
2025-05-21 14:59:00,191 - GCN_Local - INFO - Evaluating GCN model on test set.
2025-05-21 14:59:00,719 - GCN_Local - INFO - Classification Report (GCN_PT_Local_Model):                  precision    recall  f1-score   support

          BENIGN       0.84      0.98      0.90     88006
   DoS GoldenEye       0.00      0.00      0.00      2059
        DoS Hulk       0.91      0.70      0.79     46215
DoS Slowhttptest       0.00      0.00      0.00      1100
   DoS slowloris       0.00      0.00      0.00      1159
      Heartbleed       0.00      0.00      0.00         2

        accuracy                           0.86    138541
       macro avg       0.29      0.28      0.28    138541
    weighted avg       0.84      0.86      0.84    138541

2025-05-21 14:59:01,537 - GCN_Local - INFO - Confusion matrix plot saved to results\results_gcn_pt_local\plots\GCN_PT_Local_Model_confusion_matrix.png
2025-05-21 14:59:01,538 - GCN_Local - INFO - Classification report saved to results\results_gcn_pt_local\GCN_PT_Local_Model_classification_report.txt
2025-05-21 14:59:01,539 - GCN_Local - INFO - Final Test Metrics (GCN_PT_Local_Model): Accuracy=85.62%, Precision=0.8352, Recall=0.8562, F1=0.8381
2025-05-21 14:59:01,560 - GCN_Local - INFO - Overall GCN training summary saved to: results\results_gcn_pt_local\GCN_PT_Local_Model_training_summary.json
2025-05-21 14:59:01,560 - GCN_Local - INFO - GCN PyTorch Local Mode Training Script Finished Successfully.
