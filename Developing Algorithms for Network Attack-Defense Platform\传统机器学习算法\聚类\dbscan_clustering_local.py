"""DBSCAN 聚类 - 增强版

此脚本使用DBSCAN算法对数据集进行聚类分析，
支持多种数据格式、灵活的预处理选项和结果可视化。

DBSCAN是一种基于密度的聚类算法，它可以发现任意形状的聚类，
并且能够自动识别噪声点，适用于无需预先指定聚类数量的场景。

功能:
- 灵活数据加载(支持pkl、csv、json格式)
- 高级聚类分析与评估
- 结果可视化(散点图、聚类分布)
- 模型保存与管理(MinIO存储)
- 完整的日志记录
- 错误处理机制

使用方法:
python dbscan_clustering_local.py --data your_data_path.pkl --eps 0.5 --min_samples 5 --output ./results
"""

import pickle
import requests
import joblib
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import logging
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 非交互式后端，适用于无显示环境
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import traceback
from sklearn.cluster import DBSCAN
from sklearn.metrics import silhouette_score, calinski_harabasz_score
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.model_selection import KFold
from sklearn.neighbors import NearestNeighbors
from sklearn.metrics import adjusted_rand_score
import subprocess
import time
import shutil

# 常量定义
MINIO_URL = "minio-prophecis.prophecis:9000"

MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"


MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

# 使用常量定义Minio凭证，便于维护
MINIO_ACCESS_KEY = 'AKIAIOSFODNN7EXAMPLE'
MINIO_SECRET_KEY = 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY'

# 辅助函数：将对象转换为JSON可序列化的格式
def convert_to_serializable(obj):
    """
    将对象转换为可JSON序列化的格式。
    
    参数:
        obj: 要转换的对象
        
    返回:
        可JSON序列化的对象
    """
    if isinstance(obj, dict):
        return {k: convert_to_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list) or isinstance(obj, tuple):
        return [convert_to_serializable(item) for item in obj]
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return convert_to_serializable(obj.tolist())
    else:
        return obj

def setup_logging(log_dir, log_level=logging.INFO):
    """
    设置日志配置。
    
    参数:
        log_dir (str): 日志文件保存目录
        log_level: 日志级别，默认为INFO
        
    返回:
        logger: 配置好的日志记录器
    """
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 生成带时间戳的日志文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"dbscan_clustering_{timestamp}.log")
    
    # 获取根日志记录器
    logger = logging.getLogger()
    
    # 清除已有的处理器
    if logger.handlers:
        for handler in logger.handlers:
            logger.removeHandler(handler)
    
    # 设置日志级别
    logger.setLevel(log_level)
    
    # 创建文件处理器
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(log_level)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    
    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器到日志记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    logger.info(f"日志配置完成，日志文件: {log_file}")
    return logger

# 灵活加载数据集，支持不同的数据格式
def flexible_data_load(data_path, feature_cols=None, data_format="pkl"):
    """
    灵活加载不同格式的数据。
    
    参数:
        data_path (str): 数据文件路径
        feature_cols (list): 要使用的特征列名列表
        data_format (str): 数据格式，支持"pkl"、"csv"、"npy"
        
    返回:
        array-like: 加载的特征数据
    """
    logger = logging.getLogger()
    logger.info(f"从 {data_path} 加载数据，格式: {data_format}")
    
    try:
        if data_format.lower() == "pkl":
            with open(data_path, 'rb') as f:
                data = pickle.load(f)
                
            # 处理不同的数据结构
    if isinstance(data, pd.DataFrame):
                if feature_cols:
                    return data[feature_cols].values
                else:
                    return data.values
            elif isinstance(data, dict) and "X" in data:
                return data["X"]
            elif isinstance(data, np.ndarray):
                return data
            else:
                raise ValueError(f"不支持的pickle数据结构: {type(data)}")
                
        elif data_format.lower() == "csv":
            data = pd.read_csv(data_path)
            if feature_cols:
                return data[feature_cols].values
        else:
                return data.values
                
        elif data_format.lower() == "npy":
            return np.load(data_path)
            
        else:
            raise ValueError(f"不支持的数据格式: {data_format}")
            
    except Exception as e:
        logger.error(f"加载数据时出错: {str(e)}")
        raise

def dbscan_clustering(dataset, job_params, model_name, result_dir, fit_params=None):
    """
    执行DBSCAN聚类分析。
    
    参数:
        dataset (dict): 数据集配置，包含数据路径和特征列
        job_params (dict): DBSCAN参数，包含eps、min_samples等
    model_name (str): 模型名称
    result_dir (str): 结果保存目录
        fit_params (dict): 训练参数配置
    
    返回:
        dict: 聚类结果，包含聚类数、噪声点数量、模型路径等信息
    """
    logger = logging.getLogger()
        logger.info("开始DBSCAN聚类分析")
        
    # 确保结果目录存在
    if not os.path.exists(result_dir):
        os.makedirs(result_dir)
    
    try:
        # 加载数据
        data_path = "/workspace/" + dataset["training_data_path"]
        logger.info(f"加载数据: {data_path}")
        
        try:
            X = flexible_data_load(data_path, dataset.get("feature_cols"), dataset.get("data_format", "pkl"))
            logger.info(f"数据加载成功，形状: {X.shape}")
        except Exception as e:
            logger.error(f"数据加载失败: {str(e)}")
            raise
        
        # 标准化数据（如果需要）
        if job_params.get("standardize", True):
            logger.info("标准化数据")
            scaler = StandardScaler()
            X = scaler.fit_transform(X)
        
        # 提取DBSCAN参数
        eps = job_params.get("eps", 0.5)
        min_samples = job_params.get("min_samples", 5)
        metric = job_params.get("metric", "euclidean")
        algorithm = job_params.get("algorithm", "auto")
        
        logger.info(f"DBSCAN参数: eps={eps}, min_samples={min_samples}, metric={metric}, algorithm={algorithm}")
        
        # 创建DBSCAN模型
        dbscan = DBSCAN(
            eps=eps,
            min_samples=min_samples,
            metric=metric,
            algorithm=algorithm
        )
        
        # 训练模型并计时
        start_time = time.time()
        logger.info("开始聚类分析...")
        dbscan.fit(X)
        training_time = time.time() - start_time
        logger.info(f"聚类分析完成，耗时: {training_time:.2f}秒")
        
        # 获取聚类标签
        labels = dbscan.labels_
        
        # 计算聚类指标
        n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
        n_noise = list(labels).count(-1)
        noise_percentage = 100.0 * n_noise / len(labels)
        
        logger.info(f"聚类数量: {n_clusters}")
        logger.info(f"噪声点数量: {n_noise} ({noise_percentage:.2f}%)")
        
        # 计算轮廓系数（如果聚类数>=2）
        silhouette_avg = None
        if n_clusters >= 2:
            try:
                # 只对非噪声点计算轮廓系数
                non_noise_mask = (labels != -1)
                if np.sum(non_noise_mask) >= 2:
                    silhouette_avg = silhouette_score(X[non_noise_mask], labels[non_noise_mask])
                    logger.info(f"轮廓系数: {silhouette_avg:.4f}")
            except Exception as e:
                logger.warning(f"计算轮廓系数失败: {str(e)}")
        
        # 保存模型
        model_path = os.path.join(result_dir, f"{model_name}.pkl")
        pickle_path = os.path.join(result_dir, f"{model_name}.pickle")
        
        # 创建包含模型和元数据的字典
        model_data = {
            "model": dbscan,
            "params": job_params,
            "n_clusters": n_clusters,
            "n_noise": n_noise,
            "noise_percentage": noise_percentage,
            "training_time": training_time
        }
        
        if silhouette_avg is not None:
            model_data["silhouette_score"] = silhouette_avg
        
        # 如果标准化了数据，保存scaler
        if job_params.get("standardize", True):
            model_data["scaler"] = scaler
        
        # 保存模型
        with open(model_path, 'wb') as f:
            pickle.dump(model_data, f)
        
        # 创建pickle文件的副本（兼容性）
        shutil.copy(model_path, pickle_path)
        
        logger.info(f"模型已保存到: {model_path}")
        
        # 保存聚类结果
        results_path = os.path.join(result_dir, f"{model_name}_results.json")
        
        # 准备结果字典
        results = {
            "n_clusters": n_clusters,
            "n_noise": n_noise,
            "noise_percentage": noise_percentage,
            "training_time": training_time,
            "model_path": model_path,
            "pickle_path": pickle_path,
            "params": job_params
        }
        
        if silhouette_avg is not None:
            results["silhouette_score"] = float(silhouette_avg)
        
        # 保存聚类标签分布
        cluster_distribution = {}
        for label in set(labels):
            count = list(labels).count(label)
            percentage = 100.0 * count / len(labels)
            cluster_distribution[str(label)] = {
                "count": count,
                "percentage": float(percentage)
            }
        
        results["cluster_distribution"] = cluster_distribution
        
        # 保存结果到JSON文件
        with open(results_path, 'w') as f:
            json.dump(convert_to_serializable(results), f, indent=4)
        
        logger.info(f"聚类结果已保存到: {results_path}")
        
        return results
        
    except Exception as e:
        logger.error(f"DBSCAN聚类过程中出错: {str(e)}")
        logger.exception("详细错误信息:")
        raise

    
def model_upload(model_name, model_path):
    """
    将模型上传到MinIO存储。

    参数:
    - model_name (str): 模型名称。
    - model_path (str): 模型文件路径。

    返回:
    - result (dict): 上传结果字典，包含source_path。
    - ret_code (int): 返回码，0 表示成功，其他值表示失败。
    """
    logger = logging.getLogger()
    logger.info(f"开始上传模型 {model_name}")
    
    # 检查模型文件是否存在
    if not os.path.exists(model_path):
        logger.error(f"模型文件不存在: {model_path}")
        return {"error": "Model file not found"}, -1
    
    try:
        # 构建上传路径和源URL
        upload_path = f"models/dbscan_clustering/{model_name}.pickle"
        source = f"s3a://mlss/users/admin/{upload_path}"
        
        # 上传模型到MinIO
        logger.info(f"上传模型到路径: {upload_path}")
        cmd = f"mc cp {model_path} mlss/mlss/users/admin/{upload_path}"
        process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()
        
        if process.returncode != 0:
            logger.error(f"模型上传失败: {stderr.decode()}")
            return {"error": stderr.decode()}, -1
        
        logger.info(f"模型上传成功: {stdout.decode()}")
        return {"source_path": source}, 0
    except Exception as e:
        logger.error(f"模型上传过程中发生错误: {str(e)}")
        logger.exception("详细错误信息:")
        return {"error": str(e)}, -1
    
def model_register(model_name, user_id, source, model_type="DBSCAN"):
    """
    注册模型到模型工厂。

    参数:
    - model_name (str): 模型名称。
    - user_id (str): 用户ID。
    - source (str): 模型的 MinIO 存储路径。
    - model_type (str): 模型类型，默认为"DBSCAN"。

    返回:
    - result (dict): 注册结果字典，包含model_id和model_version_id。
    - ret_code (int): 返回码，0 表示成功，其他值表示失败。
    """
    logger = logging.getLogger()
    logger.info(f"开始注册模型 {model_name} 到模型工厂")
    
    # 生成请求头
    headers = {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: user_id
    }
    
    # 构建请求参数
    params = {
          "model_name": model_name,
        "model_type": model_type,
        "file_name": model_name + ".pickle",
          "s3_path": source,
        "group_id": 1,  # 默认组ID
          "training_id": model_name,
          "training_flag": 1,
        }
    
    try:
        logger.info(f"发送模型注册请求: {params}")
        r = requests.post(MODEL_FACTORY_URL + MODEL_ADD_URL, 
                         data=json.dumps(params), 
                         headers=headers)
        
    status_code = r.status_code
        res_data = r.content.decode()
        
    if status_code == 200:
            result = json.loads(res_data)
            logger.info(f"模型注册成功，模型ID: {result['result']['model_id']}")
            return result, 0
    else:
            logger.error(f"模型注册失败，状态码: {status_code}, 响应: {res_data}")
        return res_data, -1
    except Exception as e:
        logger.error(f"模型注册过程中发生错误: {str(e)}")
        logger.exception("详细错误信息:")
        return str(e), -1

def model_push(model_id, model_version_id, factory_name):
    """
    推送模型到工厂生产系统。

    参数:
    - model_id (str): 模型ID。
    - model_version_id (str): 模型版本ID。
    - factory_name (str): 工厂名称。

    返回:
    - result (dict): 推送结果字典。
    - ret_code (int): 返回码，0 表示成功，其他值表示失败。
    """
    logger = logging.getLogger()
    
    # 如果工厂名称为None，跳过推送
    if factory_name == "None":
        logger.info("工厂名称为None，跳过模型推送")
        return "Factory Name is none, Skip Model Push.", 0        
  
    # 生成请求头
    headers = {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: "admin"  # 默认使用admin用户
    }
    
    # 构建请求参数
    params = {
        "factory_name": factory_name,
        "model_type": "DBSCAN",
        "model_usage": "Clustering"
    }
    
    try:
        logger.info(f"推送模型 (ID: {model_id}, 版本: {model_version_id}) 到工厂 {factory_name}")
        r = requests.post(
            f"{MODEL_FACTORY_URL}{MODEL_PUSH_URL}/{str(model_version_id)}",
            data=json.dumps(params),
            headers=headers
        )
        
    status_code = r.status_code
        res_data = r.content.decode()
        
    if status_code == 200:
            result = json.loads(res_data)
            logger.info(f"模型推送成功: {result}")
            return result, 0
    else:
            logger.error(f"模型推送失败，状态码: {status_code}, 响应: {res_data}")
        return res_data, -1
    except Exception as e:
        logger.error(f"模型推送过程中发生错误: {str(e)}")
        logger.exception("详细错误信息:")
        return str(e), -1

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

def cross_validate_dbscan(X, param_grid, cv=5, random_state=42):
    """
    为DBSCAN执行交叉验证，找到最佳参数组合。
    
    参数:
        X (array-like): 特征数据
        param_grid (dict): 参数网格，包含eps和min_samples的可能值
        cv (int): 交叉验证折数
        random_state (int): 随机种子
        
    返回:
        tuple: (最佳参数字典, 所有交叉验证结果)
    """
    logger = logging.getLogger()
    logger.info(f"开始DBSCAN交叉验证，参数网格大小: {len(param_grid['eps']) * len(param_grid['min_samples'])}")
    
    # 创建交叉验证分割
    cv_splits = cross_validation_split(X, n_splits=cv, shuffle=True, random_state=random_state)
    
    # 存储结果
    results = []
    
    # 遍历参数组合
    for eps in param_grid['eps']:
        for min_samples in param_grid['min_samples']:
            logger.info(f"评估参数: eps={eps}, min_samples={min_samples}")
            
            fold_scores = []
            fold_n_clusters = []
            fold_noise_percentages = []
            
            # 对每个交叉验证折执行DBSCAN
            for fold_idx, (train_idx, val_idx) in enumerate(cv_splits):
                # 在训练集上拟合DBSCAN
                dbscan = DBSCAN(
                    eps=eps,
                    min_samples=min_samples,
                    metric=param_grid.get('metric', 'euclidean'),
                    algorithm=param_grid.get('algorithm', 'auto')
                )
                
                X_train = X[train_idx]
                X_val = X[val_idx]
                
                # 训练模型
                start_time = time.time()
                dbscan.fit(X_train)
                train_time = time.time() - start_time
                
                # 获取训练集上的聚类标签
                train_labels = dbscan.labels_
                
                # 计算训练集上的指标
                n_clusters = len(set(train_labels)) - (1 if -1 in train_labels else 0)
                n_noise = list(train_labels).count(-1)
                noise_percentage = 100.0 * n_noise / len(train_labels)
                
                # 如果只有一个聚类或全是噪声点，则评分为最低
                if n_clusters <= 1:
                    silhouette_avg = -1.0
                else:
                    # 计算轮廓系数
                    try:
                        silhouette_avg = silhouette_score(X_train, train_labels)
                    except:
                        silhouette_avg = -1.0
                
                # 记录此折的结果
                fold_scores.append(silhouette_avg)
                fold_n_clusters.append(n_clusters)
                fold_noise_percentages.append(noise_percentage)
                
                logger.debug(f"折 {fold_idx+1}: 聚类数={n_clusters}, 噪声点={n_noise} ({noise_percentage:.2f}%), 轮廓系数={silhouette_avg:.4f}")
            
            # 计算平均指标
            avg_score = np.mean(fold_scores)
            avg_n_clusters = np.mean(fold_n_clusters)
            avg_noise_percentage = np.mean(fold_noise_percentages)
            
            # 存储此参数组合的结果
            param_result = {
                'eps': eps,
                'min_samples': min_samples,
                'avg_silhouette_score': float(avg_score),
                'avg_n_clusters': float(avg_n_clusters),
                'avg_noise_percentage': float(avg_noise_percentage),
                'fold_scores': fold_scores,
                'fold_n_clusters': fold_n_clusters,
                'fold_noise_percentages': fold_noise_percentages
            }
            
            results.append(param_result)
            logger.info(f"参数 eps={eps}, min_samples={min_samples}: 平均轮廓系数={avg_score:.4f}, 平均聚类数={avg_n_clusters:.1f}")
    
    # 找出最佳参数组合（基于轮廓系数）
    valid_results = [r for r in results if r['avg_silhouette_score'] > -1.0 and r['avg_n_clusters'] > 1]
    
    if not valid_results:
        logger.warning("没有找到有效的参数组合，使用默认参数")
        best_params = {
            'eps': param_grid['eps'][0],
            'min_samples': param_grid['min_samples'][0]
        }
                        else:
        # 按轮廓系数排序
        valid_results.sort(key=lambda x: x['avg_silhouette_score'], reverse=True)
        best_result = valid_results[0]
        
        best_params = {
            'eps': best_result['eps'],
            'min_samples': best_result['min_samples']
        }
        
        logger.info(f"最佳参数: eps={best_params['eps']}, min_samples={best_params['min_samples']}, "
                    f"轮廓系数={best_result['avg_silhouette_score']:.4f}, "
                    f"聚类数={best_result['avg_n_clusters']:.1f}")
    
    return best_params, results

def cross_validation_split(X, n_splits=5, shuffle=True, random_state=42):
    """
    创建交叉验证的数据分割，使用标准K折交叉验证
    
    参数:
    X: 特征数据
    n_splits: 折数
    shuffle: 是否打乱数据
    random_state: 随机种子
    
    返回:
    splits: 包含训练和验证索引的列表
    """
    logger = logging.getLogger()
    logger.info(f"执行 {n_splits} 折交叉验证")
    
    # 使用K折交叉验证
    kf = KFold(n_splits=n_splits, shuffle=shuffle, random_state=random_state)
    logger.info("使用标准k折交叉验证")
    
    splits = list(kf.split(X))
    return splits

# 更新main函数，添加交叉验证相关参数
def main():
    """
    命令行入口函数，使用argparse处理命令行参数
    """
    # 使用新的主函数，由if __name__ == "__main__"部分处理
    pass

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='DBSCAN 聚类算法 - 支持网络流量分类和其他数据集')
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='DBSCAN 模型参数，以JSON字典形式提供，例如：{"eps": 0.5, "min_samples": 5, "metric": "euclidean", "algorithm": "auto", "standardize": true}')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='数据集配置，以JSON字典形式提供，例如：{"training_data_path": "path/to/data.pkl", "feature_cols": ["col1", "col2"], "data_format": "pkl"}')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='模型信息，包含model_name和group_id')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='模型工厂名称')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='训练结果保存目录')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='''训练配置参数，以JSON字典形式提供，例如：
                    {
                        "use_cross_validation": true,
                        "cv_folds": 5,
                        "data_format": "pkl"
                    }
                    交叉验证参数说明：
                    - use_cross_validation: 是否使用交叉验证 (布尔值)
                    - cv_folds: 交叉验证折数 (整数)
                    - data_format: 数据格式 (字符串)
                    ''')
    parser.add_argument('--log_level', dest='log_level', type=str, default="INFO",
                    help='日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL')
                    
    print("开始 DBSCAN 聚类任务，参数:\n" + str(sys.argv) + "\n")
    args = parser.parse_args()
    
    # 设置日志级别
    log_level = getattr(logging, args.log_level.upper(), logging.INFO)
    
    # 解析参数
    job_params = args.job_params
    dataset = args.dataset
    fit_params = args.fit_params if args.fit_params else {}
    model = args.model
    factory_name = args.factory_name
    result_dir = args.result_dir
    
    if not os.path.exists(result_dir):
        os.makedirs(result_dir)
    
    # 设置日志
    logger = setup_logging(result_dir, log_level)
    
    try:
        # 运行聚类分析
        model_name = model["model_name"] if model and "model_name" in model else "dbscan_model"
        
        # 检查是否使用交叉验证
        use_cross_validation = fit_params.get("use_cross_validation", False)
        
        if use_cross_validation:
            logger.info("使用交叉验证模式")
            cv_folds = fit_params.get("cv_folds", 5)
            
            # 加载数据
            logger.info("加载数据进行交叉验证")
            data_path = "/workspace/" + dataset["training_data_path"]
            
            try:
                X = flexible_data_load(data_path, dataset.get("feature_cols"), dataset.get("data_format", "pkl"))
            except Exception as e:
                logger.error(f"加载数据失败: {str(e)}")
                sys.exit(1)
            
            # 标准化数据
            if job_params.get("standardize", True):
                logger.info("标准化数据")
                scaler = StandardScaler()
                X = scaler.fit_transform(X)
            
            # 准备参数网格
            eps_values = job_params.get("eps_values", [0.3, 0.5, 0.7, 1.0])
            min_samples_values = job_params.get("min_samples_values", [3, 5, 10, 15])
            
            param_grid = {
                'eps': eps_values,
                'min_samples': min_samples_values
            }
            
            logger.info(f"参数网格: eps={eps_values}, min_samples={min_samples_values}")
            
            # 执行交叉验证
            best_params, cv_results = cross_validate_dbscan(
                X=X,
                param_grid=param_grid,
                cv=cv_folds,
                random_state=42
            )
            
            # 更新最佳参数
            job_params.update(best_params)
            logger.info(f"使用交叉验证选择的最佳参数: eps={best_params['eps']}, min_samples={best_params['min_samples']}")
            
            # 保存交叉验证结果
            cv_results_path = os.path.join(result_dir, f"{model_name}_cv_results.json")
            with open(cv_results_path, 'w') as f:
                json.dump(convert_to_serializable(cv_results), f, indent=4)
            
            logger.info(f"交叉验证结果已保存至 {cv_results_path}")
        
        # 聚类分析
        results = dbscan_clustering(dataset, job_params, model_name, result_dir, fit_params)
        
        # 上传模型
        try:
            model_path = results["pickle_path"]
            logger.info(f"上传模型: {model_path}")
            upload_result, upload_code = model_upload(model_name, model_path)
            
            if upload_code != 0:
                logger.error(f"模型上传失败: {upload_result}")
                sys.exit(1)
            
            # 如果提供了模型信息，则注册和推送模型
            if model and "group_id" in model:
                user_id = "admin"  # 默认使用admin用户
                
                # 注册模型
                logger.info(f"注册模型: {model_name}")
                register_result, register_code = model_register(
                    model_name, 
                    user_id, 
                    upload_result["source_path"]
                )
                
                if register_code != 0:
                    logger.error(f"模型注册失败: {register_result}")
                    sys.exit(1)
                
                model_id = register_result["result"]["model_id"]
                model_version_id = register_result["result"]["model_version_id"]
                
                # 推送模型
                if factory_name and factory_name != "None":
                    logger.info(f"推送模型: {model_id}, 版本: {model_version_id}, 工厂: {factory_name}")
                    push_result, push_code = model_push(model_id, model_version_id, factory_name)
                    
                    if push_code != 0:
                        logger.error(f"模型推送失败: {push_result}")
                        sys.exit(1)
                    
                    logger.info(f"模型推送结果: {push_result}")
        except Exception as e:
            logger.error(f"模型上传/注册/推送过程中出错: {str(e)}")
            logger.exception("详细错误信息:")
        
        # 输出结果摘要
        logger.info("=== DBSCAN聚类分析结果摘要 ===")
        logger.info(f"聚类数量: {results['n_clusters']}")
        logger.info(f"噪声点数量: {results['n_noise']} ({results['noise_percentage']:.2f}%)")
        
        if 'silhouette_score' in results:
            logger.info(f"轮廓系数: {results['silhouette_score']:.4f}")
        
        logger.info(f"训练时间: {results['training_time']:.2f}秒")
        logger.info(f"模型保存路径: {results['model_path']}")
        logger.info("================================")
        
    except Exception as e:
        logger.error(f"运行过程中发生错误: {str(e)}")
        logger.exception("详细错误信息:")
        sys.exit(1)
    
    sys.exit(0)
   
  
