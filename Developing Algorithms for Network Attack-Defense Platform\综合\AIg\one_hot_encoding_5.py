"""独热编码"""

import sys
import pandas as pd
from sklearn.preprocessing import OneHotEncoder

def one_hot_encoding(input_file, target_column):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - target_column: 目标变量的列名
    """
    data = pd.read_csv(input_file)
    X = data.drop(target_column, axis=1)
    
    encoder = OneHotEncoder(sparse=False)
    X_new = encoder.fit_transform(X)
    
    output_file = 'one_hot_encoded_data.csv'
    pd.DataFrame(X_new, columns=encoder.get_feature_names_out(X.columns)).to_csv(output_file, index=False)
    print(f"One-hot encoding completed. Output saved to {output_file}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python one_hot_encoding.py <input_file> <target_column>")
        sys.exit(1)
    input_file, target_column = sys.argv[1], sys.argv[2]
    one_hot_encoding(input_file, target_column)
