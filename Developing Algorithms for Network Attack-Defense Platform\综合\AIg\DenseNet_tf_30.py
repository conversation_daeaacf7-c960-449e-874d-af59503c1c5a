import os
import tensorflow as tf
import numpy as np
import pickle
import pandas as pd
import json
from sklearn.preprocessing import StandardScaler
from sklearn.preprocessing import LabelEncoder
from sklearn.preprocessing import OrdinalEncoder
from sklearn.model_selection import train_test_split
# from tensorflow.keras.models import Model
# from tensorflow.keras.layers import Dense, Input
# from tensorflow.keras.applications.densenet import DenseNet121
# from tensorflow.keras.optimizers import Adam
# from tensorflow.keras.losses import CategoricalCrossentropy
# from tensorflow.keras.preprocessing.image import ImageDataGenerator
# from tensorflow.keras.utils import to_categorical
from PIL import Image
import xml.etree.ElementTree as ET


class DenseNetForTabular(tf.keras.Model):
    def __init__(self, input_size, num_classes):
        super(DenseNetForTabular, self).__init__()
        self.densenet = tf.keras.applications.densenet.DenseNet121(include_top=False, weights='imagenet', input_shape=(input_size, input_size, 3))
        self.global_pool = tf.keras.layers.GlobalAveragePooling2D()
        self.classifier = tf.keras.layers.Dense(num_classes, activation='softmax')

    def call(self, x):
        x = self.densenet(x)
        x = self.global_pool(x)
        return self.classifier(x)


class UniversalImageDataset(tf.keras.utils.Sequence):
    def __init__(self, data_dir, transform=None, dataset_type='folder', annotations_file=None):
        self.data_dir = data_dir
        self.transform = transform
        self.dataset_type = dataset_type

        if dataset_type in ['folder', 'imagenet', 'flower']:
            self.image_paths, self.labels = self.load_from_folder()
        elif dataset_type == 'coco':
            self.image_paths, self.labels = self.load_coco(annotations_file)
        elif dataset_type == 'voc':
            self.image_paths, self.labels = self.load_voc(annotations_file)
        elif dataset_type == 'yolo':
            self.image_paths, self.labels = self.load_yolo(annotations_file)
        elif dataset_type == 'pickle':
            self.image_paths, self.labels = self.load_pickle(annotations_file)
        elif dataset_type == 'mnist':
            self.dataset = self.load_mnist()
        elif dataset_type == 'cifar10':
            self.dataset = self.load_cifar10()
        elif dataset_type == 'cifar100':
            self.dataset = self.load_cifar100()
        else:
            raise ValueError("Unsupported dataset type.")

    def load_from_folder(self):
        classes = os.listdir(self.data_dir)
        class_to_idx = {cls: idx for idx, cls in enumerate(classes)}

        image_paths = []
        labels = []

        for cls in classes:
            class_dir = os.path.join(self.data_dir, cls)
            if os.path.isdir(class_dir):
                for img_file in os.listdir(class_dir):
                    if img_file.endswith(('.jpg', '.jpeg', '.png')):
                        img_path = os.path.join(class_dir, img_file)
                        image_paths.append(img_path)
                        labels.append(class_to_idx[cls])

        return image_paths, labels

    def load_coco(self, annotations_file):
        with open(annotations_file) as f:
            annotations = json.load(f)

        image_paths = []
        labels = []
        for item in annotations['images']:
            img_id = item['id']
            img_file = os.path.join(self.data_dir, item['file_name'])
            image_paths.append(img_file)
            label = self.get_label_for_image(img_id, annotations)
            labels.append(label)

        return image_paths, labels

    def load_voc(self, annotations_file):
        image_paths = []
        labels = []

        # 读取所有 XML 文件
        with open(annotations_file, 'r') as file:
            xml_files = file.readlines()

        for xml_file in xml_files:
            xml_file = xml_file.strip()
            tree = ET.parse(xml_file)
            root = tree.getroot()

            # 获取图像文件路径
            image_name = root.find('filename').text
            img_path = os.path.join(self.data_dir, image_name)
            image_paths.append(img_path)

            # 提取标签
            objects = root.findall('object')
            boxes = []
            for obj in objects:
                class_name = obj.find('name').text
                bbox = obj.find('bndbox')
                xmin = float(bbox.find('xmin').text)
                ymin = float(bbox.find('ymin').text)
                xmax = float(bbox.find('xmax').text)
                ymax = float(bbox.find('ymax').text)
                boxes.append((class_name, xmin, ymin, xmax, ymax))

            labels.append(boxes)

        return image_paths, labels

    def load_yolo(self, annotations_file):
        image_paths = []
        labels = []

        for img_file in os.listdir(self.data_dir):
            if img_file.endswith(('.jpg', '.png', '.jpeg')):
                img_path = os.path.join(self.data_dir, img_file)
                image_paths.append(img_path)

                # 加载对应的YOLO标签文件
                label_file = img_file.replace('.jpg', '.txt').replace('.png', '.txt').replace('.jpeg', '.txt')
                label_path = os.path.join(self.data_dir, label_file)

                if os.path.exists(label_path):
                    with open(label_path, 'r') as f:
                        boxes = []
                        for line in f.readlines():
                            class_id, x_center, y_center, width, height = map(float, line.strip().split())
                            boxes.append((class_id, x_center, y_center, width, height))
                        labels.append(boxes)  # 以边界框列表形式存储
                else:
                    labels.append([])  # 无标签时返回空列表

        return image_paths, labels

    def load_pickle(self, pkl_file):
        # 从.pkl 文件加载数据
        with open(pkl_file, 'rb') as f:
            data = pickle.load(f)

        # 假设数据为字典格式，包含特征和标签
        if isinstance(data, dict):
            images = data['images']  # 假设图像数据在 'images' 键下
            labels = data['labels']  # 假设标签在 'labels' 键下
        elif isinstance(data, pd.DataFrame):
            images = data['image_paths'].tolist()  # 假设图像路径在某列
            labels = data['labels'].tolist()  # 假设标签在某列
        else:
            raise ValueError("Unsupported data format in pickle file.")

        return images, labels

    def load_mnist(self):
        (x_train, y_train), (x_test, y_test) = tf.keras.datasets.mnist.load_data()
        x_train = np.expand_dims(x_train, axis=-1)
        x_test = np.expand_dims(x_test, axis=-1)
        x_train = x_train.astype('float32') / 255.
        x_test = x_test.astype('float32') / 255.
        y_train = tf.keras.utils.to_categorical(y_train, num_classes=10)
        y_test = tf.keras.utils.to_categorical(y_test, num_classes=10)

        return [(x_train[i], y_train[i]) for i in range(len(x_train))], [(x_test[i], y_test[i]) for i in range(len(x_test))]

    def load_cifar10(self):
        (x_train, y_train), (x_test, y_test) = tf.keras.datasets.cifar10.load_data()
        x_train = x_train.astype('float32') / 255.
        x_test = x_test.astype('float32') / 255.
        y_train = tf.keras.utils.to_categorical(y_train, num_classes=10)
        y_test = tf.keras.utils.to_categorical(y_test, num_classes=10)

        return [(x_train[i], y_train[i]) for i in range(len(x_train))], [(x_test[i], y_test[i]) for i in range(len(x_test))]

    def load_cifar100(self):
        (x_train, y_train), (x_test, y_test) = tf.keras.datasets.cifar100.load_data()
        x_train = x_train.astype('float32') / 255.
        x_test = x_test.astype('float32') / 255.
        y_train = tf.keras.utils.to_categorical(y_train, num_classes=100)
        y_test = tf.keras.utils.to_categorical(y_test, num_classes=100)

        return [(x_train[i], y_train[i]) for i in range(len(x_train))], [(x_test[i], y_test[i]) for i in range(len(x_test))]

    def __len__(self):
        if hasattr(self, 'dataset'):
            return len(self.dataset)
        return len(self.image_paths)

    def __getitem__(self, idx):
        if hasattr(self, 'dataset'):
            image, label = self.dataset[idx]
        else:
            img_path = self.image_paths[idx]
            image = Image.open(img_path).convert("RGB")
            label = self.labels[idx]

        if self.transform:
            image = self.transform(image)

        return image, label


def prepare_data(data_dir, dataset_type, batch_size, image_size, annotations_file=None):
    if dataset_type in ['folder', 'imagenet', 'flower', 'coco', 'voc', 'yolo']:
        # 创建一个 ImageDataGenerator 对象，设置一些常见的预处理操作
        transform = tf.keras.preprocessing.image.ImageDataGenerator(
            rescale=1./255,  # 将图像像素值归一化到 [0, 1] 区间
            shear_range=0.2,
            zoom_range=0.2,
            horizontal_flip=True
        )

        dataset = UniversalImageDataset(data_dir, transform=transform, dataset_type=dataset_type,
                                        annotations_file=annotations_file)

        # 先对数据集里的每张图像进行尺寸调整操作
        resized_images = []
        for image, label in dataset:
            resized_image = tf.image.resize(image, [image_size, image_size])
            resized_images.append((resized_image, label))

        train_size = int(0.8 * len(resized_images))
        test_size = len(resized_images) - train_size
        train_dataset, test_dataset = resized_images[:train_size], resized_images[train_size:]

        train_loader = tf.data.Dataset.from_tensor_slices(train_dataset).batch(batch_size).shuffle(buffer_size=len(train_dataset))
        test_loader = tf.data.Dataset.from_tensor_slices(test_dataset).batch(batch_size)

        return train_loader, test_loader
    elif dataset_type in ['mnist', 'cifar10', 'cifar100']:
        dataset = UniversalImageDataset(data_dir, dataset_type=dataset_type)

        train_size = int(0.2 * len(dataset))
        test_size = len(dataset) - train_size
        train_dataset, test_dataset = tf.data.Dataset.from_tensor_slices(dataset).take(train_size), \
                                     tf.data.Dataset.from_tensor_slices(dataset).skip(train_size)

        train_loader = train_dataset.batch(batch_size).shuffle(buffer_size=len(train_dataset))
        test_loader = test_dataset.batch(batch_size)

        return train_loader, test_loader


def train_model(train_loader, model, criterion, optimizer, num_epochs):
    for epoch in range(num_epochs):
        running_loss = 0.0
        for batch in train_loader:
            images, labels = batch
            with tf.GradientTape() as tape:
                outputs = model(images)
                loss = criterion(labels, outputs)
            gradients = tape.gradient(loss, model.trainable_variables)
            optimizer.apply_gradients(zip(gradients, model.trainable_variables))
            running_loss += loss.numpy()

        print(f'Epoch [{epoch + 1}/{num_epochs}], Loss: {running_loss / len(train_loader):.4f}')


def test_model(test_loader, model):
    correct, total = 0, 0
    for batch in test_loader:
        images, labels = batch
        outputs = model(images)
        predicted = tf.argmax(outputs, axis=1)
        total += labels.shape[0]
        correct += tf.reduce_sum(tf.cast(tf.equal(predicted, tf.argmax(labels, axis=1)), tf.int32))

    print(f'Accuracy: {100 * correct / total:.2f}%')


def denseNet_train(input_file, dataset_type, input_size, num_classes, epochs, learning_rate, batch_size, result_dir,
                   model_name):
    train_loader, test_loader = prepare_data(input_file, dataset_type, batch_size, input_size)

    model = DenseNetForTabular(input_size, num_classes)
    criterion = tf.keras.losses.CategoricalCrossentropy()
    optimizer = tf.keras.optimizers.Adam(learning_rate=learning_rate)

    train_model(train_loader, model, criterion, optimizer, epochs)
    test_model(test_loader, model)

    # 模型保存
    os.makedirs(result_dir, exist_ok=True)
    model.save(os.path.join(result_dir, f"{model_name}.h5"))
    print(f'Model saved to {os.path.join(result_dir, model_name)}')


if __name__ == "__main__":
    # 设置参数
    input_file = 'E:/data/Flowers Recognition/flowers'
    input_size = 224  # 输入图像大小（DenseNet 121 默认大小）
    dataset_type = "folder"
    num_classes = 5
    epochs = 10
    learning_rate = 0.001
    batch_size = 4
    result_dir = 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_'
    model_name = 'DenseNet_tf'

    denseNet_train(input_file, dataset_type, input_size, num_classes, epochs, learning_rate, batch_size, result_dir,
                   model_name)