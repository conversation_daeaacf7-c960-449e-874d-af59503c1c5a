import numpy as np
import caffe
from caffe import layers as L
from caffe import params as P
# import lmdb
import cv2
from PIL import Image
import pickle
import requests
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import xml.etree.ElementTree as ET
import pandas as pd

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type" 
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

def create_densenet(input_size, num_classes):
    """Create DenseNet model structure"""
    n = caffe.NetSpec()
    
    # Input layer
    n.data = L.Input(shape=[dict(dim=[1, 3, input_size, input_size])])
    
    # First convolution
    n.conv1 = L.Convolution(n.data, kernel_size=7, stride=2, num_output=64,
                           weight_filler=dict(type='msra'),
                           bias_filler=dict(type='constant'))
    n.bn1 = L.BatchNorm(n.conv1)
    n.relu1 = L.ReLU(n.bn1)
    n.pool1 = L.Pooling(n.relu1, kernel_size=3, stride=2, pool=P.Pooling.MAX)
    
    # Dense blocks
    def dense_block(net, num_layers, growth_rate, in_layer):
        layers = [in_layer]
        for i in range(num_layers):
            bn = L.BatchNorm(layers[-1])
            relu = L.ReLU(bn)
            conv1 = L.Convolution(relu, kernel_size=1, num_output=growth_rate*4,
                                weight_filler=dict(type='msra'),
                                bias_filler=dict(type='constant'))
            bn2 = L.BatchNorm(conv1)
            relu2 = L.ReLU(bn2)
            conv2 = L.Convolution(relu2, kernel_size=3, num_output=growth_rate, pad=1,
                                weight_filler=dict(type='msra'),
                                bias_filler=dict(type='constant'))
            if len(layers) > 1:
                concat = L.Concat(*layers)
                layers.append(concat)
            else:
                layers.append(conv2)
        return layers[-1]
    
    # Add dense blocks
    n.dense1 = dense_block(n, 6, 32, n.pool1)
    n.trans1 = L.BatchNorm(n.dense1)
    n.trans1_relu = L.ReLU(n.trans1)
    n.trans1_conv = L.Convolution(n.trans1_relu, num_output=128, kernel_size=1,
                                weight_filler=dict(type='msra'),
                                bias_filler=dict(type='constant'))
    n.trans1_pool = L.Pooling(n.trans1_conv, kernel_size=2, stride=2, pool=P.Pooling.AVE)
    
    n.dense2 = dense_block(n, 12, 32, n.trans1_pool)
    n.trans2 = L.BatchNorm(n.dense2)
    n.trans2_relu = L.ReLU(n.trans2)
    n.trans2_conv = L.Convolution(n.trans2_relu, num_output=256, kernel_size=1,
                                weight_filler=dict(type='msra'),
                                bias_filler=dict(type='constant'))
    n.trans2_pool = L.Pooling(n.trans2_conv, kernel_size=2, stride=2, pool=P.Pooling.AVE)
    
    n.dense3 = dense_block(n, 24, 32, n.trans2_pool)
    n.trans3 = L.BatchNorm(n.dense3)
    n.trans3_relu = L.ReLU(n.trans3)
    n.trans3_conv = L.Convolution(n.trans3_relu, num_output=512, kernel_size=1,
                                weight_filler=dict(type='msra'),
                                bias_filler=dict(type='constant'))
    n.trans3_pool = L.Pooling(n.trans3_conv, kernel_size=2, stride=2, pool=P.Pooling.AVE)
    
    n.dense4 = dense_block(n, 16, 32, n.trans3_pool)
    
    # Final layers
    n.bn_final = L.BatchNorm(n.dense4)
    n.relu_final = L.ReLU(n.bn_final)
    n.pool_final = L.Pooling(n.relu_final, kernel_size=7, pool=P.Pooling.AVE)
    n.fc = L.InnerProduct(n.pool_final, num_output=num_classes,
                         weight_filler=dict(type='msra'),
                         bias_filler=dict(type='constant'))
    n.loss = L.SoftmaxWithLoss(n.fc, n.label)
    n.accuracy = L.Accuracy(n.fc, n.label)
    
    return n.to_proto()

class CaffeDataLayer:
    """Data layer for feeding images to Caffe"""
    def __init__(self, data_dir, batch_size, image_size, dataset_type='folder', annotations_file=None):
        self.data_dir = data_dir
        self.batch_size = batch_size
        self.image_size = image_size
        self.dataset_type = dataset_type
        
        if dataset_type == 'folder':
            self.image_paths, self.labels = self.load_from_folder()
        elif dataset_type == 'coco':
            self.image_paths, self.labels = self.load_coco(annotations_file)
        elif dataset_type == 'voc':
            self.image_paths, self.labels = self.load_voc(annotations_file)
        elif dataset_type == 'yolo':
            self.image_paths, self.labels = self.load_yolo(annotations_file)
        elif dataset_type == 'pickle':
            self.image_paths, self.labels = self.load_pickle(annotations_file)
            
        self.num_samples = len(self.image_paths)
        self.idx = 0
        
    def load_from_folder(self):
        # Implementation similar to PyTorch version
        classes = os.listdir(self.data_dir)
        class_to_idx = {cls: idx for idx, cls in enumerate(classes)}
        
        image_paths = []
        labels = []
        
        for cls in classes:
            class_dir = os.path.join(self.data_dir, cls)
            if os.path.isdir(class_dir):
                for img_file in os.listdir(class_dir):
                    if img_file.endswith(('.jpg', '.jpeg', '.png')):
                        img_path = os.path.join(class_dir, img_file)
                        image_paths.append(img_path)
                        labels.append(class_to_idx[cls])
                        
        return image_paths, labels


    def load_coco(self, annotations_file):
        with open(annotations_file) as f:
            annotations = json.load(f)

        image_paths = []
        labels = []
        for item in annotations['images']:
            img_id = item['id']
            img_file = os.path.join(self.data_dir, item['file_name'])
            image_paths.append(img_file)
            label = self.get_label_for_image(img_id, annotations)
            labels.append(label)

        return image_paths, labels

    def load_voc(self, annotations_file):
        image_paths = []
        labels = []

        # 读取所有 XML 文件
        with open(annotations_file, 'r') as file:
            xml_files = file.readlines()

        for xml_file in xml_files:
            xml_file = xml_file.strip()
            tree = ET.parse(xml_file)
            root = tree.getroot()

            # 获取图像文件路径
            image_name = root.find('filename').text
            img_path = os.path.join(self.data_dir, image_name)
            image_paths.append(img_path)

            # 提取标签
            objects = root.findall('object')
            boxes = []
            for obj in objects:
                class_name = obj.find('name').text
                bbox = obj.find('bndbox')
                xmin = float(bbox.find('xmin').text)
                ymin = float(bbox.find('ymin').text)
                xmax = float(bbox.find('xmax').text)
                ymax = float(bbox.find('ymax').text)
                boxes.append((class_name, xmin, ymin, xmax, ymax))

            labels.append(boxes)

        return image_paths, labels

    def load_yolo(self):
        image_paths = []
        labels = []

        for img_file in os.listdir(self.data_dir):
            if img_file.endswith(('.jpg', '.png', '.jpeg')):
                img_path = os.path.join(self.data_dir, img_file)
                image_paths.append(img_path)

                # 加载对应的YOLO标签文件
                label_file = img_file.replace('.jpg', '.txt').replace('.png', '.txt').replace('.jpeg', '.txt')
                label_path = os.path.join(self.data_dir, label_file)

                if os.path.exists(label_path):
                    with open(label_path, 'r') as f:
                        boxes = []
                        for line in f.readlines():
                            class_id, x_center, y_center, width, height = map(float, line.strip().split())
                            boxes.append((class_id, x_center, y_center, width, height))
                        labels.append(boxes)  # 以边界框列表形式存储
                else:
                    labels.append([])  # 无标签时返回空列表

        return image_paths, labels
    
    def load_pickle(self, pkl_file):
        # 从 .pkl 文件加载数据
        with open(pkl_file, 'rb') as f:
            data = pickle.load(f)

        # 假设数据为字典格式，包含特征和标签
        if isinstance(data, dict):
            images = data['images']  # 假设图像数据在 'images' 键下
            labels = data['labels']    # 假设标签在 'labels' 键下
        elif isinstance(data, pd.DataFrame):
            images = data['image_paths'].tolist()  # 假设图像路径在某列
            labels = data['labels'].tolist()        # 假设标签在某列
        else:
            raise ValueError("Unsupported data format in pickle file.")

        return images, labels
    
    def __len__(self):
        if hasattr(self, 'dataset'):
            return len(self.dataset)
        return len(self.image_paths)

    def __getitem__(self, idx):
        if hasattr(self, 'dataset'):
            image, label = self.dataset[idx]
        else:
            img_path = self.image_paths[idx]
            image = Image.open(img_path).convert("RGB")
            label = self.labels[idx]

        if self.transform:
            image = self.transform(image)

        return image, label
    
    def get_next_batch(self):
        images = []
        batch_labels = []
        
        for _ in range(self.batch_size):
            if self.idx >= self.num_samples:
                self.idx = 0
                
            # Load and preprocess image
            img = cv2.imread(self.image_paths[self.idx])
            img = cv2.resize(img, (self.image_size, self.image_size))
            img = img.astype(np.float32)
            img = (img - 127.5) / 127.5  # Normalize to [-1, 1]
            img = img.transpose(2, 0, 1)  # HWC to CHW format
            
            images.append(img)
            batch_labels.append(self.labels[self.idx])
            
            self.idx += 1
            
        return np.array(images), np.array(batch_labels)

def densenet_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """Main training function"""
    input_size = job_params["input_size"]
    dataset_type = job_params["dataset_type"]
    output_size = job_params["output_size"]
    num_epochs = job_params["num_epochs"]
    learning_rate = job_params["learning_rate"]
    batch_size = job_params["batch_size"]
    
    # Create network
    net_proto = create_densenet(input_size, output_size)
    with open(os.path.join(result_dir, 'densenet.prototxt'), 'w') as f:
        f.write(str(net_proto))
    
    # Create solver
    solver_proto = caffe.SolverParameter(
        train_net=os.path.join(result_dir, 'densenet.prototxt'),
        base_lr=learning_rate,
        momentum=0.9,
        weight_decay=0.0001,
        lr_policy='step',
        stepsize=30000,
        gamma=0.1,
        max_iter=num_epochs * (len(dataset) // batch_size),
        display=100,
        snapshot=5000,
        snapshot_prefix=os.path.join(result_dir, model_name)
    )
    
    with open(os.path.join(result_dir, 'solver.prototxt'), 'w') as f:
        f.write(str(solver_proto))
    
    # Create solver and load pretrained weights if available
    solver = caffe.SGDSolver(os.path.join(result_dir, 'solver.prototxt'))
    if os.path.exists('/workspace/pretrained_model/densenet121.caffemodel'):
        solver.net.copy_from('/workspace/pretrained_model/densenet121.caffemodel')
    
    # Create data layer
    data_layer = CaffeDataLayer(
        data_dir='/workspace/' + dataset["training_data_path"],
        batch_size=batch_size,
        image_size=input_size,
        dataset_type=dataset_type
    )
    
    # Training loop
    for iteration in range(solver.param.max_iter):
        images, labels = data_layer.get_next_batch()
        
        solver.net.blobs['data'].data[...] = images
        solver.net.blobs['label'].data[...] = labels
        
        solver.step(1)
        
        if iteration % solver.param.display == 0:
            print(f'Iteration {iteration}, loss: {solver.net.blobs["loss"].data}')
    
    # Save final model
    solver.net.save(os.path.join(result_dir, model_name + '.caffemodel'))
    return None, 0

def model_upload(result_dir,model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".pickle"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".pickle")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "pytorch",
          "file_name": model_name+".pickle",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "Logistic_Regression",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

if __name__ == "__main__":
    # Main script remains largely the same, just updating the model training call
    parser = argparse.ArgumentParser(description='Caffe DenseNet Train.')
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='DenseNet Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='DenseNet DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')
    
    print("Start DenseNet training job, params :\n" + str(sys.argv) + "\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("DenseNet job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("DenseNet dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("DenseNet result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("DenseNet factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("DenseNet fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("DenseNet sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    
    print("Step 1 DenseNet training:\n")
    result, ret_code = densenet_train(dataset, job_params, model["model_name"], result_dir, fit_params)
    if ret_code != 0:
        print("DenseNet train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()