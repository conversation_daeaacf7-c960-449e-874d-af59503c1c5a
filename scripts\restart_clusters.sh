#!/bin/bash

# =======================================================
# 集群重启脚本
# 功能: 安全重启PVE、Hadoop、K8S集群服务
# 作者: 系统管理员
# 版本: 1.0
# =======================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志文件
LOG_FILE="/var/log/cluster_restart_$(date +%Y%m%d_%H%M%S).log"

# 配置文件 - 根据实际环境修改
HADOOP_NODES=("hadoop-node1" "hadoop-node2" "hadoop-node3" "hadoop-node4" "hadoop-node5" "hadoop-node6")
K8S_NODES=("k8s-master" "k8s-worker1" "k8s-worker2" "k8s-worker3" "k8s-worker4")
PVE_HOST="localhost"

# 重启等待时间(秒)
RESTART_WAIT_TIME=30
SERVICE_START_WAIT=10

# 记录日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 打印标题
print_header() {
    echo -e "${BLUE}=================================${NC}"
    echo -e "${BLUE}    $1${NC}"
    echo -e "${BLUE}=================================${NC}"
}

# 确认操作
confirm_action() {
    local action=$1
    echo -e "${YELLOW}警告: 即将执行 $action${NC}"
    echo -e "${YELLOW}这将影响系统可用性，请确认是否继续？${NC}"
    read -p "输入 'YES' 继续，其他任何输入将取消: " confirm
    
    if [[ "$confirm" != "YES" ]]; then
        echo -e "${RED}操作已取消${NC}"
        log "Operation cancelled by user"
        exit 0
    fi
    
    log "User confirmed action: $action"
}

# 重启服务
restart_service() {
    local host=$1
    local service=$2
    local wait_time=${3:-$SERVICE_START_WAIT}
    
    echo -e "${YELLOW}重启 $host 上的 $service...${NC}"
    log "Restarting $service on $host"
    
    if ssh -o ConnectTimeout=10 "$host" "systemctl restart $service"; then
        echo "等待 $wait_time 秒让服务完全启动..."
        sleep "$wait_time"
        
        if ssh -o ConnectTimeout=10 "$host" "systemctl is-active $service" >/dev/null 2>&1; then
            echo -e "${GREEN}✓${NC} $host: $service 重启成功"
            log "SUCCESS: $service restarted successfully on $host"
            return 0
        else
            echo -e "${RED}✗${NC} $host: $service 重启失败"
            log "ERROR: Failed to restart $service on $host"
            return 1
        fi
    else
        echo -e "${RED}✗${NC} $host: 无法重启 $service"
        log "ERROR: Unable to restart $service on $host"
        return 1
    fi
}

# 停止服务
stop_service() {
    local host=$1
    local service=$2
    
    echo -e "${YELLOW}停止 $host 上的 $service...${NC}"
    log "Stopping $service on $host"
    
    if ssh -o ConnectTimeout=10 "$host" "systemctl stop $service"; then
        echo -e "${GREEN}✓${NC} $host: $service 已停止"
        log "SUCCESS: $service stopped on $host"
        return 0
    else
        echo -e "${RED}✗${NC} $host: 无法停止 $service"
        log "ERROR: Unable to stop $service on $host"
        return 1
    fi
}

# 启动服务
start_service() {
    local host=$1
    local service=$2
    local wait_time=${3:-$SERVICE_START_WAIT}
    
    echo -e "${YELLOW}启动 $host 上的 $service...${NC}"
    log "Starting $service on $host"
    
    if ssh -o ConnectTimeout=10 "$host" "systemctl start $service"; then
        echo "等待 $wait_time 秒让服务完全启动..."
        sleep "$wait_time"
        
        if ssh -o ConnectTimeout=10 "$host" "systemctl is-active $service" >/dev/null 2>&1; then
            echo -e "${GREEN}✓${NC} $host: $service 启动成功"
            log "SUCCESS: $service started successfully on $host"
            return 0
        else
            echo -e "${RED}✗${NC} $host: $service 启动失败"
            log "ERROR: Failed to start $service on $host"
            return 1
        fi
    else
        echo -e "${RED}✗${NC} $host: 无法启动 $service"
        log "ERROR: Unable to start $service on $host"
        return 1
    fi
}

# 重启PVE服务
restart_pve_services() {
    print_header "重启 PVE 服务"
    
    confirm_action "重启PVE服务"
    
    local pve_services=("pve-cluster" "pvestatd" "pvedaemon" "pveproxy")
    local error_count=0
    
    for service in "${pve_services[@]}"; do
        if ! restart_service "$PVE_HOST" "$service" 15; then
            ((error_count++))
        fi
        echo ""
    done
    
    if [ $error_count -eq 0 ]; then
        echo -e "${GREEN}PVE 服务重启完成${NC}"
        log "PVE services restart completed successfully"
    else
        echo -e "${RED}PVE 服务重启出现 $error_count 个错误${NC}"
        log "PVE services restart completed with $error_count errors"
    fi
    
    return $error_count
}

# 重启Hadoop集群
restart_hadoop_cluster() {
    print_header "重启 Hadoop 集群"
    
    confirm_action "重启Hadoop集群"
    
    local error_count=0
    
    echo -e "${YELLOW}=== 第1步: 停止所有Hadoop服务 ===${NC}"
    
    # 停止YARN NodeManager
    echo -e "\n${YELLOW}停止 YARN NodeManager...${NC}"
    for node in "${HADOOP_NODES[@]:1}"; do
        stop_service "$node" "hadoop-yarn-nodemanager"
    done
    
    # 停止YARN ResourceManager
    echo -e "\n${YELLOW}停止 YARN ResourceManager...${NC}"
    stop_service "hadoop-node1" "hadoop-yarn-resourcemanager"
    
    # 停止MapReduce History Server
    echo -e "\n${YELLOW}停止 MapReduce History Server...${NC}"
    stop_service "hadoop-node1" "hadoop-mapreduce-historyserver"
    
    # 停止DataNode
    echo -e "\n${YELLOW}停止 DataNode...${NC}"
    for node in "${HADOOP_NODES[@]:1}"; do
        stop_service "$node" "hadoop-hdfs-datanode"
    done
    
    # 停止Secondary NameNode
    echo -e "\n${YELLOW}停止 Secondary NameNode...${NC}"
    stop_service "hadoop-node2" "hadoop-hdfs-secondarynamenode"
    
    # 停止NameNode
    echo -e "\n${YELLOW}停止 NameNode...${NC}"
    stop_service "hadoop-node1" "hadoop-hdfs-namenode"
    
    # 停止ZooKeeper
    echo -e "\n${YELLOW}停止 ZooKeeper...${NC}"
    for node in "${HADOOP_NODES[@]:0:3}"; do
        stop_service "$node" "zookeeper"
    done
    
    echo "等待 $RESTART_WAIT_TIME 秒后开始启动服务..."
    sleep $RESTART_WAIT_TIME
    
    echo -e "${YELLOW}=== 第2步: 启动所有Hadoop服务 ===${NC}"
    
    # 启动ZooKeeper
    echo -e "\n${YELLOW}启动 ZooKeeper...${NC}"
    for node in "${HADOOP_NODES[@]:0:3}"; do
        start_service "$node" "zookeeper" 15
    done
    
    # 启动NameNode
    echo -e "\n${YELLOW}启动 NameNode...${NC}"
    start_service "hadoop-node1" "hadoop-hdfs-namenode" 20
    
    # 启动Secondary NameNode
    echo -e "\n${YELLOW}启动 Secondary NameNode...${NC}"
    start_service "hadoop-node2" "hadoop-hdfs-secondarynamenode" 15
    
    # 启动DataNode
    echo -e "\n${YELLOW}启动 DataNode...${NC}"
    for node in "${HADOOP_NODES[@]:1}"; do
        start_service "$node" "hadoop-hdfs-datanode" 10
    done
    
    # 启动YARN ResourceManager
    echo -e "\n${YELLOW}启动 YARN ResourceManager...${NC}"
    start_service "hadoop-node1" "hadoop-yarn-resourcemanager" 15
    
    # 启动YARN NodeManager
    echo -e "\n${YELLOW}启动 YARN NodeManager...${NC}"
    for node in "${HADOOP_NODES[@]:1}"; do
        start_service "$node" "hadoop-yarn-nodemanager" 10
    done
    
    # 启动MapReduce History Server
    echo -e "\n${YELLOW}启动 MapReduce History Server...${NC}"
    start_service "hadoop-node1" "hadoop-mapreduce-historyserver" 10
    
    # 验证HDFS状态
    echo -e "\n${YELLOW}验证 HDFS 状态...${NC}"
    sleep 30  # 等待系统稳定
    
    if ssh hadoop-node1 "hdfs dfsadmin -report" >/dev/null 2>&1; then
        echo -e "${GREEN}✓${NC} HDFS 系统正常"
        log "SUCCESS: HDFS system is healthy after restart"
    else
        echo -e "${RED}✗${NC} HDFS 系统可能有问题"
        log "ERROR: HDFS system might have issues after restart"
        ((error_count++))
    fi
    
    if [ $error_count -eq 0 ]; then
        echo -e "\n${GREEN}Hadoop 集群重启完成${NC}"
        log "Hadoop cluster restart completed successfully"
    else
        echo -e "\n${RED}Hadoop 集群重启出现 $error_count 个错误${NC}"
        log "Hadoop cluster restart completed with $error_count errors"
    fi
    
    return $error_count
}

# 重启K8S集群
restart_k8s_cluster() {
    print_header "重启 Kubernetes 集群"
    
    confirm_action "重启Kubernetes集群"
    
    local error_count=0
    
    echo -e "${YELLOW}=== 第1步: 排空并停止Worker节点 ===${NC}"
    
    # 排空Worker节点
    for node in "${K8S_NODES[@]:1}"; do
        echo -e "\n${YELLOW}排空节点 $node...${NC}"
        if kubectl drain "$node" --ignore-daemonsets --delete-emptydir-data --force --timeout=300s; then
            echo -e "${GREEN}✓${NC} 节点 $node 排空成功"
            log "SUCCESS: Node $node drained successfully"
        else
            echo -e "${RED}✗${NC} 节点 $node 排空失败"
            log "ERROR: Failed to drain node $node"
            ((error_count++))
        fi
    done
    
    # 停止Worker节点服务
    echo -e "\n${YELLOW}停止 Worker 节点服务...${NC}"
    for node in "${K8S_NODES[@]:1}"; do
        stop_service "$node" "kubelet"
        stop_service "$node" "kube-proxy"
    done
    
    # 停止Master节点服务
    echo -e "\n${YELLOW}停止 Master 节点服务...${NC}"
    stop_service "k8s-master" "kube-scheduler"
    stop_service "k8s-master" "kube-controller-manager"
    stop_service "k8s-master" "kube-apiserver"
    stop_service "k8s-master" "etcd"
    
    echo "等待 $RESTART_WAIT_TIME 秒后开始启动服务..."
    sleep $RESTART_WAIT_TIME
    
    echo -e "${YELLOW}=== 第2步: 启动Kubernetes服务 ===${NC}"
    
    # 启动Master节点服务
    echo -e "\n${YELLOW}启动 Master 节点服务...${NC}"
    start_service "k8s-master" "etcd" 20
    start_service "k8s-master" "kube-apiserver" 15
    start_service "k8s-master" "kube-controller-manager" 10
    start_service "k8s-master" "kube-scheduler" 10
    
    # 启动Worker节点服务
    echo -e "\n${YELLOW}启动 Worker 节点服务...${NC}"
    for node in "${K8S_NODES[@]:1}"; do
        start_service "$node" "kubelet" 15
        start_service "$node" "kube-proxy" 10
    done
    
    # 取消节点隔离
    echo -e "\n${YELLOW}取消节点隔离...${NC}"
    sleep 30  # 等待节点重新加入
    
    for node in "${K8S_NODES[@]:1}"; do
        if kubectl uncordon "$node"; then
            echo -e "${GREEN}✓${NC} 节点 $node 重新启用"
            log "SUCCESS: Node $node uncordoned successfully"
        else
            echo -e "${RED}✗${NC} 节点 $node 重新启用失败"
            log "ERROR: Failed to uncordon node $node"
            ((error_count++))
        fi
    done
    
    # 验证集群状态
    echo -e "\n${YELLOW}验证集群状态...${NC}"
    sleep 60  # 等待集群稳定
    
    if kubectl get nodes | grep -v "NotReady"; then
        echo -e "${GREEN}✓${NC} Kubernetes 集群节点状态正常"
        log "SUCCESS: Kubernetes cluster nodes are healthy after restart"
    else
        echo -e "${RED}✗${NC} Kubernetes 集群可能有问题"
        log "ERROR: Kubernetes cluster might have issues after restart"
        ((error_count++))
    fi
    
    if [ $error_count -eq 0 ]; then
        echo -e "\n${GREEN}Kubernetes 集群重启完成${NC}"
        log "Kubernetes cluster restart completed successfully"
    else
        echo -e "\n${RED}Kubernetes 集群重启出现 $error_count 个错误${NC}"
        log "Kubernetes cluster restart completed with $error_count errors"
    fi
    
    return $error_count
}

# 重启指定虚拟机
restart_vm() {
    local vm_id=$1
    local vm_name=$2
    
    print_header "重启虚拟机 $vm_name (ID: $vm_id)"
    
    confirm_action "重启虚拟机 $vm_name"
    
    echo -e "${YELLOW}关闭虚拟机 $vm_name...${NC}"
    log "Shutting down VM $vm_name (ID: $vm_id)"
    
    if pvesh create "/nodes/$PVE_HOST/qemu/$vm_id/status/shutdown"; then
        echo "等待虚拟机完全关闭..."
        sleep 60
        
        echo -e "${YELLOW}启动虚拟机 $vm_name...${NC}"
        log "Starting VM $vm_name (ID: $vm_id)"
        
        if pvesh create "/nodes/$PVE_HOST/qemu/$vm_id/status/start"; then
            echo "等待虚拟机完全启动..."
            sleep 120
            echo -e "${GREEN}✓${NC} 虚拟机 $vm_name 重启成功"
            log "SUCCESS: VM $vm_name restarted successfully"
            return 0
        else
            echo -e "${RED}✗${NC} 虚拟机 $vm_name 启动失败"
            log "ERROR: Failed to start VM $vm_name"
            return 1
        fi
    else
        echo -e "${RED}✗${NC} 虚拟机 $vm_name 关闭失败"
        log "ERROR: Failed to shutdown VM $vm_name"
        return 1
    fi
}

# 显示使用帮助
show_help() {
    echo "集群重启脚本使用说明:"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -p, --pve           仅重启PVE服务"
    echo "  -H, --hadoop        仅重启Hadoop集群"
    echo "  -k, --k8s           仅重启Kubernetes集群"
    echo "  -a, --all           重启所有服务 (默认)"
    echo "  -v, --vm ID         重启指定ID的虚拟机"
    echo ""
    echo "示例:"
    echo "  $0                  # 重启所有服务"
    echo "  $0 -p              # 仅重启PVE服务"
    echo "  $0 -H              # 仅重启Hadoop集群"
    echo "  $0 -k              # 仅重启Kubernetes集群"
    echo "  $0 -v 100          # 重启VM ID为100的虚拟机"
}

# 主函数
main() {
    local restart_pve=false
    local restart_hadoop=false
    local restart_k8s=false
    local restart_all=true
    local vm_id=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -p|--pve)
                restart_pve=true
                restart_all=false
                shift
                ;;
            -H|--hadoop)
                restart_hadoop=true
                restart_all=false
                shift
                ;;
            -k|--k8s)
                restart_k8s=true
                restart_all=false
                shift
                ;;
            -a|--all)
                restart_all=true
                shift
                ;;
            -v|--vm)
                vm_id="$2"
                restart_all=false
                shift 2
                ;;
            *)
                echo "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo -e "${BLUE}开始集群重启操作...${NC}"
    log "Starting cluster restart operations"
    
    local total_errors=0
    
    # 处理VM重启
    if [[ -n "$vm_id" ]]; then
        restart_vm "$vm_id" "VM-$vm_id"
        total_errors=$((total_errors + $?))
        
    # 处理服务重启
    else
        if [[ "$restart_all" == true ]] || [[ "$restart_pve" == true ]]; then
            restart_pve_services
            total_errors=$((total_errors + $?))
            echo ""
        fi
        
        if [[ "$restart_all" == true ]] || [[ "$restart_hadoop" == true ]]; then
            restart_hadoop_cluster
            total_errors=$((total_errors + $?))
            echo ""
        fi
        
        if [[ "$restart_all" == true ]] || [[ "$restart_k8s" == true ]]; then
            restart_k8s_cluster
            total_errors=$((total_errors + $?))
            echo ""
        fi
    fi
    
    # 生成结果摘要
    print_header "重启操作摘要"
    
    echo "操作时间: $(date)"
    echo "日志文件: $LOG_FILE"
    echo ""
    
    if [ $total_errors -eq 0 ]; then
        echo -e "${GREEN}✓ 所有重启操作成功完成${NC}"
        log "SUMMARY: All restart operations completed successfully"
    else
        echo -e "${RED}✗ 重启过程中发现 $total_errors 个错误${NC}"
        log "SUMMARY: Restart process completed with $total_errors errors"
    fi
    
    echo ""
    echo "建议操作:"
    echo "1. 运行状态检查脚本验证服务状态"
    echo "2. 查看详细日志: cat $LOG_FILE"
    echo "3. 如有问题,请检查相关服务日志"
    
    log "Cluster restart operations completed with $total_errors total errors"
    
    exit $total_errors
}

# 检查依赖工具
check_dependencies() {
    local missing_tools=()
    
    for tool in ssh pvesh kubectl; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        echo -e "${RED}错误: 缺少必要工具: ${missing_tools[*]}${NC}"
        echo "请安装缺少的工具后重新运行"
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    check_dependencies
    main "$@"
fi 