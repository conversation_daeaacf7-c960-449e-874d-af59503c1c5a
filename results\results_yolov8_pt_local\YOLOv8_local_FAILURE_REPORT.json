{"args": {"data_path": "./demo_data", "dataset_type": "folder", "annotations_file": null, "input_size": 640, "model_name": "YOLOv8_local", "model_type": "nano", "pretrained_model_path": null, "mode": "train", "num_epochs": 1, "batch_size": 16, "conf_thres": 0.25, "iou_thres": 0.45, "early_stopping_patience": 30, "use_cv": false, "cv_folds": 5, "trained_model_path": null, "test_data_path": null, "annotations_file_test": null, "result_dir": "./results/results_yolov8_pt_local", "random_seed": 42, "force_cpu": false, "log_level": "INFO"}, "error": "Training failed: {'error': \"Dataset 'Folder_YOLO/dataset.yaml' error  Dataset 'Folder_YOLO/dataset.yaml' images not found, missing path 'E:\\\\work\\\\\\\\\\\\2025.3\\\\bypt\\\\desin\\\\datasets\\\\Folder_YOLO\\\\images\\\\val'\\nNote dataset download directory is 'E:\\\\work\\\\\\\\\\\\2025.3\\\\bypt\\\\desin\\\\datasets'. You can update this in 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Ultralytics\\\\settings.json'\"}", "traceback": "Traceback (most recent call last):\n  File \"卷积神经网络/yolov8_pt_local_mode.py\", line 1171, in yolov8_main_local\n    raise RuntimeError(f\"Training failed: {train_results}\")\nRuntimeError: Training failed: {'error': \"Dataset 'Folder_YOLO/dataset.yaml' error  Dataset 'Folder_YOLO/dataset.yaml' images not found, missing path 'E:\\\\work\\\\\\\\\\\\2025.3\\\\bypt\\\\desin\\\\datasets\\\\Folder_YOLO\\\\images\\\\val'\\nNote dataset download directory is 'E:\\\\work\\\\\\\\\\\\2025.3\\\\bypt\\\\desin\\\\datasets'. You can update this in 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Roaming\\\\Ultralytics\\\\settings.json'\"}\n", "timestamp": "2025-06-13T14:37:29.882475"}