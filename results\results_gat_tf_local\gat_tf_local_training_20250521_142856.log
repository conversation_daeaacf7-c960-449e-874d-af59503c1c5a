2025-05-21 14:28:56,120 - GAT_TF_Local - INFO - Logging to console and file: ./results/results_gat_tf_local\gat_tf_local_training_20250521_142856.log
2025-05-21 14:28:56,120 - GAT_TF_Local - INFO - GAT TensorFlow Local Mode Training Script Started.
2025-05-21 14:28:56,120 - GAT_TF_Local - INFO - TensorFlow Version: 2.13.0
2025-05-21 14:28:56,121 - GAT_TF_Local - INFO - Provided arguments: Namespace(adj_type='identity', categorical_impute_strategy='most_frequent', cv_folds=5, data_format='pkl', dropout=0.5, final_dropout=0.5, handle_imbalance=False, hidden_size=64, input_file='E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', k_neighbors=5, l2_reg=0.0001, label_column='Label', learning_rate=0.005, log_level='INFO', model_name='GAT_TF_Local_Model', normalize=True, num_epochs=3, num_heads=4, numeric_impute_strategy='mean', random_state=42, result_dir='./results/results_gat_tf_local', scaler_type='standard', stratify_split=True, test_split_size=0.2, use_cv=False)
2025-05-21 14:28:56,165 - GAT_TF_Local - INFO - Loading data from: E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl (Format: pkl, Label: 'Label')
2025-05-21 14:28:59,643 - GAT_TF_Local - INFO - Data loaded: X shape (692703, 84), y shape (692703,). Label dist: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-21 14:28:59,672 - GAT_TF_Local - INFO - Standard train/test split mode selected for GAT (TF).
2025-05-21 14:29:00,212 - GAT_TF_Local - INFO - Replaced infinite values with NaN.
2025-05-21 14:29:00,323 - GAT_TF_Local - INFO - Handling missing values.
2025-05-21 14:29:02,520 - GAT_TF_Local - INFO - Imputed numeric missing values using 'mean'.
2025-05-21 14:29:03,020 - GAT_TF_Local - INFO - Imputed categorical missing values using 'most_frequent'.
2025-05-21 14:29:03,130 - GAT_TF_Local - INFO - Encoded labels. Classes: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-21 14:29:03,233 - GAT_TF_Local - INFO - Encoding categorical features: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-21 14:29:07,297 - GAT_TF_Local - INFO - Normalizing features using standard scaler.
2025-05-21 14:29:09,008 - GAT_TF_Local - INFO - Split data: X_train (554162, 84), y_train (554162,), X_test (138541, 84), y_test (138541,)
2025-05-21 14:29:09,212 - GAT_TF_Local - INFO - Preprocessing info saved to ./results/results_gat_tf_local\GAT_TF_Local_Model_preprocessing_info.json
2025-05-21 14:29:09,213 - GAT_TF_Local - INFO - Data after preprocessing: Input size=84, Num classes=6
2025-05-21 14:29:09,322 - GAT_TF_Local - INFO - Combined train/test for graph: Total nodes 692703, Train 554162, Val/Test 138541
2025-05-21 14:29:09,327 - GAT_TF_Local - CRITICAL - An unexpected critical error occurred: Unable to allocate 1.75 TiB for an array with shape (692703, 692703) and data type float32
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GAT_tf_local_mode.py", line 1103, in gat_train_tf_local_entrypoint
    graph_data_dict = prepare_graph_dataset_tf_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GAT_tf_local_mode.py", line 562, in prepare_graph_dataset_tf_local
    adj_matrix_tf = create_adjacency_matrix_tf_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GAT_tf_local_mode.py", line 504, in create_adjacency_matrix_tf_local
    adj_matrix = np.zeros((num_nodes, num_nodes), dtype=np.float32)
numpy.core._exceptions._ArrayMemoryError: Unable to allocate 1.75 TiB for an array with shape (692703, 692703) and data type float32
