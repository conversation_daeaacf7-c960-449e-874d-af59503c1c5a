"""图SAGE (GraphSAGE)"""


import pickle
import requests
import joblib
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import tensorflow as tf
import numpy as np
# from tensorflow.keras import Model
# from tensorflow.keras.layers import Dense
# import spektral

MINIO_URL = "minio-prophecis.prophecis:9000"

MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class GraphSAGEAggregator(tf.keras.layers.Layer):
    def __init__(self, units, **kwargs):
        super(GraphSAGEAggregator, self).__init__(**kwargs)
        self.units = units

    def build(self, input_shape):
        self.weight = self.add_weight(
            shape=(input_shape[-1], self.units),
            initializer='glorot_uniform',
            trainable=True
        )
        self.bias = self.add_weight(
            shape=(self.units,),
            initializer='zeros',
            trainable=True
        )
        super(GraphSAGEAggregator, self).build(input_shape)

    def call(self, inputs, adj):
        # Mean aggregation
        neigh_mean = tf.sparse.sparse_dense_matmul(adj, inputs)
        output = tf.matmul(neigh_mean, self.weight) + self.bias
        return output

class GraphSAGE(tf.keras.Model):
    def __init__(self, in_channels, hidden_channels, num_classes):
        super(GraphSAGE, self).__init__()
        self.conv1 = GraphSAGEAggregator(hidden_channels)
        self.conv2 = GraphSAGEAggregator(num_classes)
        
    def call(self, inputs):
        x, adj = inputs
        
        # First GraphSAGE layer
        h = self.conv1(x, adj)
        h = tf.nn.relu(h)
        
        # Second GraphSAGE layer
        output = self.conv2(h, adj)
        return tf.nn.log_softmax(output, axis=1)

def convert_to_sparse_tensor(edge_index, num_nodes):
    # Convert edge_index to sparse tensor format
    edges = tf.cast(edge_index.T, tf.int64)
    values = tf.ones(edges.shape[0])
    shape = (num_nodes, num_nodes)
    return tf.sparse.SparseTensor(edges, values, shape)

def graphsage_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """
    参数说明：
    - in_channels: 输入通道数
    - hidden_channels: 隐藏层通道数
    - num_classes: 类别数
    - num_epochs: 训练轮数
    - learning_rate: 学习率
    """
    training_data_path = "/workspace/" + dataset["training_data_path"]
    with open(training_data_path, 'rb') as f:
        data = pickle.load(f, encoding='bytes')
    
    # 转换数据格式
    x = tf.convert_to_tensor(data.x.numpy(), dtype=tf.float32)
    y = tf.convert_to_tensor(data.y.numpy(), dtype=tf.int64)
    adj = convert_to_sparse_tensor(data.edge_index.numpy(), data.num_nodes)
    train_mask = tf.convert_to_tensor(data.train_mask.numpy())

    # 初始化模型参数
    in_channels = job_params["in_channels"]
    hidden_channels = job_params["hidden_channels"]
    num_classes = job_params["num_classes"]
    num_epochs = job_params["num_epochs"]
    learning_rate = job_params["learning_rate"]

    # 初始化模型和优化器
    model = GraphSAGE(in_channels, hidden_channels, num_classes)
    optimizer = tf.keras.optimizers.Adam(learning_rate=learning_rate)
    loss_fn = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True)

    # 训练循环
    for epoch in range(num_epochs):
        with tf.GradientTape() as tape:
            # 前向传播
            logits = model([x, adj])
            # 计算损失
            mask = tf.cast(train_mask, tf.bool)
            loss = loss_fn(
                tf.boolean_mask(y, mask),
                tf.boolean_mask(logits, mask)
            )
        
        # 反向传播
        gradients = tape.gradient(loss, model.trainable_variables)
        optimizer.apply_gradients(zip(gradients, model.trainable_variables))
        
        if epoch % 10 == 0:
            print(f"Epoch {epoch}: Loss = {loss:.4f}")

    # 保存模型
    model_path = os.path.join(result_dir, f"{model_name}.h5")
    model.save(model_path)
    print(f'GraphSAGE训练完成，模型保存到 {model_path}')

    return None, 0

def model_upload(result_dir, model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = f"{obj_name}/{model_name}.h5"
        source = f"s3://mlss-mf/{obj_name}"
        res = minioClient.fput_object('mlss-mf', upload_path, f"{result_dir}/{model_name}.h5")
        result = {"source": source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1

def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "TENSORFLOW",
          "file_name": f"{model_name}.h5",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
    }
    
    r = requests.post(f"{MODEL_FACTORY_URL}{MODEL_ADD_URL}",
                     data=json.dumps(params),
                     headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "Logistic_Regression",
        "model_usage": "Classification"
    }
    r = requests.post(f"{MODEL_FACTORY_URL}{MODEL_PUSH_URL}/{str(model_version_id)}",
                     data=json.dumps(params),
                     headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: user_id
    }
    return headers

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='tensorflow GraphSAGE Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='GraphSAGE Job Params, set all params in dict')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='GraphSAGE DataSet, set as a dict')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,
                    default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost', nargs='?', const=None, dest='nodehost',
                    type=str, default="**************",
                    help='nodehost params')
    
    print("Start GraphSAGE training job, params:\n" + str(sys.argv) + "\n")
    args = parser.parse_args()
    
    print("Step 1 GraphSAGE training:\n")
    result, ret_code = graphsage_train(args.dataset, args.job_params,
                                     args.model["model_name"], args.result_dir,
                                     args.fit_params)
    if ret_code != 0:
        print(f"GraphSAGE train err, stop job....\nError Msg:{result}\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    print("Step 2 Model Upload to MinIO: \n")
    result, ret_code = model_upload(args.result_dir, args.model["model_name"])
    if ret_code != 0:
        print(f"model storage err, stop job....error msg: {result}\n")
        sys.exit(-1)
    print("Storage model finish, start model register...\n")
    
    print("Step 3 Model Register:\n")
    headers = header_gen(os.environ.get("USER_ID"))
    result, ret_code = model_register(args.model["model_name"],
                                    result["source"],
                                    args.model["group_id"],
                                    headers)
    if ret_code != 0:
        print(f"model register error, stop job....,err msg: {result}")
        sys.exit(-1)
    print("Register model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result, ret_code = model_push(model_id, model_version_id, args.factory_name)
    if ret_code != 0:
        print(f"model push error, stop job....err msg: {result}\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()
  
