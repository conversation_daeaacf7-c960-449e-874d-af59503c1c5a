import numpy as np
import caffe
import os
import pickle
import requests
import joblib
import sys
import argparse
import json
from minio import Minio
import uuid
from sklearn.preprocessing import StandardScaler, LabelEncoder, OrdinalEncoder
from sklearn.model_selection import train_test_split
import pandas as pd

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

# Header constants
MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

def write_prototxt(input_size, hidden_size, num_layers, num_classes, batch_size, prototxt_path):
    """Generate network definition prototxt file"""
    with open(prototxt_path, 'w') as f:
        # Data layer
        f.write("""name: "BiLSTM"
layer {
  name: "data"
  type: "Input"
  top: "data"
  input_param { shape: { dim: %d dim: 1 dim: %d } }
}
""" % (batch_size, input_size))

        # BiLSTM layers
        for i in range(num_layers):
            f.write("""
layer {
  name: "lstm_forward_%d"
  type: "LSTM"
  bottom: "%s"
  top: "lstm_forward_%d"
  lstm_param {
    num_output: %d
    weight_filler {
      type: "xavier"
    }
    bias_filler {
      type: "constant"
    }
  }
}

layer {
  name: "lstm_backward_%d"
  type: "LSTM"
  bottom: "%s"
  top: "lstm_backward_%d"
  lstm_param {
    num_output: %d
    weight_filler {
      type: "xavier"
    }
    bias_filler {
      type: "constant"
    }
  }
}

layer {
  name: "concat_%d"
  type: "Concat"
  bottom: "lstm_forward_%d"
  bottom: "lstm_backward_%d"
  top: "lstm_out_%d"
  concat_param {
    axis: 2
  }
}
""" % (i, "data" if i == 0 else f"lstm_out_{i-1}", i, hidden_size,
       i, "data" if i == 0 else f"lstm_out_{i-1}", i, hidden_size,
       i, i, i, i))

        # Final fully connected layer
        f.write("""
layer {
  name: "ip1"
  type: "InnerProduct"
  bottom: "lstm_out_%d"
  top: "ip1"
  inner_product_param {
    num_output: %d
    weight_filler {
      type: "xavier"
    }
    bias_filler {
      type: "constant"
    }
  }
}

layer {
  name: "loss"
  type: "SoftmaxWithLoss"
  bottom: "ip1"
  bottom: "label"
  top: "loss"
}
""" % (num_layers - 1, num_classes))

def write_solver_prototxt(prototxt_path, solver_path, max_iter):
    """Generate solver prototxt file"""
    with open(solver_path, 'w') as f:
        f.write("""net: "%s"
test_iter: 100
test_interval: 500
base_lr: 0.01
momentum: 0.9
weight_decay: 0.0005
lr_policy: "step"
gamma: 0.1
stepsize: 5000
display: 100
max_iter: %d
snapshot: 5000
snapshot_prefix: "bilstm"
solver_mode: GPU""" % (prototxt_path, max_iter))

def load_data_preprocess(pkl_file):
    with open(pkl_file, 'rb') as file:
        data = pickle.load(file)
    
    data.columns = data.columns.str.strip()
    
    if isinstance(data, pd.DataFrame):
        X = data.drop(['Label'], axis=1)
        y = data['Label']
    elif isinstance(data, dict):
        X = data['features']
        y = data['labels']
    
    return X, y

def preprocess_data(X, y):
    # Handle missing values
    if X.isnull().any().any() or y.isnull().any():
        X, y = X.align(y, join='inner', axis=0)
        X = X.dropna()
        y = y[X.index]

    # Label encoding
    le = LabelEncoder()
    y = le.fit_transform(y)

    # Categorical feature encoding
    categorical_cols = X.select_dtypes(include=['object']).columns
    encoder = OrdinalEncoder()
    if not categorical_cols.empty:
        X[categorical_cols] = encoder.fit_transform(X[categorical_cols])
    
    # Handle infinite values
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.mean(), inplace=True)
    
    # Standardization
    scaler = StandardScaler()
    X = scaler.fit_transform(X)

    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    return X_train, X_test, y_train, y_test

def bilstm_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """Train BiLSTM model using Caffe"""
    input_size = job_params["input_size"]
    hidden_size = job_params["hidden_size"]
    output_size = job_params["output_size"]
    num_layers = job_params["num_layers"]
    num_epochs = job_params["num_epochs"]
    batch_size = job_params["batch_size"]

    # Load and preprocess data
    training_data_path = "/workspace/" + dataset["training_data_path"]
    X, y = load_data_preprocess(training_data_path)
    train_x, test_x, train_y, test_y = preprocess_data(X, y)

    # Create network and solver prototxt files
    prototxt_path = os.path.join(result_dir, "bilstm_net.prototxt")
    solver_path = os.path.join(result_dir, "bilstm_solver.prototxt")
    
    write_prototxt(input_size, hidden_size, num_layers, output_size, 
                  batch_size, prototxt_path)
    max_iter = (len(train_x) // batch_size) * num_epochs
    write_solver_prototxt(prototxt_path, solver_path, max_iter)

    # Initialize solver
    caffe.set_mode_gpu()
    solver = caffe.SGDSolver(solver_path)

    # Training loop
    for epoch in range(num_epochs):
        for i in range(0, len(train_x), batch_size):
            batch_x = train_x[i:i + batch_size]
            batch_y = train_y[i:i + batch_size]
            
            # Reshape data for Caffe
            batch_x = batch_x.reshape(batch_size, 1, -1)
            
            # Set data
            solver.net.blobs['data'].data[...] = batch_x
            solver.net.blobs['label'].data[...] = batch_y
            
            # Single SGD step
            solver.step(1)
            
        print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {solver.net.blobs["loss"].data}')

    # Save model
    model_path = os.path.join(result_dir, f"{model_name}.caffemodel")
    solver.net.save(model_path)
    
    return None, 0

def model_upload(result_dir, model_name):    
    minioClient = Minio(MINIO_URL,
                       access_key='AKIAIOSFODNN7EXAMPLE',
                       secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                       secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".caffemodel"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, 
                                    result_dir + "/" + model_name + ".caffemodel")
        result = {"source": source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1

def model_register(model_name, source, group_id, headers):    
    params = {
        "model_name": model_name,
        "model_type": "caffe",
        "file_name": model_name + ".caffemodel",
        "s3_path": source,
        "group_id": int(float(group_id)),
        "training_id": model_name,
        "training_flag": 1,
    }
    
    r = requests.post(MODEL_FACTORY_URL + MODEL_ADD_URL,
                     data=json.dumps(params),
                     headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "BiLSTM",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL + MODEL_PUSH_URL + "/" + str(model_version_id),
                     data=json.dumps(params),
                     headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def header_gen(user_id):
    return {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: user_id
    }

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Caffe BiLSTM Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                       help='BiLSTM Job Params, set all params in dict')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                       help='BiLSTM DataSet, set as a dict')
    parser.add_argument('--model', dest='model', type=json.loads,
                       help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                       help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                       help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                       help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,
                       default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                       help='sparkconf params')
    parser.add_argument('--nodehost', nargs='?', const=None, dest='nodehost', 
                       type=str, default="**************",
                       help='nodehost params')     
    
    args = parser.parse_args()
    
    job_params = args.job_params
    print(" BiLSTM job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print(" BiLSTM dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print(" BiLSTM result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print(" BiLSTM factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print(" BiLSTM fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("BiLSTM sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    
    print("Step 1 BiLSTM training:\n")
    result, ret_code = bilstm_train(args.dataset, args.job_params, 
                                  args.model["model_name"], args.result_dir, 
                                  fit_params)
    if ret_code != 0:
        print("BiLSTM train err, stop job....\n")
        print("Error Msg:" + result + "\n")
        sys.exit(-1)
        
    print("Training finish, start storage model...\n")
    
    print("Step 2 Model Upload to MinIO:\n")
    result, ret_code = model_upload(args.result_dir, args.model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
        
    print("Storage model finish, start model register...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()