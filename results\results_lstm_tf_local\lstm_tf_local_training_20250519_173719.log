2025-05-19 17:37:19,800 - LSTM_TF_Local_Main - INFO - Starting LSTM TensorFlow Local Mode main script.
2025-05-19 17:37:19,801 - LSTM_TF_Local_Main - INFO - TensorFlow version: 2.13.0
2025-05-19 17:37:19,801 - LSTM_TF_Local_Main - INFO - Received arguments: Namespace(batch_size=32, cv_folds=5, data_format='pkl', handle_imbalance=False, hidden_size=128, input_file='E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', input_size=84, label_column='Label', learning_rate=0.001, model_name='LSTM_TF_Local_Model', normalize=True, num_epochs=1, num_layers=2, output_size=6, result_dir='./results/results_lstm_tf_local', stratify=False, test_split_size=0.3, use_cv=False)
2025-05-19 17:37:19,803 - LSTM_TF_Local_Main - INFO - Effective preprocessing options: {
  "test_size": 0.3,
  "random_state": 42,
  "normalize": true,
  "encode_labels": true,
  "handle_imbalance": false,
  "stratify": false,
  "drop_missing": false,
  "fill_method": "mean",
  "handle_outliers": true
}
2025-05-19 17:37:19,804 - LSTM_TF_Local_Main - INFO - LSTM TensorFlow training (local mode) function started.
2025-05-19 17:37:19,804 - LSTM_TF_Local_Main - INFO - No GPU found, using CPU.
2025-05-19 17:37:19,804 - LSTM_TF_Local_Main - INFO - Run parameters: Input File='E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', Model Name='LSTM_TF_Local_Model'
2025-05-19 17:37:19,805 - LSTM_TF_Local_Main - INFO - Model Hyperparameters: Input Size=84, Hidden Size=128, Output Classes=6, Layers=2
2025-05-19 17:37:19,805 - LSTM_TF_Local_Main - INFO - Training Parameters: Epochs=1, LR=0.001, Batch Size=32, CV Mode=False
2025-05-19 17:37:19,805 - LSTM_TF_Local_Main - INFO - Preprocessing Options: {
  "test_size": 0.3,
  "random_state": 42,
  "normalize": true,
  "encode_labels": true,
  "handle_imbalance": false,
  "stratify": false,
  "drop_missing": false,
  "fill_method": "mean",
  "handle_outliers": true
}
2025-05-19 17:37:19,806 - LSTM_TF_Local_Main - INFO - Loading data from E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl with format pkl
2025-05-19 17:37:20,159 - LSTM_TF_Local_Main - INFO - Data loaded as DataFrame with shape (692703, 85)
2025-05-19 17:37:20,292 - LSTM_TF_Local_Main - INFO - Features shape: (692703, 84), Labels shape: (692703,)
2025-05-19 17:37:20,322 - LSTM_TF_Local_Main - INFO - Label distribution: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-19 17:37:20,367 - LSTM_TF_Local_Main - INFO - Original classes for reporting (from data): ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-19 17:37:20,368 - LSTM_TF_Local_Main - INFO - Using Keras LSTM standard train/test split.
2025-05-19 17:37:20,584 - LSTM_TF_Local_Main - INFO - Handling missing values in features
2025-05-19 17:37:20,918 - LSTM_TF_Local_Main - INFO - Missing values: 1008 -> 0
2025-05-19 17:37:20,918 - LSTM_TF_Local_Main - INFO - Encoding labels
2025-05-19 17:37:21,021 - LSTM_TF_Local_Main - INFO - Encoded 6 classes: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']")
2025-05-19 17:37:21,049 - LSTM_TF_Local_Main - INFO - Encoding 4 categorical features: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-19 17:37:24,957 - LSTM_TF_Local_Main - INFO - Checking and handling infinite values (replacing with NaN, then re-imputing).
2025-05-19 17:37:25,296 - LSTM_TF_Local_Main - INFO - Normalizing features
2025-05-19 17:37:27,447 - LSTM_TF_Local_Main - INFO - Data split into: Train X(484892, 84), y(484892,); Test X(207811, 84), y(207811,)
2025-05-19 17:37:27,508 - LSTM_TF_Local_Main - INFO - LSTM TF Preprocessing info saved to ./results/results_lstm_tf_local\preprocessing_info_lstm_tf.json
2025-05-19 17:37:27,603 - LSTM_TF_Local_Main - INFO - Prepared TF Datasets: Train X(484892, 1, 84), Y(484892,); Test X(207811, 1, 84), Y(207811,). Batch size: 32
2025-05-19 17:37:27,760 - LSTM_TF_Local_Main - INFO - Starting Keras LSTM model training (standard split).
2025-05-19 17:37:27,773 - LSTM_TF_Local_Main - INFO - Best LSTM model weights will be saved to: ./results/results_lstm_tf_local\best_model_lstm.weights.h5 based on val_loss.
2025-05-19 17:37:27,773 - LSTM_TF_Local_Main - INFO - Starting Keras LSTM model training for 1 epochs.
2025-05-19 17:38:34,505 - LSTM_TF_Local_Main - INFO - Saved final LSTM model weights to: ./results/results_lstm_tf_local\final_model_lstm.weights.h5
2025-05-19 17:38:35,034 - LSTM_TF_Local_Main - INFO - Loss history plot saved to ./results/results_lstm_tf_local\plots_lstm_tf\loss_history_lstm_tf.png
2025-05-19 17:38:35,430 - LSTM_TF_Local_Main - INFO - Accuracy history plot saved to ./results/results_lstm_tf_local\plots_lstm_tf\accuracy_history_lstm_tf.png
2025-05-19 17:38:35,431 - LSTM_TF_Local_Main - INFO - Evaluating Keras LSTM model on test set.
2025-05-19 17:38:35,431 - LSTM_TF_Local_Main - INFO - Loading best LSTM weights from ./results/results_lstm_tf_local\best_model_lstm.weights.h5 for final testing.
2025-05-19 17:38:36,134 - absl - WARNING - Skipping variable loading for optimizer 'Adam', because it has 1 variables whereas the saved optimizer has 17 variables. 
2025-05-19 17:38:36,137 - LSTM_TF_Local_Main - INFO - Starting LSTM Keras model testing.
2025-05-19 17:38:53,289 - LSTM_TF_Local_Main - INFO - Classification Report (LSTM TF):
                  precision    recall  f1-score   support

          BENIGN       1.00      1.00      1.00    132082
   DoS GoldenEye       1.00      0.99      0.99      3047
        DoS Hulk       1.00      1.00      1.00     69364
DoS Slowhttptest       1.00      0.98      0.99      1644
   DoS slowloris       0.99      1.00      0.99      1670
      Heartbleed       1.00      1.00      1.00         4

        accuracy                           1.00    207811
       macro avg       1.00      0.99      1.00    207811
    weighted avg       1.00      1.00      1.00    207811

2025-05-19 17:38:53,971 - LSTM_TF_Local_Main - INFO - Confusion matrix plot saved to ./results/results_lstm_tf_local\plots_lstm_tf with suffix '_lstm_tf'
2025-05-19 17:38:53,972 - LSTM_TF_Local_Main - INFO - LSTM TF Classification report saved to ./results/results_lstm_tf_local\classification_report_lstm_tf.txt
2025-05-19 17:38:53,973 - LSTM_TF_Local_Main - INFO - LSTM TF Test Metrics: Acc=0.9997, Precision=0.9997, Recall=0.9997, F1=0.9997
2025-05-19 17:38:53,976 - LSTM_TF_Local_Main - INFO - LSTM TF Training results saved to ./results/results_lstm_tf_local\training_results_lstm_tf.json
2025-05-19 17:38:53,976 - LSTM_TF_Local_Main - INFO - LSTM TensorFlow training (local mode) finished successfully.
2025-05-19 17:38:54,081 - LSTM_TF_Local_Main - INFO - LSTM TF training process finished. Results logged and saved in ./results/results_lstm_tf_local
2025-05-19 17:38:54,082 - LSTM_TF_Local_Main - INFO - Keras LSTM Model weights saved at: ./results/results_lstm_tf_local\best_model_lstm.weights.h5
2025-05-19 17:38:54,083 - LSTM_TF_Local_Main - INFO - Keras LSTM Test set accuracy: 99.97%
2025-05-19 17:38:54,083 - LSTM_TF_Local_Main - INFO - Keras LSTM Test set F1 score: 0.9997
2025-05-19 17:38:54,083 - LSTM_TF_Local_Main - INFO - LSTM TF Local Mode script execution completed successfully.
