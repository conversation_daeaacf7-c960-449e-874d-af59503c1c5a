import tensorflow as tf
import numpy as np
import os
import uuid
import json
import requests
from PIL import Image
import matplotlib.pyplot as plt
import sys
from minio import Minio
import argparse

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class VOC2007Dataset:
    def __init__(self, voc_root, image_size=64):
        """
        VOC2007数据集加载器的TensorFlow实现
        """
        self.voc_root = voc_root
        self.image_size = image_size
        self.images_dir = os.path.join(voc_root, "JPEGImages")
        
        # 获取所有图像文件路径
        self.image_paths = []
        for img_name in os.listdir(self.images_dir):
            if img_name.endswith(('.jpg', '.jpeg', '.png')):
                self.image_paths.append(os.path.join(self.images_dir, img_name))
                
        print(f"找到 {len(self.image_paths)} 张图像")

    def load_and_preprocess_image(self, path):
        image = tf.io.read_file(path)
        image = tf.image.decode_jpeg(image, channels=3)
        image = tf.image.resize(image, [self.image_size, self.image_size])
        image = tf.cast(image, tf.float32)
        image = (image - 127.5) / 127.5  # 归一化到 [-1, 1]
        return image

    def create_dataset(self, batch_size):
        dataset = tf.data.Dataset.from_tensor_slices(self.image_paths)
        dataset = dataset.map(self.load_and_preprocess_image, 
                            num_parallel_calls=tf.data.AUTOTUNE)
        dataset = dataset.shuffle(1000).batch(batch_size).prefetch(tf.data.AUTOTUNE)
        return dataset

class Generator(tf.keras.Model):
    def __init__(self, latent_dim):
        super(Generator, self).__init__()
        self.latent_dim = latent_dim
        
        self.model = tf.keras.Sequential([
            # 从潜在空间开始
            tf.keras.layers.Dense(4 * 4 * 128, use_bias=False, input_shape=(latent_dim,)),
            tf.keras.layers.Reshape((4, 4, 128)),
            
            # 4x4 -> 8x8
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.UpSampling2D(),
            tf.keras.layers.Conv2D(128, 3, padding='same', use_bias=False),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.LeakyReLU(0.2),
            
            # 8x8 -> 16x16
            tf.keras.layers.UpSampling2D(),
            tf.keras.layers.Conv2D(64, 3, padding='same', use_bias=False),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.LeakyReLU(0.2),
            
            # 16x16 -> 32x32
            tf.keras.layers.UpSampling2D(),
            tf.keras.layers.Conv2D(32, 3, padding='same', use_bias=False),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.LeakyReLU(0.2),
            
            # 32x32 -> 64x64
            tf.keras.layers.UpSampling2D(),
            tf.keras.layers.Conv2D(3, 3, padding='same', use_bias=False, activation='tanh')
        ])

    def call(self, inputs):
        return self.model(inputs)

class Discriminator(tf.keras.Model):
    def __init__(self):
        super(Discriminator, self).__init__()
        
        self.model = tf.keras.Sequential([
            # 64x64 -> 32x32
            tf.keras.layers.Conv2D(16, 3, strides=2, padding='same'),
            tf.keras.layers.LeakyReLU(0.2),
            tf.keras.layers.Dropout(0.25),
            
            # 32x32 -> 16x16
            tf.keras.layers.Conv2D(32, 3, strides=2, padding='same'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.LeakyReLU(0.2),
            tf.keras.layers.Dropout(0.25),
            
            # 16x16 -> 8x8
            tf.keras.layers.Conv2D(64, 3, strides=2, padding='same'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.LeakyReLU(0.2),
            tf.keras.layers.Dropout(0.25),
            
            # 8x8 -> 4x4
            tf.keras.layers.Conv2D(128, 3, strides=2, padding='same'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.LeakyReLU(0.2),
            tf.keras.layers.Dropout(0.25),
            
            # 分类层
            tf.keras.layers.Flatten(),
            tf.keras.layers.Dense(1, activation='sigmoid')
        ])

    def call(self, inputs):
        return self.model(inputs)

class GANTrainer:
    def __init__(self, generator, discriminator, latent_dim):
        self.generator = generator
        self.discriminator = discriminator
        self.latent_dim = latent_dim
        
        self.g_optimizer = tf.keras.optimizers.Adam(2e-4, beta_1=0.5)
        self.d_optimizer = tf.keras.optimizers.Adam(2e-4, beta_1=0.5)
        
        self.cross_entropy = tf.keras.losses.BinaryCrossentropy()
        
    @tf.function
    def train_step(self, real_images):
        batch_size = tf.shape(real_images)[0]
        noise = tf.random.normal([batch_size, self.latent_dim])
        
        with tf.GradientTape() as gen_tape, tf.GradientTape() as disc_tape:
            # 生成图像
            generated_images = self.generator(noise, training=True)
            
            # 判别器决策
            real_output = self.discriminator(real_images, training=True)
            fake_output = self.discriminator(generated_images, training=True)
            
            # 计算损失
            gen_loss = self.generator_loss(fake_output)
            disc_loss = self.discriminator_loss(real_output, fake_output)
        
        # 计算梯度
        gen_gradients = gen_tape.gradient(gen_loss, self.generator.trainable_variables)
        disc_gradients = disc_tape.gradient(disc_loss, self.discriminator.trainable_variables)
        
        # 应用梯度
        self.g_optimizer.apply_gradients(zip(gen_gradients, self.generator.trainable_variables))
        self.d_optimizer.apply_gradients(zip(disc_gradients, self.discriminator.trainable_variables))
        
        return gen_loss, disc_loss
    
    def generator_loss(self, fake_output):
        return self.cross_entropy(tf.ones_like(fake_output), fake_output)
    
    def discriminator_loss(self, real_output, fake_output):
        real_loss = self.cross_entropy(tf.ones_like(real_output), real_output)
        fake_loss = self.cross_entropy(tf.zeros_like(fake_output), fake_output)
        return real_loss + fake_loss

def train_gan(dataset, generator, discriminator, trainer, num_epochs, latent_dim,
              sample_interval=100, result_dir='results'):
    os.makedirs(result_dir, exist_ok=True)
    
    # 创建固定噪声用于可视化训练进程
    fixed_noise = tf.random.normal([16, latent_dim])
    
    for epoch in range(num_epochs):
        print(f"\nEpoch {epoch+1}/{num_epochs}")
        
        for batch_idx, real_images in enumerate(dataset):
            g_loss, d_loss = trainer.train_step(real_images)
            
            if batch_idx % 100 == 0:
                print(f"Batch {batch_idx}, G_loss: {g_loss:.4f}, D_loss: {d_loss:.4f}")
            
            # 保存采样结果
            batches_done = epoch * len(dataset) + batch_idx
            if batches_done % sample_interval == 0:
                save_sample_images(generator, fixed_noise, epoch, batches_done, result_dir)

def save_sample_images(generator, noise, epoch, batches_done, result_dir, n_row=4):
    """保存生成的样本图像"""
    predictions = generator(noise, training=False)
    
    # 将值范围从[-1, 1]转换为[0, 1]
    predictions = (predictions + 1) / 2.0
    
    fig = plt.figure(figsize=(8, 8))
    for i in range(predictions.shape[0]):
        plt.subplot(4, 4, i+1)
        plt.imshow(predictions[i])
        plt.axis('off')
    
    save_path = os.path.join(result_dir, f'samples_epoch_{epoch}_batch_{batches_done}.png')
    plt.savefig(save_path)
    plt.close()

def gan_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """
    GAN训练主函数
    """
    # 解析参数
    latent_dim = job_params.get("latent_dim", 100)
    image_size = job_params.get("image_size", 64)
    num_epochs = job_params.get("num_epochs", 100)
    batch_size = job_params.get("batch_size", 64)
    
    # 准备数据
    training_data_path = "/workspace/" + dataset["training_data_path"]
    voc_dataset = VOC2007Dataset(training_data_path, image_size)
    train_dataset = voc_dataset.create_dataset(batch_size)
    
    # 初始化模型
    generator = Generator(latent_dim)
    discriminator = Discriminator()
    trainer = GANTrainer(generator, discriminator, latent_dim)
    
    try:
        # 创建结果目录
        os.makedirs(result_dir, exist_ok=True)
        
        # 训练模型
        train_gan(train_dataset, generator, discriminator, trainer,
                 num_epochs, latent_dim, sample_interval=100, 
                 result_dir=result_dir)
        
        # 保存模型
        generator.save(os.path.join(result_dir, f"{model_name}_generator.h5"))
        discriminator.save(os.path.join(result_dir, f"{model_name}_discriminator.h5"))
        print(f'训练完成，模型保存到 {result_dir}')
        
    except Exception as e:
        print(f"训练过程中发生错误: {e}")
        return str(e), -1
    
    return None, 0

def model_upload(result_dir, model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".h5"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".h5")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1

def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "TENSORFLOW",
          "file_name": model_name+".h5",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "GAN",
        "model_usage": "Generation"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='tensorflow GAN Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='GAN Job Params, set all params in dict')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='GAN DataSet, set as a dict')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')
    
    print("Start GAN training job, params:\n" + str(sys.argv) + "\n")
    args = parser.parse_args()
    
    job_params = args.job_params
    print("GAN job params:" + str(job_params) + "\n")
    dataset = args.dataset
    print("GAN dataset:" + str(dataset) + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("GAN result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("GAN factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("GAN fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("GAN sparkconf params:" + str(sparkconf) + "\n")
    
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    
    if fit_params is None:
        fit_params = {}
    
    device = '/GPU:0' if tf.config.list_physical_devices('GPU') else '/CPU:0'
    
    print("Step 1 GAN training:\n")
    result, ret_code = gan_train(dataset, job_params, model["model_name"], result_dir, fit_params,device)
    if ret_code != 0:
        print("FCN train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()