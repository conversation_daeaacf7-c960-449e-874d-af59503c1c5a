"""保序回归 - 增强版

此脚本使用保序回归器(IsotonicRegression)对数据集进行训练和评估，
支持多种数据格式、灵活的预处理选项和结果可视化。

保序回归是一种非参数回归技术，它保持特征和目标之间的单调关系。
适用于当您确信特征和目标之间存在单调关系但不确定确切形式的情况。

功能:
- 灵活数据加载(支持pkl、csv、json格式)
- 模型训练与评估
- 交叉验证参数优化
- 结果可视化(预测vs真实值)
- 模型保存与管理(MinIO存储)
- 完整的日志记录
- 错误处理机制

使用方法:
python isotonic_regression_local.py --data your_data.pkl --increasing true --output ./results

使用交叉验证:
python isotonic_regression_local.py --data your_data.pkl --cv --output ./results
"""

import pickle
import requests
import joblib
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import logging
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 非交互式后端，适用于无显示环境
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import traceback
from sklearn.isotonic import IsotonicRegression
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.model_selection import train_test_split, KFold
import subprocess

# 常量定义
MINIO_URL = "minio-prophecis.prophecis:9000"

MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

# 请求头常量
MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

# 使用常量定义Minio凭证，便于维护
MINIO_ACCESS_KEY = 'AKIAIOSFODNN7EXAMPLE'
MINIO_SECRET_KEY = 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY'

# 辅助函数：将对象转换为JSON可序列化的格式
def convert_to_serializable(obj):
    """
    递归地将对象转换为JSON可序列化的格式
    
    参数:
    obj: 需要转换的对象
    
    返回:
    转换后的对象
    """
    if isinstance(obj, dict):
        return {convert_to_serializable(k): convert_to_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_serializable(item) for item in obj]
    elif isinstance(obj, (np.integer, np.int64, np.int32)):
        return int(obj)
    elif isinstance(obj, (np.floating, np.float64, np.float32)):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    else:
        return obj

def setup_logging(log_dir, log_level=logging.INFO):
    """
    设置日志配置
    
    参数:
    log_dir: 日志文件保存目录
    log_level: 日志级别，默认为INFO
    
    返回:
    logger: 配置好的日志记录器
    """
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 生成带时间戳的日志文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"isotonic_regression_{timestamp}.log")
    
    # 获取根日志记录器
    logger = logging.getLogger("IsotonicRegression")
    
    # 清除已有的处理器
    if logger.handlers:
        for handler in logger.handlers:
            logger.removeHandler(handler)
    
    # 设置日志级别
    logger.setLevel(log_level)
    
    # 创建文件处理器
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(log_level)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    
    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理器到日志记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    logger.info(f"日志配置完成，日志文件: {log_file}")
    return logger

# 灵活加载数据集，支持不同的数据格式和目标列
def flexible_data_load(file_path, target_col='target', feature_cols=None, data_format='pkl'):
    """
    灵活加载不同格式的数据集
    
    参数:
    file_path: 数据文件路径
    target_col: 目标列名，默认为'target'
    feature_cols: 特征列名列表，None表示使用除目标列外的所有列
    data_format: 数据格式，支持'pkl'、'csv'、'json'等
    
    返回:
    X: 特征数据
    y: 目标数据
    """
    logger = logging.getLogger("IsotonicRegression")
    logger.info(f"加载数据，文件路径: {file_path}，格式: {data_format}")
    
    # 加载不同格式的数据
    if data_format.lower() == 'pkl':
        with open(file_path, 'rb') as file:
            data = pickle.load(file)
    elif data_format.lower() == 'csv':
        data = pd.read_csv(file_path)
    elif data_format.lower() == 'json':
        data = pd.read_json(file_path)
    else:
        raise ValueError(f"不支持的数据格式: {data_format}")
    
    # 处理不同类型的数据结构
    if isinstance(data, pd.DataFrame):
        logger.info(f"数据加载为DataFrame，形状: {data.shape}")
        
        # 清理列名，去除可能的空格
        data.columns = data.columns.str.strip()
        
        # 检查目标列是否存在
        if target_col not in data.columns:
            available_cols = ', '.join(data.columns.tolist())
            logger.error(f"未找到目标列 '{target_col}'。可用列: {available_cols}")
            raise ValueError(f"未找到目标列 '{target_col}'")
        
        # 根据特征列选择特征
        if feature_cols is not None:
            # 保序回归通常只使用一个特征
            if len(feature_cols) > 1:
                logger.warning("保序回归通常使用单特征，已检测到多个特征。若需使用多个特征，请考虑先进行降维。")
            
            missing_cols = [col for col in feature_cols if col not in data.columns]
            if missing_cols:
                logger.warning(f"部分特征列未在数据中找到: {missing_cols}")
            X = data[feature_cols]
        else:
            # 保序回归通常只使用一个特征
            if data.drop([target_col], axis=1).shape[1] > 1:
                logger.warning("保序回归通常使用单特征，已检测到多个特征。若需使用多个特征，请考虑先进行降维。")
            X = data.drop([target_col], axis=1)
            
        y = data[target_col]
        
        logger.info(f"特征形状: {X.shape}, 目标形状: {y.shape}")
        logger.info(f"目标变量统计: 平均值={y.mean():.4f}, 标准差={y.std():.4f}")
    
    elif isinstance(data, tuple) and len(data) >= 2:
        # 处理已经分割好的数据集
        logger.info("数据加载为元组（已分割）")
        # 假设前两个元素是训练数据和训练标签
        X, y = data[0], data[1]
        
        # 检查数据类型，确保可以处理
        logger.info(f"特征类型: {type(X)}, 形状: {X.shape if hasattr(X, 'shape') else '未知'}")
        logger.info(f"目标类型: {type(y)}, 形状: {y.shape if hasattr(y, 'shape') else '未知'}")
    
    elif isinstance(data, dict):
        logger.info("数据加载为字典")
        if 'features' in data and 'labels' in data:
            X = data['features']
            y = data['labels']
        else:
            # 检查是否包含训练、测试数据
            if 'train' in data and 'test' in data:
                # 只返回训练数据
                if isinstance(data['train'], tuple) and len(data['train']) >= 2:
                    X, y = data['train'][0], data['train'][1]
                else:
                    raise ValueError("无法识别的训练数据格式")
            else:
                raise ValueError("字典数据应包含'features'和'labels'键或'train'和'test'键")
    else:
        # 尝试处理tuple类型的特殊情况：(train_data, validation_data, test_data)
        if isinstance(data, tuple) and len(data) == 3:
            # 处理原始代码中的特殊格式
            training_data, validation_data, test_data = data
            if isinstance(training_data, tuple) and len(training_data) >= 2:
                X, y = training_data
                logger.info(f"从元组格式加载数据，形状: X={X.shape if hasattr(X, 'shape') else '未知'}, y={y.shape if hasattr(y, 'shape') else '未知'}")
                return X, y
            else:
                raise TypeError(f"训练数据格式不符合预期: {type(training_data)}")
        else:
            raise TypeError(f"不支持的数据类型: {type(data)}")
    
    # 保序回归需要对数据进行检查和处理
    # 如果X是多维的，需要特殊处理或警告
    if hasattr(X, 'ndim') and X.ndim > 1 and X.shape[1] > 1:
        logger.warning("保序回归通常应用于一维数据。多维数据可能需要特殊处理。")
        # 对于简单起见，如果是多维的，我们只取第一个特征
        if isinstance(X, pd.DataFrame):
            logger.info(f"多维数据，选择第一列作为特征: {X.columns[0]}")
            X = X.iloc[:, 0].values
        else:
            logger.info("多维数据，选择第一列作为特征")
            X = X[:, 0]
    elif isinstance(X, pd.DataFrame):
        # 将DataFrame转换为numpy数组
        X = X.values.reshape(-1)
    
    # 确保X是一维的
    if hasattr(X, 'ndim') and X.ndim > 1:
        X = X.reshape(-1)
    
    logger.info(f"处理后的特征形状: {X.shape if hasattr(X, 'shape') else '未知'}")
    return X, y

def isotonic_regressor(X_train, y_train, X_test=None, y_test=None, model_params=None, model_name="isotonic_regression_model", result_dir="./result"):
    """
    使用保序回归进行回归分析

    参数:
    X_train (numpy.ndarray): 训练特征数据
    y_train (numpy.ndarray): 训练标签数据
    X_test (numpy.ndarray, optional): 测试特征数据
    y_test (numpy.ndarray, optional): 测试标签数据
    model_params (dict): 保序回归模型参数
    model_name (str): 模型名称
    result_dir (str): 结果保存目录

    返回:
    dict: 包含模型评估结果和模型路径的字典
    str: 模型文件路径
    """
        logger = logging.getLogger("IsotonicRegression")
    logger.info("开始保序回归模型训练")
        
    try:
        # 参数验证
        if model_params is None:
            model_params = {}
            
        # 创建结果目录
        if not os.path.exists(result_dir):
            os.makedirs(result_dir)
            logger.info(f"创建结果目录: {result_dir}")
        
        # 获取模型参数
        increasing = model_params.get('increasing', True)  # 默认为递增
        y_min = model_params.get('y_min', None)
        y_max = model_params.get('y_max', None)
        out_of_bounds = model_params.get('out_of_bounds', 'nan')  # 'nan', 'clip', 'raise'
        
        logger.info(f"模型参数: increasing={increasing}, y_min={y_min}, y_max={y_max}, out_of_bounds={out_of_bounds}")
        
        # 训练模型
        model = IsotonicRegression(
            y_min=y_min,
            y_max=y_max,
            increasing=increasing,
            out_of_bounds=out_of_bounds
        )
        
        logger.info(f"开始训练模型，训练数据形状: X={X_train.shape}, y={y_train.shape}")
        start_time = datetime.now()
        model.fit(X_train, y_train)
        training_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"模型训练完成，用时 {training_time:.2f} 秒")
        
        # 保存模型
        model_path = os.path.join(result_dir, f"{model_name}.joblib")
        joblib.dump(model, model_path)
        
        # 为了兼容性，也保存pickle格式
        pickle_path = os.path.join(result_dir, f"{model_name}.pickle")
        with open(pickle_path, "wb") as model_file:
            pickle.dump(model, model_file)
        
        logger.info(f"模型已保存至 {model_path} 和 {pickle_path}")
        
        # 模型评估
        metrics = {}
        if X_test is not None and y_test is not None:
            logger.info("使用测试集评估模型")
            y_pred = model.predict(X_test)
            mse = mean_squared_error(y_test, y_pred)
            rmse = np.sqrt(mse)
            mae = mean_absolute_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)
            
            metrics = {
                "mse": float(mse),
                "rmse": float(rmse),
                "mae": float(mae),
                "r2": float(r2)
            }
            
            logger.info(f"测试集评估结果 - MSE: {mse:.4f}, RMSE: {rmse:.4f}, MAE: {mae:.4f}, R²: {r2:.4f}")
        
        # 绘制预测值vs真实值
        fig = plt.figure(figsize=(10, 6))
            plt.scatter(y_test, y_pred, alpha=0.5)
            plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'k--', lw=2)
        plt.xlabel('真实值')
        plt.ylabel('预测值')
        plt.title('保序回归 - 预测值vs真实值')
        plt.grid(True)
        
        prediction_plot_path = os.path.join(result_dir, f"{model_name}_predictions.png")
        plt.savefig(prediction_plot_path, dpi=300)
        plt.close(fig)
        logger.info(f"预测值vs真实值图已保存至 {prediction_plot_path}")
        
        # 保存预测结果
        results_df = pd.DataFrame({
                'actual': y_test,
                'predicted': y_pred,
                'error': y_test - y_pred
        })
        predictions_path = os.path.join(result_dir, f"{model_name}_predictions.csv")
        results_df.to_csv(predictions_path, index=False)
        logger.info(f"预测结果已保存至 {predictions_path}")
        else:
            logger.info("未提供测试集，跳过模型评估")
        
        # 准备返回结果
        result = {
            "model_name": model_name,
            "training_time": training_time,
            "model_path": pickle_path,
            "metrics": metrics
        }
        
        # 保存结果摘要
        results_json_path = os.path.join(result_dir, f"{model_name}_summary.json")
        with open(results_json_path, 'w') as f:
            json.dump(convert_to_serializable(result), f, indent=4)
        logger.info(f"结果摘要已保存至 {results_json_path}")
        
        return result, pickle_path
        
    except Exception as e:
        logger.error(f"保序回归模型训练失败: {str(e)}")
        logger.exception("详细错误信息:")
        raise

def model_upload(model_name, model_path):
    """
    将模型上传到MinIO存储。

    参数:
    - model_name (str): 模型名称。
    - model_path (str): 模型文件路径。

    返回:
    - result (dict): 上传结果字典，包含source_path。
    - ret_code (int): 返回码，0 表示成功，其他值表示失败。
    """
    logger = logging.getLogger("IsotonicRegression")
    logger.info(f"开始上传模型 {model_name}")
    
    # 检查模型文件是否存在
    if not os.path.exists(model_path):
        logger.error(f"模型文件不存在: {model_path}")
        return {"error": "Model file not found"}, -1
    
    try:
        # 构建上传路径和源URL
        upload_path = f"models/isotonic_regression/{model_name}.pickle"
        source = f"s3a://mlss/users/admin/{upload_path}"
        
        # 上传模型到MinIO
        logger.info(f"上传模型到路径: {upload_path}")
        cmd = f"mc cp {model_path} mlss/mlss/users/admin/{upload_path}"
        process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        stdout, stderr = process.communicate()
        
        if process.returncode != 0:
            logger.error(f"模型上传失败: {stderr.decode()}")
            return {"error": stderr.decode()}, -1
        
        logger.info(f"模型上传成功: {stdout.decode()}")
        return {"source_path": source}, 0
    except Exception as e:
        logger.error(f"模型上传过程中发生错误: {str(e)}")
        logger.exception("详细错误信息:")
        return {"error": str(e)}, -1

def model_register(model_name, user_id, source, model_type="IsotonicRegression"):
    """
    注册模型到模型工厂。

    参数:
    - model_name (str): 模型名称。
    - user_id (str): 用户ID。
    - source (str): 模型的 MinIO 存储路径。
    - model_type (str): 模型类型，默认为"IsotonicRegression"。

    返回:
    - result (dict): 注册结果字典，包含model_id和model_version_id。
    - ret_code (int): 返回码，0 表示成功，其他值表示失败。
    """
    logger = logging.getLogger("IsotonicRegression")
    logger.info(f"开始注册模型 {model_name} 到模型工厂")
    
    # 生成请求头
    headers = {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: user_id
    }
    
    # 构建请求参数
    params = {
          "model_name": model_name,
        "model_type": model_type,
        "file_name": model_name + ".pickle",
          "s3_path": source,
        "group_id": 1,  # 默认组ID
          "training_id": model_name,
          "training_flag": 1,
        }
    
    try:
        logger.info(f"发送模型注册请求: {params}")
        r = requests.post(MODEL_FACTORY_URL + MODEL_ADD_URL, 
                         data=json.dumps(params), 
                         headers=headers)
        
    status_code = r.status_code
        res_data = r.content.decode()
        
    if status_code == 200:
            result = json.loads(res_data)
            logger.info(f"模型注册成功，模型ID: {result['result']['model_id']}")
            return result, 0
    else:
            logger.error(f"模型注册失败，状态码: {status_code}, 响应: {res_data}")
        return res_data, -1
    except Exception as e:
        logger.error(f"模型注册过程中发生错误: {str(e)}")
        logger.exception("详细错误信息:")
        return str(e), -1

def model_push(model_id, model_version_id, factory_name):
    """
    推送模型到工厂生产系统。

    参数:
    - model_id (str): 模型ID。
    - model_version_id (str): 模型版本ID。
    - factory_name (str): 工厂名称。

    返回:
    - result (dict): 推送结果字典。
    - ret_code (int): 返回码，0 表示成功，其他值表示失败。
    """
    logger = logging.getLogger("IsotonicRegression")
    
    # 如果工厂名称为None，跳过推送
    if factory_name == "None":
        logger.info("工厂名称为None，跳过模型推送")
        return "Factory Name is none, Skip Model Push.", 0        
  
    # 生成请求头
    headers = {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: "admin"  # 默认使用admin用户
    }
    
    # 构建请求参数
    params = {
        "factory_name": factory_name,
        "model_type": "IsotonicRegression",
        "model_usage": "Regression"
    }
    
    try:
        logger.info(f"推送模型 (ID: {model_id}, 版本: {model_version_id}) 到工厂 {factory_name}")
        r = requests.post(
            f"{MODEL_FACTORY_URL}{MODEL_PUSH_URL}/{str(model_version_id)}",
            data=json.dumps(params),
            headers=headers
        )
        
    status_code = r.status_code
        res_data = r.content.decode()
        
    if status_code == 200:
            result = json.loads(res_data)
            logger.info(f"模型推送成功: {result}")
            return result, 0
    else:
            logger.error(f"模型推送失败，状态码: {status_code}, 响应: {res_data}")
        return res_data, -1
    except Exception as e:
        logger.error(f"模型推送过程中发生错误: {str(e)}")
        logger.exception("详细错误信息:")
        return str(e), -1
    

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

    
def cross_validate_isotonic(X, y, param_grid, cv=5, scoring='neg_mean_squared_error', random_state=42):
    """
    对保序回归进行参数交叉验证
    
    参数:
    X: 特征数据
    y: 目标数据
    param_grid: 参数网格，包含increasing和out_of_bounds的不同组合
    cv: 交叉验证折数
    scoring: 评分标准，可选 'neg_mean_squared_error', 'neg_mean_absolute_error', 'r2'
    random_state: 随机种子
    
    返回:
    best_params: 最佳参数
    cv_results: 交叉验证结果
    """
    logger = logging.getLogger("IsotonicRegression")
    logger.info(f"开始保序回归参数交叉验证")
    
    # 检查输入数据
    if X.ndim > 1 and X.shape[1] > 1:
        logger.warning("保序回归通常应用于一维数据，但传入了多维数据。只使用第一列进行交叉验证。")
        X = X[:, 0]
    
    # 如果X是一维的，确保它是列向量形式
    if X.ndim == 1:
        X = X.reshape(-1, 1)
    
    # 准备存储结果的数据结构
    cv_results = []
    
    # 创建交叉验证分割器
    kf = KFold(n_splits=cv, shuffle=True, random_state=random_state)
    
    # 记录开始时间
    start_time = datetime.now()
    
    # 遍历参数组合
    total_combinations = len(param_grid['increasing']) * len(param_grid['out_of_bounds'])
    logger.info(f"参数组合总数: {total_combinations}")
    
    for increasing in param_grid['increasing']:
        for out_of_bounds in param_grid['out_of_bounds']:
            # 记录当前参数组合
            param_dict = {'increasing': increasing, 'out_of_bounds': out_of_bounds}
            logger.info(f"评估参数: increasing={increasing}, out_of_bounds={out_of_bounds}")
            
            # 存储每个折叠的指标
            fold_metrics = []
            
            # 对数据进行K折交叉验证
            for fold, (train_idx, val_idx) in enumerate(kf.split(X)):
                X_train, X_val = X[train_idx], X[val_idx]
                y_train, y_val = y[train_idx], y[val_idx]
                
                # 创建保序回归模型
                model = IsotonicRegression(
                    increasing=increasing,
                    out_of_bounds=out_of_bounds
                )
                
                # 训练模型
                model.fit(X_train, y_train)
                
                # 在验证集上评估模型
                y_pred = model.predict(X_val)
                
                # 计算评估指标
                mse = mean_squared_error(y_val, y_pred)
                rmse = np.sqrt(mse)
                mae = mean_absolute_error(y_val, y_pred)
                r2 = r2_score(y_val, y_pred)
                
                # 存储该折叠的结果
                fold_result = {
                    'params': param_dict.copy(),
                    'fold': fold,
                    'mse': mse,
                    'rmse': rmse,
                    'mae': mae,
                    'r2': r2
                }
                
                fold_metrics.append(fold_result)
                logger.debug(f"折叠 {fold+1}/{cv} 完成, RMSE: {rmse:.4f}, R²: {r2:.4f}")
            
            # 计算平均指标
            avg_mse = np.mean([fold['mse'] for fold in fold_metrics])
            avg_rmse = np.mean([fold['rmse'] for fold in fold_metrics])
            avg_mae = np.mean([fold['mae'] for fold in fold_metrics])
            avg_r2 = np.mean([fold['r2'] for fold in fold_metrics])
            
            # 计算指标标准差
            std_mse = np.std([fold['mse'] for fold in fold_metrics])
            std_rmse = np.std([fold['rmse'] for fold in fold_metrics])
            std_mae = np.std([fold['mae'] for fold in fold_metrics])
            std_r2 = np.std([fold['r2'] for fold in fold_metrics])
            
            # 汇总该参数组合的结果
            param_result = {
                'params': param_dict,
                'avg_mse': avg_mse,
                'avg_rmse': avg_rmse,
                'avg_mae': avg_mae,
                'avg_r2': avg_r2,
                'std_mse': std_mse,
                'std_rmse': std_rmse,
                'std_mae': std_mae,
                'std_r2': std_r2,
                'fold_metrics': fold_metrics
            }
            
            # 将结果添加到结果列表
            cv_results.append(param_result)
            
            # 记录当前参数组合的平均结果
            logger.info(f"参数 {param_dict}: 平均RMSE={avg_rmse:.4f}±{std_rmse:.4f}, 平均R²={avg_r2:.4f}±{std_r2:.4f}")
    
    # 根据评分方式选择最佳参数
    if scoring == 'neg_mean_squared_error':
        best_result = min(cv_results, key=lambda x: x['avg_mse'])
        logger.info(f"根据MSE选择最佳参数: {best_result['params']}, MSE: {best_result['avg_mse']:.4f}")
    elif scoring == 'neg_mean_absolute_error':
        best_result = min(cv_results, key=lambda x: x['avg_mae'])
        logger.info(f"根据MAE选择最佳参数: {best_result['params']}, MAE: {best_result['avg_mae']:.4f}")
    elif scoring == 'r2':
        best_result = max(cv_results, key=lambda x: x['avg_r2'])
        logger.info(f"根据R²选择最佳参数: {best_result['params']}, R²: {best_result['avg_r2']:.4f}")
    else:
        best_result = min(cv_results, key=lambda x: x['avg_rmse'])
        logger.info(f"根据RMSE选择最佳参数: {best_result['params']}, RMSE: {best_result['avg_rmse']:.4f}")
    
    # 计算总耗时
    total_time = (datetime.now() - start_time).total_seconds()
    logger.info(f"交叉验证完成，总耗时: {total_time:.2f}秒")
    
    return best_result['params'], cv_results

def cross_validation_split(X, y, n_splits=5, shuffle=True, random_state=42):
    """
    创建交叉验证的数据分割，使用标准K折交叉验证
    
    参数:
    X: 特征数据
    y: 标签数据
    n_splits: 折数
    shuffle: 是否打乱数据
    random_state: 随机种子
    
    返回:
    splits: 包含训练和验证索引的列表
    """
    logger = logging.getLogger("IsotonicRegression")
    logger.info(f"执行 {n_splits} 折交叉验证")
    
    # 使用K折交叉验证
    kf = KFold(n_splits=n_splits, shuffle=shuffle, random_state=random_state)
    logger.info("使用标准k折交叉验证")
    
    splits = list(kf.split(X, y))
    return splits

def cross_validation_train(X, y, job_params, model_name, result_dir, cv_params=None):
    """
    使用交叉验证训练保序回归模型
    
    参数:
    X: 特征数据
    y: 标签数据
    job_params: 模型参数
    model_name: 模型名称
    result_dir: 结果保存目录
    cv_params: 交叉验证参数字典
        n_splits: 折数 (默认5)
        shuffle: 是否打乱数据 (默认True)
    
    返回:
    trained_model: 在全部数据上训练的最终模型
    cv_results: 交叉验证结果
    """
    logger = logging.getLogger("IsotonicRegression")
    
    # 设置默认交叉验证参数
    if cv_params is None:
        cv_params = {}
    
    n_splits = cv_params.get("n_splits", 5)
    shuffle = cv_params.get("shuffle", True)
    
    logger.info(f"开始 {n_splits} 折交叉验证训练")
    logger.info(f"交叉验证参数: {cv_params}")
    
    # 提取模型参数
    increasing = job_params.get('increasing', True)
    y_min = job_params.get('y_min', None)
    y_max = job_params.get('y_max', None)
    out_of_bounds = job_params.get('out_of_bounds', 'nan')
    
    # 获取交叉验证分割
    splits = cross_validation_split(X, y, n_splits=n_splits, shuffle=shuffle, 
                                   random_state=42)  # 使用固定的随机种子
    
    # 用于存储每个折的性能
    fold_metrics = []
    
    # 对每个折进行训练和评估
    for fold, (train_idx, val_idx) in enumerate(splits):
        logger.info(f"训练折 {fold+1}/{n_splits}")
        
        # 分割数据
        X_train, X_val = X[train_idx], X[val_idx]
        y_train, y_val = y[train_idx], y[val_idx]
        
        # 创建保序回归模型
        model = IsotonicRegression(
            y_min=y_min,
            y_max=y_max,
            increasing=increasing,
            out_of_bounds=out_of_bounds
        )
        
        # 训练模型
        model.fit(X_train, y_train)
        
        # 验证模型
        y_pred = model.predict(X_val)
        mse = mean_squared_error(y_val, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_val, y_pred)
        r2 = r2_score(y_val, y_pred)
        
        # 保存本折的模型
        fold_dir = os.path.join(result_dir, f'fold_{fold+1}')
        if not os.path.exists(fold_dir):
            os.makedirs(fold_dir)
            
        model_path = os.path.join(fold_dir, f"{model_name}_fold_{fold+1}.joblib")
        joblib.dump(model, model_path)
        
        # 记录性能
        fold_metrics.append({
            'fold': fold + 1,
            'mse': mse,
            'rmse': rmse,
            'mae': mae,
            'r2': r2
        })
        
        logger.info(f"折 {fold+1} 完成，验证MSE: {mse:.4f}, RMSE: {rmse:.4f}, MAE: {mae:.4f}, R²: {r2:.4f}")
    
    # 汇总交叉验证结果
    cv_mse = np.mean([m['mse'] for m in fold_metrics])
    cv_rmse = np.mean([m['rmse'] for m in fold_metrics])
    cv_mae = np.mean([m['mae'] for m in fold_metrics])
    cv_r2 = np.mean([m['r2'] for m in fold_metrics])
    
    logger.info(f"交叉验证完成，平均MSE: {cv_mse:.4f}, RMSE: {cv_rmse:.4f}, MAE: {cv_mae:.4f}, R²: {cv_r2:.4f}")
    
    # 在全部数据上训练最终模型
    logger.info("在所有数据上训练最终模型")
    
    final_model = IsotonicRegression(
        y_min=y_min,
        y_max=y_max,
        increasing=increasing,
        out_of_bounds=out_of_bounds
    )
    
    final_model.fit(X, y)
    
    # 保存最终模型
    final_model_path = os.path.join(result_dir, f"{model_name}.joblib")
    joblib.dump(final_model, final_model_path)
    logger.info(f"最终模型已保存到 {final_model_path}")
    
    # 为了兼容性，也保存pickle格式
    pickle_path = os.path.join(result_dir, f"{model_name}.pickle")
    with open(pickle_path, "wb") as model_file:
        pickle.dump(final_model, model_file)
    logger.info(f"最终模型也保存为pickle格式: {pickle_path}")
    
    # 保存交叉验证结果到JSON文件
    cv_results = {
        'folds': fold_metrics,
        'mean_mse': float(cv_mse),
        'mean_rmse': float(cv_rmse),
        'mean_mae': float(cv_mae),
        'mean_r2': float(cv_r2),
        'cv_params': {
            'n_splits': n_splits,
            'shuffle': shuffle
        }
    }
    
    with open(os.path.join(result_dir, 'cv_results.json'), 'w') as f:
        # 使用辅助函数转换为可序列化格式
        json.dump(convert_to_serializable(cv_results), f, indent=4)
    
    return final_model, cv_results

# 更新main函数，添加交叉验证相关参数
def main():
    """
    命令行入口函数，使用argparse处理命令行参数
    """
    parser = argparse.ArgumentParser(
        description="等渗回归模型训练和部署",
        epilog="示例: python isotonic_regression_local.py --job_params '{\"isotonic_regression_params\":{\"y_min\":0,\"y_max\":1,\"out_of_bounds\":\"clip\"}}' --dataset '{\"train_data_path\":\"data/train.csv\",\"test_data_path\":\"data/test.csv\",\"features\":[\"x1\",\"x2\"],\"target\":\"y\"}'",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # 必需参数
    parser.add_argument("--job_params", type=str, required=True, 
                        help="模型参数的JSON字符串，包含isotonic_regression_params")
    parser.add_argument("--dataset", type=str, required=True,
                        help="数据集配置的JSON字符串，包含train_data_path, test_data_path, features, target")
    
    # 可选参数
    parser.add_argument("--result_dir", type=str, default="./result",
                        help="结果保存目录，默认为./result")
    parser.add_argument("--model_name", type=str, default="isotonic_regression_model",
                        help="模型名称，默认为isotonic_regression_model")
    parser.add_argument("--fit_params", type=str, default="{}",
                        help="拟合参数的JSON字符串，包含cv_enable等")
    parser.add_argument("--user_id", type=str, default="admin",
                        help="用户ID，默认为admin")
    parser.add_argument("--factory_name", type=str, default="None",
                        help="工厂名称，用于模型推送，默认为None")
    
    args = parser.parse_args()
    
    # 设置日志
    if not os.path.exists(args.result_dir):
        os.makedirs(args.result_dir)
    setup_logging(args.result_dir)
    logger = logging.getLogger("IsotonicRegression")
    
    try:
        # 解析参数
        job_params = json.loads(args.job_params)
        dataset_config = json.loads(args.dataset)
        fit_params = json.loads(args.fit_params) if args.fit_params else {}
        
        logger.info(f"开始等渗回归模型训练，参数: {job_params}")
        logger.info(f"数据集配置: {dataset_config}")
        
        # 加载数据
        train_data_path = dataset_config.get("train_data_path")
        test_data_path = dataset_config.get("test_data_path")
        features = dataset_config.get("features", [])
        target = dataset_config.get("target")
        
        if not train_data_path or not target or not features:
            logger.error("缺少必要的数据集配置参数")
            sys.exit(1)
        
        # 读取训练数据
        logger.info(f"读取训练数据: {train_data_path}")
        train_data = pd.read_csv(train_data_path)
        X_train = train_data[features].values
        y_train = train_data[target].values
        
        # 读取测试数据（如果有）
        X_test = None
        y_test = None
        if test_data_path:
            logger.info(f"读取测试数据: {test_data_path}")
            test_data = pd.read_csv(test_data_path)
            X_test = test_data[features].values
            y_test = test_data[target].values
        
        # 检查是否启用交叉验证
        cv_enable = fit_params.get("cv_enable", False)
        
        # 模型训练
        if cv_enable:
            logger.info("使用交叉验证进行模型训练")
            result, model_path = cross_validation_train(
                X_train, y_train, 
                job_params, 
                args.model_name, 
                args.result_dir,
                fit_params.get("cv_params", {})
            )
        else:
            logger.info("使用标准方式进行模型训练")
            result, model_path = isotonic_regressor(
                X_train, y_train, 
                X_test, y_test,
                job_params.get("isotonic_regression_params", {}), 
                args.model_name, 
                args.result_dir
            )
        
        # 上传模型
        logger.info("上传模型到MinIO")
        upload_result, upload_code = model_upload(args.model_name, model_path)
        if upload_code != 0:
            logger.error(f"模型上传失败: {upload_result}")
            sys.exit(1)
        
        # 注册模型
        logger.info("注册模型到模型工厂")
        register_result, register_code = model_register(
            args.model_name, 
            args.user_id, 
            upload_result["source_path"]
        )
        if register_code != 0:
            logger.error(f"模型注册失败: {register_result}")
            sys.exit(1)
        
        # 推送模型（如果指定了工厂名称）
        model_id = register_result["result"]["model_id"]
        model_version_id = register_result["result"]["model_version_id"]
        
        if args.factory_name != "None":
            logger.info(f"推送模型到工厂: {args.factory_name}")
            push_result, push_code = model_push(model_id, model_version_id, args.factory_name)
            if push_code != 0:
                logger.error(f"模型推送失败: {push_result}")
                sys.exit(1)
        
        # 结果汇总
        final_result = {
            "model_path": model_path,
            "model_id": model_id,
            "model_version_id": model_version_id,
            "metrics": result["metrics"] if isinstance(result, dict) and "metrics" in result else result,
            "upload_result": upload_result,
            "register_result": register_result
        }
        
        # 保存结果到JSON文件
        result_file = os.path.join(args.result_dir, f"{args.model_name}_result.json")
        with open(result_file, "w") as f:
            json.dump(convert_to_serializable(final_result), f, indent=2)
        
        logger.info(f"等渗回归模型训练和部署完成，结果保存在: {result_file}")
        logger.info(f"模型性能指标: {convert_to_serializable(result['metrics'] if isinstance(result, dict) and 'metrics' in result else result)}")
        
    except Exception as e:
        logger.error(f"程序执行过程中发生错误: {str(e)}")
        logger.exception("详细错误信息:")
        sys.exit(1)

if __name__ == "__main__":
    # 设置命令行参数解析
    parser = argparse.ArgumentParser(
        description="等渗回归模型训练和部署",
        epilog="示例: python isotonic_regression_local.py --job_params '{\"isotonic_regression_params\":{\"y_min\":0,\"y_max\":1,\"out_of_bounds\":\"clip\"}}' --dataset '{\"train_data_path\":\"data/train.csv\",\"test_data_path\":\"data/test.csv\",\"features\":[\"x1\",\"x2\"],\"target\":\"y\"}'",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # 必需参数
    parser.add_argument("--job_params", type=str, required=True, 
                        help="模型参数的JSON字符串，包含isotonic_regression_params")
    parser.add_argument("--dataset", type=str, required=True,
                        help="数据集配置的JSON字符串，包含train_data_path, test_data_path, features, target")
    
    # 可选参数
    parser.add_argument("--result_dir", type=str, default="./result",
                        help="结果保存目录，默认为./result")
    parser.add_argument("--model_name", type=str, default="isotonic_regression_model",
                        help="模型名称，默认为isotonic_regression_model")
    parser.add_argument("--fit_params", type=str, default="{}",
                        help="拟合参数的JSON字符串，包含cv_enable等")
    parser.add_argument("--user_id", type=str, default="admin",
                        help="用户ID，默认为admin")
    parser.add_argument("--factory_name", type=str, default="None",
                        help="工厂名称，用于模型推送，默认为None")
    
    args = parser.parse_args()
    
    # 设置日志
    if not os.path.exists(args.result_dir):
        os.makedirs(args.result_dir)
    setup_logging(args.result_dir)
    logger = logging.getLogger("IsotonicRegression")
    
    try:
        # 解析参数
        job_params = json.loads(args.job_params)
        dataset_config = json.loads(args.dataset)
        fit_params = json.loads(args.fit_params) if args.fit_params else {}
        
        logger.info(f"开始等渗回归模型训练，参数: {job_params}")
        logger.info(f"数据集配置: {dataset_config}")
        
        # 加载数据
        train_data_path = dataset_config.get("train_data_path")
        test_data_path = dataset_config.get("test_data_path")
        features = dataset_config.get("features", [])
        target = dataset_config.get("target")
        
        if not train_data_path or not target or not features:
            logger.error("缺少必要的数据集配置参数")
            sys.exit(1)
        
        # 读取训练数据
        logger.info(f"读取训练数据: {train_data_path}")
        train_data = pd.read_csv(train_data_path)
        X_train = train_data[features].values
        y_train = train_data[target].values
        
        # 读取测试数据（如果有）
        X_test = None
        y_test = None
        if test_data_path:
            logger.info(f"读取测试数据: {test_data_path}")
            test_data = pd.read_csv(test_data_path)
            X_test = test_data[features].values
            y_test = test_data[target].values
        
        # 检查是否启用交叉验证
        cv_enable = fit_params.get("cv_enable", False)
        
        # 模型训练
        if cv_enable:
            logger.info("使用交叉验证进行模型训练")
            result, model_path = cross_validation_train(
                X_train, y_train, 
                job_params, 
                args.model_name, 
                args.result_dir,
                fit_params.get("cv_params", {})
            )
        else:
            logger.info("使用标准方式进行模型训练")
            result, model_path = isotonic_regressor(
                X_train, y_train, 
                X_test, y_test,
                job_params.get("isotonic_regression_params", {}), 
                args.model_name, 
                args.result_dir
            )
        
        # 上传模型
        logger.info("上传模型到MinIO")
        upload_result, upload_code = model_upload(args.model_name, model_path)
        if upload_code != 0:
            logger.error(f"模型上传失败: {upload_result}")
            sys.exit(1)
        
        # 注册模型
        logger.info("注册模型到模型工厂")
        register_result, register_code = model_register(
            args.model_name, 
            args.user_id, 
            upload_result["source_path"]
        )
        if register_code != 0:
            logger.error(f"模型注册失败: {register_result}")
            sys.exit(1)
        
        # 推送模型（如果指定了工厂名称）
        model_id = register_result["result"]["model_id"]
        model_version_id = register_result["result"]["model_version_id"]
        
        if args.factory_name != "None":
            logger.info(f"推送模型到工厂: {args.factory_name}")
            push_result, push_code = model_push(model_id, model_version_id, args.factory_name)
            if push_code != 0:
                logger.error(f"模型推送失败: {push_result}")
                sys.exit(1)
        
        # 结果汇总
        final_result = {
            "model_path": model_path,
            "model_id": model_id,
            "model_version_id": model_version_id,
            "metrics": result["metrics"] if isinstance(result, dict) and "metrics" in result else result,
            "upload_result": upload_result,
            "register_result": register_result
        }
        
        # 保存结果到JSON文件
        result_file = os.path.join(args.result_dir, f"{args.model_name}_result.json")
        with open(result_file, "w") as f:
            json.dump(convert_to_serializable(final_result), f, indent=2)
        
        logger.info(f"等渗回归模型训练和部署完成，结果保存在: {result_file}")
        logger.info(f"模型性能指标: {convert_to_serializable(result['metrics'] if isinstance(result, dict) and 'metrics' in result else result)}")
        
    except Exception as e:
        logger.error(f"程序执行过程中发生错误: {str(e)}")
        logger.exception("详细错误信息:")
        sys.exit(1)
   
  
