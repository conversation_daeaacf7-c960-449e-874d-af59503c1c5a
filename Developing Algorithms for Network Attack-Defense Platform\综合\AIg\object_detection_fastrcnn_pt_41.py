"""对象检测 (Fast-RCNN)"""

import torch
import torchvision
from torchvision.models.detection import fasterrcnn_resnet50_fpn
from torchvision.transforms import functional as F
from PIL import Image
import os

def object_detection_fastrcnn(image_dir, model_path=None):
    """
    参数说明：
    - image_dir: 图像目录
    - model_path: 预训练模型路径（可选）
    """
    model = fasterrcnn_resnet50_fpn(pretrained=True)
    if model_path:
        model.load_state_dict(torch.load(model_path))
    model.eval()

    images = [Image.open(os.path.join(image_dir, img)) for img in os.listdir(image_dir)]
    for image in images:
        image_tensor = F.to_tensor(image).unsqueeze(0)
        with torch.no_grad():
            predictions = model(image_tensor)
        print(predictions)

if __name__ == "__main__":
    image_dir = 'data/images'
    model_path = 'models/fastrcnn.pth'

    object_detection_fastrcnn(image_dir, model_path)
