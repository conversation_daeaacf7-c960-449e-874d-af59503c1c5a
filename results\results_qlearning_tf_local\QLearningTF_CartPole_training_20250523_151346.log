2025-05-23 15:13:46,669 - QLearningTF_CartPole - INFO - Logging to file: ./results/qlearning_tf_local_run\QLearningTF_CartPole_training_20250523_151346.log
2025-05-23 15:13:46,669 - QLearningTF_CartPole - INFO - Q-Learning TF Local Training (QLearningTF_CartPole) starting.
2025-05-23 15:13:46,669 - QLearningTF_CartPole - INFO - Params:
{
  "env_name": "CartPole-v1",
  "result_dir": "./results/qlearning_tf_local_run",
  "model_name": "QLearningTF_CartPole",
  "hidden_size": 64,
  "learning_rate": 0.001,
  "gamma": 0.99,
  "epsilon_start": 1.0,
  "epsilon_end": 0.01,
  "epsilon_decay": 0.995,
  "buffer_size": 10000,
  "batch_size": 64,
  "num_episodes": 3,
  "max_steps_per_episode": 200,
  "random_seed": null,
  "save_freq": 50,
  "plot_freq": 20,
  "target_update_freq": 10,
  "tf_log_level": "ERROR",
  "logger": "<Logger QLearningTF_CartPole (INFO)>"
}
2025-05-23 15:13:46,693 - QLearningTF_CartPole - INFO - Env: CartPole-v1, State Dim=4, Action Dim=2
2025-05-23 15:13:46,758 - QLearningTF_CartPole - INFO - Ep 1/3 | Score: 13.00 | AvgScore(100): 13.00 | Epsilon: 0.995 | Steps: 13 | Time: 0.00s
2025-05-23 15:13:46,798 - QLearningTF_CartPole - INFO - Ep 2/3 | Score: 32.00 | AvgScore(100): 22.50 | Epsilon: 0.990 | Steps: 32 | Time: 0.04s
2025-05-23 15:13:47,872 - QLearningTF_CartPole - INFO - Ep 3/3 | Score: 21.00 | AvgScore(100): 22.00 | Epsilon: 0.985 | Steps: 21 | Time: 1.07s
2025-05-23 15:13:49,241 - QLearningTF_CartPole - INFO - Training done. Final agent saved with prefix: ./results/qlearning_tf_local_run\models_tf_local\QLearningTF_CartPole_final_agent_ep3. Total time: 2.50s
