"""循环神经网络 (RNN)"""

import pickle
import requests
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import numpy as np
import pandas as pd
import tensorflow as tf
from tensorflow import keras
from sklearn.preprocessing import StandardScaler, LabelEncoder, OrdinalEncoder
from sklearn.model_selection import train_test_split

MINIO_URL = "minio-prophecis.prophecis:9000"

MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class RNN(keras.Model):
    """循环神经网络 (TensorFlow implementation)"""
    def __init__(self, input_size, hidden_size, num_layers, num_classes):
        super(RNN, self).__init__()
        
        # Convert parameters to integers and validate
        self.input_size = int(input_size)
        self.hidden_size = int(hidden_size)
        self.num_layers = int(num_layers)
        self.num_classes = int(num_classes)
        
        # Validate parameters
        if self.input_size <= 0 or self.hidden_size <= 0 or self.num_layers <= 0 or self.num_classes <= 0:
            raise ValueError("All parameters must be positive integers")
        
        # Create RNN layers
        self.rnn_layers = []
        for i in range(self.num_layers):
            # For the first layer, specify input shape
            if i == 0:
                self.rnn_layers.append(
                    keras.layers.SimpleRNN(
                        units=self.hidden_size,
                        return_sequences=(self.num_layers > 1)
                    )
                )
            else:
                # For subsequent layers, keep returning sequences if not the last layer
                self.rnn_layers.append(
                    keras.layers.SimpleRNN(
                        units=self.hidden_size,
                        return_sequences=(i < self.num_layers - 1)
                    )
                )
        
        # Output layer
        self.fc = keras.layers.Dense(self.num_classes, activation='softmax')
    
    def build(self, input_shape):
        # Recreate the layers during build to ensure proper initialization
        self.rnn_layers = []
        for i in range(self.num_layers):
            if i == 0:
                self.rnn_layers.append(
                    keras.layers.SimpleRNN(
                        units=self.hidden_size,
                        return_sequences=(self.num_layers > 1)
                    )
                )
            else:
                self.rnn_layers.append(
                    keras.layers.SimpleRNN(
                        units=self.hidden_size,
                        return_sequences=(i < self.num_layers - 1)
                    )
                )
        
        # Output layer
        self.fc = keras.layers.Dense(self.num_classes, activation='softmax')
        
        super(RNN, self).build(input_shape)

    def call(self, x):
        # Ensure input has correct dimensionality
        if len(x.shape) == 2:
            x = tf.expand_dims(x, axis=1)  # Add sequence dimension
        
        # Process through RNN layers
        for rnn_layer in self.rnn_layers:
            x = rnn_layer(x)
        
        # Use the final output for classification
        out = self.fc(x)
        return out

def validate_params(job_params):
    required_params = ["input_size", "hidden_size", "output_size", "num_layers", 
                      "num_epochs", "learning_rate", "batch_size"]
    
    # Check if all required parameters exist
    for param in required_params:
        if param not in job_params:
            raise ValueError(f"Missing required parameter: {param}")
    
    # Convert and validate numeric parameters
    try:
        job_params["input_size"] = int(job_params["input_size"])
        job_params["hidden_size"] = int(job_params["hidden_size"])
        job_params["output_size"] = int(job_params["output_size"])
        job_params["num_layers"] = int(job_params["num_layers"])
        job_params["num_epochs"] = int(job_params["num_epochs"])
        job_params["batch_size"] = int(job_params["batch_size"])
        job_params["learning_rate"] = float(job_params["learning_rate"])
    except (ValueError, TypeError) as e:
        raise ValueError(f"Invalid parameter value: {str(e)}")
    
    # Validate parameter values
    if job_params["input_size"] <= 0:
        raise ValueError("input_size must be positive")
    if job_params["hidden_size"] <= 0:
        raise ValueError("hidden_size must be positive")
    if job_params["output_size"] <= 0:
        raise ValueError("output_size must be positive")
    if job_params["num_layers"] <= 0:
        raise ValueError("num_layers must be positive")
    if job_params["num_epochs"] <= 0:
        raise ValueError("num_epochs must be positive")
    if job_params["batch_size"] <= 0:
        raise ValueError("batch_size must be positive")
    if job_params["learning_rate"] <= 0:
        raise ValueError("learning_rate must be positive")
    
    return job_params

def load_data_preprocess(pkl_file):
    # 从pkl文件中加载数据
    with open(pkl_file, 'rb') as file:
        data = pickle.load(file)
    
    # 清理列名，去除可能的空格
    data.columns = data.columns.str.strip()

    print(type(data))  # 检查数据类型
    print(data.head())  # 查看前几行数据
    print(data.info())  # 查看数据信息

    # 假设数据是一个DataFrame格式，或者是特征和标签分别存储的形式
    if isinstance(data, pd.DataFrame):
        print(data.columns)
        X = data.drop(['Label'], axis=1)  # 假设 'Label' 是标签列
        y = data['Label']
    elif isinstance(data, dict):  # 如果是字典形式
        X = data['features']  # 假设特征存储在 'features' 键下
        y = data['labels']    # 假设标签存储在 'labels' 键下
    
    return X, y

def preprocess_data(X, y):
    # 1. 处理缺失值（删除缺失值）
    if X.isnull().any().any() or y.isnull().any():
        # 删除包含缺失值的行
        X, y = X.align(y, join='inner', axis=0)
        X = X.dropna()
        y = y[X.index]  # 保证 y 和 X 同步

    # 2. 标签编码
    le = LabelEncoder()
    y = le.fit_transform(y)

    # 3. 分类特征的编码（如果存在分类特征）
    categorical_cols = X.select_dtypes(include=['object']).columns
    encoder = OrdinalEncoder()
    if not categorical_cols.empty:
        X[categorical_cols] = encoder.fit_transform(X[categorical_cols])
    
    # 4. 检查并处理无限值
    X.replace([np.inf, -np.inf], np.nan, inplace=True)  # 替换为 NaN
    X.fillna(X.mean(), inplace=True)  # 用均值填充 NaN
    
    # 5. 数据标准化
    scaler = StandardScaler()
    X = scaler.fit_transform(X)

    # 6. 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    return X_train, X_test, y_train, y_test

def rnn_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """
    参数说明：
    input_size: 输入特征数。
    hidden_size: 隐藏层特征数。
    output_size: 类别数量。
    num_layers: 隐藏层数量。
    num_epochs: 训练的轮数。
    learning_rate: 学习率。
    batch_size: 批处理大小。
    """
    try:
        # Validate and convert parameters
        job_params = validate_params(job_params)
        
        print("Model parameters:")
        print(f"Input size: {job_params['input_size']}")
        print(f"Hidden size: {job_params['hidden_size']}")
        print(f"Output size: {job_params['output_size']}")
        print(f"Number of layers: {job_params['num_layers']}")
        
        # 准备数据集
        training_data_path = "/workspace/" + dataset["training_data_path"]
        X, y = load_data_preprocess(training_data_path)
        
        # Verify input size matches data
        if X.shape[1] != job_params["input_size"]:
            raise ValueError(f"Input size mismatch. Expected {job_params['input_size']}, got {X.shape[1]}")
        
        # 数据预处理
        train_x, test_x, train_y, test_y = preprocess_data(X, y)

        # Reshape data for RNN (add sequence dimension)
        train_x = train_x.reshape(train_x.shape[0], 1, train_x.shape[1])
        test_x = test_x.reshape(test_x.shape[0], 1, test_x.shape[1])

        # Create TensorFlow datasets
        train_dataset = tf.data.Dataset.from_tensor_slices(
            (train_x, train_y)
        ).batch(job_params["batch_size"])

        test_dataset = tf.data.Dataset.from_tensor_slices(
            (test_x, test_y)
        ).batch(job_params["batch_size"])

        # 初始化RNN模型
        model = RNN(
            input_size=job_params["input_size"],
            hidden_size=job_params["hidden_size"],
            num_layers=job_params["num_layers"],
            num_classes=job_params["output_size"]
        )

        # Compile the model
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=job_params["learning_rate"]),
            loss=keras.losses.SparseCategoricalCrossentropy(),
            metrics=['accuracy']
        )

        # 训练模型
        history = model.fit(
            train_dataset, 
            epochs=job_params["num_epochs"], 
            validation_data=test_dataset,
            verbose=1
        )

        # 测试模型
        test_loss, test_accuracy = model.evaluate(test_dataset, verbose=0)
        print(f'Test Accuracy: {test_accuracy * 100:.2f}%')

        # 保存模型
        model_path = os.path.join(result_dir, f"{model_name}.h5")
        model.save_weights(model_path)
        print(f'RNN训练完成，模型保存到 {model_path}')

        # 保存模型结构的配置
        model_config = {
            "input_size": job_params["input_size"],
            "hidden_size": job_params["hidden_size"],
            "num_layers": job_params["num_layers"],
            "num_classes": job_params["output_size"]
        }
        config_path = os.path.join(result_dir, f"{model_name}_config.json")
        with open(config_path, 'w') as f:
            json.dump(model_config, f)
        print(f'模型配置保存到 {config_path}')

        return None, 0
        
    except Exception as e:
        print(f"Error in RNN training: {str(e)}")
        return str(e), -1

# 模型加载
def load_rnn_model(model_path, config_path):
    # 读取模型配置
    with open(config_path, 'r') as f:
        model_config = json.load(f)
    
    # 重建模型
    model = RNN(
        input_size=model_config["input_size"],
        hidden_size=model_config["hidden_size"],
        num_layers=model_config["num_layers"],
        num_classes=model_config["num_classes"]
    )
    
    # 编译模型
    model.compile(
        optimizer=keras.optimizers.Adam(learning_rate=0.001),  # 使用默认学习率
        loss=keras.losses.SparseCategoricalCrossentropy(),
        metrics=['accuracy']
    )
    
    # 加载权重
    model.load_weights(model_path)
    
    return model


def model_upload(result_dir, model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".h5"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, os.path.join(result_dir, f"{model_name}.h5"))
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "TENSORFLOW",
          "file_name": model_name + ".h5",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "RNN",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='tensorflow RNN Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='RNN Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='RNN DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,
                    default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost', nargs='?', const=None, dest='nodehost',
                    type=str, default="**************",
                    help='nodehost params')     

    print("Start RNN training job, params:\n" + str(sys.argv) + "\n")
    args = parser.parse_args()
    
    job_params = args.job_params
    print("RNN job params:" + str(job_params) + "\n")
    
    dataset = args.dataset
    print("RNN dataSet:" + str(dataset) + "\n")
    
    model = args.model
    print(model)
    
    result_dir = args.result_dir
    print("RNN result dir:" + result_dir + "\n")    
    
    factory_name = args.factory_name
    print("RNN factory name:" + factory_name + "\n")    
    
    fit_params = args.fit_params
    print("RNN fit params:" + str(fit_params) + "\n")
    
    sparkconf = json.loads(args.sparkconf)
    print("RNN sparkconf params:" + str(sparkconf) + "\n")
    
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    
    if fit_params is None:
        fit_params = {}
     
    print("Step 1 RNN training:\n")
    result, ret_code = rnn_train(dataset, job_params, model["model_name"], result_dir, fit_params)
    if ret_code != 0:
        print("RNN train err, stop job....\n")
        print("Error Msg:" + result + "\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    print("Step 2 Model Upload to MinIO:\n")
    result, ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model register...\n")
    
    print("Step 3 Model Register:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result, ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register err, stop job....,err msg: " + result)
        sys.exit(-1)
    print("Register model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result, ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: " + result + "\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()
