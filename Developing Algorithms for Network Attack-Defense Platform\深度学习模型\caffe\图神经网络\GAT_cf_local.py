import os
import sys
import numpy as np
import caffe
from caffe import layers as L
from caffe import params as P
import pickle
import json
from minio import Minio
import uuid
import requests
import argparse

MINIO_URL = "minio-prophecis.prophecis:9000"

MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"


MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

# Custom Python Layer for Graph Attention
class GraphAttentionLayer(caffe.Layer):
    def setup(self, bottom, top):
        # Parse layer parameters
        layer_params = eval(self.param_str)
        self.in_features = layer_params['in_features']
        self.out_features = layer_params['out_features']
        self.dropout = layer_params.get('dropout', 0.6)
        self.alpha = layer_params.get('alpha', 0.2)
        self.concat = layer_params.get('concat', True)
        
        # Initialize weights
        self.W = np.random.uniform(-np.sqrt(1./self.in_features), 
                                 np.sqrt(1./self.in_features), 
                                 (self.in_features, self.out_features))
        self.a = np.random.uniform(-np.sqrt(1./self.out_features),
                                 np.sqrt(1./self.out_features),
                                 (2*self.out_features, 1))
        
        # Check bottom and top shapes
        if len(bottom) != 2:
            raise Exception("Layer needs two inputs: features and adjacency matrix")

    def reshape(self, bottom, top):
        # Reshape tops based on bottoms
        features_shape = bottom[0].data.shape
        batch_size = features_shape[0]
        n_nodes = features_shape[1]
        
        # Output shape
        top[0].reshape(batch_size, n_nodes, self.out_features)

    def forward(self, bottom, top):
        features = bottom[0].data
        adj = bottom[1].data
        
        # Linear transformation
        Wh = np.dot(features, self.W)
        
        # Attention mechanism
        # Prepare input for attention
        Wh1 = np.dot(Wh, self.a[:self.out_features])
        Wh2 = np.dot(Wh, self.a[self.out_features:])
        
        # Broadcast attention coefficients
        e = Wh1 + Wh2.T
        
        # LeakyReLU
        e = np.where(e > 0, e, self.alpha * e)
        
        # Mask attention coefficients using adjacency matrix
        zero_vec = -9e15 * np.ones_like(e)
        attention = np.where(adj > 0, e, zero_vec)
        
        # Softmax
        attention = np.exp(attention)
        attention = attention / (np.sum(attention, axis=1, keepdims=True) + 1e-10)
        
        # Apply dropout
        if self.phase == caffe.TRAIN:
            attention = np.where(np.random.uniform(0, 1, attention.shape) > self.dropout,
                               attention, 0)
            attention = attention / (np.sum(attention, axis=1, keepdims=True) + 1e-10)
        
        # Compute output
        h_prime = np.dot(attention, Wh)
        
        if self.concat:
            # ELU activation
            h_prime = np.where(h_prime > 0, h_prime,
                             self.alpha * (np.exp(h_prime) - 1))
        
        top[0].data[...] = h_prime

    def backward(self, top, propagate_down, bottom):
        # Implement backward pass (gradient computation)
        # Note: This is a simplified version
        if propagate_down[0]:
            bottom[0].diff[...] = top[0].diff
        if propagate_down[1]:
            bottom[1].diff[...] = top[0].diff

def create_gat_model(model_name, nfeat, nhid, nclass, dropout=0.6, alpha=0.2, nheads=8):
    # Create network definition
    n = caffe.NetSpec()
    
    # Input layers
    n.features = L.Input(shape=[dict(dim=[1, nfeat])])
    n.adj_matrix = L.Input(shape=[dict(dim=[1, 1])])
    
    # Multiple attention heads
    attention_outputs = []
    for i in range(nheads):
        attention_params = {
            'in_features': nfeat,
            'out_features': nhid,
            'dropout': dropout,
            'alpha': alpha,
            'concat': True
        }
        attention_layer = L.Python(n.features, n.adj_matrix,
                                 module='graph_attention_layer',
                                 layer='GraphAttentionLayer',
                                 param_str=str(attention_params))
        attention_outputs.append(attention_layer)
    
    # Concatenate attention heads
    if len(attention_outputs) > 1:
        n.concat = L.Concat(*attention_outputs, axis=1)
    else:
        n.concat = attention_outputs[0]
    
    # Output attention layer
    out_attention_params = {
        'in_features': nhid * nheads,
        'out_features': nclass,
        'dropout': dropout,
        'alpha': alpha,
        'concat': False
    }
    n.out_attention = L.Python(n.concat, n.adj_matrix,
                              module='graph_attention_layer',
                              layer='GraphAttentionLayer',
                              param_str=str(out_attention_params))
    
    # Final log softmax
    n.prob = L.Softmax(n.out_attention)
    
    return n.to_proto()

def gat_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """GAT training function for Caffe implementation"""
    
    # Load training data
    training_data_path = "/workspace/" + dataset["training_data_path"]
    with open(training_data_path, 'rb') as f:
        training_data, validation_data, test_data = pickle.load(f, encoding='bytes')
    train_x, train_adj, train_y = training_data
    
    # Create model architecture
    model_def = create_gat_model(
        model_name=model_name,
        nfeat=job_params["nfeat"],
        nhid=job_params["nhid"],
        nclass=job_params["nclass"],
        dropout=job_params["dropout"],
        alpha=job_params["alpha"],
        nheads=job_params["nheads"]
    )
    
    # Save model architecture
    model_def_path = os.path.join(result_dir, f"{model_name}.prototxt")
    with open(model_def_path, 'w') as f:
        f.write(str(model_def))
    
    # Initialize solver
    solver_param = {
        'base_lr': job_params["learning_rate"],
        'max_iter': job_params["num_epochs"],
        'solver_type': 'ADAM',
        'momentum': 0.9,
        'weight_decay': 0.0005,
        'snapshot': 1000,
        'snapshot_prefix': os.path.join(result_dir, model_name),
        'net': model_def_path,
    }
    
    solver_path = os.path.join(result_dir, f"{model_name}_solver.prototxt")
    with open(solver_path, 'w') as f:
        f.write(str(solver_param))
    
    # Create solver
    solver = caffe.SGDSolver(solver_path)
    
    # Training loop
    for i in range(job_params["num_epochs"]):
        solver.step(1)
    
    # Save final model
    model_path = os.path.join(result_dir, f"{model_name}.caffemodel")
    solver.net.save(model_path)
    
    return None, 0

def model_upload(result_dir,model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".pickle"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".pickle")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "pytorch",
          "file_name": model_name+".pickle",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "Logistic_Regression",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers


if __name__ == "__main__":
    # Keep the same command line interface
    parser = argparse.ArgumentParser(description='Caffe GAT Train.')
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='GAT Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='GAT DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start GAT training job, params :\n"+  str(sys.argv) +"\n")
    
    args = parser.parse_args()
    job_params = args.job_params
    print("GAT job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("GAT dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("GAT result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("GAT factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("GAT fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("GAT sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    # Execute training pipeline
    print("Step 1 GAT training:\n")
    result, ret_code = gat_train(args.dataset, args.job_params, 
                                args.model["model_name"], args.result_dir, 
                                args.fit_params)
    
    if ret_code != 0:
        print("GAT train err, stop job....\n")
        print("Error Msg:" + result + "\n")
        sys.exit(-1)
        
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()