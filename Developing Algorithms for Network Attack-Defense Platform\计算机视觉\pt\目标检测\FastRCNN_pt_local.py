"""fastRCNN"""

import pickle
import requests
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
from torchvision import datasets, transforms
from torchvision import models
from PIL import Image
import xml.etree.ElementTree as ET
import pandas as pd
from torchvision.models.detection import fasterrcnn_resnet50_fpn
from torchvision.ops import RoIPool
from tqdm.auto import tqdm
import logging

MINIO_URL = "minio-prophecis.prophecis:9000"

MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"


MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"


class FastRCNNForDetection(nn.Module):
    def __init__(self, num_classes, pretrained_model_path):
        super(FastRCNNForDetection, self).__init__()
        # 使用预训练的 ResNet50 作为基础网络
        self.base_network = models.resnet50(pretrained=False)
        
        # 加载本地的预训练模型参数
        self.base_network.load_state_dict(torch.load(pretrained_model_path))
        
        # 移除最后的全连接层
        self.features = nn.Sequential(*list(self.base_network.children())[:-2])
        
        # RoI池化层
        self.roi_pool = RoIPool(output_size=(7, 7), spatial_scale=1/16)
        
        # 分类器和边界框回归器
        self.classifier = nn.Sequential(
            nn.Linear(2048 * 7 * 7, 4096),
            nn.ReLU(inplace=True),
            nn.Dropout(),
            nn.Linear(4096, 4096),
            nn.ReLU(inplace=True),
            nn.Dropout()
        )
        self.cls_score = nn.Linear(4096, num_classes)
        self.bbox_pred = nn.Linear(4096, num_classes * 4)

    def forward(self, images, rois):
        if not isinstance(images, torch.Tensor):
            images = torch.stack(images)
        features = self.features(images)
        
        pooled_features = []
        for i, image_rois in enumerate(rois):
            # 确保 image_rois 是正确的形状
            if not isinstance(image_rois, torch.Tensor):
                image_rois = torch.tensor(image_rois, dtype=torch.float32)
            
            # 添加 batch 索引
            batch_index = torch.full((image_rois.size(0), 1), i, dtype=torch.float32, device=image_rois.device)
            image_rois = torch.cat([batch_index, image_rois], dim=1)
            
            pooled_features.append(self.roi_pool(features[i].unsqueeze(0), image_rois))
        
        pooled_features = torch.cat(pooled_features, dim=0)
        flattened_features = pooled_features.view(pooled_features.size(0), -1)
        fc_features = self.classifier(flattened_features)
        class_scores = self.cls_score(fc_features)
        bbox_preds = self.bbox_pred(fc_features)
        return class_scores, bbox_preds

class UniversalImageDataset(Dataset):
    def __init__(self, data_dir, transform=None, dataset_type='folder', annotations_file=None):
        self.data_dir = data_dir
        self.transform = transform
        self.dataset_type = dataset_type
        self.logger = logging.getLogger("FastRCNN")
        self.logger.info(f"初始化数据集: {dataset_type}, 目录: {data_dir}")

        self.classes = ['__background__', 'aeroplane', 'bicycle', 'bird', 'boat', 'bottle', 'bus', 'car', 
                        'cat', 'chair', 'cow', 'diningtable', 'dog', 'horse', 'motorbike', 
                        'person', 'pottedplant', 'sheep', 'sofa', 'train', 'tvmonitor']
        self.class_to_idx = {cls: idx for idx, cls in enumerate(self.classes)}

        try:
            if dataset_type in ['folder', 'imagenet']:
                self.image_paths, self.labels = self.load_from_folder()
            elif dataset_type == 'coco':
                self.image_paths, self.labels = self.load_coco(annotations_file)
            elif dataset_type == 'voc':
                self.image_paths, self.labels = self.load_voc(annotations_file)
            elif dataset_type == 'yolo':
                self.image_paths, self.labels = self.load_yolo()
            elif dataset_type == 'pickle':
                self.image_paths, self.labels = self.load_pickle(annotations_file)
            else:
                raise ValueError(f"不支持的数据集类型: {dataset_type}")
                
            self.logger.info(f"数据集加载完成，图像数: {len(self.image_paths)}")
        except Exception as e:
            self.logger.error(f"加载数据集失败: {str(e)}")
            self.logger.exception("详细错误信息:")
            raise

    def load_from_folder(self):
        classes = os.listdir(self.data_dir)
        class_to_idx = {cls: idx for idx, cls in enumerate(classes)}
        
        image_paths = []
        labels = []

        for cls in classes:
            class_dir = os.path.join(self.data_dir, cls)
            if os.path.isdir(class_dir):
                for img_file in os.listdir(class_dir):
                    if img_file.endswith(('.jpg', '.jpeg', '.png')):
                        img_path = os.path.join(class_dir, img_file)
                        image_paths.append(img_path)
                        labels.append(class_to_idx[cls])

        return image_paths, labels

    def load_coco(self, annotations_file):
        with open(annotations_file) as f:
            annotations = json.load(f)

        image_paths = []
        labels = []
        for item in annotations['images']:
            img_id = item['id']
            img_file = os.path.join(self.data_dir, item['file_name'])
            image_paths.append(img_file)
            label = self.get_label_for_image(img_id, annotations)
            labels.append(label)

        return image_paths, labels

    def load_voc(self, annotations_file):
        image_paths = []
        labels = []

        # 读取所有 XML 文件
        with open(annotations_file, 'r') as file:
            xml_files = file.readlines()

        for xml_file in xml_files:
            xml_file = xml_file.strip()
            tree = ET.parse(xml_file)
            root = tree.getroot()

            # 获取图像文件路径
            image_name = root.find('filename').text
            img_path = os.path.join(self.data_dir, image_name)
            image_paths.append(img_path)

            # 提取标签
            objects = root.findall('object')
            boxes = []
            for obj in objects:
                class_name = obj.find('name').text
                bbox = obj.find('bndbox')
                xmin = float(bbox.find('xmin').text)
                ymin = float(bbox.find('ymin').text)
                xmax = float(bbox.find('xmax').text)
                ymax = float(bbox.find('ymax').text)
                boxes.append((class_name, xmin, ymin, xmax, ymax))

            labels.append(boxes)

        return image_paths, labels

    def load_yolo(self):
        """
        加载YOLO格式的数据集
        
        返回:
        image_paths: 图像路径列表
        labels: 标签列表，每个标签为边界框列表
        """
        self.logger.info(f"加载YOLO格式数据集: {self.data_dir}")
        image_paths = []
        labels = []

        # 确保目录存在
        if not os.path.exists(self.data_dir):
            raise FileNotFoundError(f"数据目录不存在: {self.data_dir}")

        # 获取所有图像文件
        image_files = [f for f in os.listdir(self.data_dir) 
                      if f.lower().endswith(('.jpg', '.png', '.jpeg'))]
        self.logger.info(f"找到 {len(image_files)} 个图像文件")

        for img_file in image_files:
            img_path = os.path.join(self.data_dir, img_file)
            image_paths.append(img_path)

            # 加载对应的YOLO标签文件
            label_file = img_file.replace('.jpg', '.txt').replace('.png', '.txt').replace('.jpeg', '.txt')
            label_path = os.path.join(self.data_dir, label_file)

            if os.path.exists(label_path):
                with open(label_path, 'r') as f:
                    boxes = []
                    for line in f.readlines():
                        try:
                            parts = line.strip().split()
                            if len(parts) == 5:  # 确保格式正确
                                class_id, x_center, y_center, width, height = map(float, parts)
                                # 将YOLO格式转换为(class_name, x1, y1, x2, y2)格式
                                class_name = self.classes[int(class_id)] if int(class_id) < len(self.classes) else "unknown"
                                x1 = x_center - width/2
                                y1 = y_center - height/2
                                x2 = x_center + width/2
                                y2 = y_center + height/2
                                boxes.append((class_name, x1, y1, x2, y2))
                            else:
                                self.logger.warning(f"忽略格式不正确的行: {line} 在文件 {label_path}")
                        except Exception as e:
                            self.logger.warning(f"解析标签行时出错: {line}, 错误: {str(e)}")
                    labels.append(boxes)
            else:
                self.logger.warning(f"未找到图像 {img_path} 的标签文件")
                labels.append([])  # 无标签时返回空列表

        self.logger.info(f"成功加载 {len(image_paths)} 个图像和对应标签")
        return image_paths, labels
    
    def load_pickle(self, pkl_file):
        # 从 .pkl 文件加载数据
        with open(pkl_file, 'rb') as f:
            data = pickle.load(f)

        # 假设数据为字典格式，包含特征和标签
        if isinstance(data, dict):
            images = data['images']  # 假设图像数据在 'images' 键下
            labels = data['labels']    # 假设标签在 'labels' 键下
        elif isinstance(data, pd.DataFrame):
            images = data['image_paths'].tolist()  # 假设图像路径在某列
            labels = data['labels'].tolist()        # 假设标签在某列
        else:
            raise ValueError("Unsupported data format in pickle file.")

        return images, labels
    
    def __len__(self):
        if hasattr(self, 'dataset'):
            return len(self.dataset)
        return len(self.image_paths)

    def __getitem__(self, idx):
        """
        获取数据集中的一个样本
        
        参数:
        idx: 样本索引
        
        返回:
        image: 图像张量
        target: 目标字典，包含boxes和labels
        """
        try:
            img_path = self.image_paths[idx]
            if not os.path.exists(img_path):
                raise FileNotFoundError(f"图像文件不存在: {img_path}")
                
            image = Image.open(img_path).convert("RGB")
            label = self.labels[idx]

            if self.transform:
                image = self.transform(image)

            # 为 Fast R-CNN 准备标签格式
            boxes = []
            labels = []
            
            for box in label:
                try:
                    if len(box) >= 5:  # (class_name, x1, y1, x2, y2)
                        class_name = box[0]
                        if class_name in self.class_to_idx:
                            boxes.append(box[1:5])  # 提取坐标
                            labels.append(self.class_to_idx[class_name])
                        else:
                            self.logger.warning(f"跳过未知类别: {class_name}")
                except Exception as e:
                    self.logger.warning(f"处理边界框时出错: {box}, 错误: {str(e)}")
            
            # 确保boxes不为空，如果为空则使用一个伪边界框
            if not boxes:
                # 创建一个覆盖整个图像的背景边界框
                boxes = [[0.0, 0.0, 1.0, 1.0]]
                labels = [0]  # 背景类
                
            target = {
                'boxes': boxes,
                'labels': labels
            }

            return image, target
            
        except Exception as e:
            self.logger.error(f"获取样本 {idx} 时出错: {str(e)}")
            # 返回一个空的样本作为回退
            dummy_image = torch.zeros(3, 224, 224)
            dummy_target = {
                'boxes': [[0.0, 0.0, 1.0, 1.0]],
                'labels': [0]
            }
            return dummy_image, dummy_target

def prepare_data(data_dir, dataset_type, batch_size, image_size,  annotations_file=None):
    transform = transforms.Compose([
        transforms.Resize((image_size, image_size)),
        transforms.ToTensor(),
    ])

    dataset = UniversalImageDataset(data_dir, transform=transform, dataset_type=dataset_type, annotations_file=annotations_file)

    train_size = int(0.8 * len(dataset))
    test_size = len(dataset) - train_size
    train_dataset, test_dataset = torch.utils.data.random_split(dataset, [train_size, test_size])

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    return train_loader, test_loader

def collate_fn(batch):
    images = []
    targets = []
    for image, target in batch:
        images.append(image)
        # 确保边界框格式正确：[x1, y1, x2, y2]
        boxes = torch.tensor(target['boxes'], dtype=torch.float32)
        if boxes.size(1) == 4:  # 如果只有坐标，不需要修改
            pass
        elif boxes.size(1) == 5:  # 如果包含类别信息，去掉类别
            boxes = boxes[:, 1:]
        else:
            raise ValueError(f"Unexpected box format: {boxes.size()}")
        
        targets.append({
            'boxes': boxes,
            'labels': torch.tensor(target['labels'], dtype=torch.int64)
        })
    return images, targets

class FastRCNNLoss(nn.Module):
    def __init__(self):
        super(FastRCNNLoss, self).__init__()
        self.cls_loss = nn.CrossEntropyLoss()
        self.bbox_loss = nn.SmoothL1Loss()

    def forward(self, class_scores, bbox_preds, targets):
        # 收集所有标签
        cls_targets = []
        bbox_targets = []
        
        for target in targets:
            cls_targets.extend(target['labels'].tolist())
            bbox_targets.append(target['boxes'])
        
        cls_targets = torch.tensor(cls_targets, device=class_scores.device)
        bbox_targets = torch.cat(bbox_targets, dim=0)
        
        # 计算分类损失
        cls_loss = self.cls_loss(class_scores, cls_targets)
        
        # 计算边界框回归损失
        # 只对正样本（非背景）计算边界框损失
        positive_indices = cls_targets > 0
        if positive_indices.any():
            bbox_loss = self.bbox_loss(bbox_preds[positive_indices], bbox_targets[positive_indices])
        else:
            bbox_loss = torch.tensor(0.0, device=class_scores.device)
        
        total_loss = cls_loss + bbox_loss
        return total_loss

def train_model(train_loader, model, criterion, optimizer, num_epochs, device):
    model.to(device)
    
    for epoch in range(num_epochs):
        model.train()
        running_loss = 0.0
        
        pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs}")
        for images, targets in pbar:
            # 将图像和目标移到设备上
            images = [img.to(device) for img in images]
            targets = [{k: v.to(device) for k, v in t.items()} for t in targets]
            
            # 清零梯度
            optimizer.zero_grad()
            
            # 使用真实边界框作为 proposals
            proposals = [t['boxes'] for t in targets]
            
            try:
                # 前向传播
                class_scores, bbox_preds = model(images, proposals)
                
                # 计算损失
                loss = criterion(class_scores, bbox_preds, targets)
                
                # 反向传播
                loss.backward()
                
                # 更新参数
                optimizer.step()
                
                running_loss += loss.item()
                pbar.set_postfix({'loss': f'{loss.item():.4f}'})
                
            except RuntimeError as e:
                print(f"Error during training: {e}")
                continue
        
        epoch_loss = running_loss / len(train_loader)
        print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}')

def test_model(test_loader, model, device):
    model.to(device)
    model.eval()
    correct = 0
    total = 0

    with torch.no_grad():
        for images, targets in tqdm(test_loader, desc="Testing"):
            images = [img.to(device) for img in images]
            targets = [{k: v.to(device) for k, v in t.items()} for t in targets]

            proposals = [t['boxes'] for t in targets]
            
            class_scores, bbox_preds = model(torch.stack(images), proposals)
            
            _, predicted = torch.max(class_scores, 1)
            total += sum(len(t['labels']) for t in targets)
            correct += sum((predicted == t['labels']).sum().item() for t in targets)

    print(f'准确率: {100 * correct / total:.2f}%')

def fastRCNN_train(dataset, job_params, model_name, result_dir, fit_params=None, device = "cpu"):

    """
    参数说明：
    - input_size: 输入图片尺寸
    - dataset_type: 数据集格式类型,共支持imagenet、coco、voc、yolo、pickle等5种类型
    - output_size: 类别数
    - num_epochs: 训练轮数
    - learning_rate: 学习率
    - batch_size: 批处理大小
    """

    input_size = job_params["input_size"]
    dataset_type = job_params["dataset_type"]
    output_size = job_params["output_size"]
    num_epochs = job_params["num_epochs"]
    learning_rate = job_params["learning_rate"]
    batch_size = job_params["batch_size"]
    
    
    training_data_path = "/workspace/" + dataset["training_data_path"]
    train_loader, test_loader = prepare_data(training_data_path, dataset_type, batch_size, input_size)

    pretrained_model_path = "/workspace/pretrained_model/" + "resnet50-0676ba61.pth"
    
    # 初始化模型
    model = FastRCNNForDetection(output_size, pretrained_model_path)
    criterion = FastRCNNLoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)

    try:
        train_model(train_loader, model, criterion, optimizer, num_epochs, device)
        test_model(test_loader, model, device)
        
        # 保存模型
        model_file = open(result_dir + "/" + model_name + ".pth", "wb")
        torch.save(model.state_dict(), model_file)
        model_file.close()
        print(f'训练完成，模型保存到 {result_dir}/{model_name}.pth')
    except Exception as e:
        print(f"训练过程中发生错误: {e}")

    return None, 0
    
def model_upload(result_dir,model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".pickle"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".pickle")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "pytorch",
          "file_name": model_name+".pickle",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "fastRCNN",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

def flexible_data_load(file_path, transform=None, dataset_type='folder', annotations_file=None, img_size=224):
    """
    灵活加载不同格式的图像数据集
    
    参数:
    file_path: 数据文件或目录路径
    transform: 图像变换函数
    dataset_type: 数据集类型，支持'folder'、'imagenet'、'coco'、'voc'、'yolo'、'pickle'等
    annotations_file: 标注文件路径，用于coco、voc等格式
    img_size: 图像大小，用于默认transform
    
    返回:
    dataset: 加载的数据集对象
    """
    logger = logging.getLogger("FastRCNN")
    logger.info(f"加载数据，文件路径: {file_path}，类型: {dataset_type}")
    
    # 如果未提供transform，创建一个默认的
    if transform is None:
        transform = transforms.Compose([
            transforms.Resize((img_size, img_size)),
            transforms.ToTensor(),
        ])
        logger.info(f"使用默认图像变换，大小: {img_size}x{img_size}")
    
    try:
        # 确保文件路径存在
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"数据路径不存在: {file_path}")
        
        # 使用UniversalImageDataset加载不同格式的数据集
        dataset = UniversalImageDataset(
            data_dir=file_path,
            transform=transform,
            dataset_type=dataset_type,
            annotations_file=annotations_file
        )
        
        logger.info(f"成功加载数据集，样本数: {len(dataset)}")
        return dataset
        
    except Exception as e:
        logger.error(f"加载数据集失败: {str(e)}")
        logger.exception("详细错误信息:")
        raise

    
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='pytorch fastRCNN Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='fastRCNN Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='fastRCNN DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start fastRCNN training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("fastRCNN job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("fastRCNN dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("fastRCNN result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("fastRCNN factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("fastRCNN fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("fastRCNN sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print("Step 1 fastRCNN training:\n")
    result,ret_code = fastRCNN_train(dataset,job_params, model["model_name"],result_dir,fit_params,device)
    if ret_code != 0:
        print("fastRCNN train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()
   
  
