 # PVE+Hadoop+K8S 集群运维管理系统

这是一个完整的运维管理系统，用于管理基于PVE虚拟化平台的Hadoop和Kubernetes集群。

## 🎯 项目概述

**环境架构**:
- **PVE物理机**: 1台 Proxmox Virtual Environment 主机
- **Hadoop集群**: 6个节点（6个虚拟机）
- **Kubernetes集群**: 5个节点（5个虚拟机）
- **总计**: 11个虚拟机 + 1台物理机

**主要功能**:
- ✅ 自动化集群状态检查
- ✅ 一键重启集群服务
- ✅ 定时备份和恢复
- ✅ 实时监控和告警
- ✅ 故障排除指南
- ✅ 性能优化建议
- ✅ 安全配置管理

## 📁 项目结构

```
desin/
├── 运维手册.md                    # 完整运维手册
├── setup_scripts.sh              # 一键安装脚本
├── README.md                      # 本文件
└── scripts/                       # 运维脚本目录
    ├── check_cluster_status.sh    # 集群状态检查
    ├── restart_clusters.sh        # 集群重启脚本
    ├── backup_clusters.sh         # 备份脚本
    └── monitor_clusters.sh        # 监控脚本
```

## 🚀 快速开始

### 1. 环境要求

- ✅ PVE 7.0+ （已安装配置）
- ✅ Hadoop 3.x 集群（已部署）
- ✅ Kubernetes 1.20+ 集群（已部署）
- ✅ Linux系统（Ubuntu 20.04+ 或 CentOS 8+）
- ✅ SSH免密登录配置

### 2. 安装运维脚本

```bash
# 克隆或下载项目到PVE主机
cd /opt
git clone <repository> cluster-ops

# 进入项目目录
cd cluster-ops

# 运行安装脚本（需要root权限）
sudo ./setup_scripts.sh
```

### 3. 配置环境

```bash
# 编辑配置文件，修改节点信息
sudo vim /etc/cluster-ops/config

# 测试连接
cluster-status

# 启动监控服务
sudo systemctl start cluster-monitor
sudo systemctl enable cluster-monitor
```

## 🛠️ 运维命令

安装完成后，您可以使用以下命令：

### 集群状态检查
```bash
cluster-status                    # 检查所有集群状态
cluster-status --help            # 查看帮助信息
```

### 集群重启
```bash
cluster-restart                   # 重启所有服务
cluster-restart -p               # 仅重启PVE服务
cluster-restart -H               # 仅重启Hadoop集群
cluster-restart -k               # 仅重启K8S集群
cluster-restart -v 100           # 重启指定VM
```

### 备份管理
```bash
cluster-backup                    # 完整备份
cluster-backup -p                # 仅备份PVE配置
cluster-backup -H -k             # 备份Hadoop和K8S
cluster-backup -d /custom/backup  # 自定义备份目录
```

### 监控面板
```bash
cluster-monitor                   # 启动实时监控
cluster-monitor -o               # 一次性检查
cluster-monitor -r               # 生成监控报告
cluster-monitor -i 60            # 自定义监控间隔
```

## 📊 服务分类清单

### PVE物理机服务
- `pve-cluster` - PVE集群服务
- `pveproxy` - Web管理界面
- `pvedaemon` - PVE守护进程
- `pvestatd` - 统计数据收集

### Hadoop集群服务
- **NameNode**: `hadoop-hdfs-namenode`
- **DataNode**: `hadoop-hdfs-datanode`
- **ResourceManager**: `hadoop-yarn-resourcemanager`
- **NodeManager**: `hadoop-yarn-nodemanager`
- **ZooKeeper**: `zookeeper`

### Kubernetes集群服务
- **Master**: `kube-apiserver`, `kube-controller-manager`, `kube-scheduler`, `etcd`
- **Worker**: `kubelet`, `kube-proxy`
- **附加服务**: `coredns`, `calico/flannel`

## 📋 日常运维

### 每日检查清单
- [ ] 集群整体状态: `cluster-status`
- [ ] 资源使用情况: `cluster-monitor -o`
- [ ] 日志检查: `tail -f /var/log/cluster_*.log`
- [ ] 备份验证: 检查`/backup`目录

### 每周维护
- [ ] 系统更新检查
- [ ] 性能报告生成: `cluster-monitor -r`
- [ ] 磁盘清理
- [ ] 安全扫描

### 每月维护
- [ ] 备份策略验证
- [ ] 容量规划评估
- [ ] 文档更新
- [ ] 技能培训

## 🚨 故障处理

### 常见问题速查

**PVE Web界面无法访问**:
```bash
systemctl restart pveproxy
ss -tlnp | grep :8006
```

**Hadoop NameNode启动失败**:
```bash
tail -f /opt/hadoop/logs/hadoop-*-namenode-*.log
hdfs namenode -recover
```

**K8S API Server无法访问**:
```bash
systemctl status kube-apiserver
kubectl cluster-info
```

详细故障排除指南请参考 `运维手册.md` 第5章。

## 📈 性能优化

### 系统级优化
```bash
# CPU性能模式
echo performance > /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor

# 内存优化
sysctl vm.swappiness=1
sysctl vm.dirty_ratio=15

# 网络优化
sysctl net.core.rmem_max=67108864
sysctl net.ipv4.tcp_congestion_control=bbr
```

### 应用优化
- **Hadoop**: 调整YARN内存分配、HDFS副本数
- **K8S**: 优化Pod资源限制、调整etcd参数
- **PVE**: 启用大页内存、优化虚拟机配置

## 🔒 安全配置

### 基础安全
```bash
# SSH安全
vim /etc/ssh/sshd_config  # 禁用root登录，使用密钥认证

# 防火墙配置
ufw enable
ufw allow from ***********/24  # 仅允许内网访问
```

### 审计日志
```bash
# 启用系统审计
systemctl enable auditd

# 查看审计日志
ausearch -k identity
```

## 📞 支持与联系

### 紧急联系
- **主管理员**: [您的联系方式]
- **技术支持群**: [群组信息]
- **故障上报**: 严重故障立即电话通知

### 技术资源
- **官方文档**: 
  - [Proxmox VE](https://pve.proxmox.com/wiki/)
  - [Apache Hadoop](https://hadoop.apache.org/)
  - [Kubernetes](https://kubernetes.io/docs/)
- **社区支持**: 相关技术论坛和Stack Overflow

## 📚 文档说明

- **运维手册.md**: 完整的运维操作指南，包含详细的故障排除和优化建议
- **scripts/**: 所有自动化运维脚本，包含完整的错误处理和日志记录
- **setup_scripts.sh**: 一键安装脚本，自动配置环境和依赖

## 🔄 版本管理

### 当前版本
- **项目版本**: v1.0
- **Hadoop版本**: 3.3.x
- **Kubernetes版本**: 1.20+
- **PVE版本**: 7.x

### 升级计划
按照LTS版本策略，定期评估和升级各组件版本。

---

**维护者**: 运维团队  
**最后更新**: 2024年  
**许可证**: [根据需要添加]

> 💡 **提示**: 首次使用请仔细阅读 `运维手册.md`，并根据实际环境修改配置文件。