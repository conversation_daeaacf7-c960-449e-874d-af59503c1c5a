# BiLSTMModel.py
import torch
import torch.nn as nn
import numpy as np
from sklearn.preprocessing import StandardScaler

class BiLSTM(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers, num_classes):
        super(BiLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.bilstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True, bidirectional=True)
        self.fc = nn.Linear(hidden_size * 2, num_classes)

    def forward(self, x):
        if x.dim() == 2:
            x = x.unsqueeze(1)
        
        batch_size, seq_len, _ = x.size()
        h0 = torch.zeros(self.num_layers * 2, batch_size, self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers * 2, batch_size, self.hidden_size).to(x.device)
        
        out, _ = self.bilstm(x, (h0, c0))
        out = self.fc(out[:, -1, :])
        return out

class BiLSTMModel:
    def __init__(self):
        self.model = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.scaler = StandardScaler()
        self.ready = False
        
    def load(self):
        # 加载模型参数和配置
        input_size = 84  # 根据你的特征维度调整
        hidden_size = 128
        num_layers = 2
        num_classes = 6
        
        self.model = BiLSTM(input_size, hidden_size, num_layers, num_classes)
        self.model.load_state_dict(torch.load('model.pth', map_location=self.device))
        self.model.to(self.device)
        self.model.eval()
        self.ready = True
        
    def predict(self, X, features_names=None):
        if not self.ready:
            self.load()
            
        # 确保输入是numpy数组
        if isinstance(X, list):
            X = np.array(X)
            
        # 数据预处理
        X = self.scaler.fit_transform(X)
        X = torch.tensor(X, dtype=torch.float32).to(self.device)
        
        with torch.no_grad():
            outputs = self.model(X)
            _, predicted = torch.max(outputs, 1)
            
        return predicted.cpu().numpy()