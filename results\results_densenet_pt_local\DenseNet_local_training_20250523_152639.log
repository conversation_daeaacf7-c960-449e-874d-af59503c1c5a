2025-05-23 15:26:39,653 - DenseNet_local - INFO - Logging to file: ./results/results_densenet_pt_local\DenseNet_local_training_20250523_152639.log
2025-05-23 15:26:39,653 - DenseNet_local - INFO - DenseNet PyTorch Local Mode. Args: {'data_path': 'E:/data/Flowers Recognition/flowers', 'dataset_type': 'folder', 'annotations_file': None, 'pickle_image_key': 'images', 'pickle_label_key': 'labels', 'image_size': 224, 'model_name': 'DenseNet_local', 'densenet_variant': 'densenet121', 'num_classes': 5, 'pretrained_model_path': None, 'mode': 'train', 'num_epochs': 3, 'batch_size': 32, 'learning_rate': 0.001, 'lr_scheduler': None, 'lr_step_size': 7, 'lr_gamma': 0.1, 'lr_patience': 3, 'early_stopping_patience': None, 'use_cv': False, 'cv_folds': 5, 'trained_model_path': None, 'test_data_path': None, 'annotations_file_test': None, 'result_dir': './results/results_densenet_pt_local', 'num_workers': 0, 'pin_memory': False, 'random_seed': 42, 'force_cpu': False, 'log_level': 'INFO', 'train_val_split_ratio': 0.2}
2025-05-23 15:26:39,653 - DenseNet_local - INFO - Using device: cpu
2025-05-23 15:26:39,653 - DenseNet_local - INFO - Random seed set to: 42
2025-05-23 15:26:39,934 - DenseNet_local - INFO - --- Training Mode ---
2025-05-23 15:26:39,934 - DenseNet_local - INFO - Starting Standard Training...
2025-05-23 16:08:11,074 - DenseNet_local - INFO - Standard training finished. Model saved at: ./results/results_densenet_pt_local\DenseNet_local_final.pth
2025-05-23 16:08:11,077 - DenseNet_local - INFO - Full run summary saved to: ./results/results_densenet_pt_local\DenseNet_local_full_summary.json
