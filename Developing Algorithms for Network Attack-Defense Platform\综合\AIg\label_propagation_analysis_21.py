"""标签传播"""

import sys
import pandas as pd
import networkx as nx
from networkx.algorithms import label_propagation

def label_propagation_analysis(input_file, source_column, target_column):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - source_column: 源节点列名
    - target_column: 目标节点列名
    """
    data = pd.read_csv(input_file)
    
    G = nx.from_pandas_edgelist(data, source=source_column, target=target_column)
    
    communities = label_propagation.asyn_lpa_communities(G)
    community_dict = {node: idx for idx, community in enumerate(communities) for node in community}
    
    output_file = 'label_propagation_communities.csv'
    pd.DataFrame(list(community_dict.items()), columns=['Node', 'Community']).to_csv(output_file, index=False)
    print(f"Label propagation completed. Output saved to {output_file}")

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python label_propagation_analysis.py <input_file> <source_column> <target_column>")
        sys.exit(1)
    input_file, source_column, target_column = sys.argv[1], sys.argv[2], sys.argv[3]
    label_propagation_analysis(input_file, source_column, target_column)
