2025-05-21 15:11:00,070 - GCN_TF_Local - INFO - 日志将记录到控制台和文件: results\results_gcn_tf_local\gcn_tf_local_training_20250521_151100.log
2025-05-21 15:11:00,070 - GCN_TF_Local - INFO - GCN TensorFlow 本地模式训练脚本已初始化。
2025-05-21 15:11:00,070 - GCN_TF_Local - INFO - TensorFlow 版本: 2.13.0
2025-05-21 15:11:00,071 - GCN_TF_Local - INFO - 参数: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'result_dir': 'results\\results_gcn_tf_local', 'model_name': 'GCN_TF_Local_Model', 'label_column': 'Label', 'data_format': 'pkl', 'hidden_size': 64, 'num_layers': 2, 'dropout_rate': 0.5, 'num_epochs': 3, 'learning_rate': 0.005, 'optimizer': 'adam', 'loss_function': 'sparsecategoricalcrossentropy', 'edge_strategy': 'fully_connected', 'test_split_size': 0.2, 'random_state': 42, 'normalize': True, 'scaler_type': 'standard', 'handle_imbalance': False, 'stratify_split': True, 'numeric_impute_strategy': 'mean', 'categorical_impute_strategy': 'most_frequent', 'use_cv': False, 'cv_folds': 5, 'log_level': 'INFO', 'force_cpu': False}
2025-05-21 15:11:00,072 - GCN_TF_Local - INFO - 未找到GPU或强制使用CPU。
2025-05-21 15:11:00,073 - GCN_TF_Local - INFO - 从 E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl (格式: pkl) 加载数据
2025-05-21 15:11:00,452 - GCN_TF_Local - INFO - 数据加载为 DataFrame，形状: (692703, 85)
2025-05-21 15:11:00,574 - GCN_TF_Local - INFO - 特征形状: (692703, 84), 标签形状: (692703,)
2025-05-21 15:11:00,605 - GCN_TF_Local - INFO - 标签分布: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-21 15:11:00,642 - GCN_TF_Local - INFO - 选择标准训练/测试分割模式。
2025-05-21 15:11:01,284 - GCN_TF_Local - INFO - 已将特征中的无限值替换为NaN。
2025-05-21 15:11:01,375 - GCN_TF_Local - INFO - 处理特征中的缺失值。
2025-05-21 15:11:03,421 - GCN_TF_Local - INFO - 使用 'mean' 填充了数值型缺失值。
2025-05-21 15:11:03,919 - GCN_TF_Local - INFO - 使用 'most_frequent' 填充了类别型缺失值。
2025-05-21 15:11:04,027 - GCN_TF_Local - INFO - 标签已编码。6 个类别: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-21 15:11:04,114 - GCN_TF_Local - INFO - 编码类别特征: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-21 15:11:08,049 - GCN_TF_Local - INFO - 使用 standard 缩放器标准化特征。
2025-05-21 15:11:10,359 - GCN_TF_Local - INFO - 数据分割: X_train (554162, 84), y_train (554162,), X_test (138541, 84), y_test (138541,)
2025-05-21 15:11:10,412 - GCN_TF_Local - INFO - 训练/测试分割的预处理信息已保存: results\results_gcn_tf_local\GCN_TF_Local_Model_preprocessing_info_tf.json
2025-05-21 15:11:10,412 - GCN_TF_Local - INFO - 预处理后数据 (训练/测试分割): 输入大小=84, 类别数=6
2025-05-21 15:11:10,415 - GCN_TF_Local - CRITICAL - 发生意外严重错误: Unable to allocate 1.12 TiB for an array with shape (554162, 554162) and data type float32
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 991, in gcn_train_tf_local_mode
    train_graph_dict, test_graph_dict = prepare_tf_datasets_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 487, in prepare_tf_datasets_local
    adj_matrix_train_np = _build_adj_matrix_np(num_train_nodes, edge_strategy, edge_list_param)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 462, in _build_adj_matrix_np
    adj = np.zeros((num_nodes, num_nodes), dtype=np.float32)
numpy.core._exceptions._ArrayMemoryError: Unable to allocate 1.12 TiB for an array with shape (554162, 554162) and data type float32
2025-05-21 15:11:10,418 - GCN_TF_Local - CRITICAL - 主程序中发生严重错误: Unable to allocate 1.12 TiB for an array with shape (554162, 554162) and data type float32
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 1123, in <module>
    gcn_train_tf_local_mode(parsed_args)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 991, in gcn_train_tf_local_mode
    train_graph_dict, test_graph_dict = prepare_tf_datasets_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 487, in prepare_tf_datasets_local
    adj_matrix_train_np = _build_adj_matrix_np(num_train_nodes, edge_strategy, edge_list_param)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 462, in _build_adj_matrix_np
    adj = np.zeros((num_nodes, num_nodes), dtype=np.float32)
numpy.core._exceptions._ArrayMemoryError: Unable to allocate 1.12 TiB for an array with shape (554162, 554162) and data type float32
