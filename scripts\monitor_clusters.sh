#!/bin/bash

# =======================================================
# 集群监控脚本
# 功能: 实时监控PVE、Hadoop、K8S集群状态和性能
# 作者: 系统管理员
# 版本: 1.0
# =======================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# 监控配置
MONITOR_INTERVAL=30  # 监控间隔(秒)
ALERT_CPU_THRESHOLD=80  # CPU使用率告警阈值(%)
ALERT_MEM_THRESHOLD=85  # 内存使用率告警阈值(%)
ALERT_DISK_THRESHOLD=90  # 磁盘使用率告警阈值(%)
LOG_FILE="/var/log/cluster_monitor_$(date +%Y%m%d).log"

# 集群配置
HADOOP_NODES=("hadoop-node1" "hadoop-node2" "hadoop-node3" "hadoop-node4" "hadoop-node5" "hadoop-node6")
K8S_NODES=("k8s-master" "k8s-worker1" "k8s-worker2" "k8s-worker3" "k8s-worker4")
PVE_HOST="localhost"

# 邮件告警配置(可选)
ALERT_EMAIL=""
SMTP_SERVER=""

# 记录日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 发送告警
send_alert() {
    local message="$1"
    local severity="$2"
    
    log "ALERT [$severity]: $message"
    
    # 控制台告警
    case $severity in
        "CRITICAL")
            echo -e "${RED}${BOLD}🚨 严重告警: $message${NC}" >&2
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  警告: $message${NC}" >&2
            ;;
        "INFO")
            echo -e "${CYAN}ℹ️  信息: $message${NC}" >&2
            ;;
    esac
    
    # 邮件告警(如果配置了)
    if [[ -n "$ALERT_EMAIL" && -n "$SMTP_SERVER" ]]; then
        echo "$message" | mail -s "集群监控告警 [$severity]" "$ALERT_EMAIL"
    fi
}

# 清屏并显示标题
display_header() {
    clear
    echo -e "${BLUE}${BOLD}================================================${NC}"
    echo -e "${BLUE}${BOLD}        集群实时监控面板${NC}"
    echo -e "${BLUE}${BOLD}        更新时间: $(date)${NC}"
    echo -e "${BLUE}${BOLD}================================================${NC}"
    echo ""
}

# 监控PVE状态
monitor_pve() {
    echo -e "${CYAN}${BOLD}PVE 监控${NC}"
    echo "----------------------------------------"
    
    # PVE服务状态
    local pve_services=("pve-cluster" "pveproxy" "pvedaemon" "pvestatd")
    local service_status=""
    
    for service in "${pve_services[@]}"; do
        if systemctl is-active "$service" >/dev/null 2>&1; then
            service_status+="${GREEN}✓${NC} $service "
        else
            service_status+="${RED}✗${NC} $service "
            send_alert "PVE服务 $service 未运行" "CRITICAL"
        fi
    done
    
    echo -e "服务状态: $service_status"
    
    # 系统资源
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    local mem_info=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    local disk_usage=$(df -h / | awk 'NR==2{print $5}' | cut -d'%' -f1)
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | cut -d',' -f1)
    
    echo -e "CPU使用率: ${cpu_usage}% | 内存使用: ${mem_info}% | 磁盘使用: ${disk_usage}% | 负载: ${load_avg}"
    
    # 告警检查
    if (( $(echo "$cpu_usage > $ALERT_CPU_THRESHOLD" | bc -l) )); then
        send_alert "PVE主机CPU使用率过高: ${cpu_usage}%" "WARNING"
    fi
    
    if (( $(echo "$mem_info > $ALERT_MEM_THRESHOLD" | bc -l) )); then
        send_alert "PVE主机内存使用率过高: ${mem_info}%" "WARNING"
    fi
    
    if (( disk_usage > ALERT_DISK_THRESHOLD )); then
        send_alert "PVE主机磁盘使用率过高: ${disk_usage}%" "WARNING"
    fi
    
    # 虚拟机状态
    local vm_running=$(pvesh get /cluster/resources --type vm | grep -c "running" || echo "0")
    local vm_total=$(pvesh get /cluster/resources --type vm | grep -c "qemu" || echo "0")
    
    echo -e "虚拟机状态: ${GREEN}运行中${NC} $vm_running/${vm_total}"
    
    # 存储状态
    local storage_info=$(pvesh get /cluster/resources --type storage 2>/dev/null | grep -E "local|shared" | head -3)
    if [[ -n "$storage_info" ]]; then
        echo "存储状态: 正常"
    else
        echo -e "存储状态: ${RED}异常${NC}"
        send_alert "PVE存储状态异常" "CRITICAL"
    fi
    
    echo ""
}

# 监控Hadoop集群
monitor_hadoop() {
    echo -e "${CYAN}${BOLD}Hadoop 集群监控${NC}"
    echo "----------------------------------------"
    
    # HDFS状态
    local hdfs_status="${RED}异常${NC}"
    local namenode_status="${RED}停止${NC}"
    local datanode_count=0
    
    if ssh -o ConnectTimeout=5 hadoop-node1 "systemctl is-active hadoop-hdfs-namenode" >/dev/null 2>&1; then
        namenode_status="${GREEN}运行${NC}"
        
        # 检查HDFS健康状态
        if ssh -o ConnectTimeout=5 hadoop-node1 "hdfs dfsadmin -report" >/dev/null 2>&1; then
            hdfs_status="${GREEN}正常${NC}"
            datanode_count=$(ssh -o ConnectTimeout=5 hadoop-node1 "hdfs dfsadmin -report" 2>/dev/null | grep "Live datanodes" | awk '{print $3}' | cut -d':' -f1 || echo "0")
        fi
    else
        send_alert "Hadoop NameNode服务未运行" "CRITICAL"
    fi
    
    echo -e "HDFS状态: $hdfs_status | NameNode: $namenode_status | DataNode: ${datanode_count}个"
    
    # YARN状态
    local yarn_status="${RED}异常${NC}"
    local yarn_rm_status="${RED}停止${NC}"
    local yarn_nm_count=0
    
    if ssh -o ConnectTimeout=5 hadoop-node1 "systemctl is-active hadoop-yarn-resourcemanager" >/dev/null 2>&1; then
        yarn_rm_status="${GREEN}运行${NC}"
        yarn_status="${GREEN}正常${NC}"
        
        # 统计活跃的NodeManager
        for node in "${HADOOP_NODES[@]:1}"; do
            if ssh -o ConnectTimeout=3 "$node" "systemctl is-active hadoop-yarn-nodemanager" >/dev/null 2>&1; then
                ((yarn_nm_count++))
            fi
        done
    else
        send_alert "Hadoop YARN ResourceManager服务未运行" "CRITICAL"
    fi
    
    echo -e "YARN状态: $yarn_status | ResourceManager: $yarn_rm_status | NodeManager: ${yarn_nm_count}个"
    
    # ZooKeeper状态
    local zk_count=0
    for node in "${HADOOP_NODES[@]:0:3}"; do
        if ssh -o ConnectTimeout=3 "$node" "systemctl is-active zookeeper" >/dev/null 2>&1; then
            ((zk_count++))
        fi
    done
    
    local zk_status="${GREEN}正常${NC}"
    if [ $zk_count -lt 2 ]; then
        zk_status="${RED}异常${NC}"
        send_alert "ZooKeeper集群节点不足: 只有${zk_count}个节点运行" "CRITICAL"
    elif [ $zk_count -lt 3 ]; then
        zk_status="${YELLOW}警告${NC}"
        send_alert "ZooKeeper集群部分节点离线: 只有${zk_count}个节点运行" "WARNING"
    fi
    
    echo -e "ZooKeeper: $zk_status (${zk_count}/3个节点)"
    
    # Hadoop节点资源监控
    echo -e "\nHadoop节点资源:"
    for node in "${HADOOP_NODES[@]}"; do
        if ping -c1 -W2 "$node" >/dev/null 2>&1; then
            local cpu=$(ssh -o ConnectTimeout=3 "$node" "top -bn1 | grep 'Cpu(s)' | awk '{print \$2}' | cut -d'%' -f1" 2>/dev/null || echo "N/A")
            local mem=$(ssh -o ConnectTimeout=3 "$node" "free | grep Mem | awk '{printf \"%.1f\", \$3/\$2 * 100.0}'" 2>/dev/null || echo "N/A")
            local disk=$(ssh -o ConnectTimeout=3 "$node" "df -h / | awk 'NR==2{print \$5}' | cut -d'%' -f1" 2>/dev/null || echo "N/A")
            
            local status_color="${GREEN}"
            if [[ "$cpu" != "N/A" ]] && (( $(echo "$cpu > $ALERT_CPU_THRESHOLD" | bc -l 2>/dev/null || echo 0) )); then
                status_color="${RED}"
            fi
            
            echo -e "  $node: ${status_color}CPU ${cpu}%${NC} | MEM ${mem}% | DISK ${disk}%"
        else
            echo -e "  $node: ${RED}离线${NC}"
            send_alert "Hadoop节点 $node 无法连接" "CRITICAL"
        fi
    done
    
    echo ""
}

# 监控Kubernetes集群
monitor_k8s() {
    echo -e "${CYAN}${BOLD}Kubernetes 集群监控${NC}"
    echo "----------------------------------------"
    
    # 集群状态
    local api_status="${RED}异常${NC}"
    local node_ready=0
    local node_total=0
    
    if kubectl cluster-info >/dev/null 2>&1; then
        api_status="${GREEN}正常${NC}"
        
        # 统计节点状态
        local node_info=$(kubectl get nodes --no-headers 2>/dev/null || echo "")
        if [[ -n "$node_info" ]]; then
            node_total=$(echo "$node_info" | wc -l)
            node_ready=$(echo "$node_info" | grep -c "Ready" || echo "0")
        fi
    else
        send_alert "Kubernetes API Server无法访问" "CRITICAL"
    fi
    
    echo -e "API状态: $api_status | 节点状态: ${GREEN}就绪${NC} ${node_ready}/${node_total}"
    
    # 控制平面组件状态
    local control_plane_status="${GREEN}正常${NC}"
    local k8s_services=("kube-apiserver" "kube-controller-manager" "kube-scheduler" "etcd")
    local service_down=0
    
    for service in "${k8s_services[@]}"; do
        if ! ssh -o ConnectTimeout=3 k8s-master "systemctl is-active $service" >/dev/null 2>&1; then
            ((service_down++))
            send_alert "Kubernetes服务 $service 未运行" "CRITICAL"
        fi
    done
    
    if [ $service_down -gt 0 ]; then
        control_plane_status="${RED}异常${NC}"
    fi
    
    echo -e "控制平面: $control_plane_status | etcd: $(ssh -o ConnectTimeout=3 k8s-master "systemctl is-active etcd" 2>/dev/null | grep -q active && echo "${GREEN}正常${NC}" || echo "${RED}异常${NC}")"
    
    # Pod状态统计
    if kubectl get pods --all-namespaces >/dev/null 2>&1; then
        local pod_stats=$(kubectl get pods --all-namespaces --no-headers 2>/dev/null | awk '
        {
            total++
            if ($4 == "Running") running++
            else if ($4 == "Pending") pending++
            else if ($4 == "Failed") failed++
            else if ($4 == "Error") error++
            else other++
        }
        END {
            printf "%d %d %d %d %d\n", total, running, pending, failed+error, other
        }' || echo "0 0 0 0 0")
        
        read -r total running pending failed other <<< "$pod_stats"
        echo -e "Pod状态: ${GREEN}运行${NC} $running | ${YELLOW}等待${NC} $pending | ${RED}失败${NC} $failed | 总计 $total"
        
        if [ $failed -gt 0 ]; then
            send_alert "Kubernetes集群中有${failed}个Pod处于失败状态" "WARNING"
        fi
    else
        echo -e "Pod状态: ${RED}无法获取${NC}"
    fi
    
    # 系统Pod监控
    local system_pods_ok=true
    local critical_pods=("coredns" "kube-proxy")
    
    for pod_pattern in "${critical_pods[@]}"; do
        local pod_count=$(kubectl get pods -n kube-system --no-headers 2>/dev/null | grep "$pod_pattern" | grep -c "Running" || echo "0")
        if [ $pod_count -eq 0 ]; then
            system_pods_ok=false
            send_alert "关键系统Pod $pod_pattern 未运行" "CRITICAL"
        fi
    done
    
    if $system_pods_ok; then
        echo -e "系统Pod: ${GREEN}正常${NC}"
    else
        echo -e "系统Pod: ${RED}异常${NC}"
    fi
    
    # 节点资源监控
    echo -e "\nK8S节点资源:"
    for node in "${K8S_NODES[@]}"; do
        if ping -c1 -W2 "$node" >/dev/null 2>&1; then
            local cpu=$(ssh -o ConnectTimeout=3 "$node" "top -bn1 | grep 'Cpu(s)' | awk '{print \$2}' | cut -d'%' -f1" 2>/dev/null || echo "N/A")
            local mem=$(ssh -o ConnectTimeout=3 "$node" "free | grep Mem | awk '{printf \"%.1f\", \$3/\$2 * 100.0}'" 2>/dev/null || echo "N/A")
            local disk=$(ssh -o ConnectTimeout=3 "$node" "df -h / | awk 'NR==2{print \$5}' | cut -d'%' -f1" 2>/dev/null || echo "N/A")
            
            local status_color="${GREEN}"
            if [[ "$cpu" != "N/A" ]] && (( $(echo "$cpu > $ALERT_CPU_THRESHOLD" | bc -l 2>/dev/null || echo 0) )); then
                status_color="${RED}"
            fi
            
            echo -e "  $node: ${status_color}CPU ${cpu}%${NC} | MEM ${mem}% | DISK ${disk}%"
        else
            echo -e "  $node: ${RED}离线${NC}"
            send_alert "Kubernetes节点 $node 无法连接" "CRITICAL"
        fi
    done
    
    echo ""
}

# 监控网络连通性
monitor_network() {
    echo -e "${CYAN}${BOLD}网络连通性监控${NC}"
    echo "----------------------------------------"
    
    local network_issues=0
    
    # 检查Hadoop节点间连通性
    echo -e "Hadoop集群网络:"
    for node in "${HADOOP_NODES[@]}"; do
        if ping -c1 -W2 "$node" >/dev/null 2>&1; then
            echo -e "  $node: ${GREEN}✓${NC}"
        else
            echo -e "  $node: ${RED}✗${NC}"
            ((network_issues++))
            send_alert "Hadoop节点 $node 网络不可达" "CRITICAL"
        fi
    done
    
    # 检查K8S节点间连通性
    echo -e "\nKubernetes集群网络:"
    for node in "${K8S_NODES[@]}"; do
        if ping -c1 -W2 "$node" >/dev/null 2>&1; then
            echo -e "  $node: ${GREEN}✓${NC}"
        else
            echo -e "  $node: ${RED}✗${NC}"
            ((network_issues++))
            send_alert "Kubernetes节点 $node 网络不可达" "CRITICAL"
        fi
    done
    
    # 检查关键端口
    echo -e "\n关键服务端口:"
    local port_checks=(
        "hadoop-node1:9870:HDFS-NameNode"
        "hadoop-node1:8088:YARN-ResourceManager"
        "k8s-master:6443:K8S-API-Server"
    )
    
    for check in "${port_checks[@]}"; do
        IFS=':' read -r host port service <<< "$check"
        if nc -z -w3 "$host" "$port" 2>/dev/null; then
            echo -e "  $service: ${GREEN}✓${NC}"
        else
            echo -e "  $service: ${RED}✗${NC}"
            ((network_issues++))
            send_alert "服务端口 $service ($host:$port) 不可达" "CRITICAL"
        fi
    done
    
    if [ $network_issues -eq 0 ]; then
        echo -e "\n网络状态: ${GREEN}全部正常${NC}"
    else
        echo -e "\n网络状态: ${RED}发现 $network_issues 个问题${NC}"
    fi
    
    echo ""
}

# 显示系统总体状态
display_summary() {
    echo -e "${CYAN}${BOLD}系统总体状态${NC}"
    echo "----------------------------------------"
    
    # 计算总体健康分数
    local total_score=100
    local issues=0
    
    # 检查关键服务
    local critical_services=(
        "pve-cluster"
        "hadoop-hdfs-namenode"
        "hadoop-yarn-resourcemanager"
        "kube-apiserver"
    )
    
    for service in "${critical_services[@]}"; do
        case $service in
            "pve-cluster")
                if ! systemctl is-active "$service" >/dev/null 2>&1; then
                    ((issues++))
                    total_score=$((total_score - 20))
                fi
                ;;
            "hadoop-hdfs-namenode")
                if ! ssh -o ConnectTimeout=3 hadoop-node1 "systemctl is-active $service" >/dev/null 2>&1; then
                    ((issues++))
                    total_score=$((total_score - 25))
                fi
                ;;
            "hadoop-yarn-resourcemanager")
                if ! ssh -o ConnectTimeout=3 hadoop-node1 "systemctl is-active $service" >/dev/null 2>&1; then
                    ((issues++))
                    total_score=$((total_score - 20))
                fi
                ;;
            "kube-apiserver")
                if ! ssh -o ConnectTimeout=3 k8s-master "systemctl is-active $service" >/dev/null 2>&1; then
                    ((issues++))
                    total_score=$((total_score - 25))
                fi
                ;;
        esac
    done
    
    # 显示健康分数
    local health_color="${GREEN}"
    local health_status="优秀"
    
    if [ $total_score -ge 80 ]; then
        health_color="${GREEN}"
        health_status="优秀"
    elif [ $total_score -ge 60 ]; then
        health_color="${YELLOW}"
        health_status="良好"
    elif [ $total_score -ge 40 ]; then
        health_color="${YELLOW}"
        health_status="一般"
    else
        health_color="${RED}"
        health_status="差"
    fi
    
    echo -e "集群健康分数: ${health_color}${total_score}/100 (${health_status})${NC}"
    echo -e "发现问题数量: $issues"
    echo -e "监控间隔: ${MONITOR_INTERVAL}秒"
    echo -e "下次更新: $(date -d "+${MONITOR_INTERVAL} seconds" '+%H:%M:%S')"
    
    if [ $issues -eq 0 ]; then
        echo -e "状态: ${GREEN}${BOLD}所有系统运行正常${NC}"
    else
        echo -e "状态: ${RED}${BOLD}需要关注 $issues 个问题${NC}"
    fi
    
    echo ""
    echo -e "${YELLOW}按 Ctrl+C 退出监控${NC}"
    echo ""
}

# 实时监控主循环
start_monitoring() {
    echo -e "${GREEN}开始实时监控集群状态...${NC}"
    log "Starting real-time cluster monitoring"
    
    while true; do
        # 显示监控面板
        display_header
        monitor_pve
        monitor_hadoop
        monitor_k8s
        monitor_network
        display_summary
        
        # 等待下次更新
        sleep $MONITOR_INTERVAL
    done
}

# 一次性检查
run_once() {
    echo -e "${GREEN}执行一次性集群状态检查...${NC}"
    log "Running one-time cluster status check"
    
    display_header
    monitor_pve
    monitor_hadoop
    monitor_k8s
    monitor_network
    display_summary
    
    echo -e "${GREEN}检查完成！${NC}"
}

# 生成监控报告
generate_report() {
    local report_file="/var/log/cluster_monitor_report_$(date +%Y%m%d_%H%M%S).html"
    
    echo -e "${GREEN}生成监控报告...${NC}"
    
    cat > "$report_file" << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>集群监控报告</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 10px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .good { color: #28a745; }
        .warning { color: #ffc107; }
        .error { color: #dc3545; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
EOF
    
    # 添加报告内容
    echo "<div class='header'>" >> "$report_file"
    echo "<h1>集群监控报告</h1>" >> "$report_file"
    echo "<p>生成时间: $(date)</p>" >> "$report_file"
    echo "</div>" >> "$report_file"
    
    # 这里可以添加更多HTML内容
    
    echo "</body></html>" >> "$report_file"
    
    echo -e "${GREEN}监控报告已生成: $report_file${NC}"
    log "Monitoring report generated: $report_file"
}

# 显示使用帮助
show_help() {
    echo "集群监控脚本使用说明:"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -m, --monitor           启动实时监控 (默认)"
    echo "  -o, --once              执行一次性检查"
    echo "  -r, --report            生成监控报告"
    echo "  -i, --interval N        设置监控间隔秒数 (默认: $MONITOR_INTERVAL)"
    echo "  -e, --email EMAIL       设置告警邮箱"
    echo "  --cpu-threshold N       设置CPU告警阈值 (默认: $ALERT_CPU_THRESHOLD%)"
    echo "  --mem-threshold N       设置内存告警阈值 (默认: $ALERT_MEM_THRESHOLD%)"
    echo "  --disk-threshold N      设置磁盘告警阈值 (默认: $ALERT_DISK_THRESHOLD%)"
    echo ""
    echo "示例:"
    echo "  $0                      # 启动实时监控"
    echo "  $0 -o                   # 执行一次性检查"
    echo "  $0 -i 60               # 60秒间隔监控"
    echo "  $0 -e <EMAIL>  # 设置告警邮箱"
    echo "  $0 --cpu-threshold 90   # 设置CPU告警阈值为90%"
}

# 信号处理
cleanup() {
    echo ""
    echo -e "${YELLOW}监控已停止${NC}"
    log "Monitoring stopped by user"
    exit 0
}

trap cleanup SIGINT SIGTERM

# 主函数
main() {
    local mode="monitor"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -m|--monitor)
                mode="monitor"
                shift
                ;;
            -o|--once)
                mode="once"
                shift
                ;;
            -r|--report)
                mode="report"
                shift
                ;;
            -i|--interval)
                MONITOR_INTERVAL="$2"
                shift 2
                ;;
            -e|--email)
                ALERT_EMAIL="$2"
                shift 2
                ;;
            --cpu-threshold)
                ALERT_CPU_THRESHOLD="$2"
                shift 2
                ;;
            --mem-threshold)
                ALERT_MEM_THRESHOLD="$2"
                shift 2
                ;;
            --disk-threshold)
                ALERT_DISK_THRESHOLD="$2"
                shift 2
                ;;
            *)
                echo "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 执行相应模式
    case $mode in
        "monitor")
            start_monitoring
            ;;
        "once")
            run_once
            ;;
        "report")
            generate_report
            ;;
    esac
}

# 检查依赖工具
check_dependencies() {
    local missing_tools=()
    
    for tool in ssh nc pvesh kubectl bc; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        echo -e "${RED}错误: 缺少必要工具: ${missing_tools[*]}${NC}"
        echo "请安装缺少的工具后重新运行"
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    check_dependencies
    main "$@"
fi 