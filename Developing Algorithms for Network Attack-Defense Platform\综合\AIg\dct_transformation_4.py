"""离散余弦变换（DCT）"""

import sys
import pandas as pd
from scipy.fftpack import dct

def dct_transformation(input_file, target_column):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - target_column: 目标变量的列名
    """
    data = pd.read_csv(input_file)
    X = data.drop(target_column, axis=1)
    
    X_new = dct(X, norm='ortho')
    
    output_file = 'dct_transformed_data.csv'
    pd.DataFrame(X_new, columns=X.columns).to_csv(output_file, index=False)
    print(f"DCT transformation completed. Output saved to {output_file}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python dct_transformation.py <input_file> <target_column>")
        sys.exit(1)
    input_file, target_column = sys.argv[1], sys.argv[2]
    dct_transformation(input_file, target_column)
