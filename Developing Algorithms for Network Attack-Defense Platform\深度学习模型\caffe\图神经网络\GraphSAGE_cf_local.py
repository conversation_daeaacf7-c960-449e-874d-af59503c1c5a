import os
import sys
import pickle
import numpy as np
import json
import uuid
import requests
from minio import Minio
import caffe
from caffe import layers as L
from caffe import params as P
import argparse

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

# Header constants
MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

def create_graphsage_prototxt(model_path, in_channels, hidden_channels, num_classes):
    """Create Caffe model architecture for GraphSAGE"""
    with open(os.path.join(model_path, 'graphsage.prototxt'), 'w') as f:
        # Network definition
        n = caffe.NetSpec()
        
        # Input layers
        n.data = L.Input(shape=dict(dim=[1, in_channels, 1, 1]))
        n.edge_index = L.Input(shape=dict(dim=[1, 2, 1, 1]))
        
        # First GraphSAGE convolution
        n.conv1 = L.Convolution(n.data,
                              num_output=hidden_channels,
                              kernel_size=1,
                              weight_filler=dict(type='xavier'))
        n.relu1 = L.ReLU(n.conv1, in_place=True)
        
        # Second GraphSAGE convolution
        n.conv2 = L.Convolution(n.relu1,
                              num_output=num_classes,
                              kernel_size=1,
                              weight_filler=dict(type='xavier'))
        
        # Log softmax
        n.prob = L.Softmax(n.conv2)
        
        f.write(str(n.to_proto()))
        
    # Create solver prototxt
    with open(os.path.join(model_path, 'solver.prototxt'), 'w') as f:
        solver_str = """
        net: "graphsage.prototxt"
        test_iter: 1
        test_interval: 100
        base_lr: 0.01
        momentum: 0.9
        weight_decay: 0.0005
        lr_policy: "step"
        gamma: 0.1
        stepsize: 1000
        display: 100
        max_iter: 5000
        snapshot: 1000
        snapshot_prefix: "graphsage"
        solver_mode: GPU
        """
        f.write(solver_str)

def graphsage_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """Train GraphSAGE model using Caffe"""
    # Load training data
    training_data_path = "/workspace/" + dataset["training_data_path"]
    with open(training_data_path, 'rb') as f:
        data = pickle.load(f, encoding='bytes')
    
    # Get parameters
    in_channels = job_params["in_channels"]
    hidden_channels = job_params["hidden_channels"]
    num_classes = job_params["num_classes"]
    num_epochs = job_params["num_epochs"]
    learning_rate = job_params["learning_rate"]
    
    # Create model architecture
    create_graphsage_prototxt(result_dir, in_channels, hidden_channels, num_classes)
    
    # Initialize solver
    solver = caffe.SGDSolver(os.path.join(result_dir, 'solver.prototxt'))
    
    # Training loop
    for epoch in range(num_epochs):
        # Forward pass
        solver.net.blobs['data'].data[...] = data.x.numpy()
        solver.net.blobs['edge_index'].data[...] = data.edge_index.numpy()
        
        # Backward pass and update
        solver.step(1)
        
        # Print training loss
        if epoch % 100 == 0:
            print(f'Epoch {epoch}, loss: {solver.net.blobs["loss"].data[...]}')
    
    # Save trained model
    solver.net.save(os.path.join(result_dir, f"{model_name}.caffemodel"))
    print(f'GraphSAGE training completed, model saved to {result_dir}/{model_name}.caffemodel')
    
    return None, 0

def model_upload(result_dir, model_name):
    """Upload model to MinIO storage"""
    minioClient = Minio(MINIO_URL,
                       access_key='AKIAIOSFODNN7EXAMPLE',
                       secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                       secure=False)
    try:
        obj_name = str(uuid.uuid1())
        upload_path = f"{obj_name}/{model_name}.caffemodel"
        source = f"s3://mlss-mf/{obj_name}"
        res = minioClient.fput_object('mlss-mf', upload_path, 
                                    os.path.join(result_dir, f"{model_name}.caffemodel"))
        result = {"source": source}
        return result, 0
    except Exception as err:
        print(err)
        return None, -1

def model_register(model_name, source, group_id, headers):
    """Register model in the model factory"""
    params = {
        "model_name": model_name,
        "model_type": "caffe",
        "file_name": f"{model_name}.caffemodel",
        "s3_path": source,
        "group_id": int(float(group_id)),
        "training_id": model_name,
        "training_flag": 1,
    }
    
    r = requests.post(MODEL_FACTORY_URL + MODEL_ADD_URL,
                     data=json.dumps(params),
                     headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    """Push model to factory"""
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0
    
    params = {
        "factory_name": factory_name,
        "model_type": "GraphSAGE",
        "model_usage": "Graph_Classification"
    }
    r = requests.post(f"{MODEL_FACTORY_URL}{MODEL_PUSH_URL}/{model_version_id}",
                     data=json.dumps(params),
                     headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def header_gen(user_id):
    """Generate request headers"""
    return {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: user_id
    }

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Caffe GraphSAGE Train.')
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                       help='GraphSAGE Job Params')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                       help='GraphSAGE DataSet')
    parser.add_argument('--model', dest='model', type=json.loads,
                       help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                       help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                       help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                       help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,
                       default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                       help='sparkconf params')
    parser.add_argument('--nodehost', nargs='?', const=None, dest='nodehost',
                       type=str, default="**************",
                       help='nodehost params')

    args = parser.parse_args()
    
    print("Start GraphSAGE training job, params:\n" + str(sys.argv) + "\n")
    
    # Parse arguments
    job_params = args.job_params
    dataset = args.dataset
    model = args.model
    result_dir = args.result_dir
    factory_name = args.factory_name
    fit_params = args.fit_params or {}
    sparkconf = json.loads(args.sparkconf)
    nodehost = args.nodehost
    
    # Print configuration
    print("GraphSAGE job params:" + str(job_params) + "\n")
    print("GraphSAGE dataSet:" + str(dataset) + "\n")
    print(model)
    print("GraphSAGE result dir:" + result_dir + "\n")
    print("GraphSAGE factory name:" + factory_name + "\n")
    print("GraphSAGE fit params:" + str(fit_params) + "\n")
    print("GraphSAGE sparkconf params:" + str(sparkconf) + "\n")
    
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    
    # Training pipeline
    print("Step 1 GraphSAGE training:\n")
    result, ret_code = graphsage_train(dataset, job_params, model["model_name"],
                                     result_dir, fit_params)
    if ret_code != 0:
        print("GraphSAGE train err, stop job....\n")
        print("Error Msg:" + result + "\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    print("Step 2 Model Upload to MinIO: \n")
    result, ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model register...\n")
    
    print("Step 3 Model Register:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result, ret_code = model_register(model["model_name"], source,
                                    model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: " + result)
        sys.exit(-1)
    print("Register model finish, start model push...")
    
    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result, ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: " + result + "\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()