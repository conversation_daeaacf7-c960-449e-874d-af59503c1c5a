#!/bin/bash

# =======================================================
# 运维脚本安装和设置脚本
# 功能: 设置所有运维脚本的执行权限和基本配置
# 作者: 系统管理员
# 版本: 1.0
# =======================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="scripts"

# 日志目录
LOG_DIR="/var/log"
BACKUP_DIR="/backup"

echo -e "${BLUE}=================================${NC}"
echo -e "${BLUE}    运维脚本环境设置${NC}"
echo -e "${BLUE}=================================${NC}"
echo ""

# 检查是否以root权限运行
if [[ $EUID -ne 0 ]]; then
    echo -e "${RED}错误: 请以root权限运行此脚本${NC}"
    echo "使用: sudo $0"
    exit 1
fi

# 创建必要的目录
echo -e "${YELLOW}创建必要目录...${NC}"
mkdir -p "$SCRIPT_DIR"
mkdir -p "$LOG_DIR"
mkdir -p "$BACKUP_DIR"
mkdir -p "/etc/cluster-ops"

echo -e "${GREEN}✓${NC} 目录创建完成"

# 设置脚本执行权限
echo -e "\n${YELLOW}设置脚本执行权限...${NC}"

scripts=(
    "check_cluster_status.sh"
    "restart_clusters.sh"
    "backup_clusters.sh"
    "monitor_clusters.sh"
)

for script in "${scripts[@]}"; do
    script_path="$SCRIPT_DIR/$script"
    if [[ -f "$script_path" ]]; then
        chmod +x "$script_path"
        echo -e "${GREEN}✓${NC} $script_path 权限已设置"
    else
        echo -e "${YELLOW}⚠${NC} $script_path 文件不存在，跳过"
    fi
done

# 创建软链接到系统PATH
echo -e "\n${YELLOW}创建系统命令软链接...${NC}"
ln -sf "$(pwd)/$SCRIPT_DIR/check_cluster_status.sh" /usr/local/bin/cluster-status
ln -sf "$(pwd)/$SCRIPT_DIR/restart_clusters.sh" /usr/local/bin/cluster-restart
ln -sf "$(pwd)/$SCRIPT_DIR/backup_clusters.sh" /usr/local/bin/cluster-backup
ln -sf "$(pwd)/$SCRIPT_DIR/monitor_clusters.sh" /usr/local/bin/cluster-monitor

echo -e "${GREEN}✓${NC} 软链接创建完成"

# 安装依赖工具
echo -e "\n${YELLOW}检查并安装依赖工具...${NC}"

# 检测Linux发行版
if command -v apt >/dev/null 2>&1; then
    PKG_MANAGER="apt"
    INSTALL_CMD="apt install -y"
elif command -v yum >/dev/null 2>&1; then
    PKG_MANAGER="yum"
    INSTALL_CMD="yum install -y"
elif command -v dnf >/dev/null 2>&1; then
    PKG_MANAGER="dnf"
    INSTALL_CMD="dnf install -y"
else
    echo -e "${RED}错误: 无法检测到包管理器${NC}"
    exit 1
fi

# 依赖包列表
dependencies=(
    "curl"
    "wget"
    "jq"
    "bc"
    "netcat-openbsd"  # Ubuntu/Debian
    "net-tools"
    "htop"
    "iotop"
    "sysstat"
    "rsync"
    "openssl"
    "gpg"
)

# CentOS/RHEL特殊包名
if [[ "$PKG_MANAGER" == "yum" || "$PKG_MANAGER" == "dnf" ]]; then
    dependencies[4]="nc"  # CentOS中netcat包名
fi

for dep in "${dependencies[@]}"; do
    if ! command -v "$dep" >/dev/null 2>&1; then
        echo "安装 $dep..."
        if $INSTALL_CMD "$dep" >/dev/null 2>&1; then
            echo -e "${GREEN}✓${NC} $dep 安装成功"
        else
            echo -e "${YELLOW}⚠${NC} $dep 安装失败，请手动安装"
        fi
    else
        echo -e "${GREEN}✓${NC} $dep 已安装"
    fi
done

# 创建配置文件
echo -e "\n${YELLOW}创建配置文件...${NC}"

cat > /etc/cluster-ops/config << 'EOF'
# 集群运维配置文件
# 请根据实际环境修改以下配置

# 节点配置
HADOOP_NODES=("hadoop-node1" "hadoop-node2" "hadoop-node3" "hadoop-node4" "hadoop-node5" "hadoop-node6")
K8S_NODES=("k8s-master" "k8s-worker1" "k8s-worker2" "k8s-worker3" "k8s-worker4")
PVE_HOST="localhost"

# 监控配置
MONITOR_INTERVAL=30
ALERT_CPU_THRESHOLD=80
ALERT_MEM_THRESHOLD=85
ALERT_DISK_THRESHOLD=90

# 备份配置
BACKUP_BASE_DIR="/backup"
RETENTION_DAYS=7

# 告警配置
ALERT_EMAIL=""
SMTP_SERVER=""

# 日志配置
LOG_LEVEL="INFO"
MAX_LOG_SIZE="100M"
EOF

echo -e "${GREEN}✓${NC} 配置文件创建完成: /etc/cluster-ops/config"

# 创建systemd服务文件（监控服务）
echo -e "\n${YELLOW}创建systemd服务...${NC}"

cat > /etc/systemd/system/cluster-monitor.service << EOF
[Unit]
Description=Cluster Monitoring Service
After=network.target

[Service]
Type=simple
User=root
ExecStart=/usr/local/bin/cluster-monitor
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
echo -e "${GREEN}✓${NC} cluster-monitor 服务创建完成"

# 创建cron任务
echo -e "\n${YELLOW}设置定时任务...${NC}"

# 备份任务（每天凌晨2点）
echo "0 2 * * * root /usr/local/bin/cluster-backup >/dev/null 2>&1" >> /etc/crontab

# 状态检查任务（每小时）
echo "0 * * * * root /usr/local/bin/cluster-status >/var/log/cluster-hourly-check.log 2>&1" >> /etc/crontab

# 重启cron服务
systemctl restart cron 2>/dev/null || systemctl restart crond 2>/dev/null

echo -e "${GREEN}✓${NC} 定时任务设置完成"

# 创建日志轮转配置
echo -e "\n${YELLOW}配置日志轮转...${NC}"

cat > /etc/logrotate.d/cluster-ops << 'EOF'
/var/log/cluster_*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
EOF

echo -e "${GREEN}✓${NC} 日志轮转配置完成"

# 设置防火墙规则（如果启用了防火墙）
if systemctl is-active ufw >/dev/null 2>&1; then
    echo -e "\n${YELLOW}配置防火墙规则...${NC}"
    
    # PVE管理端口
    ufw allow 8006/tcp comment "PVE Web Interface"
    
    # Hadoop端口
    ufw allow 9000/tcp comment "HDFS NameNode"
    ufw allow 9870/tcp comment "HDFS NameNode Web UI"
    ufw allow 8088/tcp comment "YARN ResourceManager Web UI"
    ufw allow 19888/tcp comment "MapReduce History Server"
    
    # Kubernetes端口
    ufw allow 6443/tcp comment "Kubernetes API Server"
    ufw allow 2379:2380/tcp comment "etcd"
    ufw allow 10250/tcp comment "Kubelet"
    
    echo -e "${GREEN}✓${NC} 防火墙规则配置完成"
fi

# 创建使用说明
echo -e "\n${YELLOW}创建使用说明文档...${NC}"

cat > /etc/cluster-ops/README.txt << 'EOF'
集群运维脚本使用说明
==================

可用命令:
- cluster-status      检查集群状态
- cluster-restart     重启集群服务
- cluster-backup      备份集群数据
- cluster-monitor     启动监控面板

配置文件:
- /etc/cluster-ops/config  主配置文件

日志文件:
- /var/log/cluster_*.log   各类操作日志

服务管理:
- systemctl start cluster-monitor    启动监控服务
- systemctl enable cluster-monitor   设置开机自启

定时任务:
- 每天2点自动备份
- 每小时状态检查

更多帮助:
- cluster-status --help
- cluster-restart --help
- cluster-backup --help
- cluster-monitor --help
EOF

echo -e "${GREEN}✓${NC} 使用说明创建完成: /etc/cluster-ops/README.txt"

# 检查配置文件的有效性
echo -e "\n${YELLOW}验证脚本配置...${NC}"

config_errors=0

# 检查SSH连接（针对第一个Hadoop节点）
if ! ping -c 1 -W 2 hadoop-node1 >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠${NC} 无法ping通hadoop-node1，请检查网络配置"
    ((config_errors++))
fi

# 检查kubectl配置
if ! command -v kubectl >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠${NC} kubectl未安装，K8S相关功能将不可用"
    ((config_errors++))
fi

# 检查PVE命令
if ! command -v pvesh >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠${NC} pvesh未安装，PVE相关功能将不可用"
    ((config_errors++))
fi

if [ $config_errors -eq 0 ]; then
    echo -e "${GREEN}✓${NC} 配置验证通过"
else
    echo -e "${YELLOW}⚠${NC} 发现 $config_errors 个配置问题，请参考文档进行修复"
fi

# 完成安装
echo ""
echo -e "${BLUE}=================================${NC}"
echo -e "${GREEN}安装完成！${NC}"
echo -e "${BLUE}=================================${NC}"
echo ""
echo "可用命令:"
echo "  cluster-status    - 检查集群状态"
echo "  cluster-restart   - 重启集群服务"
echo "  cluster-backup    - 备份集群数据"
echo "  cluster-monitor   - 启动监控面板"
echo ""
echo "配置文件: /etc/cluster-ops/config"
echo "使用说明: /etc/cluster-ops/README.txt"
echo ""
echo "下一步:"
echo "1. 编辑配置文件以匹配您的环境"
echo "2. 运行 cluster-status 测试连接"
echo "3. 启动监控服务: systemctl start cluster-monitor"
echo ""
echo -e "${YELLOW}注意: 请根据实际环境修改 /etc/cluster-ops/config 中的节点配置${NC}" 