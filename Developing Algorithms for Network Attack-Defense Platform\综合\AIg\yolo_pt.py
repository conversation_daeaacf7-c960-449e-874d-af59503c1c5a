import torch
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from ultralytics import YOLO
import os
from PIL import Image
import json
import pickle
import pandas as pd
import xml.etree.ElementTree as ET
from tqdm.auto import tqdm
import yaml
import shutil

class UniversalImageDataset(Dataset):
    def __init__(self, data_dir, transform=None, dataset_type='folder', annotations_file=None):
        self.data_dir = data_dir
        self.transform = transform
        self.dataset_type = dataset_type
        self.annotations_file = annotations_file

        # YOLO格式的类别列表
        self.classes = ['aeroplane', 'bicycle', 'bird', 'boat', 'bottle', 'bus', 'car', 
                       'cat', 'chair', 'cow', 'diningtable', 'dog', 'horse', 'motorbike', 
                       'person', 'pottedplant', 'sheep', 'sofa', 'train', 'tvmonitor']
        self.class_to_idx = {cls: idx for idx, cls in enumerate(self.classes)}

        if dataset_type == 'voc':
            self.image_paths, self.labels = self.load_voc(annotations_file)
        elif dataset_type == 'coco':
            self.image_paths, self.labels = self.load_coco(annotations_file)
        elif dataset_type == 'yolo':
            self.image_paths, self.labels = self.load_yolo(annotations_file)
        elif dataset_type == 'pickle':
            self.image_paths, self.labels = self.load_pickle(annotations_file)
        else:
            raise ValueError("Unsupported dataset type.")

    def load_voc(self, annotations_file):
        """将VOC格式的数据转换为YOLO格式"""
        image_paths = []
        labels = []

        for xml_file in os.listdir(annotations_file):
            if xml_file.endswith('.xml'):
                xml_path = os.path.join(annotations_file, xml_file)
                tree = ET.parse(xml_path)
                root = tree.getroot()

                # 获取图像尺寸
                size = root.find('size')
                width = float(size.find('width').text)
                height = float(size.find('height').text)

                # 获取图像路径
                image_name = root.find('filename').text
                img_path = os.path.join(self.data_dir, image_name)
                image_paths.append(img_path)

                # 转换标注
                label = []
                for obj in root.findall('object'):
                    class_name = obj.find('name').text
                    class_idx = self.class_to_idx[class_name]
                    
                    bbox = obj.find('bndbox')
                    xmin = float(bbox.find('xmin').text)
                    ymin = float(bbox.find('ymin').text)
                    xmax = float(bbox.find('xmax').text)
                    ymax = float(bbox.find('ymax').text)

                    # 转换为YOLO格式 [class_id, x_center, y_center, width, height]
                    x_center = ((xmin + xmax) / 2) / width
                    y_center = ((ymin + ymax) / 2) / height
                    box_width = (xmax - xmin) / width
                    box_height = (ymax - ymin) / height

                    label.append([class_idx, x_center, y_center, box_width, box_height])
                
                labels.append(label)

        return image_paths, labels

    def load_coco(self, annotations_file):
        """将COCO格式的数据转换为YOLO格式"""
        with open(annotations_file) as f:
            annotations = json.load(f)

        # 创建图像ID到尺寸的映射
        image_info = {img['id']: (img['width'], img['height'], img['file_name']) 
                     for img in annotations['images']}

        # 组织标注数据
        image_annotations = {}
        for ann in annotations['annotations']:
            img_id = ann['image_id']
            if img_id not in image_annotations:
                image_annotations[img_id] = []
            
            # 获取图像尺寸
            img_width, img_height, _ = image_info[img_id]
            
            # 转换边界框为YOLO格式
            x, y, w, h = ann['bbox']
            x_center = (x + w/2) / img_width
            y_center = (y + h/2) / img_height
            width = w / img_width
            height = h / img_height
            
            category_id = ann['category_id']
            image_annotations[img_id].append([category_id, x_center, y_center, width, height])

        # 整理最终数据
        image_paths = []
        labels = []
        for img_id, (_, _, file_name) in image_info.items():
            img_path = os.path.join(self.data_dir, file_name)
            image_paths.append(img_path)
            labels.append(image_annotations.get(img_id, []))

        return image_paths, labels

    def load_yolo(self, annotations_file):
        """直接加载YOLO格式的数据"""
        image_paths = []
        labels = []

        # 获取所有图片文件
        for img_file in os.listdir(self.data_dir):
            if img_file.endswith(('.jpg', '.jpeg', '.png')):
                img_path = os.path.join(self.data_dir, img_file)
                image_paths.append(img_path)

                # 查找对应的标签文件
                label_file = os.path.join(annotations_file, 
                                        os.path.splitext(img_file)[0] + '.txt')
                
                if os.path.exists(label_file):
                    with open(label_file, 'r') as f:
                        label = []
                        for line in f:
                            class_id, x_center, y_center, width, height = map(float, line.strip().split())
                            label.append([class_id, x_center, y_center, width, height])
                        labels.append(label)
                else:
                    labels.append([])  # 空标签

        return image_paths, labels

    def load_pickle(self, pkl_file):
        """加载pickle格式的数据"""
        with open(pkl_file, 'rb') as f:
            data = pickle.load(f)

        if isinstance(data, dict):
            return data.get('images', []), data.get('labels', [])
        elif isinstance(data, pd.DataFrame):
            return data['image_paths'].tolist(), data['labels'].tolist()
        else:
            raise ValueError("Unsupported pickle data format")

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        image = Image.open(img_path).convert("RGB")
        label = self.labels[idx]

        if self.transform:
            image = self.transform(image)

        return image, torch.tensor(label)

def create_yolo_dataset_structure(voc_root):
    """
    创建YOLO格式的数据集目录结构
    """
    # 创建YOLO所需的目录结构
    yolo_root = os.path.join(os.path.dirname(voc_root), 'VOC2007_YOLO')
    os.makedirs(yolo_root, exist_ok=True)
    
    for split in ['images', 'labels']:
        for subset in ['train', 'val', 'test']:
            os.makedirs(os.path.join(yolo_root, split, subset), exist_ok=True)
    
    return yolo_root

def convert_voc_to_yolo(voc_root):
    """
    将VOC格式的数据集转换为YOLO格式
    """
    # VOC数据集路径
    annotations_dir = os.path.join(voc_root, 'Annotations')
    images_dir = os.path.join(voc_root, 'JPEGImages')
    imagesets_dir = os.path.join(voc_root, 'ImageSets', 'Main')
    
    # 创建YOLO格式的目录结构
    yolo_root = create_yolo_dataset_structure(voc_root)
    
    # VOC类别列表
    classes = ['aeroplane', 'bicycle', 'bird', 'boat', 'bottle', 'bus', 'car', 
               'cat', 'chair', 'cow', 'diningtable', 'dog', 'horse', 'motorbike', 
               'person', 'pottedplant', 'sheep', 'sofa', 'train', 'tvmonitor']
    class_to_idx = {cls: idx for idx, cls in enumerate(classes)}
    
    # 读取数据集划分
    def read_image_sets(split):
        with open(os.path.join(imagesets_dir, f'{split}.txt'), 'r') as f:
            return [line.strip() for line in f.readlines()]
    
    train_ids = read_image_sets('train')
    val_ids = read_image_sets('val')
    test_ids = read_image_sets('test')
    
    def convert_annotation(image_id, subset):
        """转换单个标注文件"""
        try:
            # 读取XML文件
            xml_path = os.path.join(annotations_dir, f'{image_id}.xml')
            tree = ET.parse(xml_path)
            root = tree.getroot()
            
            # 获取图像尺寸
            size = root.find('size')
            width = float(size.find('width').text)
            height = float(size.find('height').text)
            
            # 复制图像文件
            src_img = os.path.join(images_dir, f'{image_id}.jpg')
            dst_img = os.path.join(yolo_root, 'images', subset, f'{image_id}.jpg')
            shutil.copy2(src_img, dst_img)
            
            # 转换标注
            label_path = os.path.join(yolo_root, 'labels', subset, f'{image_id}.txt')
            with open(label_path, 'w') as f:
                for obj in root.findall('object'):
                    class_name = obj.find('name').text
                    class_idx = class_to_idx[class_name]
                    
                    bbox = obj.find('bndbox')
                    xmin = float(bbox.find('xmin').text)
                    ymin = float(bbox.find('ymin').text)
                    xmax = float(bbox.find('xmax').text)
                    ymax = float(bbox.find('ymax').text)
                    
                    # 转换为YOLO格式：<class> <x_center> <y_center> <width> <height>
                    x_center = ((xmin + xmax) / 2) / width
                    y_center = ((ymin + ymax) / 2) / height
                    box_width = (xmax - xmin) / width
                    box_height = (ymax - ymin) / height
                    
                    # 写入标注文件
                    f.write(f"{class_idx} {x_center:.6f} {y_center:.6f} {box_width:.6f} {box_height:.6f}\n")
                    
        except Exception as e:
            print(f"Error processing {image_id}: {str(e)}")
    
    # 转换训练集
    print("Converting training set...")
    for image_id in tqdm(train_ids):
        convert_annotation(image_id, 'train')
    
    # 转换验证集
    print("Converting validation set...")
    for image_id in tqdm(val_ids):
        convert_annotation(image_id, 'val')
    
    # 转换测试集
    print("Converting test set...")
    for image_id in tqdm(test_ids):
        convert_annotation(image_id, 'test')
    
    # 创建数据集配置文件
    dataset_yaml = {
        'path': yolo_root,
        'train': 'images/train',
        'val': 'images/val',
        'test': 'images/test',
        'nc': len(classes),  # 类别数量
        'names': classes     # 类别名称
    }
    
    # 保存配置文件
    yaml_path = os.path.join(yolo_root, 'dataset.yaml')
    with open(yaml_path, 'w') as f:
        yaml.safe_dump(dataset_yaml, f)
    
    return yolo_root, yaml_path

def train_yolo(voc_root, epochs=10, batch_size=16, img_size=640, device='0'):
    """
    训练YOLOv8模型
    
    参数:
        voc_root: VOC数据集根目录
        epochs: 训练轮数
        batch_size: 批次大小
        img_size: 输入图像尺寸
        device: 训练设备 ('0' for GPU 0, 'cpu' for CPU)
    """
    try:
        # 转换数据集格式
        print("Converting VOC dataset to YOLO format...")
        yolo_root, yaml_path = convert_voc_to_yolo(voc_root)
        
        # 初始化YOLOv8模型
        print("Initializing YOLOv8 model...")
        model = YOLO('yolov8n.pt')  # 使用预训练的YOLOv8 nano模型
        
        # 开始训练
        print("Starting training...")
        results = model.train(
            data=yaml_path,
            epochs=epochs,
            batch=batch_size,
            imgsz=img_size,
            device=device,
            project=os.path.join(yolo_root, 'runs'),
            name='train',
            exist_ok=True,
            pretrained=True,
            optimizer='auto',
            verbose=True,
            seed=42
        )
        
        # 在验证集上评估模型
        print("Evaluating model...")
        results = model.val(data=yaml_path, split='test')
        
        return results
        
    except Exception as e:
        print(f"Error during training: {str(e)}")
        raise

if __name__ == "__main__":
    # 训练参数设置
    data_dir = 'E:/data/VOCdevkit/VOC2007'
    input_file = os.path.join(data_dir, 'JPEGImages')
    annotations_file = os.path.join(data_dir, "Annotations")
    dataset_type = "voc"
    epochs = 1
    batch_size = 16
    img_size = 640  # YOLOv8默认输入尺寸
    result_dir = 'E:/data/VOCdevkit/model'
    model_name = 'yolov8_model'
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 设置随机种子
    torch.manual_seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(42)

    # 开始训练
    results = train_yolo(
        voc_root=data_dir,
        epochs=epochs,
        batch_size=batch_size,
        img_size=img_size,
        device=device
    )