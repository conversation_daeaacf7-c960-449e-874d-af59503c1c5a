2025-05-23 15:13:46,669 - QLearningTF_CartPole_main - INFO - Logging to file: ./results/qlearning_tf_local_run\QLearningTF_CartPole_main_training_20250523_151346.log
2025-05-23 15:13:46,669 - QLearningTF_CartPole_main - INFO - Executing Q-Learning TF Local Mode. Args: {'env_name': 'CartPole-v1', 'result_dir': './results/qlearning_tf_local_run', 'model_name': 'QLearningTF_CartPole', 'hidden_size': 64, 'learning_rate': 0.001, 'gamma': 0.99, 'epsilon_start': 1.0, 'epsilon_end': 0.01, 'epsilon_decay': 0.995, 'buffer_size': 10000, 'batch_size': 64, 'num_episodes': 3, 'max_steps_per_episode': 200, 'random_seed': None, 'save_freq': 50, 'plot_freq': 20, 'target_update_freq': 10, 'tf_log_level': 'ERROR', 'log_level': 'INFO', 'mode': 'train', 'test_qnetwork_weights_path': None, 'test_hyperparams_path': None, 'test_episodes': 5, 'render_test': False}
2025-05-23 15:13:49,313 - QLearningTF_CartPole_main - INFO - Script finished successfully.
