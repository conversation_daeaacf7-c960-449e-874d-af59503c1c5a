# 🚀 BiLSTM推理服务 - 优化版

## 📋 概述

基于优化的 `BiLSTMPredictor.py` 构建的BiLSTM推理服务，支持：

- ✅ **轻量化部署** - 简化依赖，快速构建
- ✅ **多模型支持** - 支持动态模型切换
- ✅ **Seldon兼容** - 完全支持Seldon Core部署  
- ✅ **生产就绪** - 健康检查、错误处理、日志记录

## 🏗️ 快速开始

### 本地开发
```bash
# 安装依赖
pip install -r requirements.txt

# 测试运行
python BiLSTMPredictor.py --mode test

# 启动服务
python BiLSTMPredictor.py --mode server
```

### Docker部署
```bash
# 构建镜像
docker build -t bilstm-inference:latest .

# 启动服务
docker run -p 5000:5000 bilstm-inference:latest
```

### Seldon部署
```bash
# 查看推理镜像配置
cd inference-image/
kubectl apply -f seldon-deployment.yaml
```

## 📁 文件结构（优化后）

```
pt基础镜像/
├── BiLSTMPredictor.py         # 🎯 主推理服务文件
├── Dockerfile                 # 🐳 Docker构建文件
├── requirements.txt           # 📦 核心依赖
├── requirements.extended.txt  # 📦 可选依赖
├── .dockerignore             # 🚫 Docker忽略文件
├── README.md                 # 📚 说明文档
├── DEPLOYMENT_SUMMARY.md     # 📝 部署总结
└── inference-image/          # 🏗️ 推理镜像目录
    ├── Dockerfile            # 🐳 推理镜像构建
    ├── build.sh             # 🔨 构建脚本
    ├── start.py             # 🚀 启动脚本
    ├── seldon-deployment.yaml # ⚙️ Seldon配置
    └── models/              # 📁 模型文件目录
```

## 🎯 核心特性

### BiLSTMPredictor.py 功能
- 🔄 **动态模型加载** - 支持运行时切换模型
- 📊 **多数据集支持** - 内置多种预设配置
- 🛡️ **健壮错误处理** - 完善的异常处理机制
- 📈 **状态监控** - 健康检查和系统监控
- 🔌 **API兼容** - 支持REST和Seldon Core接口

### 优化改进
- ⚡ **依赖精简** - 从96个依赖减少到约20个核心依赖
- 🏃 **快速构建** - 构建时间大幅缩短
- 💾 **镜像瘦身** - 去除6.2GB基础镜像和127MB安装包
- 🎯 **专用化** - 专门针对BiLSTM推理优化

## 💡 API接口

### 基础接口
```bash
# 健康检查
curl http://localhost:5000/health

# 获取服务信息
curl http://localhost:5000/

# 获取可用模型列表
curl http://localhost:5000/models
```

### 模型管理
```bash
# 切换模型
curl -X POST http://localhost:5000/models/switch \
  -H "Content-Type: application/json" \
  -d '{"model_name": "BiLSTM_NetworkTraffic"}'

# 重新加载当前模型
curl -X POST http://localhost:5000/models/reload
```

### 推理预测
```bash
# 单样本预测
curl -X POST http://localhost:5000/predict \
  -H "Content-Type: application/json" \
  -d '{"data": [[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]]}'

# Seldon格式预测
curl -X POST http://localhost:5000/predict \
  -H "Content-Type: application/json" \
  -d '{"data": [[0.1, 0.2, 0.3]], "meta": {"seldon_format": true}}'
```

## 🔧 配置选项

### 环境变量
```bash
MODEL_NAME=PyTorchModel_v1           # 模型名称
DATASET_TYPE=network_traffic         # 数据集类型
MODEL_DIR=/app                       # 模型目录
API_TYPE=REST                        # API类型
SERVICE_TYPE=MODEL                   # 服务类型
```

### 支持的数据集
- `network_traffic` - 网络流量分析 (84维输入, 6分类)
- `iris` - 鸢尾花分类 (4维输入, 3分类)  
- `wine` - 红酒分类 (13维输入, 3分类)
- `mnist` - 手写数字识别 (784维输入, 10分类)

## 📈 性能优化

### 镜像大小对比
- **优化前**: 6.2GB+ (包含大型基础镜像)
- **优化后**: ~2GB (精简依赖)

### 构建时间对比  
- **优化前**: 20-30分钟 (大量依赖编译)
- **优化后**: 5-10分钟 (核心依赖)

### 依赖数量对比
- **优化前**: 96个包 (全功能)
- **优化后**: ~20个包 (核心功能)

## 🚀 下一步

1. **添加模型** - 在 `inference-image/models/` 目录添加新模型
2. **自定义配置** - 修改 `DATASET_CONFIGS` 添加新数据集支持
3. **扩展功能** - 安装 `requirements.extended.txt` 获得更多功能
4. **生产部署** - 使用 `inference-image/` 进行Seldon部署

## 📚 详细文档

- `inference-image/README.md` - 推理镜像详细指南
- `DEPLOYMENT_SUMMARY.md` - 完整部署文档 