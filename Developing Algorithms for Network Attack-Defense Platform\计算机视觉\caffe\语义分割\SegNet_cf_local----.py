import os
import sys
import argparse
import json
import uuid
import numpy as np
import caffe
from caffe import layers as L
from caffe import params as P
import lmdb
from PIL import Image
from tqdm import tqdm
import requests
from minio import Minio

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

# Headers constants
MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type" 
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_USER_ID_KEY = "MLSS-UserID"

def create_segnet(net, num_classes):
    # The network definition using Caffe layers

    # First Encoder Block
    net.conv1_1 = L.Convolution(net.data, kernel_size=3, num_output=64, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu1_1 = L.ReLU(net.conv1_1, in_place=True)
    net.conv1_2 = L.Convolution(net.relu1_1, kernel_size=3, num_output=64, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu1_2 = L.ReLU(net.conv1_2, in_place=True)
    net.pool1 = L.Pooling(net.relu1_2, pool=P.Pooling.MAX, kernel_size=2, stride=2)

    # Second Encoder Block  
    net.conv2_1 = L.Convolution(net.pool1, kernel_size=3, num_output=128, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu2_1 = L.ReLU(net.conv2_1, in_place=True)
    net.conv2_2 = L.Convolution(net.relu2_1, kernel_size=3, num_output=128, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu2_2 = L.ReLU(net.conv2_2, in_place=True)
    net.pool2 = L.Pooling(net.relu2_2, pool=P.Pooling.MAX, kernel_size=2, stride=2)

    # Third Encoder Block
    net.conv3_1 = L.Convolution(net.pool2, kernel_size=3, num_output=256, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu3_1 = L.ReLU(net.conv3_1, in_place=True)
    net.conv3_2 = L.Convolution(net.relu3_1, kernel_size=3, num_output=256, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu3_2 = L.ReLU(net.conv3_2, in_place=True)
    net.conv3_3 = L.Convolution(net.relu3_2, kernel_size=3, num_output=256, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu3_3 = L.ReLU(net.conv3_3, in_place=True)
    net.pool3 = L.Pooling(net.relu3_3, pool=P.Pooling.MAX, kernel_size=2, stride=2)

    # Fourth Encoder Block
    net.conv4_1 = L.Convolution(net.pool3, kernel_size=3, num_output=512, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu4_1 = L.ReLU(net.conv4_1, in_place=True)
    net.conv4_2 = L.Convolution(net.relu4_1, kernel_size=3, num_output=512, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu4_2 = L.ReLU(net.conv4_2, in_place=True)
    net.conv4_3 = L.Convolution(net.relu4_2, kernel_size=3, num_output=512, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu4_3 = L.ReLU(net.conv4_3, in_place=True)
    net.pool4 = L.Pooling(net.relu4_3, pool=P.Pooling.MAX, kernel_size=2, stride=2)

    # Fifth Encoder Block
    net.conv5_1 = L.Convolution(net.pool4, kernel_size=3, num_output=512, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu5_1 = L.ReLU(net.conv5_1, in_place=True)
    net.conv5_2 = L.Convolution(net.relu5_1, kernel_size=3, num_output=512, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu5_2 = L.ReLU(net.conv5_2, in_place=True)
    net.conv5_3 = L.Convolution(net.relu5_2, kernel_size=3, num_output=512, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu5_3 = L.ReLU(net.conv5_3, in_place=True)
    net.pool5 = L.Pooling(net.relu5_3, pool=P.Pooling.MAX, kernel_size=2, stride=2)

    # Fifth Decoder Block
    net.upsample5 = L.Deconvolution(net.pool5, convolution_param=dict(num_output=512, kernel_size=2, stride=2))
    net.conv5_3_D = L.Convolution(net.upsample5, kernel_size=3, num_output=512, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu5_3_D = L.ReLU(net.conv5_3_D, in_place=True)
    net.conv5_2_D = L.Convolution(net.relu5_3_D, kernel_size=3, num_output=512, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu5_2_D = L.ReLU(net.conv5_2_D, in_place=True)
    net.conv5_1_D = L.Convolution(net.relu5_2_D, kernel_size=3, num_output=512, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu5_1_D = L.ReLU(net.conv5_1_D, in_place=True)

    # Fourth Decoder Block
    net.upsample4 = L.Deconvolution(net.relu5_1_D, convolution_param=dict(num_output=512, kernel_size=2, stride=2))
    net.conv4_3_D = L.Convolution(net.upsample4, kernel_size=3, num_output=512, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu4_3_D = L.ReLU(net.conv4_3_D, in_place=True)
    net.conv4_2_D = L.Convolution(net.relu4_3_D, kernel_size=3, num_output=512, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu4_2_D = L.ReLU(net.conv4_2_D, in_place=True)
    net.conv4_1_D = L.Convolution(net.relu4_2_D, kernel_size=3, num_output=256, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu4_1_D = L.ReLU(net.conv4_1_D, in_place=True)

    # Third Decoder Block
    net.upsample3 = L.Deconvolution(net.relu4_1_D, convolution_param=dict(num_output=256, kernel_size=2, stride=2))
    net.conv3_3_D = L.Convolution(net.upsample3, kernel_size=3, num_output=256, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu3_3_D = L.ReLU(net.conv3_3_D, in_place=True)
    net.conv3_2_D = L.Convolution(net.relu3_3_D, kernel_size=3, num_output=256, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu3_2_D = L.ReLU(net.conv3_2_D, in_place=True)
    net.conv3_1_D = L.Convolution(net.relu3_2_D, kernel_size=3, num_output=128, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu3_1_D = L.ReLU(net.conv3_1_D, in_place=True)

    # Second Decoder Block
    net.upsample2 = L.Deconvolution(net.relu3_1_D, convolution_param=dict(num_output=128, kernel_size=2, stride=2))
    net.conv2_2_D = L.Convolution(net.upsample2, kernel_size=3, num_output=128, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu2_2_D = L.ReLU(net.conv2_2_D, in_place=True)
    net.conv2_1_D = L.Convolution(net.relu2_2_D, kernel_size=3, num_output=64, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu2_1_D = L.ReLU(net.conv2_1_D, in_place=True)

    # First Decoder Block
    net.upsample1 = L.Deconvolution(net.relu2_1_D, convolution_param=dict(num_output=64, kernel_size=2, stride=2))
    net.conv1_2_D = L.Convolution(net.upsample1, kernel_size=3, num_output=64, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    net.relu1_2_D = L.ReLU(net.conv1_2_D, in_place=True)
    net.conv1_1_D = L.Convolution(net.relu1_2_D, kernel_size=3, num_output=num_classes, pad=1,
        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2,衰变_mult=0)])

    # Loss Layer
    net.loss = L.SoftmaxWithLoss(net.conv1_1_D, net.label)

    return net

def create_solver(solver_file, train_net_path, test_net_path=None):
    s = caffe.proto.caffe_pb2.SolverParameter()
    s.train_net = train_net_path
    if test_net_path is not None:
        s.test_net.append(test_net_path)
        s.test_interval = 1000
        s.test_iter.append(100)

    s.max_iter = 100000
    s.base_lr = 0.01
    s.momentum = 0.9
    s.weight_decay = 0.0005
    s.lr_policy = 'step'
    s.stepsize = 20000
    s.gamma = 0.1
    s.display = 20
    s.snapshot = 5000
    s.snapshot_prefix = 'segnet'
    s.solver_mode = caffe.proto.caffe_pb2.SolverParameter.GPU

    with open(solver_file, 'w') as f:
        f.write(str(s))

def prepare_lmdb(data_dir, output_dir, image_size):
    """Convert dataset to LMDB format for Caffe"""

    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Create LMDB environment
    env_data = lmdb.open(os.path.join(output_dir, "train-data-lmdb"), map_size=1e12)
    env_label = lmdb.open(os.path.join(output_dir, "train-label-lmdb"), map_size=1e12)

    # Prepare data and labels for LMDB
    data_transform = caffe.io.Transformer({'data': (1, 3, image_size, image_size)})
    data_transform.set_transpose('data', (2, 0, 1))
    data_transform.set_mean('data', np.array([104, 107, 123]))
    data_transform.set_raw_scale('data', 255)
    data_transform.set_channel_swap('data', (2, 1, 0))

    label_transform = caffe.io.Transformer({'label': (1, 1, image_size, image_size)})
    label_transform.set_transpose('label', (2, 0, 1))

    image_paths = []
    label_paths = []
    for root, dirs, files in os.walk(data_dir):
        for file in files:
            if file.endswith('.jpg') or file.endswith('.png'):
                image_path = os.path.join(root, file)
                image_paths.append(image_path)
                label_path = os.path.join(root, file.replace('.jpg', '.png').replace('.png', '.png'))
                label_paths.append(label_path)

    with env_data.begin(write=True) as txn_data, env_label.begin(write=True) as txn_label:
        for i, (image_path, label_path) in enumerate(zip(image_paths, label_paths)):
            image = caffe.io.load_image(image_path)
            label = caffe.io.load_image(label_path, is_color=False)

            image_data = data_transform.preprocess('data', image)
            label_data = label_transform.preprocess('label', label)

            key = '{:0>10d}'.format(i)
            txn_data.put(key.encode('ascii'), image_data.tobytes())
            txn_label.put(key.encode('ascii'), label_data.tobytes())

    env_data.close()
    env_label.close()

def train_model(train_net_path, solver_file):
    caffe.set_mode_gpu()
    solver = caffe.get_solver(solver_file)
    net = caffe.Net(train_net_path, caffe.TEST)

    for epoch in range(solver.max_iter):
        solver.step(1)
        if epoch % solver.display == 0:
            loss = net.blobs['loss'].data
            print('Epoch {}: Loss {}'.format(epoch, loss))

def test_model(test_net_path):
    caffe.set_mode_gpu()
    net = caffe.Net(test_net_path, caffe.TEST)
    num_images = 0
    total_iou = 0

    for i in range(len(net.blobs['data'].data)):
        net.forward()
        prediction = net.blobs['conv1_1_D'].data.argmax(axis=1)
        label = net.blobs['label'].data

        intersection = np.logical_and