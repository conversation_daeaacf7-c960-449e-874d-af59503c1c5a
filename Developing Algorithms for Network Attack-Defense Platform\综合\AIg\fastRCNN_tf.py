import tensorflow as tf
import numpy as np
import json
import xml.etree.ElementTree as ET
import pickle
import pandas as pd
from tqdm import tqdm
from PIL import Image
import os
import uuid

# 定义FastRCNNForDetection类，对应原来的PyTorch模型结构
class FastRCNNForDetection(tf.keras.Model):
    def __init__(self, num_classes):
        super(FastRCNNForDetection, self).__init__()

        # 使用预训练的ResNet50（这里假设已经有对应的TensorFlow实现方式来加载预训练权重）
        self.base_network = tf.keras.applications.ResNet50(include_top=False, weights='imagenet')

        # 移除最后的全连接层
        self.features = tf.keras.Sequential(self.base_network.layers[:-2])

        # RoI池化层（这里假设已经有对应的TensorFlow实现方式，例如使用tf.image.crop_and_resize）
        self.roi_pool = None  # 需要根据具体的TensorFlow实现替换

        # 分类器和边界框回归器
        self.classifier = tf.keras.Sequential([
            tf.keras.layers.Dense(4096, activation='relu'),
            tf.keras.layers.Dropout(0.5),
            tf.keras.layers.Dense(4096, activation='relu'),
            tf.keras.layers.Dropout(0.5)
        ])
        self.cls_score = tf.keras.layers.Dense(num_classes)
        self.bbox_pred = tf.keras.layers.Dense(num_classes * 4)

    def call(self, images, rois):
        features = self.features(images)

        pooled_features = []
        for i, image_rois in enumerate(rois):
            image_rois = tf.convert_to_tensor(image_rois, dtype=tf.float32)

            # 添加batch索引（这里的实现方式可能需要根据具体情况调整）
            batch_index = tf.fill((tf.shape(image_rois)[0], 1), i)
            image_rois = tf.concat([batch_index, image_rois], axis=1)

            pooled_features.append(self.roi_pool(features[i][tf.newaxis,...], image_rois))

        pooled_features = tf.concat(pooled_features, axis=0)
        flattened_features = tf.reshape(pooled_features, (tf.shape(pooled_features)[0], -1))
        fc_features = self.classifier(flattened_features)
        class_scores = self.cls_score(fc_features)
        bbox_preds = self.bbox_pred(fc_features)

        return class_scores, bbox_preds

# 定义UniversalImageDataset类，用于加载不同类型数据集
class UniversalImageDataset(tf.keras.utils.Sequence):
    def __init__(self, data_dir, image_size, dataset_type='folder', annotations_file=None):
        self.data_dir = data_dir
        self.image_size = image_size
        self.dataset_type = dataset_type
        self.annotations_file = annotations_file

        self.classes = ['__background__', 'aeroplane', 'bicycle', 'bird', 'boat', 'bottle', 'bus', 'car',
                        'cat', 'chair', 'cow', 'diningtable', 'dog', 'horse', 'motorbike',
                        'person', 'pottedplant', 'sheep', 'sofa', 'train', 'tvmonitor']
        self.class_to_idx = {cls: idx for idx, cls in enumerate(self.classes)}

        if dataset_type in ['folder', 'imagenet']:
            self.image_paths, self.labels = self.load_from_folder()
        elif dataset_type == 'coco':
            self.image_paths, self.labels = self.load_coco(annotations_file)
        elif dataset_type == 'voc':
            self.image_paths, self.labels = self.load_voc(annotations_file)
        elif dataset_type == 'yolo':
            self.image_paths, self.labels = self.load_yolo(annotations_file)
        elif dataset_type == 'pickle':
            self.image_paths, self.labels = self.load_pickle(annotations_file)
        else:
            raise ValueError("Unsupported dataset type.")

    def load_from_folder(self):
        classes = os.listdir(self.data_dir)
        class_to_idx = {cls: idx for idx, cls in enumerate(classes)}

        image_paths = []
        labels = []

        for cls in classes:
            class_dir = os.path.join(self.data_dir, cls)
            if os.path.isdir(class_dir):
                for img_file in os.listdir(class_dir):
                    if img_file.endswith(('.jpg', '.jpeg', '.png')):
                        img_path = os.path.join(class_dir, img_file)
                        image_paths.append(img_path)
                        labels.append(class_to_idx[cls])

        return image_paths, labels

    def load_coco(self, annotations_file):
        with open(annotations_file) as f:
            annotations = json.load(f)

        image_paths = []
        labels = []
        for item in annotations['images']:
            img_id = item['id']
            img_file = os.path.join(self.data_dir, item['file_name'])
            image_paths.append(img_file)
            label = self.get_label_for_image(img_id, annotations)
            labels.append(label)

        return image_paths, labels

    def load_voc(self, annotations_file):
        image_paths = []
        labels = []

        # 遍历文件夹中的所有XML文件
        for xml_file in os.listdir(annotations_file):
            if xml_file.endswith('.xml'):
                full_path = os.path.join(annotations_file, xml_file)

                tree = ET.parse(full_path)
                root = tree.getroot()

                # 获取图像文件路径
                image_name = root.find('filename').text
                img_path = os.path.join(self.data_dir, image_name)
                image_paths.append(img_path)

                # 提取标签
                objects = root.findall('object')
                boxes = []
                for obj in object:
                    class_name = obj.find('name').text
                    bbox = obj.find('bndbox')
                    xmin = float(bbox.find('xmin').text)
                    ymin = float(bbox.find('ymin').text)
                    xmax = float(bbox.find('xmax').text)
                    ymax = float(bbox.find('ymax').text)
                    boxes.append((class_name, xmin, ymin, xmax, ymax))

                labels.append(boxes)

        return image_paths, labels

    def load_yolo(self):
        image_paths = []
        labels = []

        for img_file in os.listdir(self.data_dir):
            if img_file.endswith(('.jpg', '.png', '.jpeg')):
                img_path = os.path.join(self.data_dir, img_file)
                image_paths.append(img_path)

                # 加载对应的YOLO标签文件
                label_file = img_file.replace('.jpg', '.txt').replace('.png', '.txt').replace('.jpeg', '.txt')
                label_path = os.path.join(self.data_dir, label_file)

                if os.path.exists(label_path):
                    with open(label_path, 'r') as f:
                        boxes = []
                        for line in f.readlines():
                            class_id, x_center, y_center, width, height = map(float, line.strip().split())
                            boxes.append((class_id, x_center, y_center, width, height))
                        labels.append(boxes)  # 以边界框列表形式存储
                else:
                    labels.append([])  # 无标签时返回空列表

        return image_paths, labels

    def load_pickle(self, pkl_file):
        # 从.pkl文件加载数据
        with open(pkl_file, 'rb') as f:
            data = pickle.load(f)

        # 假设数据为字典格式，包含特征和标签
        if isinstance(data, dict):
            images = data['images']  # 假设图像数据在 'images' 键下
            labels = data['labels']    # 假设标签在 'labels' 键下
        elif isinstance(data, pd.DataFrame):
            images = data['image_paths'].tolist()  # 假设图像路径在某列
            labels = data['labels'].tolist()        # 假设标签在某列
        else:
            raise ValueError("Unsupported data format in pickle file.")

        return images, labels

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        image = Image.open(img_path).convert("RGB")
        image = image.resize((self.image_size, self.image_size))
        image = tf.keras.preprocessing.image.img_to_array(image)
        image = tf.expand_dims(image, axis=0)

        label = self.labels[idx]

        # 为Fast R-CNN准备标签格式
        boxes = [box[1:] for box in label]
        labels = [self.class_to_idx[box[0]] for box in label]

        target = {
            'boxes': boxes,
            'labels': labels
        }

        return image, target

# 定义数据整理函数，对应原来的collate_fn
def collate_fn(batch):
    images = []
    targets = []
    for image, target in batch:
        images.append(image)

        # 确保边界框格式正确：[x1, y1, x2, y2]
        boxes = tf.convert_to_tensor(target['boxes'], dtype=tf.float32)
        if boxes.shape[1] == 4:  # 如果只有坐标，不需要修改
            pass
        elif boxes.shape[1] == 5:  # 如果包含类别信息，去掉类别
            boxes = boxes[:, 1:]
        else:
            raise ValueError(f"Unexpected box format: {boxes.shape}")

        targets.append({
            'boxes': boxes,
            'labels': tf.convert_to_tensor(target['labels'], dtype=tf.int64)
        })

    return images, targets

# 定义FastRCNNLoss类，对应原来的损失函数计算
class FastRCNNLoss(tf.keras.losses.Loss):
    def __init__(self):
        super(FastRCNNLoss, self).__init__()
        self.cls_loss = tf.keras.losses.CategoricalCrossentropy(from_logits=True)
        self.bbox_loss = tf.keras.losses.Huber()

    def call(self, class_scores, bbox_preds, targets):
        # 收集所有标签
        cls_targets = []
        bbox_targets = []

        for target in targets:
            cls_targets.extend(target['labels'])
            bbox_targets.append(target['boxes'])

        cls_targets = tf.convert_to_tensor(cls_targets, dtype=tf.int64)
        bbox_targets = tf.concat(bbox_targets, axis=0)

        # 计算分类损失
        cls_loss = self.cls_loss(class_scores, cls_targets)

        # 计算边界框回归损失
        # 只对正样本（非背景）计算边界框损失
        positive_indices = cls_targets > 0
        if positive_indices.any():
            bbox_loss = self.bbox_loss(bbox_preds[positive_indices], bbox_targets[positive_indices])
        else:
            bbox_loss = tf.constant(0.0)

        total_loss = cls_loss + bbox_loss

        return total_loss

# 定义训练模型函数，对应原来的train_model
def train_model(train_loader, model, criterion, optimizer, num_epochs, device):
    for epoch in range(num_epochs):
        model.train()
        running_loss = 0.0

        pbar = tqdm(train_loader, desc=f"Epoch {epoch + 1}/{num_epochs}")
        for images, targets in pbar:
            with tf.GradientTape() as tape:
                # 前向传播
                class_scores, bbox_preds = model(images, targets['boxes'])

                # 计算损失
                loss = criterion(class_scores, bbox_preds, targets)

            # 反向传播
            gradients = tape.gradient(loss, model.trainable_variables)
            optimizer.apply_gradients(zip(gradients, model.trainable_variables))

            running_loss += loss.numpy()
            pbar.set_postfix({'loss': f'{loss.numpy():.4f}'})

        epoch_loss = running_loss / len(train_loader)
        print(f'Epoch [{epoch + 1}/{num_epochs}], Loss: {epoch_loss:.4f}')

# 定义测试模型函数，对应原来的test_model
def test_model(test_loader, model, device):
    model.eval()
    correct = 0
    total = 0

    for images, targets in tqdm(test_loader, desc="Testing"):
        class_scores, bbox_preds = model(images, targets['boxes'])

        _, predicted = tf.math.argmax(class_scores, axis=1)
        total += sum(len(t['labels']) for t in targets)
        correct += sum((predicted == t['labels']).numpy().sum() for t in targets)

    print(f'准确率: {100 * correct / total:.2f}%')

# 定义主函数，对应原来的fastRCNN_train
def fastRCNN_train(input_file, dataset_type, input_size, annotations_file, num_classes, num_epochs, learning_rate, batch_size, result_dir, model_name, device):
    # 准备数据
    train_loader = UniversalImageDataset(input_file, input_size, dataset_type, annotations_file)
    test_loader = UniversalImageDataset(input_file, input_size, dataset_type, annotations_file)

    train_loader = tf.data.Dataset.from_generator(
        lambda: train_loader,
        output_types=(tf.float32, {'boxes': tf.float32, 'labels': tf.int64})
    )
    test_loader = tf.data.Dataset.from_generator(
        lambda: test_loader,
        output_types=(tf.float32, {'boxes': tf.float32, 'labels': tf.int64})
    )

    train_loader = train_loader.batch(batch_size).prefetch(tf.data.experimental.AUTOPREFETCH)
    test_loader = test_loader.batch(batch_size).prefetch(tf.data.experimental.AUTOPREFETCH)

    # 初始化模型
    model = FastRCNNForDetection(num_classes)

    # 使用正确的损失函数
    criterion = FastRCNNLoss()

    # 优化器
    optimizer = tf.keras.optimizers.Adam(learning_rate=learning_rate)

    # 训练模型
    try:
        train_model(train_loader, model, criterion, optimizer, num_epochs, device)
        test_model(test_loader, model, device)

        # 保存模型
        os.makedirs(result_dir, exist_ok=True)
        model_path = os.path.join(result_dir, f"{model_name}.h5")
        model.save(model_path)
        print(f'训练完成，模型保存到 {model_path}')

    except Exception as e:
        print(f"训练过程中发生错误: {e}")

if __name__ == "__main__":
    # 设置参数
    data_dir = 'E:/data/VOCdevkit/VOC2007'
    input_file = os.path.join(data_dir, 'JPEGImages')
    annotations_file = os.path.join(data_dir, "Annotations")
    input_size = 224  # 输入图像大小（DenseNet 121 默认大小）
    dataset_type = "voc"
    num_classes = 21
    epochs = 10
    learning_rate = 0.001
    batch_size = 4
    result_dir = 'E:/data/VOCdevkit/model'
    model_name = 'fastRCNN_tf'
    device = 'GPU:0' if tf.test.is_gpu_available() else 'CPU'

    # 设置随机种子以确保可重复性
    tf.random.set_seed(42)

    fastRCNN_train(input_file, dataset_type, input_size, annotations_file,
                   num_classes, epochs, learning_rate, batch_size,
                   result_dir, model_name, device)