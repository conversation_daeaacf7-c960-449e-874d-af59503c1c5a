{"model_type": "train_test_split_pt_graphsage", "final_model_state_path": "E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\results\\results_graphsage_pt_local\\models\\GraphSAGE_PT_Local_Model_reported.pth", "final_model_config_path": "E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\results\\results_graphsage_pt_local\\models\\GraphSAGE_PT_Local_Model_reported_config.json", "test_metrics": {"accuracy": 0.866978006510708, "precision_weighted": 0.8476824480564911, "recall_weighted": 0.866978006510708, "f1_weighted": 0.8571616394496042, "classification_report_str": "                  precision    recall  f1-score   support\n\n          BENIGN       0.89      0.92      0.90     88006\n   DoS GoldenEye       0.00      0.00      0.00      2059\n        DoS Hulk       0.83      0.84      0.84     46215\nDoS Slowhttptest       0.48      0.58      0.52      1100\n   DoS slowloris       0.00      0.00      0.00      1159\n      Heartbleed       0.00      0.00      0.00         2\n\n        accuracy                           0.87    138541\n       macro avg       0.37      0.39      0.38    138541\n    weighted avg       0.85      0.87      0.86    138541\n"}, "training_history": {"train_loss": [0.48920734667622184, 0.44249240796579975, 0.43575256097685067], "train_acc": [82.27413644385577, 83.95685738105463, 84.21544602480863], "val_loss": [0.3812506007497985, 0.37278102934065793, 0.3641604472444956], "val_acc": [85.39277181484182, 85.35595960762518, 86.6978006510708]}, "class_names": ["BENIGN", "DoS GoldenEye", "DoS Hulk", "DoS Slowhttptest", "DoS slowloris", "Heartbleed"], "preprocessing_info_path": "E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\results\\results_graphsage_pt_local\\GraphSAGE_PT_Local_Model_preproc_info.json"}