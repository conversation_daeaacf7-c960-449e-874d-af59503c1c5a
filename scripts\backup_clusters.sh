#!/bin/bash

# =======================================================
# 集群备份脚本
# 功能: 备份PVE、Hadoop、K8S集群的配置和数据
# 作者: 系统管理员
# 版本: 1.0
# =======================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 备份配置
BACKUP_BASE_DIR="/backup"
BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="$BACKUP_BASE_DIR/cluster_backup_$BACKUP_DATE"
LOG_FILE="$BACKUP_DIR/backup.log"

# 保留备份天数
RETENTION_DAYS=7

# 集群配置
HADOOP_NODES=("hadoop-node1" "hadoop-node2" "hadoop-node3" "hadoop-node4" "hadoop-node5" "hadoop-node6")
K8S_NODES=("k8s-master" "k8s-worker1" "k8s-worker2" "k8s-worker3" "k8s-worker4")
PVE_HOST="localhost"

# 记录日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 打印标题
print_header() {
    echo -e "${BLUE}=================================${NC}"
    echo -e "${BLUE}    $1${NC}"
    echo -e "${BLUE}=================================${NC}"
}

# 创建备份目录
create_backup_dirs() {
    local dirs=(
        "$BACKUP_DIR"
        "$BACKUP_DIR/pve"
        "$BACKUP_DIR/hadoop"
        "$BACKUP_DIR/hadoop/configs"
        "$BACKUP_DIR/hadoop/metadata" 
        "$BACKUP_DIR/k8s"
        "$BACKUP_DIR/k8s/configs"
        "$BACKUP_DIR/k8s/etcd"
        "$BACKUP_DIR/vms"
        "$BACKUP_DIR/logs"
    )
    
    for dir in "${dirs[@]}"; do
        if mkdir -p "$dir"; then
            log "Created backup directory: $dir"
        else
            log "ERROR: Failed to create directory: $dir"
            exit 1
        fi
    done
}

# 备份PVE配置
backup_pve() {
    print_header "备份 PVE 配置"
    
    local pve_backup_dir="$BACKUP_DIR/pve"
    local error_count=0
    
    # 备份PVE集群配置
    echo -e "${YELLOW}备份PVE集群配置...${NC}"
    if cp -r /etc/pve "$pve_backup_dir/pve_config" 2>/dev/null; then
        echo -e "${GREEN}✓${NC} PVE集群配置备份成功"
        log "SUCCESS: PVE cluster config backed up"
    else
        echo -e "${RED}✗${NC} PVE集群配置备份失败"
        log "ERROR: Failed to backup PVE cluster config"
        ((error_count++))
    fi
    
    # 备份网络配置
    echo -e "${YELLOW}备份网络配置...${NC}"
    if cp /etc/network/interfaces "$pve_backup_dir/interfaces" 2>/dev/null; then
        echo -e "${GREEN}✓${NC} 网络配置备份成功"
        log "SUCCESS: Network config backed up"
    else
        echo -e "${RED}✗${NC} 网络配置备份失败"
        log "ERROR: Failed to backup network config"
        ((error_count++))
    fi
    
    # 导出虚拟机列表
    echo -e "${YELLOW}导出虚拟机列表...${NC}"
    if pvesh get /cluster/resources --type vm --output-format json > "$pve_backup_dir/vm_list.json" 2>/dev/null; then
        echo -e "${GREEN}✓${NC} 虚拟机列表导出成功"
        log "SUCCESS: VM list exported"
    else
        echo -e "${RED}✗${NC} 虚拟机列表导出失败"
        log "ERROR: Failed to export VM list"
        ((error_count++))
    fi
    
    # 导出存储配置
    echo -e "${YELLOW}导出存储配置...${NC}"
    if pvesh get /storage --output-format json > "$pve_backup_dir/storage_config.json" 2>/dev/null; then
        echo -e "${GREEN}✓${NC} 存储配置导出成功"
        log "SUCCESS: Storage config exported"
    else
        echo -e "${RED}✗${NC} 存储配置导出失败"
        log "ERROR: Failed to export storage config"
        ((error_count++))
    fi
    
    # 备份防火墙规则
    echo -e "${YELLOW}备份防火墙规则...${NC}"
    if cp -r /etc/pve/firewall "$pve_backup_dir/firewall_rules" 2>/dev/null; then
        echo -e "${GREEN}✓${NC} 防火墙规则备份成功"
        log "SUCCESS: Firewall rules backed up"
    else
        echo -e "${RED}✗${NC} 防火墙规则备份失败"
        log "ERROR: Failed to backup firewall rules"
        ((error_count++))
    fi
    
    if [ $error_count -eq 0 ]; then
        echo -e "\n${GREEN}PVE 备份完成${NC}"
        log "PVE backup completed successfully"
    else
        echo -e "\n${RED}PVE 备份出现 $error_count 个错误${NC}"
        log "PVE backup completed with $error_count errors"
    fi
    
    return $error_count
}

# 备份Hadoop集群
backup_hadoop() {
    print_header "备份 Hadoop 集群"
    
    local hadoop_backup_dir="$BACKUP_DIR/hadoop"
    local error_count=0
    
    # 备份Hadoop配置文件
    echo -e "${YELLOW}备份Hadoop配置文件...${NC}"
    for node in "${HADOOP_NODES[@]}"; do
        echo "备份 $node 配置文件..."
        local node_config_dir="$hadoop_backup_dir/configs/$node"
        mkdir -p "$node_config_dir"
        
        if ssh "$node" "tar czf - /opt/hadoop/etc/hadoop /opt/hive/conf /opt/spark/conf 2>/dev/null" | tar xzf - -C "$node_config_dir" 2>/dev/null; then
            echo -e "${GREEN}✓${NC} $node: 配置文件备份成功"
            log "SUCCESS: Config files backed up from $node"
        else
            echo -e "${RED}✗${NC} $node: 配置文件备份失败"
            log "ERROR: Failed to backup config files from $node"
            ((error_count++))
        fi
    done
    
    # 备份HDFS元数据
    echo -e "\n${YELLOW}备份HDFS元数据...${NC}"
    if ssh hadoop-node1 "sudo cp -r /opt/hadoop/dfs/name/current $hadoop_backup_dir/metadata/namenode_metadata" 2>/dev/null; then
        echo -e "${GREEN}✓${NC} NameNode元数据备份成功"
        log "SUCCESS: NameNode metadata backed up"
    else
        echo -e "${RED}✗${NC} NameNode元数据备份失败"
        log "ERROR: Failed to backup NameNode metadata"
        ((error_count++))
    fi
    
    # 导出HDFS文件系统映像
    echo -e "${YELLOW}导出HDFS文件系统映像...${NC}"
    if ssh hadoop-node1 "hdfs dfsadmin -saveNamespace" >/dev/null 2>&1; then
        if ssh hadoop-node1 "hdfs dfsadmin -fetchImage $hadoop_backup_dir/metadata/fsimage" >/dev/null 2>&1; then
            echo -e "${GREEN}✓${NC} HDFS文件系统映像导出成功"
            log "SUCCESS: HDFS filesystem image exported"
        else
            echo -e "${RED}✗${NC} HDFS文件系统映像导出失败"
            log "ERROR: Failed to export HDFS filesystem image"
            ((error_count++))
        fi
    else
        echo -e "${RED}✗${NC} 无法保存命名空间"
        log "ERROR: Failed to save namespace"
        ((error_count++))
    fi
    
    # 备份ZooKeeper数据
    echo -e "${YELLOW}备份ZooKeeper数据...${NC}"
    for i in {0..2}; do
        local node="${HADOOP_NODES[$i]}"
        echo "备份 $node ZooKeeper数据..."
        
        if ssh "$node" "tar czf - /opt/zookeeper/data 2>/dev/null" | tar xzf - -C "$hadoop_backup_dir/metadata" 2>/dev/null; then
            mv "$hadoop_backup_dir/metadata/opt/zookeeper/data" "$hadoop_backup_dir/metadata/zookeeper_$node"
            echo -e "${GREEN}✓${NC} $node: ZooKeeper数据备份成功"
            log "SUCCESS: ZooKeeper data backed up from $node"
        else
            echo -e "${RED}✗${NC} $node: ZooKeeper数据备份失败"
            log "ERROR: Failed to backup ZooKeeper data from $node"
            ((error_count++))
        fi
    done
    
    # 备份Hive元数据
    echo -e "${YELLOW}备份Hive元数据...${NC}"
    if ssh hadoop-node1 "mysqldump -u hive -p hive_metastore > $hadoop_backup_dir/metadata/hive_metastore.sql" 2>/dev/null; then
        echo -e "${GREEN}✓${NC} Hive元数据备份成功"
        log "SUCCESS: Hive metastore backed up"
    else
        echo -e "${RED}✗${NC} Hive元数据备份失败"
        log "ERROR: Failed to backup Hive metastore"
        ((error_count++))
    fi
    
    # 列出重要HDFS目录
    echo -e "${YELLOW}导出HDFS目录结构...${NC}"
    if ssh hadoop-node1 "hdfs dfs -ls -R / > $hadoop_backup_dir/hdfs_directory_structure.txt" 2>/dev/null; then
        echo -e "${GREEN}✓${NC} HDFS目录结构导出成功"
        log "SUCCESS: HDFS directory structure exported"
    else
        echo -e "${RED}✗${NC} HDFS目录结构导出失败"
        log "ERROR: Failed to export HDFS directory structure"
        ((error_count++))
    fi
    
    if [ $error_count -eq 0 ]; then
        echo -e "\n${GREEN}Hadoop 集群备份完成${NC}"
        log "Hadoop cluster backup completed successfully"
    else
        echo -e "\n${RED}Hadoop 集群备份出现 $error_count 个错误${NC}"
        log "Hadoop cluster backup completed with $error_count errors"
    fi
    
    return $error_count
}

# 备份Kubernetes集群
backup_k8s() {
    print_header "备份 Kubernetes 集群"
    
    local k8s_backup_dir="$BACKUP_DIR/k8s"
    local error_count=0
    
    # 备份etcd数据
    echo -e "${YELLOW}备份etcd数据...${NC}"
    if ssh k8s-master "sudo etcdctl snapshot save $k8s_backup_dir/etcd/etcd-snapshot-$BACKUP_DATE.db --endpoints=https://127.0.0.1:2379 --cacert=/etc/kubernetes/pki/etcd/ca.crt --cert=/etc/kubernetes/pki/etcd/server.crt --key=/etc/kubernetes/pki/etcd/server.key" 2>/dev/null; then
        echo -e "${GREEN}✓${NC} etcd数据备份成功"
        log "SUCCESS: etcd data backed up"
    else
        echo -e "${RED}✗${NC} etcd数据备份失败"
        log "ERROR: Failed to backup etcd data"
        ((error_count++))
    fi
    
    # 备份Kubernetes配置文件
    echo -e "${YELLOW}备份Kubernetes配置文件...${NC}"
    for node in "${K8S_NODES[@]}"; do
        echo "备份 $node 配置文件..."
        local node_config_dir="$k8s_backup_dir/configs/$node"
        mkdir -p "$node_config_dir"
        
        if ssh "$node" "tar czf - /etc/kubernetes /var/lib/kubelet/config.yaml 2>/dev/null" | tar xzf - -C "$node_config_dir" 2>/dev/null; then
            echo -e "${GREEN}✓${NC} $node: 配置文件备份成功"
            log "SUCCESS: Config files backed up from $node"
        else
            echo -e "${RED}✗${NC} $node: 配置文件备份失败"
            log "ERROR: Failed to backup config files from $node"
            ((error_count++))
        fi
    done
    
    # 导出所有资源对象
    echo -e "${YELLOW}导出Kubernetes资源对象...${NC}"
    local resources=(
        "namespaces"
        "configmaps"
        "secrets" 
        "services"
        "deployments"
        "daemonsets"
        "statefulsets"
        "persistentvolumes"
        "persistentvolumeclaims"
        "ingresses"
        "networkpolicies"
        "serviceaccounts"
        "roles"
        "rolebindings"
        "clusterroles"
        "clusterrolebindings"
    )
    
    for resource in "${resources[@]}"; do
        echo "导出 $resource..."
        if kubectl get "$resource" --all-namespaces -o yaml > "$k8s_backup_dir/configs/all-$resource.yaml" 2>/dev/null; then
            echo -e "${GREEN}✓${NC} $resource 导出成功"
            log "SUCCESS: $resource exported"
        else
            echo -e "${RED}✗${NC} $resource 导出失败"
            log "ERROR: Failed to export $resource"
            ((error_count++))
        fi
    done
    
    # 备份网络插件配置
    echo -e "${YELLOW}备份网络插件配置...${NC}"
    if kubectl get pods -n kube-system -l k8s-app=calico-node -o yaml > "$k8s_backup_dir/configs/calico-config.yaml" 2>/dev/null; then
        echo -e "${GREEN}✓${NC} Calico网络配置备份成功"
        log "SUCCESS: Calico network config backed up"
    elif kubectl get pods -n kube-system -l app=flannel -o yaml > "$k8s_backup_dir/configs/flannel-config.yaml" 2>/dev/null; then
        echo -e "${GREEN}✓${NC} Flannel网络配置备份成功"
        log "SUCCESS: Flannel network config backed up"
    else
        echo -e "${RED}✗${NC} 网络插件配置备份失败"
        log "ERROR: Failed to backup network plugin config"
        ((error_count++))
    fi
    
    # 导出节点信息
    echo -e "${YELLOW}导出节点信息...${NC}"
    if kubectl describe nodes > "$k8s_backup_dir/nodes-info.txt" 2>/dev/null; then
        echo -e "${GREEN}✓${NC} 节点信息导出成功"
        log "SUCCESS: Node information exported"
    else
        echo -e "${RED}✗${NC} 节点信息导出失败"
        log "ERROR: Failed to export node information"
        ((error_count++))
    fi
    
    if [ $error_count -eq 0 ]; then
        echo -e "\n${GREEN}Kubernetes 集群备份完成${NC}"
        log "Kubernetes cluster backup completed successfully"
    else
        echo -e "\n${RED}Kubernetes 集群备份出现 $error_count 个错误${NC}"
        log "Kubernetes cluster backup completed with $error_count errors"
    fi
    
    return $error_count
}

# 备份虚拟机
backup_vms() {
    print_header "备份虚拟机"
    
    local vm_backup_dir="$BACKUP_DIR/vms"
    local error_count=0
    
    echo -e "${YELLOW}创建虚拟机快照...${NC}"
    
    # 获取所有虚拟机列表
    local vm_list=$(pvesh get /cluster/resources --type vm --output-format json | jq -r '.[] | select(.type=="qemu") | "\(.vmid):\(.name)"')
    
    while IFS=':' read -r vmid vmname; do
        if [[ -n "$vmid" && -n "$vmname" ]]; then
            echo "为虚拟机 $vmname (ID: $vmid) 创建快照..."
            
            local snapshot_name="backup-$BACKUP_DATE"
            if pvesh create "/nodes/$PVE_HOST/qemu/$vmid/snapshot" -snapname "$snapshot_name" -description "Automated backup snapshot" 2>/dev/null; then
                echo -e "${GREEN}✓${NC} $vmname: 快照创建成功"
                log "SUCCESS: Snapshot created for VM $vmname ($vmid)"
                
                # 记录快照信息
                echo "$vmid:$vmname:$snapshot_name" >> "$vm_backup_dir/snapshots.txt"
            else
                echo -e "${RED}✗${NC} $vmname: 快照创建失败"
                log "ERROR: Failed to create snapshot for VM $vmname ($vmid)"
                ((error_count++))
            fi
        fi
    done <<< "$vm_list"
    
    if [ $error_count -eq 0 ]; then
        echo -e "\n${GREEN}虚拟机备份完成${NC}"
        log "VM backup completed successfully"
    else
        echo -e "\n${RED}虚拟机备份出现 $error_count 个错误${NC}"
        log "VM backup completed with $error_count errors"
    fi
    
    return $error_count
}

# 收集系统日志
collect_logs() {
    print_header "收集系统日志"
    
    local logs_dir="$BACKUP_DIR/logs"
    local error_count=0
    
    # 收集PVE日志
    echo -e "${YELLOW}收集PVE日志...${NC}"
    local pve_logs=("/var/log/pve-cluster.log" "/var/log/pveproxy/access.log" "/var/log/pvedaemon.log")
    
    for log_file in "${pve_logs[@]}"; do
        if [[ -f "$log_file" ]]; then
            cp "$log_file" "$logs_dir/$(basename "$log_file")"
            echo -e "${GREEN}✓${NC} $(basename "$log_file") 收集成功"
            log "SUCCESS: Collected $(basename "$log_file")"
        else
            echo -e "${RED}✗${NC} $(basename "$log_file") 不存在"
            log "WARNING: $(basename "$log_file") does not exist"
        fi
    done
    
    # 收集各节点系统日志
    echo -e "\n${YELLOW}收集节点系统日志...${NC}"
    for node in "${HADOOP_NODES[@]}" "${K8S_NODES[@]}"; do
        echo "收集 $node 系统日志..."
        local node_log_dir="$logs_dir/$node"
        mkdir -p "$node_log_dir"
        
        # 收集关键系统日志
        if ssh "$node" "journalctl --since='24 hours ago' --no-pager" > "$node_log_dir/system.log" 2>/dev/null; then
            echo -e "${GREEN}✓${NC} $node: 系统日志收集成功"
            log "SUCCESS: System logs collected from $node"
        else
            echo -e "${RED}✗${NC} $node: 系统日志收集失败"
            log "ERROR: Failed to collect system logs from $node"
            ((error_count++))
        fi
    done
    
    # 收集Kubernetes日志
    echo -e "\n${YELLOW}收集Kubernetes日志...${NC}"
    if kubectl logs --all-containers=true --tail=1000 -n kube-system > "$logs_dir/k8s-system-pods.log" 2>/dev/null; then
        echo -e "${GREEN}✓${NC} Kubernetes系统Pod日志收集成功"
        log "SUCCESS: Kubernetes system pod logs collected"
    else
        echo -e "${RED}✗${NC} Kubernetes系统Pod日志收集失败"
        log "ERROR: Failed to collect Kubernetes system pod logs"
        ((error_count++))
    fi
    
    if [ $error_count -eq 0 ]; then
        echo -e "\n${GREEN}日志收集完成${NC}"
        log "Log collection completed successfully"
    else
        echo -e "\n${RED}日志收集出现 $error_count 个错误${NC}"
        log "Log collection completed with $error_count errors"
    fi
    
    return $error_count
}

# 创建备份压缩包
create_backup_archive() {
    print_header "创建备份压缩包"
    
    local archive_name="cluster_backup_$BACKUP_DATE.tar.gz"
    local archive_path="$BACKUP_BASE_DIR/$archive_name"
    
    echo -e "${YELLOW}压缩备份文件...${NC}"
    
    if tar czf "$archive_path" -C "$BACKUP_BASE_DIR" "cluster_backup_$BACKUP_DATE"; then
        local archive_size=$(du -h "$archive_path" | cut -f1)
        echo -e "${GREEN}✓${NC} 备份压缩包创建成功: $archive_name (大小: $archive_size)"
        log "SUCCESS: Backup archive created: $archive_name (size: $archive_size)"
        
        # 计算校验和
        local checksum=$(sha256sum "$archive_path" | cut -d' ' -f1)
        echo "$checksum  $archive_name" > "$archive_path.sha256"
        echo -e "${GREEN}✓${NC} 校验和文件创建成功"
        log "SUCCESS: Checksum file created"
        
        return 0
    else
        echo -e "${RED}✗${NC} 备份压缩包创建失败"
        log "ERROR: Failed to create backup archive"
        return 1
    fi
}

# 清理旧备份
cleanup_old_backups() {
    print_header "清理旧备份"
    
    echo -e "${YELLOW}清理 $RETENTION_DAYS 天前的备份...${NC}"
    
    local deleted_count=0
    
    # 删除旧的备份目录
    find "$BACKUP_BASE_DIR" -maxdepth 1 -type d -name "cluster_backup_*" -mtime +$RETENTION_DAYS -print0 | while IFS= read -r -d '' dir; do
        echo "删除旧备份目录: $(basename "$dir")"
        rm -rf "$dir"
        ((deleted_count++))
        log "CLEANUP: Deleted old backup directory: $(basename "$dir")"
    done
    
    # 删除旧的压缩包
    find "$BACKUP_BASE_DIR" -maxdepth 1 -type f -name "cluster_backup_*.tar.gz*" -mtime +$RETENTION_DAYS -print0 | while IFS= read -r -d '' file; do
        echo "删除旧备份文件: $(basename "$file")"
        rm -f "$file"
        ((deleted_count++))
        log "CLEANUP: Deleted old backup file: $(basename "$file")"
    done
    
    echo -e "${GREEN}✓${NC} 清理完成，删除了旧备份文件"
    log "CLEANUP: Completed cleanup of old backups"
}

# 生成备份报告
generate_backup_report() {
    print_header "备份报告"
    
    local total_errors=$1
    local report_file="$BACKUP_DIR/backup_report.txt"
    
    # 创建报告
    cat > "$report_file" << EOF
=====================================
集群备份报告
=====================================

备份时间: $(date)
备份目录: $BACKUP_DIR
日志文件: $LOG_FILE

备份内容:
- PVE配置和虚拟机信息
- Hadoop集群配置和元数据  
- Kubernetes集群配置和etcd数据
- 虚拟机快照
- 系统日志

备份结果:
EOF

    if [ $total_errors -eq 0 ]; then
        echo "状态: 成功" >> "$report_file"
        echo "所有备份任务都已成功完成" >> "$report_file"
    else
        echo "状态: 部分成功" >> "$report_file"
        echo "发现 $total_errors 个错误，请检查日志文件" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF

备份文件大小:
$(du -sh "$BACKUP_DIR")

建议操作:
1. 验证备份文件完整性
2. 测试恢复流程
3. 将备份文件传输到异地存储
4. 定期清理旧备份文件

EOF

    # 显示报告
    cat "$report_file"
    
    echo ""
    echo "备份报告已保存到: $report_file"
    log "Backup report generated: $report_file"
}

# 显示使用帮助
show_help() {
    echo "集群备份脚本使用说明:"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -p, --pve           仅备份PVE配置"
    echo "  -H, --hadoop        仅备份Hadoop集群"
    echo "  -k, --k8s           仅备份Kubernetes集群"
    echo "  -v, --vms           仅备份虚拟机快照"
    echo "  -l, --logs          仅收集日志"
    echo "  -a, --all           完整备份 (默认)"
    echo "  -d, --dir DIR       指定备份目录 (默认: $BACKUP_BASE_DIR)"
    echo "  -r, --retention N   备份保留天数 (默认: $RETENTION_DAYS)"
    echo ""
    echo "示例:"
    echo "  $0                  # 完整备份"
    echo "  $0 -p              # 仅备份PVE"
    echo "  $0 -H -k           # 备份Hadoop和K8S"
    echo "  $0 -d /custom/backup # 使用自定义备份目录"
}

# 主函数
main() {
    local backup_pve=false
    local backup_hadoop=false
    local backup_k8s=false
    local backup_vms=false
    local backup_logs=false
    local backup_all=true
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -p|--pve)
                backup_pve=true
                backup_all=false
                shift
                ;;
            -H|--hadoop)
                backup_hadoop=true
                backup_all=false
                shift
                ;;
            -k|--k8s)
                backup_k8s=true
                backup_all=false
                shift
                ;;
            -v|--vms)
                backup_vms=true
                backup_all=false
                shift
                ;;
            -l|--logs)
                backup_logs=true
                backup_all=false
                shift
                ;;
            -a|--all)
                backup_all=true
                shift
                ;;
            -d|--dir)
                BACKUP_BASE_DIR="$2"
                BACKUP_DIR="$BACKUP_BASE_DIR/cluster_backup_$BACKUP_DATE"
                LOG_FILE="$BACKUP_DIR/backup.log"
                shift 2
                ;;
            -r|--retention)
                RETENTION_DAYS="$2"
                shift 2
                ;;
            *)
                echo "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    echo -e "${BLUE}开始集群备份操作...${NC}"
    
    # 创建备份目录
    create_backup_dirs
    
    log "Starting cluster backup operations"
    log "Backup directory: $BACKUP_DIR"
    
    local total_errors=0
    
    # 执行备份任务
    if [[ "$backup_all" == true ]] || [[ "$backup_pve" == true ]]; then
        backup_pve
        total_errors=$((total_errors + $?))
        echo ""
    fi
    
    if [[ "$backup_all" == true ]] || [[ "$backup_hadoop" == true ]]; then
        backup_hadoop
        total_errors=$((total_errors + $?))
        echo ""
    fi
    
    if [[ "$backup_all" == true ]] || [[ "$backup_k8s" == true ]]; then
        backup_k8s
        total_errors=$((total_errors + $?))
        echo ""
    fi
    
    if [[ "$backup_all" == true ]] || [[ "$backup_vms" == true ]]; then
        backup_vms
        total_errors=$((total_errors + $?))
        echo ""
    fi
    
    if [[ "$backup_all" == true ]] || [[ "$backup_logs" == true ]]; then
        collect_logs
        total_errors=$((total_errors + $?))
        echo ""
    fi
    
    # 创建压缩包
    if [[ "$backup_all" == true ]]; then
        create_backup_archive
        total_errors=$((total_errors + $?))
        echo ""
    fi
    
    # 清理旧备份
    cleanup_old_backups
    echo ""
    
    # 生成报告
    generate_backup_report $total_errors
    
    log "Cluster backup operations completed with $total_errors total errors"
    
    exit $total_errors
}

# 检查依赖工具
check_dependencies() {
    local missing_tools=()
    
    for tool in ssh pvesh kubectl jq tar; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        echo -e "${RED}错误: 缺少必要工具: ${missing_tools[*]}${NC}"
        echo "请安装缺少的工具后重新运行"
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    check_dependencies
    main "$@"
fi 