# Git和版本控制
.git
.gitignore
.gitattributes

# 文档和说明
README.md
*.md
docs/

# 临时文件
*.tmp
*.temp
*.log
*.swp
*.swo
*~

# Python缓存
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
env/
ENV/

# IDE文件
.vscode/
.idea/
*.sublime-*
.DS_Store
Thumbs.db

# 测试和覆盖率
.coverage
.pytest_cache/
.tox/
htmlcov/

# 不需要的脚本和工具
build.sh
test.py
*.test.py 