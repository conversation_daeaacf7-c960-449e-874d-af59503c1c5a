{"options_used": {"test_size": 0.2, "random_state": 42, "normalize": true, "scaler_type": "standard", "handle_imbalance": false, "stratify_split": true, "numeric_impute_strategy": "mean", "categorical_impute_strategy": "most_frequent"}, "numeric_imputer": "SimpleImputer()", "categorical_imputer": "SimpleImputer(strategy='most_frequent')", "label_encoder": "LabelEncoder()", "classes": ["BENIGN", "DoS GoldenEye", "DoS Hulk", "DoS Slowhttptest", "DoS slowloris", "Heartbleed"], "categorical_encoder": "OrdinalEncoder(handle_unknown='use_encoded_value', unknown_value=-1)", "categorical_columns_encoded": ["Flow ID", "Source IP", "Destination IP", "Timestamp"], "feature_scaler": "StandardScaler()"}