import pickle
import requests
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
from torchvision import datasets, transforms
import torchvision.transforms.functional as TF
from torchvision import models
from PIL import Image
import xml.etree.ElementTree as ET
import pandas as pd
from torchvision.models.detection import fasterrcnn_resnet50_fpn
from torchvision.ops import RoIPool
from tqdm.auto import tqdm

class FastRCNNForDetection(nn.Module):
    def __init__(self, num_classes):
        super(FastRCNNForDetection, self).__init__()
        # 使用预训练的 ResNet50 作为基础网络
        self.base_network = models.resnet50(pretrained=True)
        
        # 加载本地的预训练模型参数
        #self.base_network.load_state_dict(torch.load(pretrained_model_path))
        
        # 移除最后的全连接层
        self.features = nn.Sequential(*list(self.base_network.children())[:-2])
        
        # RoI池化层
        self.roi_pool = RoIPool(output_size=(7, 7), spatial_scale=1/16)
        
        # 分类器和边界框回归器
        self.classifier = nn.Sequential(
            nn.Linear(2048 * 7 * 7, 4096),
            nn.ReLU(inplace=True),
            nn.Dropout(),
            nn.Linear(4096, 4096),
            nn.ReLU(inplace=True),
            nn.Dropout()
        )
        self.cls_score = nn.Linear(4096, num_classes)
        self.bbox_pred = nn.Linear(4096, num_classes * 4)

    def forward(self, images, rois):
        if not isinstance(images, torch.Tensor):
            images = torch.stack(images)
        features = self.features(images)
        
        pooled_features = []
        for i, image_rois in enumerate(rois):
            # 确保 image_rois 是正确的形状
            if not isinstance(image_rois, torch.Tensor):
                image_rois = torch.tensor(image_rois, dtype=torch.float32)
            
            # 添加 batch 索引
            batch_index = torch.full((image_rois.size(0), 1), i, dtype=torch.float32, device=image_rois.device)
            image_rois = torch.cat([batch_index, image_rois], dim=1)
            
            pooled_features.append(self.roi_pool(features[i].unsqueeze(0), image_rois))
        
        pooled_features = torch.cat(pooled_features, dim=0)
        flattened_features = pooled_features.view(pooled_features.size(0), -1)
        fc_features = self.classifier(flattened_features)
        class_scores = self.cls_score(fc_features)
        bbox_preds = self.bbox_pred(fc_features)
        return class_scores, bbox_preds

class UniversalImageDataset(Dataset):
    def __init__(self, data_dir, transform=None, dataset_type='folder', annotations_file = None):
        self.data_dir = data_dir 
        self.transform = transform
        self.dataset_type = dataset_type
        self.annotations_file = annotations_file

        self.classes = ['__background__', 'aeroplane', 'bicycle', 'bird', 'boat', 'bottle', 'bus', 'car', 
                        'cat', 'chair', 'cow', 'diningtable', 'dog', 'horse', 'motorbike', 
                        'person', 'pottedplant', 'sheep', 'sofa', 'train', 'tvmonitor']
        self.class_to_idx = {cls: idx for idx, cls in enumerate(self.classes)}

        if dataset_type in ['folder', 'imagenet']:
            self.image_paths, self.labels = self.load_from_folder()
        elif dataset_type == 'coco':
            self.image_paths, self.labels = self.load_coco(annotations_file)
        elif dataset_type == 'voc':
            self.image_paths, self.labels = self.load_voc(annotations_file)
        elif dataset_type == 'yolo':
            self.image_paths, self.labels = self.load_yolo(annotations_file)
        elif dataset_type == 'pickle':
            self.image_paths, self.labels = self.load_pickle(annotations_file)
        else:
            raise ValueError("Unsupported dataset type.")

    def load_from_folder(self):
        classes = os.listdir(self.data_dir)
        class_to_idx = {cls: idx for idx, cls in enumerate(classes)}
        
        image_paths = []
        labels = []

        for cls in classes:
            class_dir = os.path.join(self.data_dir, cls)
            if os.path.isdir(class_dir):
                for img_file in os.listdir(class_dir):
                    if img_file.endswith(('.jpg', '.jpeg', '.png')):
                        img_path = os.path.join(class_dir, img_file)
                        image_paths.append(img_path)
                        labels.append(class_to_idx[cls])

        return image_paths, labels

    def load_coco(self, annotations_file):
        with open(annotations_file) as f:
            annotations = json.load(f)

        image_paths = []
        labels = []
        for item in annotations['images']:
            img_id = item['id']
            img_file = os.path.join(self.data_dir, item['file_name'])
            image_paths.append(img_file)
            label = self.get_label_for_image(img_id, annotations)
            labels.append(label)

        return image_paths, labels

    def load_voc(self, annotations_file):
        image_paths = []
        labels = []

        # 遍历文件夹中的所有 XML 文件
        for xml_file in os.listdir(annotations_file):
            if xml_file.endswith('.xml'):
                full_path = os.path.join(annotations_file, xml_file)
                
                tree = ET.parse(full_path)
                root = tree.getroot()

                # 获取图像文件路径
                image_name = root.find('filename').text
                img_path = os.path.join(self.data_dir, image_name)
                image_paths.append(img_path)

                # 提取标签
                objects = root.findall('object')
                boxes = []
                for obj in objects:
                    class_name = obj.find('name').text
                    bbox = obj.find('bndbox')
                    xmin = float(bbox.find('xmin').text)
                    ymin = float(bbox.find('ymin').text)
                    xmax = float(bbox.find('xmax').text)
                    ymax = float(bbox.find('ymax').text)
                    boxes.append((class_name, xmin, ymin, xmax, ymax))

                labels.append(boxes)

        return image_paths, labels

    def load_yolo(self):
        image_paths = []
        labels = []

        for img_file in os.listdir(self.data_dir):
            if img_file.endswith(('.jpg', '.png', '.jpeg')):
                img_path = os.path.join(self.data_dir, img_file)
                image_paths.append(img_path)

                # 加载对应的YOLO标签文件
                label_file = img_file.replace('.jpg', '.txt').replace('.png', '.txt').replace('.jpeg', '.txt')
                label_path = os.path.join(self.data_dir, label_file)

                if os.path.exists(label_path):
                    with open(label_path, 'r') as f:
                        boxes = []
                        for line in f.readlines():
                            class_id, x_center, y_center, width, height = map(float, line.strip().split())
                            boxes.append((class_id, x_center, y_center, width, height))
                        labels.append(boxes)  # 以边界框列表形式存储
                else:
                    labels.append([])  # 无标签时返回空列表

        return image_paths, labels
    
    def load_pickle(self, pkl_file):
        # 从 .pkl 文件加载数据
        with open(pkl_file, 'rb') as f:
            data = pickle.load(f)

        # 假设数据为字典格式，包含特征和标签
        if isinstance(data, dict):
            images = data['images']  # 假设图像数据在 'images' 键下
            labels = data['labels']    # 假设标签在 'labels' 键下
        elif isinstance(data, pd.DataFrame):
            images = data['image_paths'].tolist()  # 假设图像路径在某列
            labels = data['labels'].tolist()        # 假设标签在某列
        else:
            raise ValueError("Unsupported data format in pickle file.")

        return images, labels
    
    def __len__(self):
        if hasattr(self, 'dataset'):
            return len(self.dataset)
        return len(self.image_paths)

    # def __getitem__(self, idx):
    #     if hasattr(self, 'dataset'):
    #         image, label = self.dataset[idx]
    #     else:
    #         img_path = self.image_paths[idx]
    #         image = Image.open(img_path).convert("RGB")
    #         label = self.labels[idx]

    #     if self.transform:
    #         image = self.transform(image)

    #     return image, label
    
    # def __getitem__(self, idx):
    #     img_path = self.image_paths[idx]
    #     image = Image.open(img_path).convert("RGB")
    #     label = self.labels[idx]

    #     if self.transform:
    #         image = self.transform(image)

    #     # 为 Fast R-CNN 准备标签格式
    #     boxes = torch.tensor([box[1:] for box in label], dtype=torch.float32)
    #     labels = torch.tensor([self.class_to_idx[box[0]] for box in label], dtype=torch.int64)

    #     target = {
    #         'boxes': boxes,
    #         'labels': labels
    #     }

    #     return image, target

    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        image = Image.open(img_path).convert("RGB")
        label = self.labels[idx]

        if self.transform:
            image = self.transform(image)

        # 为 Fast R-CNN 准备标签格式
        boxes = [box[1:] for box in label]
        labels = [self.class_to_idx[box[0]] for box in label]

        target = {
            'boxes': boxes,
            'labels': labels
        }

        return image, target
    
def prepare_data(data_dir, dataset_type, batch_size, image_size, annotations_file):
    transform = transforms.Compose([
        transforms.Resize((image_size, image_size)),
        transforms.ToTensor(),
    ])

    dataset = UniversalImageDataset(data_dir, transform=transform, dataset_type=dataset_type, annotations_file=annotations_file)

    train_size = int(0.8 * len(dataset))
    test_size = len(dataset) - train_size
    train_dataset, test_dataset = torch.utils.data.random_split(dataset, [train_size, test_size])

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, collate_fn=collate_fn)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False, collate_fn=collate_fn)
    
    return train_loader, test_loader

# def collate_fn(batch):
#     images = []
#     targets = []
#     for image, target in batch:
#         images.append(image)
#         targets.append(target)
#     return images, targets

def collate_fn(batch):
    images = []
    targets = []
    for image, target in batch:
        images.append(image)
        # 确保边界框格式正确：[x1, y1, x2, y2]
        boxes = torch.tensor(target['boxes'], dtype=torch.float32)
        if boxes.size(1) == 4:  # 如果只有坐标，不需要修改
            pass
        elif boxes.size(1) == 5:  # 如果包含类别信息，去掉类别
            boxes = boxes[:, 1:]
        else:
            raise ValueError(f"Unexpected box format: {boxes.size()}")
        
        targets.append({
            'boxes': boxes,
            'labels': torch.tensor(target['labels'], dtype=torch.int64)
        })
    return images, targets

class FastRCNNLoss(nn.Module):
    def __init__(self):
        super(FastRCNNLoss, self).__init__()
        self.cls_loss = nn.CrossEntropyLoss()
        self.bbox_loss = nn.SmoothL1Loss()

    def forward(self, class_scores, bbox_preds, targets):
        # 收集所有标签
        cls_targets = []
        bbox_targets = []
        
        for target in targets:
            cls_targets.extend(target['labels'].tolist())
            bbox_targets.append(target['boxes'])
        
        cls_targets = torch.tensor(cls_targets, device=class_scores.device)
        bbox_targets = torch.cat(bbox_targets, dim=0)
        
        # 计算分类损失
        cls_loss = self.cls_loss(class_scores, cls_targets)
        
        # 计算边界框回归损失
        # 只对正样本（非背景）计算边界框损失
        positive_indices = cls_targets > 0
        if positive_indices.any():
            bbox_loss = self.bbox_loss(bbox_preds[positive_indices], bbox_targets[positive_indices])
        else:
            bbox_loss = torch.tensor(0.0, device=class_scores.device)
        
        total_loss = cls_loss + bbox_loss
        return total_loss

def train_model(train_loader, model, criterion, optimizer, num_epochs, device):
    model.to(device)
    
    for epoch in range(num_epochs):
        model.train()
        running_loss = 0.0
        
        pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs}")
        for images, targets in pbar:
            # 将图像和目标移到设备上
            images = [img.to(device) for img in images]
            targets = [{k: v.to(device) for k, v in t.items()} for t in targets]
            
            # 清零梯度
            optimizer.zero_grad()
            
            # 使用真实边界框作为 proposals
            proposals = [t['boxes'] for t in targets]
            
            try:
                # 前向传播
                class_scores, bbox_preds = model(images, proposals)
                
                # 计算损失
                loss = criterion(class_scores, bbox_preds, targets)
                
                # 反向传播
                loss.backward()
                
                # 更新参数
                optimizer.step()
                
                running_loss += loss.item()
                pbar.set_postfix({'loss': f'{loss.item():.4f}'})
                
            except RuntimeError as e:
                print(f"Error during training: {e}")
                continue
        
        epoch_loss = running_loss / len(train_loader)
        print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}')


def test_model(test_loader, model, device):
    model.to(device)
    model.eval()
    correct = 0
    total = 0

    with torch.no_grad():
        for images, targets in tqdm(test_loader, desc="Testing"):
            images = [img.to(device) for img in images]
            targets = [{k: v.to(device) for k, v in t.items()} for t in targets]

            proposals = [t['boxes'] for t in targets]
            
            class_scores, bbox_preds = model(torch.stack(images), proposals)
            
            _, predicted = torch.max(class_scores, 1)
            total += sum(len(t['labels']) for t in targets)
            correct += sum((predicted == t['labels']).sum().item() for t in targets)

    print(f'准确率: {100 * correct / total:.2f}%')

def fastRCNN_train(input_file, dataset_type, input_size, annotations_file, num_classes, num_epochs, learning_rate, batch_size, result_dir, model_name, device):
    # 准备数据
    train_loader, test_loader = prepare_data(input_file, dataset_type, batch_size, input_size, annotations_file)
    
    # 初始化模型
    model = FastRCNNForDetection(num_classes)
    
    # 使用正确的损失函数
    criterion = FastRCNNLoss()
    
    # 优化器
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    
    # 训练模型
    try:
        train_model(train_loader, model, criterion, optimizer, num_epochs, device)
        test_model(test_loader, model, device)
        
        # 保存模型
        os.makedirs(result_dir, exist_ok=True)
        model_path = os.path.join(result_dir, f"{model_name}.pth")
        torch.save(model.state_dict(), model_path)
        print(f'训练完成，模型保存到 {model_path}')
        
    except Exception as e:
        print(f"训练过程中发生错误: {e}")


if __name__ == "__main__":
    # 设置参数
    data_dir = 'E:/data/VOCdevkit/VOC2007'
    input_file = os.path.join(data_dir, 'JPEGImages')
    annotations_file = os.path.join(data_dir, "Annotations")
    input_size = 224  # 输入图像大小（DenseNet 121 默认大小）
    dataset_type = "voc"
    num_classes = 21
    epochs = 10
    learning_rate = 0.001
    batch_size = 4
    result_dir = 'E:/data/VOCdevkit/model'
    model_name = 'fastRCNN_pt'
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 设置随机种子以确保可重复性
    torch.manual_seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(42)
    
    fastRCNN_train(input_file, dataset_type, input_size, annotations_file, 
                   num_classes, epochs, learning_rate, batch_size, 
                   result_dir, model_name, device)