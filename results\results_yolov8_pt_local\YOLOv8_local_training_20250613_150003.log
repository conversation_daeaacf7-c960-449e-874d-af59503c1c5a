2025-06-13 15:00:03,343 - YOLOv8_local - INFO - Logging to file: ./results/results_yolov8_pt_local\YOLOv8_local_training_20250613_150003.log
2025-06-13 15:00:03,343 - YOLOv8_local - INFO - YOLOv8 PyTorch Local Mode. Args: {'data_path': './demo_data', 'dataset_type': 'folder', 'annotations_file': None, 'input_size': 640, 'model_name': 'YOLOv8_local', 'model_type': 'nano', 'pretrained_model_path': None, 'mode': 'all', 'num_epochs': 1, 'batch_size': 16, 'conf_thres': 0.25, 'iou_thres': 0.45, 'early_stopping_patience': 30, 'use_cv': False, 'cv_folds': 5, 'trained_model_path': None, 'test_data_path': None, 'annotations_file_test': None, 'result_dir': './results/results_yolov8_pt_local', 'random_seed': 42, 'force_cpu': False, 'log_level': 'INFO'}
2025-06-13 15:00:03,343 - YOLOv8_local - INFO - Using device: cpu
2025-06-13 15:00:03,343 - YOLOv8_local - INFO - Random seed set to: 42
2025-06-13 15:00:03,343 - YOLOv8_local - INFO - --- Training Mode ---
2025-06-13 15:00:03,343 - YOLOv8_local - INFO - Starting Standard Training...
2025-06-13 15:00:30,709 - YOLOv8_local - INFO - Training completed. Model saved: ./results/results_yolov8_pt_local\YOLOv8_local.pt
2025-06-13 15:00:31,291 - YOLOv8_local - INFO - --- Testing Mode ---
2025-06-13 15:00:33,900 - YOLOv8_local - INFO - Testing completed. Test metrics: {'map50': 0.0, 'map50-95': 0.0, 'precision': 0.0, 'recall': 0.0, 'conf_thres': 0.25, 'iou_thres': 0.45}
2025-06-13 15:00:33,900 - YOLOv8_local - INFO - Full run summary saved to: ./results/results_yolov8_pt_local\YOLOv8_local_full_summary.json
