"""ResNet TensorFlow Implementation"""

import tensorflow as tf
import numpy as np
import pickle
import requests
import sys
import argparse
import json
from minio import Minio
import uuid
import os
from PIL import Image
import xml.etree.ElementTree as ET
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.preprocessing import LabelEncoder
from sklearn.preprocessing import OrdinalEncoder
from sklearn.model_selection import train_test_split
from tqdm import tqdm

MINIO_URL = "minio-prophecis.prophecis:9000"

MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"


MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class ResNetForTabular(tf.keras.Model):
    def __init__(self, input_size, num_classes, pretrained_model_path):
        super(ResNetForTabular, self).__init__()
        # 加载预训练的ResNet18
        base_model = tf.keras.applications.ResNet50(
            include_top=False,
            weights=None,
            input_shape=(None, None, input_size)
        )
        
        # 加载预训练权重
        base_model.load_weights(pretrained_model_path)
        
        self.base_model = base_model
        self.global_pool = tf.keras.layers.GlobalAveragePooling2D()
        self.classifier = tf.keras.layers.Dense(num_classes, activation='softmax')

    def call(self, inputs):
        x = self.base_model(inputs)
        x = self.global_pool(x)
        return self.classifier(x)

# 加载数据集
def load_data(pkl_file):
    with open(pkl_file, 'rb') as f:
        data = pickle.load(f, encoding='bytes')
    train_x, train_y = data['train']
    test_x, test_y = data['test']
    return train_x, train_y, test_x, test_y

#读取 .pkl 文件之后加入预处理逻辑，需要先从 .pkl 文件中反序列化数据，然后执行相应的预处理步骤。
def load_data_preprocess(pkl_file):
    # 从pkl文件中加载数据
    with open(pkl_file, 'rb') as file:
        data = pickle.load(file)
    
    # 清理列名，去除可能的空格
    data.columns = data.columns.str.strip()

    print(type(data))  # 检查数据类型
    print(data.head())  # 查看前几行数据
    print(data.info())  # 查看数据信息


    # 假设数据是一个DataFrame格式，或者是特征和标签分别存储的形式
    if isinstance(data, pd.DataFrame):
        print(data.columns)
        X = data.drop(['Label'], axis=1)  # 假设 'Label' 是标签列
        y = data['Label']
    elif isinstance(data, dict):  # 如果是字典形式
        X = data['features']  # 假设特征存储在 'features' 键下
        y = data['labels']    # 假设标签存储在 'labels' 键下
    
    return X, y

def preprocess_data(X, y):
    if X.isnull().any().any():
        X = X.dropna()
        y = y[X.index]
    
    le = LabelEncoder()
    y = le.fit_transform(y)

    categorical_cols = X.select_dtypes(include=['object']).columns
    if len(categorical_cols) > 0:
        encoder = OrdinalEncoder()
        X[categorical_cols] = encoder.fit_transform(X[categorical_cols])
    
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.mean(), inplace=True)
    
    scaler = StandardScaler()
    X = scaler.fit_transform(X)
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    return X_train, X_test, y_train, y_test

def reshape_to_image(X, input_size):
    """TensorFlow版本的reshape函数"""
    num_samples, num_features = X.shape
    side_len = int(np.ceil(np.sqrt(num_features / input_size)))
    padding = side_len ** 2 * input_size - num_features
    X_padded = np.pad(X, ((0, 0), (0, padding)), mode='constant')
    X_reshaped = X_padded.reshape(num_samples, side_len, side_len, input_size)
    return X_reshaped

def prepare_dataset(X_train, y_train, X_test, y_test, input_size):
    X_train = reshape_to_image(X_train, input_size)
    X_test = reshape_to_image(X_test, input_size)
    train_dataset = tf.data.Dataset.from_tensor_slices((X_train, y_train))
    test_dataset = tf.data.Dataset.from_tensor_slices((X_test, y_test))
    return train_dataset, test_dataset

def train_model(train_dataset, model, num_epochs, batch_size):
    optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
    loss_fn = tf.keras.losses.SparseCategoricalCrossentropy()
    
    train_dataset = train_dataset.batch(batch_size).prefetch(tf.data.AUTOTUNE)
    
    # 编译模型
    model.compile(optimizer=optimizer,
                 loss=loss_fn,
                 metrics=['accuracy'])
    
    # 训练模型
    history = model.fit(train_dataset,
                       epochs=num_epochs,
                       verbose=1)
    return history

def test_model(test_dataset, model, batch_size):
    test_dataset = test_dataset.batch(batch_size)
    results = model.evaluate(test_dataset, verbose=1)
    print(f'Test accuracy: {results[1]*100:.2f}%')

def resnet18_train(dataset, job_params, model_name, result_dir, fit_params=None):
    # 参数提取部分保持不变
    input_size = job_params["input_size"]
    output_size = job_params["output_size"]
    num_epochs = job_params["num_epochs"]
    batch_size = job_params["batch_size"]
    
    # 数据加载和预处理
    training_data_path = "/workspace/" + dataset["training_data_path"]
    pretrained_model_path = "/workspace/pretrained_model/resnet18_weights.h5"
    
    X, y = load_data_preprocess(training_data_path)
    X_train, X_test, y_train, y_test = preprocess_data(X, y)
    train_dataset, test_dataset = prepare_dataset(X_train, y_train, X_test, y_test, input_size)
    
    # 创建和训练模型
    model = ResNetForTabular(input_size, output_size, pretrained_model_path)
    train_model(train_dataset, model, num_epochs, batch_size)
    test_model(test_dataset, model, batch_size)
    
    # 保存模型
    model.save(os.path.join(result_dir, f"{model_name}.h5"))
    print(f'Model saved to {result_dir}/{model_name}')

def model_upload(result_dir,model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".h5"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".h5")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "TENSORFLOW",
          "file_name": model_name+".h5",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "Resnet",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='tensorflow ResNet Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='ResNet Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='ResNet DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start ResNet training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("ResNet job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("ResNet dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("ResNet result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("ResNet factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("ResNet fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("ResNet sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
        
    print("Step 1 ResNet training:\n")
    result,ret_code = resnet18_train(dataset,job_params, model["model_name"],result_dir,fit_params)
    if ret_code != 0:
        print("ResNet train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()