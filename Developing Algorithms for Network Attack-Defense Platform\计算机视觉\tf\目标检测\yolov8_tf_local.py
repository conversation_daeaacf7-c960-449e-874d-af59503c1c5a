"""yolov8 tensorflow"""

import pickle
import requests
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import tensorflow as tf
import numpy as np
import yaml
import shutil
from PIL import Image
import xml.etree.ElementTree as ET
import pandas as pd
from tqdm.auto import tqdm

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class UniversalImageDataset(tf.keras.utils.Sequence):
    def __init__(self, data_dir, batch_size=32, transform=None, dataset_type='folder', annotations_file=None):
        self.data_dir = data_dir
        self.batch_size = batch_size
        self.transform = transform
        self.dataset_type = dataset_type
        self.annotations_file = annotations_file

        # YOLO格式的类别列表
        self.classes = ['aeroplane', 'bicycle', 'bird', 'boat', 'bottle', 'bus', 'car', 
                       'cat', 'chair', 'cow', 'diningtable', 'dog', 'horse', 'motorbike', 
                       'person', 'pottedplant', 'sheep', 'sofa', 'train', 'tvmonitor']
        self.class_to_idx = {cls: idx for idx, cls in enumerate(self.classes)}

        if dataset_type == 'voc':
            self.image_paths, self.labels = self.load_voc(annotations_file)
        elif dataset_type == 'coco':
            self.image_paths, self.labels = self.load_coco(annotations_file)
        elif dataset_type == 'yolo':
            self.image_paths, self.labels = self.load_yolo(annotations_file)
        elif dataset_type == 'pickle':
            self.image_paths, self.labels = self.load_pickle(annotations_file)
        else:
            raise ValueError("Unsupported dataset type.")

    def __len__(self):
        return len(self.image_paths) // self.batch_size

    def __getitem__(self, idx):
        batch_paths = self.image_paths[idx * self.batch_size:(idx + 1) * self.batch_size]
        batch_labels = self.labels[idx * self.batch_size:(idx + 1) * self.batch_size]

        # 初始化批次数组
        batch_images = []
        batch_boxes = []

        for img_path, label in zip(batch_paths, batch_labels):
            # 读取和预处理图像
            image = tf.keras.preprocessing.image.load_img(img_path)
            image = tf.keras.preprocessing.image.img_to_array(image)
            
            if self.transform:
                image = self.transform(image)
            
            batch_images.append(image)
            batch_boxes.append(np.array(label))

        return np.array(batch_images), np.array(batch_boxes)

    def load_voc(self, annotations_file):
        """将VOC格式的数据转换为YOLO格式"""
        image_paths = []
        labels = []

        for xml_file in os.listdir(annotations_file):
            if xml_file.endswith('.xml'):
                xml_path = os.path.join(annotations_file, xml_file)
                tree = ET.parse(xml_path)
                root = tree.getroot()

                size = root.find('size')
                width = float(size.find('width').text)
                height = float(size.find('height').text)

                image_name = root.find('filename').text
                img_path = os.path.join(self.data_dir, image_name)
                image_paths.append(img_path)

                label = []
                for obj in root.findall('object'):
                    class_name = obj.find('name').text
                    class_idx = self.class_to_idx[class_name]
                    
                    bbox = obj.find('bndbox')
                    xmin = float(bbox.find('xmin').text)
                    ymin = float(bbox.find('ymin').text)
                    xmax = float(bbox.find('xmax').text)
                    ymax = float(bbox.find('ymax').text)

                    x_center = ((xmin + xmax) / 2) / width
                    y_center = ((ymin + ymax) / 2) / height
                    box_width = (xmax - xmin) / width
                    box_height = (ymax - ymin) / height

                    label.append([class_idx, x_center, y_center, box_width, box_height])
                
                labels.append(label)

        return image_paths, labels

    def load_coco(self, annotations_file):
        """将COCO格式的数据转换为YOLO格式"""
        with open(annotations_file) as f:
            annotations = json.load(f)

        image_info = {img['id']: (img['width'], img['height'], img['file_name']) 
                     for img in annotations['images']}

        image_annotations = {}
        for ann in annotations['annotations']:
            img_id = ann['image_id']
            if img_id not in image_annotations:
                image_annotations[img_id] = []
            
            img_width, img_height, _ = image_info[img_id]
            
            x, y, w, h = ann['bbox']
            x_center = (x + w/2) / img_width
            y_center = (y + h/2) / img_height
            width = w / img_width
            height = h / img_height
            
            category_id = ann['category_id']
            image_annotations[img_id].append([category_id, x_center, y_center, width, height])

        image_paths = []
        labels = []
        for img_id, (_, _, file_name) in image_info.items():
            img_path = os.path.join(self.data_dir, file_name)
            image_paths.append(img_path)
            labels.append(image_annotations.get(img_id, []))

        return image_paths, labels

    def load_yolo(self, annotations_file):
        """直接加载YOLO格式的数据"""
        image_paths = []
        labels = []

        for img_file in os.listdir(self.data_dir):
            if img_file.endswith(('.jpg', '.jpeg', '.png')):
                img_path = os.path.join(self.data_dir, img_file)
                image_paths.append(img_path)

                label_file = os.path.join(annotations_file, 
                                        os.path.splitext(img_file)[0] + '.txt')
                
                if os.path.exists(label_file):
                    with open(label_file, 'r') as f:
                        label = []
                        for line in f:
                            class_id, x_center, y_center, width, height = map(float, line.strip().split())
                            label.append([class_id, x_center, y_center, width, height])
                        labels.append(label)
                else:
                    labels.append([])

        return image_paths, labels

    def load_pickle(self, pkl_file):
        """加载pickle格式的数据"""
        with open(pkl_file, 'rb') as f:
            data = pickle.load(f)

        if isinstance(data, dict):
            return data.get('images', []), data.get('labels', [])
        elif isinstance(data, pd.DataFrame):
            return data['image_paths'].tolist(), data['labels'].tolist()
        else:
            raise ValueError("Unsupported pickle data format")

class YOLOv8(tf.keras.Model):
    def __init__(self, num_classes=80):
        super(YOLOv8, self).__init__()
        # 使用EfficientNet作为backbone
        self.backbone = tf.keras.applications.EfficientNetB0(
            include_top=False,
            weights='imagenet'
        )
        
        # 检测头
        self.detection_head = tf.keras.Sequential([
            tf.keras.layers.Conv2D(512, 3, padding='same'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.LeakyReLU(0.1),
            tf.keras.layers.Conv2D(5 + num_classes, 1, padding='same')
        ])

    def call(self, x):
        features = self.backbone(x)
        return self.detection_head(features)

def yolov8_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """
    TensorFlow版本的YOLOv8训练函数
    """
    input_size = job_params["input_size"]
    dataset_type = job_params["dataset_type"]
    num_epochs = job_params["num_epochs"]
    batch_size = job_params["batch_size"]
    
    training_data_path = "/workspace/" + dataset["training_data_path"]
    yolo_root, yaml_path = convert_voc_to_yolo(training_data_path)

    # 创建数据集
    train_dataset = UniversalImageDataset(
        os.path.join(yolo_root, 'images/train'),
        batch_size=batch_size,
        dataset_type='yolo',
        annotations_file=os.path.join(yolo_root, 'labels/train')
    )

    # 初始化模型
    model = YOLOv8(num_classes=20)  # VOC数据集有20个类别

    # 编译模型
    optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
    loss = tf.keras.losses.MeanSquaredError()  # 简化版损失函数
    model.compile(optimizer=optimizer, loss=loss)

    try:
        # 训练模型
        print("Starting training...")
        history = model.fit(
            train_dataset,
            epochs=num_epochs,
            verbose=1
        )

        # 保存模型
        model.save(os.path.join(result_dir, f"{model_name}.h5"))
        print(f'训练完成，模型保存到 {result_dir}/{model_name}')
        
        return None, 0
    except Exception as e:
        print(f"训练过程中发生错误: {e}")
        return str(e), -1

def convert_voc_to_yolo(voc_root):
    """与原始代码相同，保持不变"""
    # 创建YOLO格式的目录结构
    yolo_root = os.path.join(os.path.dirname(voc_root), 'VOC2007_YOLO')
    os.makedirs(yolo_root, exist_ok=True)
    
    for split in ['images', 'labels']:
        for subset in ['train', 'val', 'test']:
            os.makedirs(os.path.join(yolo_root, split, subset), exist_ok=True)
    
    # 类别列表
    classes = ['aeroplane', 'bicycle', 'bird', 'boat', 'bottle', 'bus', 'car', 
               'cat', 'chair', 'cow', 'diningtable', 'dog', 'horse', 'motorbike', 
               'person', 'pottedplant', 'sheep', 'sofa', 'train', 'tvmonitor']
    class_to_idx = {cls: idx for idx, cls in enumerate(classes)}
    
    # 读取数据集划分
    def read_image_sets(split):
        with open(os.path.join(voc_root, 'ImageSets', 'Main', f'{split}.txt'), 'r') as f:
            return [line.strip() for line in f.readlines()]
    
    train_ids = read_image_sets('train')
    val_ids = read_image_sets('val')
    test_ids = read_image_sets('test')
    
    def convert_annotation(image_id, subset):
        try:
            xml_path = os.path.join(voc_root, 'Annotations', f'{image_id}.xml')
            tree = ET.parse(xml_path)
            root = tree.getroot()
            
            size = root.find('size')
            width = float(size.find('width').text)
            height = float(size.find('height').text)
            
            # 复制图像
            src_img = os.path.join(voc_root, 'JPEGImages', f'{image_id}.jpg')
            dst_img = os.path.join(yolo_root, 'images', subset, f'{image_id}.jpg')
            shutil.copy2(src_img, dst_img)
            
            # 转换标注
            label_path = os.path.join(yolo_root, 'labels', subset, f'{image_id}.txt')
            with open(label_path, 'w') as f:
                for obj in root.findall('object'):
                    class_name = obj.find('name').text
                    class_idx = class_to_idx[class_name]
                    
                    bbox = obj.find('bndbox')
                    xmin = float(bbox.find('xmin').text)
                    ymin = float(bbox.find('ymin').text)
                    xmax = float(bbox.find('xmax').text)
                    ymax = float(bbox.find('ymax').text)
                    
                    x_center = ((xmin + xmax) / 2) / width
                    y_center = ((ymin + ymax) / 2) / height
                    box_width = (xmax - xmin) / width
                    box_height = (ymax - ymin) / height
                    
                    f.write(f"{class_idx} {x_center:.6f} {y_center:.6f} {box_width:.6f} {box_height:.6f}\n")
                    
        except Exception as e:
            print(f"Error processing {image_id}: {str(e)}")
    
    # 转换数据集
    print("Converting training set...")
    for image_id in tqdm(train_ids):
        convert_annotation(image_id, 'train')
    
    # 转换验证集
    print("Converting validation set...")
    for image_id in tqdm(val_ids):
        convert_annotation(image_id, 'val')
    
    # 转换测试集
    print("Converting test set...")
    for image_id in tqdm(test_ids):
        convert_annotation(image_id, 'test')
    
    # 创建数据集配置文件
    dataset_yaml = {
        'path': yolo_root,
        'train': 'images/train',
        'val': 'images/val',
        'test': 'images/test',
        'nc': len(classes),  # 类别数量
        'names': classes     # 类别名称
    }
    
    # 保存配置文件
    yaml_path = os.path.join(yolo_root, 'dataset.yaml')
    with open(yaml_path, 'w') as f:
        yaml.safe_dump(dataset_yaml, f)
    
    return yolo_root, yaml_path

def train_model(train_loader, model, criterion, optimizer, num_epochs, device):
    model.to(device)
    
    for epoch in range(num_epochs):
        model.train()
        running_loss = 0.0
        
        pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs}")
        for images, targets in pbar:
            # 将图像和目标移到设备上
            images = [img.to(device) for img in images]
            targets = [{k: v.to(device) for k, v in t.items()} for t in targets]
            
            # 清零梯度
            optimizer.zero_grad()
            
            # 使用真实边界框作为 proposals
            proposals = [t['boxes'] for t in targets]
            
            try:
                # 前向传播
                class_scores, bbox_preds = model(images, proposals)
                
                # 计算损失
                loss = criterion(class_scores, bbox_preds, targets)
                
                # 反向传播
                loss.backward()
                
                # 更新参数
                optimizer.step()
                
                running_loss += loss.item()
                pbar.set_postfix({'loss': f'{loss.item():.4f}'})
                
            except RuntimeError as e:
                print(f"Error during training: {e}")
                continue
        
        epoch_loss = running_loss / len(train_loader)
        print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}')

def test_model(test_dataset, model):
    correct = 0
    total = 0
    
    for images, targets in test_dataset:
        class_scores, _ = model([images, targets['boxes']])
        predictions = tf.argmax(class_scores, axis=1)
        correct += tf.reduce_sum(tf.cast(predictions == targets['labels'], tf.int32))
        total += tf.size(targets['labels'])
    
    accuracy = float(correct) / float(total)
    print(f'Test Accuracy: {accuracy * 100:.2f}%')

    
def model_upload(result_dir,model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".h5"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".h5")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "TENSORFLOW",
          "file_name": model_name+".h5",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "YOLOv8",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

    
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='tensorflow YOLOv8 Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='YOLOv8 Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='YOLOv8 DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start YOLOv8 training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("YOLOv8 job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("YOLOv8 dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("YOLOv8 result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("YOLOv8 factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("YOLOv8 fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("YOLOv8 sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    
    device = '/GPU:0' if tf.config.list_physical_devices('GPU') else '/CPU:0'
    print("Step 1 YOLOv8 training:\n")
    result,ret_code = yolov8_train(dataset,job_params, model["model_name"],result_dir,fit_params,device)
    if ret_code != 0:
        print("YOLOv8 train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()