import os
import sys
import uuid
import json
import pickle
import argparse
import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler, LabelEncoder, OrdinalEncoder
from sklearn.model_selection import train_test_split

import caffe
from caffe import layers as L
from caffe import params as P
# from caffe.proto import caffe_pb2
from caffe import proto
import lmdb

from minio import Minio
import requests

# Constants
MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

# Headers constants
MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

def create_resnet_architecture(input_size, num_classes, batch_size):
    """Create ResNet architecture for tabular data in Caffe"""
    n = caffe.NetSpec()
    
    # Input layer
    n.data = L.Input(shape=dict(dim=[batch_size, input_size, 1, 1]))
    
    # First conv layer
    n.conv1 = L.Convolution(n.data, kernel_size=7, stride=2, num_output=64,
                           weight_filler=dict(type='msra'))
    n.bn1 = L.BatchNorm(n.conv1)
    n.relu1 = L.ReLU(n.bn1)
    n.pool1 = L.Pooling(n.relu1, kernel_size=3, stride=2, pool=P.Pooling.MAX)
    
    # ResNet blocks
    def residual_block(bottom, num_output, stride=1, identity_connection=True):
        conv1 = L.Convolution(bottom, kernel_size=3, stride=stride, num_output=num_output,
                            pad=1, weight_filler=dict(type='msra'))
        bn1 = L.BatchNorm(conv1)
        relu1 = L.ReLU(bn1)
        
        conv2 = L.Convolution(relu1, kernel_size=3, stride=1, num_output=num_output,
                            pad=1, weight_filler=dict(type='msra'))
        bn2 = L.BatchNorm(conv2)
        
        if identity_connection:
            if stride != 1:
                identity = L.Convolution(bottom, kernel_size=1, stride=stride,
                                      num_output=num_output, weight_filler=dict(type='msra'))
                identity = L.BatchNorm(identity)
            else:
                identity = bottom
        else:
            identity = L.Convolution(bottom, kernel_size=1, stride=stride,
                                   num_output=num_output, weight_filler=dict(type='msra'))
            identity = L.BatchNorm(identity)
        
        addition = L.Eltwise(bn2, identity)
        return L.ReLU(addition)
    
    # Layer2
    n.res2a = residual_block(n.pool1, 64)
    n.res2b = residual_block(n.res2a, 64)
    
    # Layer3
    n.res3a = residual_block(n.res2b, 128, stride=2, identity_connection=False)
    n.res3b = residual_block(n.res3a, 128)
    
    # Layer4
    n.res4a = residual_block(n.res3b, 256, stride=2, identity_connection=False)
    n.res4b = residual_block(n.res4a, 256)
    
    # Layer5
    n.res5a = residual_block(n.res4b, 512, stride=2, identity_connection=False)
    n.res5b = residual_block(n.res5a, 512)
    
    # Average pooling
    n.pool5 = L.Pooling(n.res5b, global_pooling=True, pool=P.Pooling.AVE)
    
    # FC layer
    n.fc = L.InnerProduct(n.pool5, num_output=num_classes,
                         weight_filler=dict(type='msra'))
    
    # Loss layer
    n.loss = L.SoftmaxWithLoss(n.fc, n.label)
    n.accuracy = L.Accuracy(n.fc, n.label)
    
    return n.to_proto()

def load_data_preprocess(pkl_file):
    """Load and preprocess data from pickle file"""
    with open(pkl_file, 'rb') as file:
        data = pickle.load(file)
    
    data.columns = data.columns.str.strip()
    
    if isinstance(data, pd.DataFrame):
        X = data.drop(['Label'], axis=1)
        y = data['Label']
    elif isinstance(data, dict):
        X = data['features']
        y = data['labels']
    
    return X, y

def preprocess_data(X, y):
    """Preprocess the data"""
    if X.isnull().any().any():
        X = X.dropna()
        y = y[X.index]
    
    le = LabelEncoder()
    y = le.fit_transform(y)
    
    categorical_cols = X.select_dtypes(include=['object']).columns
    if len(categorical_cols) > 0:
        encoder = OrdinalEncoder()
        X[categorical_cols] = encoder.fit_transform(X[categorical_cols])
    
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.mean(), inplace=True)
    
    scaler = StandardScaler()
    X = scaler.fit_transform(X)
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    return X_train, X_test, y_train, y_test

def create_lmdb_dataset(X, y, lmdb_path):
    """Create LMDB dataset for Caffe"""
    num_samples = X.shape[0]
    
    env = lmdb.open(lmdb_path, map_size=int(1e12))
    with env.begin(write=True) as txn:
        for i in range(num_samples):
            datum = proto.caffe_pb2.Datum()
            datum.channels = X.shape[1]
            datum.height = 1
            datum.width = 1
            datum.data = X[i].tobytes()
            datum.label = int(y[i])
            str_id = '{:08}'.format(i)
            txn.put(str_id.encode('ascii'), datum.SerializeToString())

def resnet18_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """Train ResNet model using Caffe"""
    input_size = job_params["input_size"]
    output_size = job_params["output_size"]
    num_epochs = job_params["num_epochs"]
    learning_rate = job_params["learning_rate"]
    batch_size = job_params["batch_size"]
    
    # Load and preprocess data
    training_data_path = "/workspace/" + dataset["training_data_path"]
    X, y = load_data_preprocess(training_data_path)
    X_train, X_test, y_train, y_test = preprocess_data(X, y)
    
    # Create LMDB datasets
    train_lmdb = os.path.join(result_dir, 'train_lmdb')
    test_lmdb = os.path.join(result_dir, 'test_lmdb')
    create_lmdb_dataset(X_train, y_train, train_lmdb)
    create_lmdb_dataset(X_test, y_test, test_lmdb)
    
    # Create network architecture
    net_proto = create_resnet_architecture(input_size, output_size, batch_size)
    
    # Write prototxt files
    with open(os.path.join(result_dir, 'train.prototxt'), 'w') as f:
        f.write(str(net_proto))
    
    # Create solver
    solver = proto.caffe_pb2.SolverParameter()
    solver.train_net = os.path.join(result_dir, 'train.prototxt')
    solver.test_net.append(os.path.join(result_dir, 'train.prototxt'))
    solver.test_iter.append(100)
    solver.test_interval = 500
    solver.base_lr = learning_rate
    solver.momentum = 0.9
    solver.weight_decay = 0.0005
    solver.lr_policy = 'step'
    solver.gamma = 0.1
    solver.stepsize = 5000
    solver.display = 100
    solver.max_iter = num_epochs * (X_train.shape[0] // batch_size)
    solver.snapshot = 5000
    solver.snapshot_prefix = os.path.join(result_dir, model_name)
    solver.solver_mode = proto.caffe_pb2.SolverParameter.GPU
    with open(os.path.join(result_dir, 'solver.prototxt'), 'w') as f:
        f.write(str(solver))
    
    # Train the model
    caffe.set_device(0)
    caffe.set_mode_gpu()
    solver = caffe.SGDSolver(os.path.join(result_dir, 'solver.prototxt'))
    
    # Load pretrained weights if available
    if os.path.exists("/workspace/pretrained_model/resnet18.caffemodel"):
        solver.net.copy_from("/workspace/pretrained_model/resnet18.caffemodel")
    
    solver.solve()
    
    return "Training completed successfully", 0

def model_upload(result_dir, model_name):
    """Upload model to MinIO"""
    minioClient = Minio(MINIO_URL,
                       access_key='AKIAIOSFODNN7EXAMPLE',
                       secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                       secure=False)
    try:
        obj_name = str(uuid.uuid1())
        upload_path = f"{obj_name}/{model_name}.caffemodel"
        source = f"s3://mlss-mf/{obj_name}"
        res = minioClient.fput_object('mlss-mf', upload_path, 
                                    os.path.join(result_dir, f"{model_name}.caffemodel"))
        return {"source": source}, 0
    except Exception as err:
        print(err)
        return None, -1

def header_gen(user_id):
    """Generate headers for API requests"""
    return {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: user_id
    }

def model_register(model_name, source, group_id, headers):
    """Register model with the model factory"""
    params = {
        "model_name": model_name,
        "model_type": "caffe",
        "file_name": f"{model_name}.caffemodel",
        "s3_path": source,
        "group_id": int(float(group_id)),
        "training_id": model_name,
        "training_flag": 1,
    }
    
    r = requests.post(MODEL_FACTORY_URL + MODEL_ADD_URL,
                     data=json.dumps(params),
                     headers=headers)
    res_data = r.content.decode()
    if r.status_code == 200:
        return json.loads(res_data), 0
    return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    """Push model to factory"""
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0
        
    params = {
        "factory_name": factory_name,
        "model_type": "Resnet",
        "model_usage": "Classification"
    }
    r = requests.post(f"{MODEL_FACTORY_URL}{MODEL_PUSH_URL}/{model_version_id}",
                     data=json.dumps(params),
                     headers=headers)
    res_data = r.content.decode()
    if r.status_code == 200:
        return json.loads(res_data), 0
    return res_data, -1

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Caffe ResNet Train.')
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                       help='ResNet Job Params')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                       help='ResNet DataSet')
    parser.add_argument('--model', dest='model', type=json.loads,
                       help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                       help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                       help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                       help='fit params')
    
    args = parser.parse_args()
    job_params = args.job_params
    print("ResNet job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("ResNet dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("ResNet result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("ResNet factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("ResNet fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("ResNet sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    
    print("Start ResNet training job...")
    result, ret_code = resnet18_train(args.dataset, args.job_params, 
                                    args.model["model_name"], args.result_dir, 
                                    args.fit_params)
    
    if ret_code != 0:
        print(f"ResNet train error: {result}")
        sys.exit(-1)
        
    print("Step 2: Model Upload to MinIO")
    result, ret_code = model_upload(args.result_dir, args.model["model_name"])
    if ret_code != 0:
        print(f"Model storage error: {result}")
        sys.exit(-1)
        
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()