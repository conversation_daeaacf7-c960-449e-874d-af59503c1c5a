2025-05-23 16:55:31,463 - GAN_TF_Local - INFO - Logging to file: ./results/results_gan_tf_local\GAN_TF_Local_training_20250523_165531.log
2025-05-23 16:55:31,464 - GAN_TF_Local - INFO - GAN TensorFlow Local Mode started. Args: {'data_path': 'E:/data/Flowers Recognition/flowers', 'dataset_type': 'folder', 'annotations_file': None, 'image_size': 224, 'image_channels': 3, 'model_name': 'GAN_TF_Local', 'latent_dim': 100, 'num_epochs': 2, 'batch_size': 32, 'g_lr': 0.0002, 'd_lr': 0.0002, 'beta1': 0.5, 'sample_interval': 200, 'fixed_noise_samples': 16, 'save_epoch_interval': 5, 'result_dir': './results/results_gan_tf_local', 'random_seed': None, 'force_cpu': False, 'log_level': 'INFO'}
2025-05-23 16:55:31,466 - GAN_TF_Local - INFO - No GPU found by TensorFlow, using CPU.
2025-05-23 16:55:31,466 - GAN_TF_Local - INFO - Preparing dataset from: E:/data/Flowers Recognition/flowers, type: folder
2025-05-23 16:55:31,466 - GAN_TF_Local - INFO - Preparing TensorFlow dataset from source: E:/data/Flowers Recognition/flowers (type: folder), image_size: 224x224x3
2025-05-23 16:55:31,467 - GAN_TF_Local - INFO - Scanning for images in E:/data/Flowers Recognition/flowers and its subdirectories.
2025-05-23 16:55:31,503 - GAN_TF_Local - INFO - Found 4317 image paths from folder scan.
2025-05-23 16:55:31,845 - GAN_TF_Local - INFO - Prepared TF Dataset with 4317 images, batched to 32. Shuffle buffer: 2158
2025-05-23 16:55:31,845 - GAN_TF_Local - WARNING - Generator target_image_size 224 not perfectly achievable by powers of 2 from init_size 4. Generator will output 256x256. Consider resizing output or using a power-of-2 target_image_size.
2025-05-23 16:55:32,331 - GAN_TF_Local - INFO - Generator created. Input latent: 100, init_size: 4, num_upsamples: 6, final conv_out_filters: 3, output_shape: (None, 256, 256, 3)
2025-05-23 16:55:32,569 - GAN_TF_Local - INFO - Discriminator created. Input_shape: (224, 224, 3), final feature map size before flatten approx 7x7, output_shape: (None, 1)
2025-05-23 16:55:32,569 - GAN_TF_Local - INFO - Generator will internally produce images of size: 256x256.
2025-05-23 16:55:32,570 - GAN_TF_Local - INFO - Generator output (256x256) will be resized to Discriminator input (224x224) during training.
2025-05-23 16:55:32,574 - GAN_TF_Local - INFO - --- Training TensorFlow GAN ---
2025-05-23 17:05:46,032 - GAN_TF_Local - INFO - Epoch 1/2 - G Loss: 4.5240, D Loss: 0.1842, D(x): 0.9394, D(G(z)): 0.0693 (Took 613.46s, Batches: 135)
2025-05-23 17:16:48,950 - GAN_TF_Local - INFO - Epoch 2/2 - G Loss: 7.5064, D Loss: 0.0089, D(x): 0.9967, D(G(z)): 0.0049 (Took 662.92s, Batches: 135)
2025-05-23 17:16:49,201 - GAN_TF_Local - INFO - Saved model weights for epoch 2 to ./results/results_gan_tf_local\models_tf_local
2025-05-23 17:16:49,201 - GAN_TF_Local - INFO - Training finished. Total time: 1276.62s
2025-05-23 17:16:50,334 - GAN_TF_Local - INFO - Final Generator weights saved to ./results/results_gan_tf_local\models_tf_local\GAN_TF_Local_generator_final.weights.h5
2025-05-23 17:16:50,334 - GAN_TF_Local - INFO - Final Discriminator weights saved to ./results/results_gan_tf_local\models_tf_local\GAN_TF_Local_discriminator_final.weights.h5
2025-05-23 17:16:50,334 - GAN_TF_Local - INFO - Full run summary saved to: ./results/results_gan_tf_local\GAN_TF_Local_full_summary.json
2025-05-23 17:16:50,334 - GAN_TF_Local - INFO - GAN TensorFlow Local Mode finished successfully.
