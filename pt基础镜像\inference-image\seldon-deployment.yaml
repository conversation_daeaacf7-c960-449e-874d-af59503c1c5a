apiVersion: machinelearning.seldon.io/v1
kind: SeldonDeployment
metadata:
  name: bilstm-inference
  namespace: seldon-system
  labels:
    app: bilstm-inference
    version: v1.0.0
spec:
  name: bilstm-inference
  protocol: rest
  annotations:
    deployment_version: v1.0.0
    model_type: BiLSTM
  predictors:
  - name: bilstm-predictor
    replicas: 1
    componentSpecs:
    - spec:
        containers:
        - name: bilstm-container
          image: bilstm-inference:latest
          imagePullPolicy: IfNotPresent
          
          # 环境变量配置
          env:
          - name: MODEL_NAME
            value: "BiLSTM_Example"
          - name: SELECTED_MODEL_NAME
            value: "BiLSTM_Example"
          - name: API_TYPE
            value: "REST"
          - name: SERVICE_TYPE
            value: "MODEL"
          - name: SELDON_MODE
            value: "true"
          - name: MODEL_LIBRARY_DIR
            value: "/app/models"
          - name: STARTUP_MODE
            value: "seldon"
          
          # 资源限制
          resources:
            requests:
              memory: "1Gi"
              cpu: "500m"
            limits:
              memory: "2Gi"
              cpu: "1000m"
          
          # 端口配置
          ports:
          - containerPort: 5000
            name: http
            protocol: TCP
          - containerPort: 9000
            name: grpc
            protocol: TCP
          
          # 健康检查
          livenessProbe:
            httpGet:
              path: /health
              port: 5000
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          
          readinessProbe:
            httpGet:
              path: /health
              port: 5000
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          
          # 启动探针
          startupProbe:
            httpGet:
              path: /health
              port: 5000
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 12
          
          # 模型文件已直接复制到镜像中，无需挂载
    
    graph:
      name: bilstm-container
      type: MODEL
      
      # Seldon Core 参数
      parameters:
      - name: model_name
        value: "BiLSTM_Example"
        type: STRING
      - name: model_version
        value: "1.0.0"
        type: STRING
    
    # 实例标签
    labels:
      version: v1.0.0
      model: bilstm
    
    # 部署策略
    replicas: 1
    
  # 服务配置
  annotations:
    seldon.io/rest-timeout: "60000"
    seldon.io/grpc-timeout: "60000"
    seldon.io/engine-seldon-log-messages-externally: "true"

---
# 可选：Service配置
apiVersion: v1
kind: Service
metadata:
  name: bilstm-inference-service
  namespace: seldon-system
  labels:
    app: bilstm-inference
spec:
  selector:
    app.kubernetes.io/name: bilstm-inference-bilstm-predictor-bilstm-container
  ports:
  - name: http
    port: 8000
    targetPort: 8000
    protocol: TCP
  - name: grpc
    port: 5001
    targetPort: 5001
    protocol: TCP
  type: ClusterIP

---
# 可选：Ingress配置
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: bilstm-inference-ingress
  namespace: seldon-system
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
spec:
  rules:
  - host: bilstm-inference.local
    http:
      paths:
      - path: /api/v1.0/predictions(/|$)(.*)
        pathType: Prefix
        backend:
          service:
            name: bilstm-inference-service
            port:
              number: 8000 