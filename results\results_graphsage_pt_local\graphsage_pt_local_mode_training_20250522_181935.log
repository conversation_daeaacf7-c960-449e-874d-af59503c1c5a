2025-05-22 18:19:35,547 - GraphSAGE_PT_Local_Mode - INFO - Logging to console and file: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\graphsage_pt_local_mode_training_20250522_181935.log
2025-05-22 18:19:35,548 - GraphSAGE_PT_Local_Mode - INFO - GraphSAGE PyTorch Local Mode training script initialized.
2025-05-22 18:19:35,548 - GraphSAGE_PT_Local_Mode - INFO - PyTorch version: 2.4.1+cpu
2025-05-22 18:19:35,549 - GraphSAGE_PT_Local_Mode - INFO - PyTorch Geometric version: 2.6.1
2025-05-22 18:19:35,549 - GraphSAGE_PT_Local_Mode - INFO - CLI Arguments: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'label_column': 'Label', 'data_format': 'pkl', 'hidden_size': 64, 'output_size': 6, 'num_layers': 2, 'dropout_rate': 0.5, 'edge_strategy': 'fully_connected_per_sample', 'k_neighbors': 5, 'num_epochs': 3, 'learning_rate': 0.001, 'batch_size': 32, 'test_split_size': 0.2, 'random_state': 42, 'normalize': True, 'scaler_type': 'standard', 'handle_imbalance': False, 'stratify_split': True, 'numeric_impute_strategy': 'mean', 'categorical_impute_strategy': 'most_frequent', 'use_cv': False, 'cv_folds': 5, 'result_dir': 'E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\results\\results_graphsage_pt_local', 'model_name': 'GraphSAGE_PT_Local_Model', 'log_level': 'INFO', 'force_cpu': False}
2025-05-22 18:19:35,550 - GraphSAGE_PT_Local_Mode - INFO - Using device: cpu
2025-05-22 18:19:35,551 - GraphSAGE_PT_Local_Mode.DataLoad - INFO - Loading data from E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl, format: pkl
2025-05-22 18:19:38,876 - GraphSAGE_PT_Local_Mode.DataLoad - INFO - Data loaded. X shape: (692703, 84), y shape: (692703,)
2025-05-22 18:19:38,909 - GraphSAGE_PT_Local_Mode.DataLoad - INFO - Label distribution:
Label
BENIGN              440031
DoS Hulk            231073
DoS GoldenEye        10293
DoS slowloris         5796
DoS Slowhttptest      5499
Heartbleed              11
Name: count, dtype: int64
2025-05-22 18:19:39,052 - GraphSAGE_PT_Local_Mode - INFO - Inferred 6 classes from the label column 'Label'. Classes: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-22 18:19:39,052 - GraphSAGE_PT_Local_Mode - INFO - Standard train/test split mode selected for GraphSAGE (PT).
2025-05-22 18:19:46,129 - GraphSAGE_PT_Local_Mode - INFO - Scaler for train/test split saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\GraphSAGE_PT_Local_Model_scaler.joblib
2025-05-22 18:19:46,131 - GraphSAGE_PT_Local_Mode - INFO - Label encoder for train/test split saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\GraphSAGE_PT_Local_Model_label_encoder.joblib
2025-05-22 18:19:46,239 - GraphSAGE_PT_Local_Mode - INFO - Categorical encoder for train/test split saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\GraphSAGE_PT_Local_Model_cat_encoder.joblib
2025-05-22 18:19:48,799 - GraphSAGE_PT_Local_Mode - INFO - Preprocessing info for train/test split saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\GraphSAGE_PT_Local_Model_preproc_info.json
2025-05-22 18:19:48,808 - GraphSAGE_PT_Local_Mode - INFO - Using stratified train/test split.
2025-05-22 18:19:49,827 - GraphSAGE_PT_Local_Mode - INFO - Data split: Train X shape (554162, 84), y shape (554162,). Test X shape (138541, 84), y shape (138541,)
2025-05-22 18:19:50,215 - GraphSAGE_PT_Local_Mode - ERROR - Value error during setup or execution: Expected input batch_size (2688) to match target batch_size (32).
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GraphSAGE_pt_local_mode.py", line 1045, in graphsage_train_pt_local_mode
    training_history_dict = train_graphsage_model_pt_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GraphSAGE_pt_local_mode.py", line 380, in train_graphsage_model_pt_local
    loss = criterion(out, batch_data.y)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\nn\modules\module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\nn\modules\module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\nn\modules\loss.py", line 1188, in forward
    return F.cross_entropy(input, target, weight=self.weight,
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\nn\functional.py", line 3104, in cross_entropy
    return torch._C._nn.cross_entropy_loss(input, target, weight, _Reduction.get_enum(reduction), ignore_index, label_smoothing)
ValueError: Expected input batch_size (2688) to match target batch_size (32).
