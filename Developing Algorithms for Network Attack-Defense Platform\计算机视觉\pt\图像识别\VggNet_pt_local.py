"""VggNet"""

import pickle
import requests
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
from torchvision import datasets, transforms
from torchvision import models
from PIL import Image
import xml.etree.ElementTree as ET
import pandas as pd

MINIO_URL = "minio-prophecis.prophecis:9000"

MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"


MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"


class VGGForTabular(nn.Module):
    def __init__(self, input_size, num_classes, pretrained_model_path):
        super(VGGForTabular, self).__init__()
        # 使用预训练的 VGG16 模型
        self.vgg = models.vgg16(pretrained=False)
        
        # 加载本地的预训练模型参数
        self.vgg.load_state_dict(torch.load(pretrained_model_path))
        
        # 替换输入层以适应输入尺寸
        #self.vgg.features[0] = nn.Conv2d(input_size, 64, kernel_size=3, stride=1, padding=1)

        # 替换最后一层全连接层
        num_ftrs = self.vgg.classifier[6].in_features
        self.vgg.classifier[6] = nn.Linear(num_ftrs, num_classes)

    def forward(self, x):
        return self.vgg(x)

class UniversalImageDataset(Dataset):
    def __init__(self, data_dir, transform=None, dataset_type='folder', annotations_file=None):
        self.data_dir = data_dir
        self.transform = transform
        self.dataset_type = dataset_type

        if dataset_type in ['folder', 'imagenet']:
            self.image_paths, self.labels = self.load_from_folder()
        elif dataset_type == 'coco':
            self.image_paths, self.labels = self.load_coco(annotations_file)
        elif dataset_type == 'voc':
            self.image_paths, self.labels = self.load_voc(annotations_file)
        elif dataset_type == 'yolo':
            self.image_paths, self.labels = self.load_yolo(annotations_file)
        elif dataset_type == 'pickle':
            self.image_paths, self.labels = self.load_pickle(annotations_file)
        else:
            raise ValueError("Unsupported dataset type.")

    def load_from_folder(self):
        classes = os.listdir(self.data_dir)
        class_to_idx = {cls: idx for idx, cls in enumerate(classes)}
        
        image_paths = []
        labels = []

        for cls in classes:
            class_dir = os.path.join(self.data_dir, cls)
            if os.path.isdir(class_dir):
                for img_file in os.listdir(class_dir):
                    if img_file.endswith(('.jpg', '.jpeg', '.png')):
                        img_path = os.path.join(class_dir, img_file)
                        image_paths.append(img_path)
                        labels.append(class_to_idx[cls])

        return image_paths, labels

    def load_coco(self, annotations_file):
        with open(annotations_file) as f:
            annotations = json.load(f)

        image_paths = []
        labels = []
        for item in annotations['images']:
            img_id = item['id']
            img_file = os.path.join(self.data_dir, item['file_name'])
            image_paths.append(img_file)
            label = self.get_label_for_image(img_id, annotations)
            labels.append(label)

        return image_paths, labels

    def load_voc(self, annotations_file):
        image_paths = []
        labels = []

        # 读取所有 XML 文件
        with open(annotations_file, 'r') as file:
            xml_files = file.readlines()

        for xml_file in xml_files:
            xml_file = xml_file.strip()
            tree = ET.parse(xml_file)
            root = tree.getroot()

            # 获取图像文件路径
            image_name = root.find('filename').text
            img_path = os.path.join(self.data_dir, image_name)
            image_paths.append(img_path)

            # 提取标签
            objects = root.findall('object')
            boxes = []
            for obj in objects:
                class_name = obj.find('name').text
                bbox = obj.find('bndbox')
                xmin = float(bbox.find('xmin').text)
                ymin = float(bbox.find('ymin').text)
                xmax = float(bbox.find('xmax').text)
                ymax = float(bbox.find('ymax').text)
                boxes.append((class_name, xmin, ymin, xmax, ymax))

            labels.append(boxes)

        return image_paths, labels

    def load_yolo(self):
        image_paths = []
        labels = []

        for img_file in os.listdir(self.data_dir):
            if img_file.endswith(('.jpg', '.png', '.jpeg')):
                img_path = os.path.join(self.data_dir, img_file)
                image_paths.append(img_path)

                # 加载对应的YOLO标签文件
                label_file = img_file.replace('.jpg', '.txt').replace('.png', '.txt').replace('.jpeg', '.txt')
                label_path = os.path.join(self.data_dir, label_file)

                if os.path.exists(label_path):
                    with open(label_path, 'r') as f:
                        boxes = []
                        for line in f.readlines():
                            class_id, x_center, y_center, width, height = map(float, line.strip().split())
                            boxes.append((class_id, x_center, y_center, width, height))
                        labels.append(boxes)  # 以边界框列表形式存储
                else:
                    labels.append([])  # 无标签时返回空列表

        return image_paths, labels
    
    def load_pickle(self, pkl_file):
        # 从 .pkl 文件加载数据
        with open(pkl_file, 'rb') as f:
            data = pickle.load(f)

        # 假设数据为字典格式，包含特征和标签
        if isinstance(data, dict):
            images = data['images']  # 假设图像数据在 'images' 键下
            labels = data['labels']    # 假设标签在 'labels' 键下
        elif isinstance(data, pd.DataFrame):
            images = data['image_paths'].tolist()  # 假设图像路径在某列
            labels = data['labels'].tolist()        # 假设标签在某列
        else:
            raise ValueError("Unsupported data format in pickle file.")

        return images, labels
    
    def __len__(self):
        if hasattr(self, 'dataset'):
            return len(self.dataset)
        return len(self.image_paths)

    def __getitem__(self, idx):
        if hasattr(self, 'dataset'):
            image, label = self.dataset[idx]
        else:
            img_path = self.image_paths[idx]
            image = Image.open(img_path).convert("RGB")
            label = self.labels[idx]

        if self.transform:
            image = self.transform(image)

        return image, label
def prepare_data(data_dir, dataset_type, batch_size, image_size,  annotations_file=None):
    transform = transforms.Compose([
        transforms.Resize((image_size, image_size)),
        transforms.ToTensor(),
    ])

    dataset = UniversalImageDataset(data_dir, transform=transform, dataset_type=dataset_type, annotations_file=annotations_file)

    train_size = int(0.8 * len(dataset))
    test_size = len(dataset) - train_size
    train_dataset, test_dataset = torch.utils.data.random_split(dataset, [train_size, test_size])

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    return train_loader, test_loader

def train_model(train_loader, model, criterion, optimizer, num_epochs, device):
    model.to(device)

    for epoch in range(num_epochs):
        model.train()
        running_loss = 0.0

        for inputs, labels in train_loader:
            inputs, labels = inputs.to(device), labels.to(device)

            optimizer.zero_grad()
            outputs = model(inputs)
            
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()

            running_loss += loss.item()

        print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {running_loss/len(train_loader):.4f}')

# 测试函数
def test_model(test_loader, model, device):
    model.to(device)  # 将模型移到指定设备
    model.eval()  # 设置模型为评估模式
    correct, total = 0, 0

    with torch.no_grad():
        for inputs, labels in test_loader:
            inputs, labels = inputs.to(device), labels.to(device)  # 将数据移到指定设备
            outputs = model(inputs)
            _, predicted = torch.max(outputs, 1)  # 使用 softmax 输出中最大的值作为预测类别
            total += labels.size(0)
            correct += (predicted == labels).sum().item()

    print(f'Accuracy: {100 * correct / total:.2f}%')

def vggnet_train(dataset, job_params, model_name, result_dir, fit_params=None, device = "cpu"):

    """
    参数说明：
    - input_size: 输入图片尺寸
    - dataset_type: 数据集格式类型,共支持imagenet、coco、voc、yolo、pickle等5种类型
    - output_size: 类别数
    - num_epochs: 训练轮数
    - learning_rate: 学习率
    - batch_size: 批处理大小
    """

    input_size = job_params["input_size"]
    dataset_type = job_params["dataset_type"]
    output_size = job_params["output_size"]
    num_epochs = job_params["num_epochs"]
    learning_rate = job_params["learning_rate"]
    batch_size = job_params["batch_size"]
    
    
    training_data_path = "/workspace/" + dataset["training_data_path"]
    train_loader, test_loader = prepare_data(training_data_path, dataset_type, batch_size, input_size)

    pretrained_model_path = "/workspace/pretrained_model/" + "VggNet121-a639ec97.pth"
    
    # 初始化VggNet模型
    model = VGGForTabular(input_size, output_size, pretrained_model_path)
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)

    train_model(train_loader, model, criterion, optimizer, num_epochs, device)
    test_model(test_loader, model, device)
    
    # 保存模型
    model_file = open(result_dir + "/" + model_name + ".pth", "wb")
    torch.save(model.state_dict(), model_file)
    model_file.close()
    print(f'VggNet训练完成，模型保存到 {result_dir}/{model_name}.pth')

    return None, 0
    
def model_upload(result_dir,model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".pickle"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".pickle")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "pytorch",
          "file_name": model_name+".pickle",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "VggNet",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

    
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='pytorch VggNet Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='VggNet Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='VggNet DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start VggNet training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("VggNet job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("VggNet dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("VggNet result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("VggNet factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("VggNet fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("VggNet sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print("Step 1 VggNet training:\n")
    result,ret_code = vggnet_train(dataset,job_params, model["model_name"],result_dir,fit_params,device)
    if ret_code != 0:
        print("VggNet train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()
   
  
