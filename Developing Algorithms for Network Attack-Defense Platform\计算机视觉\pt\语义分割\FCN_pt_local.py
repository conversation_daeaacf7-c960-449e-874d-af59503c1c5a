import pickle
import requests
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
from torchvision import datasets, transforms
from torchvision import models
from PIL import Image
import xml.etree.ElementTree as ET
import pandas as pd
from tqdm.auto import tqdm
import numpy as np

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class FCNSegmentation(nn.Module):
    def __init__(self, num_classes, pretrained_model_path):
        super(FCNSegmentation, self).__init__()
        # 使用预训练的 VGG16 作为基础网络
        vgg16 = models.vgg16(pretrained=False)
        vgg16.load_state_dict(torch.load(pretrained_model_path))
        
        # 提取并修改VGG16的特征层
        features = list(vgg16.features.children())
        self.features = nn.ModuleList(features)
        
        # 获取池化层的索引，用于跳跃连接
        self.pool_indices = [4, 9, 16, 23, 30]
        
        # FCN-specific layers
        self.fcn_conv = nn.Sequential(
            nn.Conv2d(512, 4096, kernel_size=7, padding=3),
            nn.ReLU(inplace=True),
            nn.Dropout2d(),
            nn.Conv2d(4096, 4096, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Dropout2d(),
            nn.Conv2d(4096, num_classes, kernel_size=1)
        )
        
        # 修改上采样层的通道数配置
        self.up_pool4 = nn.ConvTranspose2d(
            num_classes, num_classes, 
            kernel_size=4, stride=2, padding=1, bias=False
        )
        self.up_pool3 = nn.ConvTranspose2d(
            num_classes, num_classes, 
            kernel_size=4, stride=2, padding=1, bias=False
        )
        self.up_final = nn.ConvTranspose2d(
            num_classes, num_classes, 
            kernel_size=16, stride=8, padding=4, bias=False
        )
        
        # 修改跳跃连接的卷积层
        self.conv_pool4 = nn.Conv2d(512, num_classes, kernel_size=1)
        self.conv_pool3 = nn.Conv2d(256, num_classes, kernel_size=1)
        
        # 初始化转置卷积层的权重
        self._initialize_weights()
    
    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.ConvTranspose2d):
                # 使用双线性插值初始化上采样权重
                m.weight.data.copy_(self._get_upsampling_weight(
                    m.in_channels, m.out_channels, m.kernel_size[0]))

    def _get_upsampling_weight(self, in_channels, out_channels, kernel_size):
        """初始化双线性上采样的权重"""
        factor = (kernel_size + 1) // 2
        if kernel_size % 2 == 1:
            center = factor - 1
        else:
            center = factor - 0.5
        og = np.ogrid[:kernel_size, :kernel_size]
        filt = (1 - abs(og[0] - center) / factor) * (1 - abs(og[1] - center) / factor)
        weight = np.zeros((in_channels, out_channels, kernel_size, kernel_size))
        for i in range(in_channels):
            weight[i, i] = filt
        return torch.from_numpy(weight).float()

    def forward(self, x):
        # 保存池化层输出用于跳跃连接
        pool3_idx = self.pool_indices[2]  # 第3个池化层
        pool4_idx = self.pool_indices[3]  # 第4个池化层
        
        # 通过VGG16特征提取层
        for idx, layer in enumerate(self.features):
            x = layer(x)
            if idx == pool3_idx:
                pool3 = x  # 保存pool3的特征图
            elif idx == pool4_idx:
                pool4 = x  # 保存pool4的特征图
        
        # FCN卷积层
        x = self.fcn_conv(x)
        
        # 上采样和跳跃连接
        # 上采样到pool4的大小
        score_pool4 = self.conv_pool4(pool4)
        x = self.up_pool4(x)
        x = x[:, :, :score_pool4.size(2), :score_pool4.size(3)]
        x = x + score_pool4
        
        # 上采样到pool3的大小
        score_pool3 = self.conv_pool3(pool3)
        x = self.up_pool3(x)
        x = x[:, :, :score_pool3.size(2), :score_pool3.size(3)]
        x = x + score_pool3
        
        # 最终上采样到原始图像大小
        x = self.up_final(x)
        
        return x

class UniversalImageDataset(Dataset):
    def __init__(self, data_dir, transform=None, target_transform=None, dataset_type='folder', annotations_file=None):
        self.data_dir = data_dir
        self.transform = transform
        self.target_transform = target_transform
        self.dataset_type = dataset_type
        self.annotations_file = annotations_file

        self.classes = ['background'] + [f'class_{i}' for i in range(1, 21)]  # 21类（包括背景）
        self.class_to_idx = {cls: idx for idx, cls in enumerate(self.classes)}

        if dataset_type == 'pickle':
            self.image_data, self.mask_data = self.load_pickle_data()
        elif dataset_type == 'coco':
            self.image_paths, self.mask_data = self.load_coco_data()
        elif dataset_type == 'yolo':
            self.image_paths, self.mask_data = self.load_yolo_data()
        elif dataset_type in ['folder', 'voc']:
            self.image_paths, self.mask_paths = self.load_segmentation_data()
        else:
            raise ValueError("Unsupported dataset type for segmentation.")

    def load_segmentation_data(self):
        image_paths = []
        mask_paths = []
        
        # 假设数据集结构为：
        # data_dir/
        #   └── JPEGImages/      # 原始图像
        #   └── SegmentationClass/  # 分割掩码
        
        images_dir = os.path.join(self.data_dir, 'JPEGImages')
        masks_dir = os.path.join(self.data_dir, 'SegmentationClass')
        
        for img_name in os.listdir(images_dir):
            if img_name.endswith(('.jpg', '.jpeg', '.png')):
                img_path = os.path.join(images_dir, img_name)
                mask_name = img_name.replace('.jpg', '.png').replace('.jpeg', '.png')
                mask_path = os.path.join(masks_dir, mask_name)
                
                if os.path.exists(mask_path):
                    image_paths.append(img_path)
                    mask_paths.append(mask_path)

        return image_paths, mask_paths
    
    def load_pickle_data(self):
        """加载pickle格式的数据集"""
        with open(self.annotations_file, 'rb') as f:
            data = pickle.load(f)
            
        if isinstance(data, dict):
            # 假设pickle文件包含'images'和'masks'键
            return data.get('images', []), data.get('masks', [])
        elif isinstance(data, tuple) and len(data) == 2:
            # 假设pickle文件直接存储(images, masks)元组
            return data
        else:
            raise ValueError("Invalid pickle data format")

    def load_coco_data(self):
        """加载COCO格式的数据集"""
        from pycocotools.coco import COCO
        
        coco = COCO(self.annotations_file)
        
        # 获取所有图像ID
        image_ids = coco.getImgIds()
        image_paths = []
        mask_data = []
        
        for img_id in image_ids:
            # 获取图像信息
            img_info = coco.loadImgs(img_id)[0]
            img_path = os.path.join(self.data_dir, img_info['file_name'])
            image_paths.append(img_path)
            
            # 获取分割掩码
            ann_ids = coco.getAnnIds(imgIds=img_id)
            anns = coco.loadAnns(ann_ids)
            
            # 合并所有分割掩码
            mask = np.zeros((img_info['height'], img_info['width']), dtype=np.uint8)
            for ann in anns:
                curr_mask = coco.annToMask(ann)
                mask[curr_mask == 1] = ann['category_id']
            
            mask_data.append(mask)
            
        return image_paths, mask_data

    def load_yolo_data(self):
        """加载YOLO格式的数据集"""
        image_paths = []
        mask_data = []
        
        # 获取图像文件列表
        images_dir = os.path.join(self.data_dir, 'images')
        labels_dir = os.path.join(self.data_dir, 'labels')
        
        for img_name in os.listdir(images_dir):
            if img_name.endswith(('.jpg', '.jpeg', '.png')):
                img_path = os.path.join(images_dir, img_name)
                label_name = os.path.splitext(img_name)[0] + '.txt'
                label_path = os.path.join(labels_dir, label_name)
                
                if os.path.exists(label_path):
                    image_paths.append(img_path)
                    
                    # 读取图像获取尺寸
                    img = Image.open(img_path)
                    img_width, img_height = img.size
                    
                    # 创建空白掩码
                    mask = np.zeros((img_height, img_width), dtype=np.uint8)
                    
                    # 读取YOLO格式的标签文件
                    with open(label_path, 'r') as f:
                        for line in f:
                            class_id, x_center, y_center, width, height = map(float, line.strip().split())
                            
                            # 转换YOLO坐标为像素坐标
                            x1 = int((x_center - width/2) * img_width)
                            y1 = int((y_center - height/2) * img_height)
                            x2 = int((x_center + width/2) * img_width)
                            y2 = int((y_center + height/2) * img_height)
                            
                            # 在掩码上填充分割区域
                            mask[y1:y2, x1:x2] = int(class_id) + 1  # +1 因为背景是0
                    
                    mask_data.append(mask)
        
        return image_paths, mask_data

    def __len__(self):
        if self.dataset_type == 'pickle':
            return len(self.image_data)
        else:
            return len(self.image_paths)

    def __getitem__(self, idx):
        if self.dataset_type == 'pickle':
            # 直接从内存加载数据
            image = self.image_data[idx]
            mask = self.mask_data[idx]
            
            # 如果图像数据是numpy数组，转换为PIL Image
            if isinstance(image, np.ndarray):
                image = Image.fromarray(image)
            
        elif self.dataset_type in ['coco', 'yolo']:
            # 从文件加载图像，从内存加载掩码
            image = Image.open(self.image_paths[idx]).convert('RGB')
            mask = self.mask_data[idx]
            mask = Image.fromarray(mask)
            
        else:  # folder or voc
            # 从文件加载图像和掩码
            image = Image.open(self.image_paths[idx]).convert('RGB')
            mask = Image.open(self.mask_paths[idx])
        
        # 应用变换
        if self.transform:
            image = self.transform(image)
        if self.target_transform:
            mask = self.target_transform(mask)
        else:
            # 确保掩码是长整型张量
            if isinstance(mask, Image.Image):
                mask = np.array(mask)
            mask = torch.from_numpy(mask).long()
        
        return image, mask

def prepare_data(data_dir, dataset_type, batch_size, image_size, annotations_file=None):
    # 图像变换
    transform = transforms.Compose([
        transforms.Resize((image_size, image_size)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # 掩码变换
    target_transform = transforms.Compose([
        transforms.Resize((image_size, image_size), interpolation=Image.NEAREST),
        lambda x: torch.from_numpy(np.array(x)).long()
    ])

    dataset = UniversalImageDataset(
        data_dir, 
        transform=transform,
        target_transform=target_transform,
        dataset_type=dataset_type,
        annotations_file=annotations_file
    )

    train_size = int(0.8 * len(dataset))
    test_size = len(dataset) - train_size
    train_dataset, test_dataset = torch.utils.data.random_split(dataset, [train_size, test_size])

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    return train_loader, test_loader

class SegmentationLoss(nn.Module):
    def __init__(self):
        super(SegmentationLoss, self).__init__()
        self.criterion = nn.CrossEntropyLoss()

    def forward(self, outputs, targets):
        return self.criterion(outputs, targets)

def train_model(train_loader, model, criterion, optimizer, num_epochs, device):
    model.to(device)
    
    for epoch in range(num_epochs):
        model.train()
        running_loss = 0.0
        
        pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs}")
        for images, masks in pbar:
            # 将数据移到设备上
            images = images.to(device)
            masks = masks.to(device)
            
            # 确保掩码的形状正确
            if masks.dim() == 4:
                masks = masks.squeeze(1)
            
            # 清零梯度
            optimizer.zero_grad()
            
            try:
                # 前向传播
                outputs = model(images)
                
                # 确保输出和目标的大小匹配
                if outputs.size()[2:] != masks.size()[1:]:
                    outputs = F.interpolate(outputs, size=masks.size()[1:], mode='bilinear', align_corners=True)
                
                # 计算损失
                loss = criterion(outputs, masks)
                
                # 反向传播
                loss.backward()
                
                # 更新参数
                optimizer.step()
                
                running_loss += loss.item()
                pbar.set_postfix({'loss': f'{loss.item():.4f}'})
                
            except Exception as e:
                print(f"Error in batch: {e}")
                continue
        
        epoch_loss = running_loss / len(train_loader)
        print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}')

def test_model(test_loader, model, device):
    model.eval()
    total_iou = 0.0
    num_images = 0

    with torch.no_grad():
        for images, masks in tqdm(test_loader, desc="Testing"):
            images = images.to(device)
            masks = masks.to(device)
            
            if masks.dim() == 4:
                masks = masks.squeeze(1)
            
            outputs = model(images)
            
            # 确保输出和目标的大小匹配
            if outputs.size()[2:] != masks.size()[1:]:
                outputs = F.interpolate(outputs, size=masks.size()[1:], mode='bilinear', align_corners=True)
            
            predictions = torch.argmax(outputs, dim=1)
            
            # 计算IoU
            for pred, mask in zip(predictions, masks):
                intersection = torch.logical_and(pred, mask).sum()
                union = torch.logical_or(pred, mask).sum()
                iou = (intersection / union).item() if union > 0 else 0
                total_iou += iou
                num_images += 1

    mean_iou = total_iou / num_images
    print(f'Mean IoU: {mean_iou:.4f}')

def fcn_train(dataset, job_params, model_name, result_dir, fit_params=None, device="cpu"):
    """
    参数说明：
    - input_size: 输入图片尺寸
    - num_classes: 类别数
    - num_epochs: 训练轮数
    - learning_rate: 学习率
    - batch_size: 批处理大小
    """
    input_size = job_params["input_size"]
    num_classes = job_params["num_classes"]
    dataset_type = job_params["dataset_type"]
    num_epochs = job_params["num_epochs"]
    learning_rate = job_params["learning_rate"]
    batch_size = job_params["batch_size"]
    
    training_data_path = "/workspace/" + dataset["training_data_path"]
    train_loader, test_loader = prepare_data(training_data_path, dataset_type,batch_size, input_size,annotations_file=None)

    pretrained_model_path = "/workspace/pretrained_model/vgg16-397923af.pth"
    
    # 初始化模型
    model = FCNSegmentation(num_classes, pretrained_model_path)
    # 损失函数
    criterion = SegmentationLoss()
    # 优化器
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)

    try:
        train_model(train_loader, model, criterion, optimizer, num_epochs, device)
        test_model(test_loader, model, device)
        
        # 保存模型
        model_file = open(result_dir + "/" + model_name + ".pth", "wb")
        torch.save(model.state_dict(), model_file)
        model_file.close()
        print(f'训练完成，模型保存到 {result_dir}/{model_name}.pth')
    except Exception as e:
        print(f"训练过程中发生错误: {e}")

    return None, 0

def model_upload(result_dir,model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".pickle"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".pickle")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "pytorch",
          "file_name": model_name+".pickle",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "FCN",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

    
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='pytorch FCN Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='FCN Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='FCN DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start FCN training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("FCN job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("FCN dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("FCN result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("FCN factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("FCN fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("FCN sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print("Step 1 FCN training:\n")
    result,ret_code = fcn_train(dataset,job_params, model["model_name"],result_dir,fit_params,device)
    if ret_code != 0:
        print("FCN train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()