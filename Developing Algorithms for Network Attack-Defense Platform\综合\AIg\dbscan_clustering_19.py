"""DBSCAN 聚类"""

import sys
import pandas as pd
from sklearn.cluster import DBSCAN

def dbscan_clustering(input_file, eps=0.5, min_samples=5):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - eps: 邻域半径参数，默认为0.5
    - min_samples: 样本数参数，默认为5
    """
    data = pd.read_csv(input_file)
    
    model = DBSCAN(eps=eps, min_samples=min_samples)
    clusters = model.fit_predict(data)
    
    output_file = 'dbscan_clusters.csv'
    pd.DataFrame({'Cluster': clusters}).to_csv(output_file, index=False)
    print(f"DBSCAN clustering completed. Output saved to {output_file}")

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python dbscan_clustering.py <input_file> <eps> <min_samples>")
        sys.exit(1)
    input_file, eps, min_samples = sys.argv[1], sys.argv[2], sys.argv[3]
    dbscan_clustering(input_file, float(eps), int(min_samples))
