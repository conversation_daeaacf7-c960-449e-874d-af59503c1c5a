2025-05-23 14:31:40,710 - DDPG_PT_Local - INFO - Logging configured. Log file: ./results/ddpg_pt_local_experiment\DDPG_Pendulum_Local_main_script_training_20250523_143140.log
2025-05-23 14:31:40,710 - DDPG_PT_Local_MainScript - INFO - DDPG PyTorch Local Mode - Main Script Execution Started. Mode: train
2025-05-23 14:31:40,710 - DDPG_PT_Local_MainScript - INFO - Command line arguments: {'env_name': 'Pendulum-v1', 'result_dir': './results/ddpg_pt_local_experiment', 'model_name': 'DDPG_Pendulum_Local', 'hidden_size': 256, 'actor_lr': 0.0001, 'critic_lr': 0.001, 'gamma': 0.99, 'tau': 0.001, 'buffer_size': 100000, 'batch_size': 32, 'noise_sigma': 0.1, 'num_episodes': 2, 'max_steps_per_episode': 500, 'random_seed': None, 'device': 'auto', 'save_checkpoint_freq': 50, 'eval_freq': 20, 'log_level': 'INFO', 'mode': 'train', 'test_model_path': None, 'test_episodes': 5, 'render_test': False}
2025-05-23 14:31:40,710 - DDPG_PT_Local_MainScript - INFO - Using device: cpu
