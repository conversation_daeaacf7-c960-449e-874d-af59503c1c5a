2025-05-22 15:18:39,047 - GIN_TF_Local_Mode - INFO - 日志将记录到控制台和文件: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_gin_tf_local\gin_tf_local_mode_training_20250522_151839.log
2025-05-22 15:18:39,048 - GIN_TF_Local_Mode - INFO - GIN TensorFlow Local Mode training script initialized.
2025-05-22 15:18:39,048 - GIN_TF_Local_Mode - INFO - TensorFlow version: 2.13.0
2025-05-22 15:18:39,049 - GIN_TF_Local_Mode - INFO - CLI Arguments: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'label_column': 'Label', 'data_format': 'pkl', 'input_size': 84, 'hidden_size': 64, 'output_size': 6, 'num_layers': 3, 'dropout_rate': 0.5, 'train_eps': True, 'l2_reg': 0.0001, 'edge_strategy': 'fully_connected', 'k_neighbors': 5, 'max_nodes_for_fully_connected': 150, 'num_epochs': 3, 'learning_rate': 0.001, 'batch_size': 32, 'early_stopping_patience': None, 'test_split_size': 0.2, 'random_state': 42, 'normalize': True, 'scaler_type': 'standard', 'handle_imbalance': False, 'stratify_split': True, 'numeric_impute_strategy': 'mean', 'categorical_impute_strategy': 'most_frequent', 'use_cv': False, 'cv_folds': 5, 'result_dir': 'E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\results\\results_gin_tf_local', 'model_name': 'GIN_TF_Local_Model', 'log_level': 'INFO'}
2025-05-22 15:18:39,050 - GIN_TF_Local_Mode - INFO - No GPU found by TensorFlow, using CPU.
2025-05-22 15:18:39,050 - GIN_TF_Local_Mode.DataLoad - INFO - Loading data from E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl, format: pkl
2025-05-22 15:18:39,531 - GIN_TF_Local_Mode.DataLoad - INFO - Data loaded. X shape: (692703, 84), y shape: (692703,)
2025-05-22 15:18:39,562 - GIN_TF_Local_Mode.DataLoad - INFO - Label distribution:
Label
BENIGN              440031
DoS Hulk            231073
DoS GoldenEye        10293
DoS slowloris         5796
DoS Slowhttptest      5499
Heartbleed              11
Name: count, dtype: int64
2025-05-22 15:18:39,702 - GIN_TF_Local_Mode - INFO - GIN model params: Node Feature Dim = 1, Num Classes = 6
2025-05-22 15:18:39,703 - GIN_TF_Local_Mode - INFO - Each graph (sample) will have 84 nodes (from original features).
2025-05-22 15:18:39,703 - GIN_TF_Local_Mode - INFO - Standard train/test split mode selected for GIN (TF).
2025-05-22 15:18:40,302 - GIN_TF_Local_Mode.Preprocess - INFO - Replaced infinite values with NaN. Initial NaN count: 2594
2025-05-22 15:18:40,394 - GIN_TF_Local_Mode.Preprocess - INFO - Handling missing values...
2025-05-22 15:18:40,530 - GIN_TF_Local_Mode.Preprocess - INFO - Missing value imputation complete.
2025-05-22 15:18:40,632 - GIN_TF_Local_Mode.Preprocess - INFO - Labels encoded. Classes: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-22 15:18:40,665 - GIN_TF_Local_Mode.Preprocess - INFO - Encoding categorical features: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-22 15:18:44,587 - GIN_TF_Local_Mode.Preprocess - INFO - Categorical features encoded.
2025-05-22 15:18:45,694 - GIN_TF_Local_Mode.Preprocess - INFO - Features scaled using standard scaler.
2025-05-22 15:18:45,908 - GIN_TF_Local_Mode - INFO - Scaler for train/test split saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_gin_tf_local\GIN_TF_Local_Model_scaler.joblib
2025-05-22 15:18:48,348 - GIN_TF_Local_Mode - INFO - Preprocessing info for train/test split saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_gin_tf_local\GIN_TF_Local_Model_preproc_info.json
2025-05-22 15:18:49,396 - GIN_TF_Local_Mode - INFO - Data split: Train (554162, 84), Test (138541, 84)
2025-05-22 15:18:49,513 - GIN_TF_Local_Mode - INFO - Starting GIN (TF) model training (standard split).
2025-05-22 15:18:49,527 - GIN_TF_Local_Mode.Train - INFO - Best model weights (on val_loss) will be saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_gin_tf_local\models\GIN_TF_Local_Model_best.weights.h5
2025-05-22 15:18:49,527 - GIN_TF_Local_Mode.Train - INFO - Starting Keras GIN model training for 3 epochs.
2025-05-22 16:49:51,475 - GIN_TF_Local_Mode.Train - INFO - Final model weights saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_gin_tf_local\models\GIN_TF_Local_Model_final.weights.h5
2025-05-22 16:49:52,318 - GIN_TF_Local_Mode.Plot - INFO - Training history plot saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_gin_tf_local\plots\GIN_TF_Local_Model_training_history.png
2025-05-22 16:49:52,319 - GIN_TF_Local_Mode - INFO - Loading best weights from checkpoint: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_gin_tf_local\models\GIN_TF_Local_Model_best.weights.h5
2025-05-22 16:49:52,542 - GIN_TF_Local_Mode - INFO - Model weights saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_gin_tf_local\models\GIN_TF_Local_Model_reported.weights.h5
2025-05-22 16:49:52,554 - GIN_TF_Local_Mode - INFO - Model configuration saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_gin_tf_local\models\GIN_TF_Local_Model_reported_config.json
2025-05-22 16:49:52,555 - GIN_TF_Local_Mode - INFO - Evaluating GIN (TF) model on test set (standard split).
2025-05-22 17:05:17,643 - GIN_TF_Local_Mode.Test - INFO - Classification Report (GIN_TF_Local_Model):
                  precision    recall  f1-score   support

          BENIGN       0.98      0.97      0.98     88006
   DoS GoldenEye       0.68      0.59      0.63      2059
        DoS Hulk       0.97      0.99      0.98     46215
DoS Slowhttptest       0.66      0.74      0.70      1100
   DoS slowloris       0.65      0.62      0.63      1159
      Heartbleed       0.00      0.00      0.00         2

        accuracy                           0.97    138541
       macro avg       0.66      0.65      0.65    138541
    weighted avg       0.97      0.97      0.97    138541

2025-05-22 17:05:18,246 - GIN_TF_Local_Mode.Plot - INFO - Confusion matrix plot saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_gin_tf_local\plots\GIN_TF_Local_Model_test_confusion_matrix.png
2025-05-22 17:05:18,247 - GIN_TF_Local_Mode.Test - INFO - Test classification report saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_gin_tf_local\GIN_TF_Local_Model_test_classification_report.txt
2025-05-22 17:05:18,247 - GIN_TF_Local_Mode.Test - INFO - GIN_TF_Local_Model Test Metrics: Accuracy=0.9685, F1 (Weighted)=0.9682
2025-05-22 17:05:18,251 - GIN_TF_Local_Mode - INFO - GIN (TF) local mode run summary saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_gin_tf_local\GIN_TF_Local_Model_run_summary_tf.json
2025-05-22 17:05:18,251 - GIN_TF_Local_Mode - INFO - GIN TensorFlow Local Mode training script finished successfully.
