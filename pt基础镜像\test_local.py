#!/usr/bin/env python3
"""
BiLSTM推理服务本地测试脚本
测试BiLSTMPredictor.py的各种功能
"""

import os
import sys
import time
import json
import requests
import numpy as np
import threading
from typing import Dict, Any
import subprocess
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 测试配置
TEST_CONFIG = {
    "host": "localhost",
    "port": 5000,
    "base_url": "http://localhost:5000",
    "timeout": 30,
    "test_data": {
        "network_traffic": {
            "input_size": 84,
            "test_data": [0.1] * 84,
            "expected_classes": ["Benign", "DDoS", "PortScan", "Bot", "Infiltration", "WebAttack"]
        },
        "iris": {
            "input_size": 4,
            "test_data": [5.1, 3.5, 1.4, 0.2],
            "expected_classes": ["setosa", "versicolor", "virginica"]
        },
        "wine": {
            "input_size": 13,
            "test_data": [14.23, 1.71, 2.43, 15.6, 127, 2.8, 3.06, 0.28, 2.29, 5.64, 1.04, 3.92, 1065],
            "expected_classes": ["Class_1", "Class_2", "Class_3"]
        }
    }
}

class BiLSTMTester:
    """BiLSTM推理服务测试器"""
    
    def __init__(self):
        self.base_url = TEST_CONFIG["base_url"]
        self.timeout = TEST_CONFIG["timeout"]
        self.server_process = None
        self.test_results = []
        
    def log(self, message: str, level: str = "INFO"):
        """日志输出"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        colors = {
            "INFO": "\033[0;34m",    # 蓝色
            "SUCCESS": "\033[0;32m", # 绿色
            "WARNING": "\033[1;33m", # 黄色
            "ERROR": "\033[0;31m",   # 红色
            "RESET": "\033[0m"       # 重置
        }
        color = colors.get(level, colors["INFO"])
        reset = colors["RESET"]
        print(f"{color}[{timestamp}] [{level}]{reset} {message}")
    
    def start_server(self) -> bool:
        """启动测试服务器"""
        try:
            self.log("启动BiLSTM推理服务器...")
            
            # 设置环境变量
            env = os.environ.copy()
            env.update({
                "DATASET_TYPE": "network_traffic",
                "MODEL_DIR": ".",
                "HOST": "0.0.0.0",
                "PORT": str(TEST_CONFIG["port"])
            })
            
            # 启动服务器
            self.server_process = subprocess.Popen(
                [sys.executable, "BiLSTMPredictor.py", "--mode", "server"],
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待服务器启动
            self.log("等待服务器启动...")
            max_retries = 30
            for i in range(max_retries):
                try:
                    response = requests.get(f"{self.base_url}/health", timeout=5)
                    if response.status_code == 200:
                        self.log("服务器启动成功!", "SUCCESS")
                        return True
                except requests.exceptions.RequestException:
                    time.sleep(1)
                    if i % 5 == 0:
                        self.log(f"等待服务器启动... ({i+1}/{max_retries})")
            
            self.log("服务器启动超时", "ERROR")
            return False
            
        except Exception as e:
            self.log(f"启动服务器失败: {e}", "ERROR")
            return False
    
    def stop_server(self):
        """停止测试服务器"""
        if self.server_process:
            self.log("正在停止服务器...")
            self.server_process.terminate()
            try:
                self.server_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.server_process.kill()
            self.log("服务器已停止", "SUCCESS")
    
    def test_health_check(self) -> bool:
        """测试健康检查"""
        self.log("测试健康检查...")
        try:
            response = requests.get(f"{self.base_url}/health", timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "healthy":
                    self.log("✅ 健康检查通过", "SUCCESS")
                    return True
                else:
                    self.log(f"❌ 健康检查失败: {data}", "ERROR")
                    return False
            else:
                self.log(f"❌ 健康检查HTTP错误: {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ 健康检查异常: {e}", "ERROR")
            return False
    
    def test_service_info(self) -> bool:
        """测试服务信息"""
        self.log("测试服务信息...")
        try:
            response = requests.get(f"{self.base_url}/", timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                self.log(f"✅ 服务信息: {data.get('service', 'Unknown')}", "SUCCESS")
                return True
            else:
                self.log(f"❌ 服务信息HTTP错误: {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ 服务信息异常: {e}", "ERROR")
            return False
    
    def test_models_list(self) -> bool:
        """测试模型列表"""
        self.log("测试模型列表...")
        try:
            response = requests.get(f"{self.base_url}/models", timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                models = data.get("available_models", [])
                self.log(f"✅ 可用模型: {models}", "SUCCESS")
                return True
            else:
                self.log(f"❌ 模型列表HTTP错误: {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ 模型列表异常: {e}", "ERROR")
            return False
    
    def test_prediction(self, dataset_type: str = "network_traffic") -> bool:
        """测试推理预测"""
        self.log(f"测试推理预测 ({dataset_type})...")
        
        try:
            config = TEST_CONFIG["test_data"].get(dataset_type)
            if not config:
                self.log(f"❌ 未找到数据集配置: {dataset_type}", "ERROR")
                return False
            
            # 准备测试数据
            test_data = config["test_data"]
            
            # 发送预测请求
            payload = {"data": [test_data]}
            response = requests.post(
                f"{self.base_url}/predict",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                predicted_class = result.get("predicted_class")
                confidence = result.get("confidence")
                
                if predicted_class and confidence is not None:
                    self.log(f"✅ 预测结果: {predicted_class} (置信度: {confidence:.4f})", "SUCCESS")
                    
                    # 检查预测类别是否在期望范围内
                    expected_classes = config["expected_classes"]
                    if predicted_class in expected_classes:
                        self.log("✅ 预测类别有效", "SUCCESS")
                        return True
                    else:
                        self.log(f"⚠️ 预测类别不在期望范围: {expected_classes}", "WARNING")
                        return True  # 仍然算成功，只是警告
                else:
                    self.log(f"❌ 预测响应格式错误: {result}", "ERROR")
                    return False
            else:
                self.log(f"❌ 预测HTTP错误: {response.status_code} - {response.text}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ 预测异常: {e}", "ERROR")
            return False
    
    def test_batch_prediction(self) -> bool:
        """测试批量预测"""
        self.log("测试批量预测...")
        
        try:
            # 准备批量测试数据
            config = TEST_CONFIG["test_data"]["network_traffic"]
            test_data = [config["test_data"]] * 3  # 3个相同的样本
            
            payload = {"data": test_data}
            response = requests.post(
                f"{self.base_url}/predict",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                predictions = result.get("predictions", [])
                
                if len(predictions) == 3:
                    self.log(f"✅ 批量预测成功: {len(predictions)} 个结果", "SUCCESS")
                    for i, pred in enumerate(predictions):
                        self.log(f"   样本{i+1}: {pred['predicted_class']} ({pred['confidence']:.4f})")
                    return True
                else:
                    self.log(f"❌ 批量预测结果数量错误: 期望3个，得到{len(predictions)}个", "ERROR")
                    return False
            else:
                self.log(f"❌ 批量预测HTTP错误: {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ 批量预测异常: {e}", "ERROR")
            return False
    
    def test_seldon_format(self) -> bool:
        """测试Seldon格式预测"""
        self.log("测试Seldon格式预测...")
        
        try:
            config = TEST_CONFIG["test_data"]["network_traffic"]
            test_data = [config["test_data"]]
            
            payload = {
                "data": test_data,
                "meta": {"seldon_format": True}
            }
            
            response = requests.post(
                f"{self.base_url}/predict",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                # Seldon格式应该返回概率数组
                if isinstance(result, list) and len(result) > 0:
                    self.log(f"✅ Seldon格式预测成功: {len(result)} 个概率值", "SUCCESS")
                    return True
                else:
                    self.log(f"❌ Seldon格式响应错误: {result}", "ERROR")
                    return False
            else:
                self.log(f"❌ Seldon格式HTTP错误: {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ Seldon格式异常: {e}", "ERROR")
            return False
    
    def test_error_handling(self) -> bool:
        """测试错误处理"""
        self.log("测试错误处理...")
        
        test_cases = [
            {
                "name": "空数据",
                "payload": {"data": []},
                "should_fail": True
            },
            {
                "name": "错误维度",
                "payload": {"data": [[1, 2, 3]]},  # 维度不匹配
                "should_fail": True
            },
            {
                "name": "无效JSON",
                "payload": "invalid json",
                "should_fail": True
            }
        ]
        
        success_count = 0
        for case in test_cases:
            try:
                if isinstance(case["payload"], str):
                    # 发送无效JSON
                    response = requests.post(
                        f"{self.base_url}/predict",
                        data=case["payload"],
                        headers={"Content-Type": "application/json"},
                        timeout=self.timeout
                    )
                else:
                    response = requests.post(
                        f"{self.base_url}/predict",
                        json=case["payload"],
                        headers={"Content-Type": "application/json"},
                        timeout=self.timeout
                    )
                
                if case["should_fail"] and response.status_code != 200:
                    self.log(f"✅ {case['name']}: 正确处理错误 ({response.status_code})", "SUCCESS")
                    success_count += 1
                elif not case["should_fail"] and response.status_code == 200:
                    self.log(f"✅ {case['name']}: 请求成功", "SUCCESS")
                    success_count += 1
                else:
                    self.log(f"❌ {case['name']}: 错误处理不当", "ERROR")
                    
            except Exception as e:
                if case["should_fail"]:
                    self.log(f"✅ {case['name']}: 正确抛出异常", "SUCCESS")
                    success_count += 1
                else:
                    self.log(f"❌ {case['name']}: 意外异常 {e}", "ERROR")
        
        return success_count == len(test_cases)
    
    def test_performance(self) -> bool:
        """测试性能"""
        self.log("测试性能...")
        
        try:
            config = TEST_CONFIG["test_data"]["network_traffic"]
            test_data = [config["test_data"]]
            payload = {"data": test_data}
            
            # 预热
            requests.post(f"{self.base_url}/predict", json=payload, timeout=self.timeout)
            
            # 性能测试
            num_requests = 10
            start_time = time.time()
            
            for _ in range(num_requests):
                response = requests.post(f"{self.base_url}/predict", json=payload, timeout=self.timeout)
                if response.status_code != 200:
                    self.log(f"❌ 性能测试请求失败: {response.status_code}", "ERROR")
                    return False
            
            end_time = time.time()
            total_time = end_time - start_time
            avg_time = total_time / num_requests
            
            self.log(f"✅ 性能测试完成:", "SUCCESS")
            self.log(f"   总请求数: {num_requests}")
            self.log(f"   总耗时: {total_time:.2f}秒")
            self.log(f"   平均响应时间: {avg_time*1000:.1f}ms")
            self.log(f"   QPS: {num_requests/total_time:.1f}")
            
            # 性能标准：平均响应时间应小于1秒
            if avg_time < 1.0:
                self.log("✅ 性能测试通过", "SUCCESS")
                return True
            else:
                self.log("⚠️ 性能测试警告: 响应时间较慢", "WARNING")
                return True  # 仍然算通过，只是警告
                
        except Exception as e:
            self.log(f"❌ 性能测试异常: {e}", "ERROR")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        self.log("="*60)
        self.log("开始BiLSTM推理服务完整测试", "INFO")
        self.log("="*60)
        
        # 启动服务器
        if not self.start_server():
            return {"server_start": False}
        
        try:
            # 等待服务器完全启动
            time.sleep(3)
            
            # 定义测试用例
            tests = [
                ("健康检查", self.test_health_check),
                ("服务信息", self.test_service_info),
                ("模型列表", self.test_models_list),
                ("单样本预测", self.test_prediction),
                ("批量预测", self.test_batch_prediction),
                ("Seldon格式", self.test_seldon_format),
                ("错误处理", self.test_error_handling),
                ("性能测试", self.test_performance),
            ]
            
            # 运行测试
            results = {}
            passed = 0
            
            for test_name, test_func in tests:
                self.log(f"\n--- {test_name} ---")
                try:
                    result = test_func()
                    results[test_name] = result
                    if result:
                        passed += 1
                except Exception as e:
                    self.log(f"❌ {test_name}执行异常: {e}", "ERROR")
                    results[test_name] = False
            
            # 测试总结
            self.log("\n" + "="*60)
            self.log("测试总结", "INFO")
            self.log("="*60)
            
            for test_name, result in results.items():
                status = "✅ 通过" if result else "❌ 失败"
                level = "SUCCESS" if result else "ERROR"
                self.log(f"{test_name}: {status}", level)
            
            self.log(f"\n总计: {passed}/{len(tests)} 项测试通过")
            
            if passed == len(tests):
                self.log("🎉 所有测试通过!", "SUCCESS")
            elif passed >= len(tests) * 0.8:
                self.log("⚠️ 大部分测试通过", "WARNING")
            else:
                self.log("❌ 多项测试失败", "ERROR")
            
            return results
            
        finally:
            self.stop_server()

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="BiLSTM推理服务测试脚本")
    parser.add_argument("--host", default="localhost", help="服务器主机")
    parser.add_argument("--port", type=int, default=5000, help="服务器端口")
    parser.add_argument("--dataset", default="network_traffic", 
                       choices=["network_traffic", "iris", "wine"],
                       help="测试数据集类型")
    
    args = parser.parse_args()
    
    # 更新测试配置
    TEST_CONFIG["host"] = args.host
    TEST_CONFIG["port"] = args.port
    TEST_CONFIG["base_url"] = f"http://{args.host}:{args.port}"
    
    # 运行测试
    tester = BiLSTMTester()
    results = tester.run_all_tests()
    
    # 退出码
    success_rate = sum(results.values()) / len(results) if results else 0
    exit_code = 0 if success_rate >= 0.8 else 1
    sys.exit(exit_code)

if __name__ == "__main__":
    main() 