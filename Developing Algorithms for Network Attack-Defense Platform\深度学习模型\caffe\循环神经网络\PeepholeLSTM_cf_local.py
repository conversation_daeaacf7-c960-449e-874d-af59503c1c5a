import numpy as np
import caffe
from caffe import layers as L
from caffe import params as P
import os
import pickle
import json
import sys
import argparse
import requests
from minio import Minio
import uuid

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

def create_peephole_lstm_net(input_size, hidden_size, output_size, num_layers, batch_size=1):
    """Create PeepholeLSTM network architecture"""
    net = caffe.NetSpec()
    
    # Input layer
    net.data = L.Input(shape=[dict(dim=[batch_size, input_size, 1, 1])])
    
    prev_layer = net.data
    for i in range(num_layers):
        # LSTM layer with peephole connections
        lstm_name = f'lstm{i+1}'
        net[lstm_name] = L.LSTM(prev_layer,
                               num_output=hidden_size,
                               weight_filler=dict(type='xavier'),
                               bias_filler=dict(type='constant', value=0),
                               param=[
                                   dict(name=f'{lstm_name}_w'),
                                   dict(name=f'{lstm_name}_b')
                               ])
        
        prev_layer = net[lstm_name]
    
    # Fully connected layer
    net.fc = L.InnerProduct(prev_layer,
                           num_output=output_size,
                           weight_filler=dict(type='xavier'),
                           bias_filler=dict(type='constant', value=0))
    
    return net.to_proto()

def save_network_definition(net_proto, filepath):
    """Save network definition to prototxt file"""
    with open(filepath, 'w') as f:
        f.write(str(net_proto))

def create_solver(model_file, train_net_path, test_net_path=None):
    """Create Caffe solver settings"""
    s = caffe.SolverParameter()
    s.train_net = train_net_path
    if test_net_path:
        s.test_net.append(test_net_path)
    
    # Solver parameters
    s.max_iter = 1000
    s.type = "Adam"
    s.base_lr = 0.001
    s.momentum = 0.9
    s.momentum2 = 0.999
    s.lr_policy = "step"
    s.gamma = 0.1
    s.stepsize = 200
    s.display = 100
    s.snapshot = 500
    s.snapshot_prefix = model_file
    s.solver_mode = caffe.SOLVER_MODE_GPU
    
    return s

def peephole_lstm_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """Train PeepholeLSTM model using Caffe"""
    training_data_path = "/workspace/" + dataset["training_data_path"]
    with open(training_data_path, 'rb') as f:
        training_data, validation_data, test_data = pickle.load(f, encoding='bytes')
    
    train_x, train_y = training_data
    test_x, test_y = test_data
    
    # Get model parameters
    input_size = job_params["input_size"]
    hidden_size = job_params["hidden_size"]
    output_size = job_params["output_size"]
    num_layers = job_params["num_layers"]
    
    # Create network architecture
    net = create_peephole_lstm_net(input_size, hidden_size, output_size, num_layers)
    
    # Save network definition
    model_file = os.path.join(result_dir, model_name)
    train_net_path = model_file + '_train.prototxt'
    save_network_definition(net, train_net_path)
    
    # Create and save solver
    solver_param = create_solver(model_file, train_net_path)
    solver_path = model_file + '_solver.prototxt'
    with open(solver_path, 'w') as f:
        f.write(str(solver_param))
    
    # Initialize solver
    solver = caffe.get_solver(solver_path)
    
    # Training loop
    max_iter = job_params.get("num_epochs", 1000)
    display_interval = 100
    
    for it in range(max_iter):
        solver.step(1)
        
        if it % display_interval == 0:
            print(f'Iteration {it}, loss: {solver.net.blobs["loss"].data}')
    
    # Save trained model
    solver.net.save(model_file + '.caffemodel')
    return None, 0

def model_upload(result_dir, model_name):    
    minioClient = Minio(MINIO_URL,
                       access_key='AKIAIOSFODNN7EXAMPLE',
                       secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                       secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".caffemodel"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, 
                                    result_dir + "/" + model_name + ".caffemodel")
        result = {"source": source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1

def model_register(model_name, source, group_id, headers):    
    params = {
        "model_name": model_name,
        "model_type": "caffe",
        "file_name": model_name + ".caffemodel",
        "s3_path": source,
        "group_id": int(float(group_id)),
        "training_id": model_name,
        "training_flag": 1,
    }
    
    r = requests.post(MODEL_FACTORY_URL + MODEL_ADD_URL,
                     data=json.dumps(params),
                     headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "PeepholeLSTM",
        "model_usage": "Regression"
    }
    r = requests.post(MODEL_FACTORY_URL + MODEL_PUSH_URL + "/" + str(model_version_id),
                     data=json.dumps(params),
                     headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: user_id
    }
    return headers

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Caffe PeepholeLSTM Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                       help='PeepholeLSTM Job Params')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                       help='PeepholeLSTM DataSet')
    parser.add_argument('--model', dest='model', type=json.loads,
                       help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                       help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                       help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                       help='fit params')
    
    args = parser.parse_args()
    
    print("Start PeepholeLSTM training job, params:\n" + str(sys.argv) + "\n")
    
    job_params = args.job_params
    dataset = args.dataset
    model = args.model
    result_dir = args.result_dir
    factory_name = args.factory_name
    fit_params = args.fit_params or {}
    
    print("Step 1 PeepholeLSTM training:\n")
    result, ret_code = peephole_lstm_train(dataset, job_params, 
                                         model["model_name"], result_dir, fit_params)
    if ret_code != 0:
        print("PeepholeLSTM train err, stop job....\n")
        print("Error Msg:" + result + "\n")
        sys.exit(-1)
    
    print("Step 2 Model Upload to MinIO:\n")
    result, ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    
    print("Step 3 Model Register:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result, ret_code = model_register(model["model_name"], source, 
                                    model["group_id"], headers)
    if ret_code != 0:
        print("model register err, stop job....,err msg: " + result)
        sys.exit(-1)
    
    print("Step 4 Model Push:\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result, ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: " + result + "\n")
    
    print("Job End..\n")
    sys.exit()