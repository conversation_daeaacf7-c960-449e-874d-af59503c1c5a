"""BiLSTM"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import pandas as pd
import pickle
from sklearn.preprocessing import LabelEncoder
from sklearn.preprocessing import OrdinalEncoder

class BiLSTMModel(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers, num_classes):
        super(BiLSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.bilstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True, bidirectional=True)
        self.fc = nn.Linear(hidden_size * 2, num_classes)

    def forward(self, x):
        # 检查输入维度并调整
        if x.dim() == 2:
            x = x.unsqueeze(1)  # 添加时间步维度
        elif x.dim() != 3:
            raise ValueError(f"Expected 2D or 3D input, but got {x.dim()}D input")

        # x shape: (batch_size, sequence_length, input_size)
        batch_size, seq_len, _ = x.size()
        
        # 初始化隐藏状态和单元状态
        h0 = torch.zeros(self.num_layers * 2, batch_size, self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers * 2, batch_size, self.hidden_size).to(x.device)
        
        # 通过LSTM
        out, _ = self.bilstm(x, (h0, c0))
        
        # 只使用最后一个时间步的输出
        out = self.fc(out[:, -1, :])
        return out

def load_data(file_path):
    # 从pkl文件中加载数据
    with open(file_path, 'rb') as file:
        data = pickle.load(file)
    
    # 假设数据是一个DataFrame格式，或者是特征和标签分别存储的形式
    if isinstance(data, pd.DataFrame):
        X = data.drop(['Label'], axis=1)  # 假设 'Label' 是标签列
        y = data['Label']
    elif isinstance(data, dict):  # 如果是字典形式
        X = data['features']  # 假设特征存储在 'features' 键下
        y = data['labels']    # 假设标签存储在 'labels' 键下
    
    return X, y

#读取 .pkl 文件之后加入预处理逻辑，需要先从 .pkl 文件中反序列化数据，然后执行相应的预处理步骤。
def load_data_preprocess(pkl_file):
    # 从pkl文件中加载数据
    with open(pkl_file, 'rb') as file:
        data = pickle.load(file)
    
    # 清理列名，去除可能的空格
    data.columns = data.columns.str.strip()

    print(type(data))  # 检查数据类型
    print(data.head())  # 查看前几行数据
    print(data.info())  # 查看数据信息


    # 假设数据是一个DataFrame格式，或者是特征和标签分别存储的形式
    if isinstance(data, pd.DataFrame):
        print(data.columns)
        X = data.drop(['Label'], axis=1)  # 假设 'Label' 是标签列
        y = data['Label']
    elif isinstance(data, dict):  # 如果是字典形式
        X = data['features']  # 假设特征存储在 'features' 键下
        y = data['labels']    # 假设标签存储在 'labels' 键下
    
    return X, y

def preprocess_data(X, y):
    # 1. 处理缺失值（删除缺失值）
    if X.isnull().any().any() or y.isnull().any():
        # 删除包含缺失值的行
        X, y = X.align(y, join='inner', axis=0)
        X = X.dropna()
        y = y[X.index]  # 保证 y 和 X 同步
    

    # 2. 标签编码
    le = LabelEncoder()
    y = le.fit_transform(y)

    # 3. 分类特征的编码（如果存在分类特征）
    categorical_cols = X.select_dtypes(include=['object']).columns
    encoder = OrdinalEncoder()
    if not categorical_cols.empty:
        X[categorical_cols] = encoder.fit_transform(X[categorical_cols])
    
    # 4. 检查并处理无限值
    X.replace([np.inf, -np.inf], np.nan, inplace=True)  # 替换为 NaN
    X.fillna(X.mean(), inplace=True)  # 用均值填充 NaN
    
    # 5. 数据标准化
    scaler = StandardScaler()
    X = scaler.fit_transform(X)

    # 6. 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    return X_train, X_test, y_train, y_test

# 数据准备
def prepare_dataset(train_x, train_y, test_x, test_y):
    train_x = torch.tensor(train_x, dtype=torch.float32)
    train_y = torch.tensor(train_y, dtype=torch.long)  # 分类任务的标签应为 long 类型
    test_x = torch.tensor(test_x, dtype=torch.float32)
    test_y = torch.tensor(test_y, dtype=torch.long)
    
    train_dataset = TensorDataset(train_x, train_y)
    test_dataset = TensorDataset(test_x, test_y)
    
    return train_dataset, test_dataset

# 训练函数
def train_model(train_loader, model, criterion, optimizer, num_epochs, device):
    model.to(device)

    for epoch in range(num_epochs):
        model.train()
        running_loss = 0.0

        for inputs, labels in train_loader:
            inputs, labels = inputs.to(device), labels.to(device)

            optimizer.zero_grad()
            outputs = model(inputs)
            
            # 打印形状以进行调试
            # print(f"Inputs shape: {inputs.shape}")
            # print(f"Outputs shape: {outputs.shape}")
            # print(f"Labels shape: {labels.shape}")
            
            # 确保输出和标签的维度正确
            assert outputs.shape[1] == 6, f"Expected output dimension 6, but got {outputs.shape[1]}"
            assert labels.dim() == 1, f"Expected labels to be 1D, but got {labels.dim()}D"
            assert outputs.shape[0] == labels.shape[0], f"Batch size mismatch: outputs {outputs.shape[0]}, labels {labels.shape[0]}"

            loss = criterion(outputs, labels.long())
            loss.backward()
            optimizer.step()

            running_loss += loss.item()

        print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {running_loss/len(train_loader):.4f}')


# 测试函数
def test_model(test_loader, model, device):
    model.to(device)  # 将模型移到指定设备
    model.eval()  # 设置模型为评估模式
    correct, total = 0, 0

    with torch.no_grad():
        for inputs, labels in test_loader:
            inputs, labels = inputs.to(device), labels.to(device)  # 将数据移到指定设备
            outputs = model(inputs)
            _, predicted = torch.max(outputs, 1)  # 使用 softmax 输出中最大的值作为预测类别
            total += labels.size(0)
            correct += (predicted == labels).sum().item()

    print(f'Accuracy: {100 * correct / total:.2f}%')




# def bilstm_analysis(input_file, input_column, target_column, input_size, hidden_size, num_layers, output_size, epochs, batch_size):
#     """
#     参数说明：
#     - input_file: 输入CSV文件路径
#     - input_column: 输入特征列名列表
#     - target_column: 目标列名
#     - input_size: 输入特征维度
#     - hidden_size: 隐藏层大小
#     - num_layers: LSTM层数
#     - output_size: 输出大小
#     - epochs: 训练轮数
#     - batch_size: 批次大小
#     """
#     data = pd.read_csv(input_file)
#     X = data[input_column].values
#     y = data[target_column].values

#     scaler = StandardScaler()
#     X = scaler.fit_transform(X)

#     X = np.reshape(X, (X.shape[0], 1, X.shape[1]))

#     X = torch.tensor(X, dtype=torch.float32)
#     y = torch.tensor(y, dtype=torch.float32)

#     dataset = TensorDataset(X, y)
#     dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)

#     model = BiLSTMModel(input_size, hidden_size, num_layers, output_size)
#     criterion = nn.MSELoss()
#     optimizer = optim.Adam(model.parameters(), lr=0.001)

#     for epoch in range(epochs):
#         for inputs, targets in dataloader:
#             outputs = model(inputs)
#             loss = criterion(outputs, targets)
#             optimizer.zero_grad()
#             loss.backward()
#             optimizer.step()
#         print(f"Epoch {epoch+1}/{epochs}, Loss: {loss.item()}")

#     print("BiLSTM training completed")

def bilstm_train(input_file, input_size, hidden_size, output_size, num_layers, num_epochs, learning_rate, batch_size, result_dir, model_name,device):

    """
    参数说明：
    - input_file: 输入pkl文件路径
    - input_size: 输入特征维度
    - hidden_size: 隐藏层大小
    - num_layers: LSTM层数
    - output_size: 输出大小（类别数）
    - epochs: 训练轮数
    - batch_size: 批次大小
    """

    # 加载数据 (pkl文件)
    X, y = load_data_preprocess(input_file)

    # 数据预处理
    X_train, X_test, y_train, y_test = preprocess_data(X, y)

    # 准备数据集
    train_dataset, test_dataset = prepare_dataset(X_train, y_train, X_test, y_test)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

    # 初始化BiLSTM模型
    model = BiLSTMModel(input_size, hidden_size, num_layers, output_size)
    criterion = nn.CrossEntropyLoss()  # 使用交叉熵损失函数
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)

    # 训练和测试模型
    train_model(train_loader, model, criterion, optimizer, num_epochs,device)
    test_model(test_loader, model,device)

    # 保存模型
    model_file = open(result_dir + "/" + model_name + ".pth", "wb")
    torch.save(model.state_dict(), model_file)
    model_file.close()
    print(f'BiLSTM训练完成，模型保存到 {result_dir}/{model_name}.pth')
    return None, 0


if __name__ == "__main__":
    input_file = 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl'
    input_size = 84
    hidden_size = 128
    num_layers = 2
    output_size = 6
    num_epochs = 10
    learning_rate = 0.001
    batch_size = 32
    result_dir = 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_'
    model_name = 'BiLSTM_pt_25'

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
#input_file, input_size, hidden_size, output_size, num_layers, num_epochs, learning_rate, batch_size, result_dir, model_name
    bilstm_train(input_file, input_size, hidden_size, output_size, num_layers, num_epochs, learning_rate, batch_size, result_dir, model_name,device)
