"""fastRCNN"""

import os
import sys
import numpy as np
import caffe
from caffe import layers as L
from caffe import params as P
import json
import argparse
import pickle
from minio import Minio
import uuid
import requests
import xml.etree.ElementTree as ET
from PIL import Image

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

def create_fastrcnn_architecture(net, num_classes):
    """定义Fast R-CNN网络架构"""
    # 数据层
    net.data, net.rois, net.labels = L.Python(module='roi_data_layer.layer',
                                             layer='RoIDataLayer',
                                             param_str=str(dict(num_classes=num_classes)),
                                             ntop=3)
    
    # ResNet-50 主干网络
    # conv1
    net.conv1 = L.Convolution(net.data, kernel_size=7, stride=2, num_output=64,
                             pad=3, weight_filler=dict(type='msra'))
    net.bn1 = L.BatchNorm(net.conv1, use_global_stats=True)
    net.relu1 = L.ReLU(net.bn1)
    net.pool1 = L.Pooling(net.relu1, pool=P.Pooling.MAX, kernel_size=3, stride=2)
    
    # ResNet blocks (简化版本)
    net.res2 = residual_block(net.pool1, base_channels=64, num_blocks=3)
    net.res3 = residual_block(net.res2, base_channels=128, num_blocks=4, stride=2)
    net.res4 = residual_block(net.res3, base_channels=256, num_blocks=6, stride=2)
    net.res5 = residual_block(net.res4, base_channels=512, num_blocks=3, stride=2)
    
    # RoI pooling
    net.roi_pool = L.ROIPooling(net.res5, net.rois,
                               pooled_w=7, pooled_h=7, spatial_scale=1.0/16)
    
    # FC layers
    net.fc6 = L.InnerProduct(net.roi_pool, num_output=4096)
    net.relu6 = L.ReLU(net.fc6)
    net.drop6 = L.Dropout(net.relu6, dropout_ratio=0.5)
    
    net.fc7 = L.InnerProduct(net.drop6, num_output=4096)
    net.relu7 = L.ReLU(net.fc7)
    net.drop7 = L.Dropout(net.relu7, dropout_ratio=0.5)
    
    # Output layers
    net.cls_score = L.InnerProduct(net.drop7, num_output=num_classes)
    net.bbox_pred = L.InnerProduct(net.drop7, num_output=num_classes * 4)
    
    # Loss layers
    net.loss_cls = L.SoftmaxWithLoss(net.cls_score, net.labels)
    net.loss_bbox = L.SmoothL1Loss(net.bbox_pred, net.bbox_targets)
    
    return net

def residual_block(bottom, base_channels, num_blocks, stride=1):
    """创建ResNet残差块"""
    layers = []
    in_channels = bottom.num_output
    
    for i in range(num_blocks):
        if i == 0:
            layer = create_res_block(bottom, base_channels, stride, True)
        else:
            layer = create_res_block(layer, base_channels, 1, False)
        layers.append(layer)
    
    return layers[-1]

def create_res_block(bottom, num_output, stride, is_first):
    """创建单个残差块"""
    conv1 = L.Convolution(bottom, kernel_size=1, stride=1, num_output=num_output,
                         pad=0, weight_filler=dict(type='msra'))
    bn1 = L.BatchNorm(conv1, use_global_stats=True)
    relu1 = L.ReLU(bn1)
    
    conv2 = L.Convolution(relu1, kernel_size=3, stride=stride, num_output=num_output,
                         pad=1, weight_filler=dict(type='msra'))
    bn2 = L.BatchNorm(conv2, use_global_stats=True)
    relu2 = L.ReLU(bn2)
    
    conv3 = L.Convolution(relu2, kernel_size=1, stride=1, num_output=num_output * 4,
                         pad=0, weight_filler=dict(type='msra'))
    bn3 = L.BatchNorm(conv3, use_global_stats=True)
    
    if is_first or stride != 1:
        shortcut = L.Convolution(bottom, kernel_size=1, stride=stride,
                               num_output=num_output * 4, weight_filler=dict(type='msra'))
        shortcut = L.BatchNorm(shortcut, use_global_stats=True)
    else:
        shortcut = bottom
    
    return L.ReLU(L.Eltwise(bn3, shortcut, operation=P.Eltwise.SUM))

class UniversalDataLayer(caffe.Layer):
    """通用数据层实现"""
    def setup(self, bottom, top):
        self.batch_size = 32
        self.transform_param = {
            'mirror': True,
            'crop_size': 224,
            'mean_value': [104, 117, 123]
        }
        
        # 解析数据源类型和路径
        params = eval(self.param_str)
        self.data_dir = params['data_dir']
        self.dataset_type = params['dataset_type']
        self.annotations_file = params.get('annotations_file', None)
        
        # 加载数据
        self.load_data()
        
    def load_data(self):
        if self.dataset_type == 'voc':
            self.load_voc_data()
        elif self.dataset_type == 'coco':
            self.load_coco_data()
        elif self.dataset_type == 'pickle':
            self.load_pickle_data()
            
    def reshape(self, bottom, top):
        # 重新设置数据维度
        top[0].reshape(self.batch_size, 3, 224, 224)  # 图像数据
        top[1].reshape(self.batch_size, 5)  # RoI数据
        top[2].reshape(self.batch_size)  # 标签
        
    def forward(self, bottom, top):
        # 前向传播实现
        indices = np.random.choice(len(self.data), self.batch_size)
        
        batch_data = []
        batch_rois = []
        batch_labels = []
        
        for idx in indices:
            img_path, rois, labels = self.data[idx]
            # 加载和预处理图像
            img = self.load_image(img_path)
            # 准备RoI和标签
            batch_data.append(img)
            batch_rois.append(rois)
            batch_labels.append(labels)
            
        # 填充top blob
        top[0].data[...] = np.array(batch_data)
        top[1].data[...] = np.array(batch_rois)
        top[2].data[...] = np.array(batch_labels)
        
    def backward(self, top, propagate_down, bottom):
        pass
        
    def load_image(self, img_path):
        """加载和预处理图像"""
        img = Image.open(img_path).convert('RGB')
        img = img.resize((224, 224))
        img = np.array(img).transpose((2, 0, 1))
        img = img.astype(np.float32)
        img -= np.array(self.transform_param['mean_value'])[:, np.newaxis, np.newaxis]
        return img

def train_model(solver_path, pretrained_model=None):
    """训练Fast R-CNN模型"""
    caffe.set_device(0)
    caffe.set_mode_gpu()
    
    solver = caffe.SGDSolver(solver_path)
    if pretrained_model:
        solver.net.copy_from(pretrained_model)
    
    print('开始训练...')
    solver.solve()
    
def create_solver(model_path, snapshot_prefix):
    """创建solver配置"""
    s = caffe.SolverParameter()
    s.train_net = model_path
    s.snapshot_prefix = snapshot_prefix
    
    # Solver参数
    s.base_lr = 0.001
    s.momentum = 0.9
    s.weight_decay = 0.0005
    s.lr_policy = 'step'
    s.gamma = 0.1
    s.stepsize = 20000
    s.display = 20
    s.max_iter = 100000
    s.snapshot = 5000
    
    return s

def fastRCNN_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """Fast R-CNN训练主函数"""
    try:
        # 创建网络定义
        net = caffe.NetParameter()
        create_fastrcnn_architecture(net, job_params['output_size'])
        
        # 保存网络定义
        model_path = os.path.join(result_dir, 'fastrcnn_train.prototxt')
        with open(model_path, 'w') as f:
            f.write(str(net))
            
        # 创建和保存solver配置
        solver = create_solver(model_path, os.path.join(result_dir, 'snapshot'))
        solver_path = os.path.join(result_dir, 'solver.prototxt')
        with open(solver_path, 'w') as f:
            f.write(str(solver))
            
        # 训练模型
        pretrained_model = os.path.join('/workspace/pretrained_model', 'ResNet-50-model.caffemodel')
        train_model(solver_path, pretrained_model)
        
        # 保存最终模型
        final_model = os.path.join(result_dir, f'{model_name}.caffemodel')
        solver.net.save(final_model)
        
        return None, 0
        
    except Exception as e:
        print(f"训练过程中发生错误: {e}")
        return str(e), -1

def model_upload(result_dir, model_name):
    """上传模型到MinIO"""
    minioClient = Minio(MINIO_URL,
                       access_key='AKIAIOSFODNN7EXAMPLE',
                       secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                       secure=False)
    try:
        obj_name = str(uuid.uuid1())
        upload_path = f"{obj_name}/{model_name}.caffemodel"
        source = f"s3://mlss-mf/{obj_name}"
        res = minioClient.fput_object('mlss-mf', upload_path, 
                                    os.path.join(result_dir, f"{model_name}.caffemodel"))
        return {"source": source}, 0
    except Exception as err:
        print(err)
        return None, -1

def model_register(model_name, source, group_id, headers):
    """注册模型"""
    params = {
        "model_name": model_name,
        "model_type": "caffe",
        "file_name": f"{model_name}.caffemodel",
        "s3_path": source,
        "group_id": int(float(group_id)),
        "training_id": model_name,
        "training_flag": 1,
    }
    
    r = requests.post(MODEL_FACTORY_URL + MODEL_ADD_URL,
                     data=json.dumps(params),
                     headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    """推送模型"""
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0
        
    params = {
        "factory_name": factory_name,
        "model_type": "fastRCNN",
        "model_usage": "Classification"
    }
    r = requests.post(f"{MODEL_FACTORY_URL}{MODEL_PUSH_URL}/{model_version_id}",
                     data=json.dumps(params),
                     headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def header_gen(user_id):
    """生成请求头"""
    return {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: user_id
    }

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Caffe Fast R-CNN Train.')
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                       help='Fast R-CNN Job Params')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='fastRCNN DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start fastRCNN training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("fastRCNN job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("fastRCNN dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("fastRCNN result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("fastRCNN factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("fastRCNN fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("fastRCNN sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    
    # device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print("Step 1 fastRCNN training:\n")
    result,ret_code = fastRCNN_train(dataset,job_params, model["model_name"],result_dir,fit_params)
    if ret_code != 0:
        print("fastRCNN train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()