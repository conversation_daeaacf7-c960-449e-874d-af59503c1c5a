2025-05-23 14:09:48,078 - GraphSAGE_TF_Local_MainScript - INFO - Logging configured. Log file: ./results/graphsage_tf_local_experiment\GraphSAGE_TF_Local_Model_training_20250523_140948.log
2025-05-23 14:09:48,078 - GraphSAGE_TF_Local_MainScript - INFO - GraphSAGE TensorFlow Local Mode training started for model: GraphSAGE_TF_Local_Model
2025-05-23 14:09:48,079 - GraphSAGE_TF_Local_MainScript - INFO - Training Parameters:
{
  "input_file": "E:/data/\u7f51\u7edc\u653b\u9632\u535a\u5f08\u5e73\u53f0\u6570\u636e\u96c6/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl",
  "result_dir": "./results/graphsage_tf_local_experiment",
  "model_name": "GraphSAGE_TF_Local_Model",
  "hidden_size": 128,
  "num_layers": 2,
  "dropout_rate": 0.5,
  "num_epochs": 3,
  "learning_rate": 0.001,
  "early_stopping_patience": 10,
  "label_column": "Label",
  "data_format": "pkl",
  "feature_columns": null,
  "normalize_features": true,
  "scaler_type": "standard",
  "fill_missing_method": "mean",
  "stratify_split": true,
  "test_split_size": 0.2,
  "random_state": 42,
  "edge_strategy": "knn",
  "k_neighbors": 10,
  "use_cross_validation": false,
  "cv_folds": 5
}
2025-05-23 14:09:48,081 - GraphSAGE_TF_Local_MainScript - INFO - No GPU found, using CPU.
2025-05-23 14:09:48,081 - GraphSAGE_TF_Local_MainScript - INFO - TensorFlow version: 2.13.0
2025-05-23 14:09:48,081 - GraphSAGE_TF_Local.DataLoad - INFO - Loading data from E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl with format pkl
2025-05-23 14:09:48,494 - GraphSAGE_TF_Local.DataLoad - INFO - Data loaded as DataFrame with shape (692703, 85)
2025-05-23 14:09:49,777 - GraphSAGE_TF_Local.DataLoad - INFO - Features shape: (692703, 84), Labels shape: (692703,)
2025-05-23 14:09:49,808 - GraphSAGE_TF_Local.DataLoad - INFO - Label distribution: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-23 14:09:49,886 - GraphSAGE_TF_Local_MainScript - INFO - Data loaded: X shape (692703, 84), y shape (692703,)
2025-05-23 14:09:49,886 - GraphSAGE_TF_Local_MainScript - INFO - Loaded feature names: ['Flow ID', 'Source IP', 'Source Port', 'Destination IP', 'Destination Port', 'Protocol', 'Timestamp', 'Flow Duration', 'Total Fwd Packets', 'Total Backward Packets']...
2025-05-23 14:09:49,887 - GraphSAGE_TF_Local_MainScript - INFO - Inferred class names from data load: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-23 14:09:49,887 - GraphSAGE_TF_Local_MainScript - INFO - Using standard Train/Test split.
2025-05-23 14:09:50,952 - GraphSAGE_TF_Local.PreProcess - INFO - Handling 1008 missing/infinite values in features.
2025-05-23 14:09:59,612 - GraphSAGE_TF_Local.PreProcess - INFO - Missing values after handling: 0
2025-05-23 14:09:59,641 - GraphSAGE_TF_Local.PreProcess - INFO - Ordinal encoding for categorical features: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-23 14:10:03,459 - GraphSAGE_TF_Local.PreProcess - INFO - Encoding labels.
2025-05-23 14:10:03,565 - GraphSAGE_TF_Local.PreProcess - INFO - Encoded 6 classes: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-23 14:10:03,778 - GraphSAGE_TF_Local.PreProcess - INFO - Normalizing features.
2025-05-23 14:10:05,533 - GraphSAGE_TF_Local.PreProcess - INFO - Data split: Train X (554162, 84), Y (554162,); Test X (138541, 84), Y (138541,)
2025-05-23 14:10:05,574 - GraphSAGE_TF_Local_MainScript - INFO - Processed data: Train X (554162, 84), Output classes: 6 (['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed'])
2025-05-23 14:10:05,580 - GraphSAGE_TF_Local_MainScript - INFO - Preprocessing info saved to ./results/graphsage_tf_local_experiment\preprocessing_info_GraphSAGE_TF_Local_Model.json
2025-05-23 14:10:05,581 - GraphSAGE_TF_Local.GraphData - INFO - Creating graph data using 'knn' strategy (k=10).
2025-05-23 14:16:22,235 - GraphSAGE_TF_Local.GraphData - INFO - Graph created: Nodes=554162, Edges=7119780 (unique, possibly bi-directional).
2025-05-23 14:16:22,635 - GraphSAGE_TF_Local.GraphData - INFO - Creating graph data using 'knn' strategy (k=10).
2025-05-23 14:16:47,891 - GraphSAGE_TF_Local.GraphData - INFO - Graph created: Nodes=138541, Edges=1791027 (unique, possibly bi-directional).
2025-05-23 14:16:48,049 - GraphSAGE_TF_Local_MainScript - INFO - Starting model training (standard split).
2025-05-23 14:16:48,050 - GraphSAGE_TF_Local.Train - INFO - Starting model training (GraphSAGE_TF_Local_Model) for 3 epochs.
2025-05-23 14:17:38,824 - GraphSAGE_TF_Local.Train - INFO - Epoch [1/3] Train Loss: 3.1872, Train Acc: 11.79%, Val Loss: 3.0383, Val Acc: 14.15% (Duration: 50.77s)
2025-05-23 14:18:11,190 - GraphSAGE_TF_Local.Train - INFO - Epoch [2/3] Train Loss: 2.7953, Train Acc: 14.24%, Val Loss: 2.5521, Val Acc: 16.84% (Duration: 32.35s)
2025-05-23 14:19:01,735 - GraphSAGE_TF_Local.Train - INFO - Epoch [3/3] Train Loss: 2.4219, Train Acc: 18.31%, Val Loss: 2.0984, Val Acc: 21.50% (Duration: 50.51s)
2025-05-23 14:19:22,315 - GraphSAGE_TF_Local.Plot - INFO - Training history plots saved to ./results/graphsage_tf_local_experiment\plots (suffix: '_GraphSAGE_TF_Local_Model')
2025-05-23 14:19:22,369 - GraphSAGE_TF_Local_MainScript - INFO - Evaluating model on the test set.
2025-05-23 14:19:22,549 - GraphSAGE_TF_Local.Test - INFO - Loading best model weights from ./results/graphsage_tf_local_experiment\GraphSAGE_TF_Local_Model_best.weights.h5 for testing.
2025-05-23 14:19:24,745 - GraphSAGE_TF_Local.Test - INFO - Test Set Evaluation - Loss: 2.0984, Accuracy: 21.50%
2025-05-23 14:19:25,650 - GraphSAGE_TF_Local.Test - INFO - Classification Report (TF GraphSAGE):
                  precision    recall  f1-score   support

          BENIGN       0.98      0.16      0.27     88006
   DoS GoldenEye       0.05      0.30      0.09      2059
        DoS Hulk       0.39      0.33      0.36     46215
DoS Slowhttptest       0.00      0.00      0.00      1100
   DoS slowloris       0.00      0.00      0.00      1159
      Heartbleed       0.00      1.00      0.00         2

        accuracy                           0.21    138541
       macro avg       0.24      0.30      0.12    138541
    weighted avg       0.76      0.21      0.29    138541

2025-05-23 14:19:29,522 - GraphSAGE_TF_Local.Plot - INFO - Confusion matrix plot saved to ./results/graphsage_tf_local_experiment\plots (suffix: '_GraphSAGE_TF_Local_Model')
2025-05-23 14:19:29,544 - GraphSAGE_TF_Local.Test - INFO - Classification report saved to ./results/graphsage_tf_local_experiment\classification_report_GraphSAGE_TF_Local_Model.txt
2025-05-23 14:19:29,544 - GraphSAGE_TF_Local.Test - INFO - TF Test Metrics: Acc=21.50%, F1=29.26%
2025-05-23 14:19:29,591 - GraphSAGE_TF_Local_MainScript - INFO - Final model weights (end of training) saved to ./results/graphsage_tf_local_experiment\GraphSAGE_TF_Local_Model_final.weights.h5
2025-05-23 14:19:29,592 - GraphSAGE_TF_Local_MainScript - INFO - Best model weights (during training) available at ./results/graphsage_tf_local_experiment\GraphSAGE_TF_Local_Model_best.weights.h5
2025-05-23 14:19:33,227 - GraphSAGE_TF_Local_MainScript - INFO - Preprocessing tools (scaler/encoders) from standard train saved in ./results/graphsage_tf_local_experiment
2025-05-23 14:19:33,375 - GraphSAGE_TF_Local_MainScript - INFO - Full training results summary saved to ./results/graphsage_tf_local_experiment\training_results_summary_GraphSAGE_TF_Local_Model.json
2025-05-23 14:19:33,375 - GraphSAGE_TF_Local_MainScript - INFO - GraphSAGE TensorFlow local mode training for 'GraphSAGE_TF_Local_Model' finished successfully.
2025-05-23 14:19:52,172 - GraphSAGE_TF_Local_MainScript - INFO - GraphSAGE TF training process for 'GraphSAGE_TF_Local_Model' finished successfully.
2025-05-23 14:19:52,172 - GraphSAGE_TF_Local_MainScript - INFO - Results, logs, and plots saved in: ./results/graphsage_tf_local_experiment
2025-05-23 14:19:52,172 - GraphSAGE_TF_Local_MainScript - INFO - Final model weights path: ./results/graphsage_tf_local_experiment\GraphSAGE_TF_Local_Model_final.weights.h5
2025-05-23 14:19:52,172 - GraphSAGE_TF_Local_MainScript - INFO - Standard Train/Test - Test Set Accuracy: 21.50%
2025-05-23 14:19:52,172 - GraphSAGE_TF_Local_MainScript - INFO - Standard Train/Test - Test Set F1 Score: 29.26%
2025-05-23 14:19:52,172 - GraphSAGE_TF_Local_MainScript - INFO - GraphSAGE TensorFlow Local Mode main script execution finished.
