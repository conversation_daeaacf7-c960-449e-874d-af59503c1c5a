2025-05-21 15:09:29,019 - GCN_TF_Local - INFO - 日志将记录到控制台和文件: results\results_gcn_tf_local\gcn_tf_local_training_20250521_150929.log
2025-05-21 15:09:29,019 - GCN_TF_Local - INFO - GCN TensorFlow 本地模式训练脚本已初始化。
2025-05-21 15:09:29,020 - GCN_TF_Local - INFO - TensorFlow 版本: 2.13.0
2025-05-21 15:09:29,020 - GCN_TF_Local - INFO - 参数: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'result_dir': 'results\\results_gcn_tf_local', 'model_name': 'GCN_TF_Local_Model', 'label_column': 'Label', 'data_format': 'pkl', 'hidden_size': 64, 'num_layers': 2, 'dropout_rate': 0.5, 'num_epochs': 3, 'learning_rate': 0.005, 'optimizer': 'adam', 'loss_function': 'sparsecategoricalcrossentropy', 'edge_strategy': 'fully_connected', 'test_split_size': 0.2, 'random_state': 42, 'normalize': True, 'scaler_type': 'standard', 'handle_imbalance': False, 'stratify_split': True, 'numeric_impute_strategy': 'mean', 'categorical_impute_strategy': 'most_frequent', 'use_cv': False, 'cv_folds': 5, 'log_level': 'INFO', 'force_cpu': False}
2025-05-21 15:09:29,023 - GCN_TF_Local - INFO - 未找到GPU或强制使用CPU。
2025-05-21 15:09:29,023 - GCN_TF_Local - INFO - 从 E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl (格式: pkl) 加载数据
2025-05-21 15:09:29,406 - GCN_TF_Local - INFO - 数据加载为 DataFrame，形状: (692703, 85)
2025-05-21 15:09:29,530 - GCN_TF_Local - ERROR - 值错误: could not convert string to float: '192.168.10.14-209.48.71.168-49459-80-6'
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 892, in gcn_train_tf_local_mode
    X_orig_df, y_orig_series = flexible_data_load_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 296, in flexible_data_load_local
    X = X_df.to_numpy(dtype=np.float32)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\pandas\core\frame.py", line 1838, in to_numpy
    result = self._mgr.as_array(dtype=dtype, copy=copy, na_value=na_value)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\pandas\core\internals\managers.py", line 1732, in as_array
    arr = self._interleave(dtype=dtype, na_value=na_value)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\pandas\core\internals\managers.py", line 1794, in _interleave
    result[rl.indexer] = arr
ValueError: could not convert string to float: '192.168.10.14-209.48.71.168-49459-80-6'
2025-05-21 15:09:29,539 - GCN_TF_Local - CRITICAL - 主程序中发生严重错误: could not convert string to float: '192.168.10.14-209.48.71.168-49459-80-6'
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 1104, in <module>
    gcn_train_tf_local_mode(parsed_args)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 892, in gcn_train_tf_local_mode
    X_orig_df, y_orig_series = flexible_data_load_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 296, in flexible_data_load_local
    X = X_df.to_numpy(dtype=np.float32)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\pandas\core\frame.py", line 1838, in to_numpy
    result = self._mgr.as_array(dtype=dtype, copy=copy, na_value=na_value)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\pandas\core\internals\managers.py", line 1732, in as_array
    arr = self._interleave(dtype=dtype, na_value=na_value)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\pandas\core\internals\managers.py", line 1794, in _interleave
    result[rl.indexer] = arr
ValueError: could not convert string to float: '192.168.10.14-209.48.71.168-49459-80-6'
