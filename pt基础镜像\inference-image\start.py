#!/usr/bin/env python3
"""
BiLSTM推理服务启动脚本 - 优化版
基于BiLSTMPredictor.py的启动脚本
支持Seldon Core、开发测试等模式
"""

import os
import sys
import time
import signal
import logging
from pathlib import Path

# 设置Python路径
sys.path.insert(0, '/app')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def print_startup_info():
    """打印启动信息"""
    print("=" * 60)
    print("🚀 BiLSTM推理服务启动中（优化版）...")
    print("=" * 60)
    print(f"📁 工作目录: {os.getcwd()}")
    print(f"🐍 Python版本: {sys.version}")
    print(f"📦 Python路径: {sys.path[:3]}")
    print("🔧 环境变量:")
    
    key_vars = [
        "MODEL_NAME", "SELECTED_MODEL_NAME", "MODEL_LIBRARY_DIR", 
        "API_TYPE", "SERVICE_TYPE", "SELDON_MODE", "MODEL_DIR"
    ]
    
    for var in key_vars:
        value = os.environ.get(var, "未设置")
        print(f"   {var}: {value}")
    
    print("=" * 60)

def check_dependencies():
    """检查依赖"""
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        
        import numpy
        print(f"✅ NumPy: {numpy.__version__}")
        
        import flask
        print(f"✅ Flask: {flask.__version__}")
        
        return True
    except ImportError as e:
        print(f"❌ 依赖检查失败: {e}")
        return False

def check_files():
    """检查必要文件"""
    required_files = [
        "/app/BiLSTMPredictor.py",
    ]
    
    optional_dirs = [
        "/app/models",
    ]
    
    print("📋 文件检查:")
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path}")
            return False
    
    for dir_path in optional_dirs:
        if os.path.exists(dir_path):
            files = list(Path(dir_path).rglob("*"))
            print(f"   📁 {dir_path} ({len(files)} 个文件)")
        else:
            print(f"   📁 {dir_path} (不存在)")
    
    return True

def start_seldon_mode():
    """Seldon Core模式启动"""
    print("🎯 启动Seldon Core模式")
    
    try:
        # 导入优化后的推理模块
        from BiLSTMPredictor import predictor, load
        
        # 初始化服务
        if load():
            print("✅ BiLSTM服务初始化成功")
            
            # 保持服务运行
            def signal_handler(signum, frame):
                print(f"\n📡 收到信号 {signum}，正在关闭服务...")
                sys.exit(0)
            
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            
            print("🚀 服务运行中，等待请求...")
            print("   按 Ctrl+C 停止服务")
            
            # 保持运行
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n👋 服务正常停止")
        else:
            print("❌ BiLSTM服务初始化失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Seldon模式启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def start_test_mode():
    """测试模式启动"""
    print("🧪 启动测试模式")
    
    try:
        # 使用优化后的测试函数
        import BiLSTMPredictor
        
        # 运行主程序中的测试代码
        os.environ["DATASET_TYPE"] = "network_traffic"
        os.environ["MODEL_DIR"] = "/app"
        
        test_predictor = BiLSTMPredictor.BiLSTMPredictor()
        
        if test_predictor.load():
            print("✅ 模型加载成功")
            print(f"✅ 配置: {test_predictor.model_config}")
            
            # 创建测试数据
            import numpy as np
            input_size = test_predictor.model_config["input_size"]
            test_data = np.random.randn(input_size).tolist()
            
            result = test_predictor.predict(test_data)
            print(f"✅ 测试预测: {result}")
        else:
            print("❌ 模型加载失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 测试模式失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def start_server_mode():
    """开发服务器模式启动"""
    print("🖥️ 启动开发服务器模式")
    
    try:
        # 使用Flask应用启动
        from BiLSTMPredictor import app, predictor
        
        # 首先加载模型
        if not predictor.load():
            print("❌ 模型加载失败")
            sys.exit(1)
        
        print("✅ 模型加载成功，启动Flask服务器")
        
        host = os.environ.get("HOST", "0.0.0.0")
        port = int(os.environ.get("PORT", "5000"))
        
        app.run(host=host, port=port, debug=False)
        
    except Exception as e:
        print(f"❌ 服务器模式失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

def health_check():
    """健康检查"""
    print("🏥 执行健康检查")
    
    try:
        from BiLSTMPredictor import health_status
        status = health_status()
        
        if status.get("status") == "healthy":
            print("✅ 健康检查通过")
            return True
        else:
            print(f"⚠️ 健康检查警告: {status}")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

def main():
    """主函数"""
    print_startup_info()
    
    # 依赖检查
    if not check_dependencies():
        sys.exit(1)
    
    # 文件检查
    if not check_files():
        sys.exit(1)
    
    # 确定启动模式
    mode = os.environ.get("STARTUP_MODE", "").lower()
    seldon_mode = os.environ.get("SELDON_MODE", "false").lower() == "true"
    
    # 命令行参数覆盖
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
    
    # 自动检测模式
    if not mode:
        if seldon_mode:
            mode = "seldon"
        else:
            mode = "server"
    
    print(f"🎯 启动模式: {mode}")
    
    # 执行对应模式
    if mode == "seldon":
        start_seldon_mode()
    elif mode == "test":
        start_test_mode()
    elif mode == "server":
        start_server_mode()
    elif mode == "health":
        success = health_check()
        sys.exit(0 if success else 1)
    else:
        print(f"❌ 未知启动模式: {mode}")
        print("可用模式: seldon, test, server, health")
        sys.exit(1)

if __name__ == "__main__":
    main() 