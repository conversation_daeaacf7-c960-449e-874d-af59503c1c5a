{"model_type": "train_test_split", "model_path": "./results/rnn_tf_local_results\\RNN_TF_Local_Model_best_model.weights.h5", "test_metrics": {"accuracy": 0.999711276806144, "precision": 0.9997111173390502, "recall": 0.999711276806144, "f1": 0.999710926034945}, "training_history": {"train_loss": [0.0063554965890944, 0.0018575586145743728, 0.0014224812621250749], "train_acc": [99.82712864875793, 99.94622468948364, 99.95651245117188], "val_loss": [0.0034317821264266968, 0.0023819678463041782, 0.0014094203943386674], "val_acc": [99.92493391036987, 99.92421269416809, 99.9711275100708]}, "class_names": ["BENIGN", "DoS GoldenEye", "DoS Hulk", "DoS Slowhttptest", "DoS slowloris", "Heartbleed"], "preprocessing_info_path": "./results/rnn_tf_local_results\\RNN_TF_Local_Model_preprocessing_info.json", "label_encoder_path": "./results/rnn_tf_local_results\\RNN_TF_Local_Model_label_encoder.joblib"}