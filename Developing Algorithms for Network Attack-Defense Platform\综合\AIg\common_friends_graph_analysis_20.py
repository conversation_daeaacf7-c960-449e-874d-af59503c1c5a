"""共同好友图分析"""

import sys
import pandas as pd
import networkx as nx

def common_friends_graph_analysis(input_file, source_column, target_column):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - source_column: 源节点列名
    - target_column: 目标节点列名
    """
    data = pd.read_csv(input_file)
    
    G = nx.from_pandas_edgelist(data, source=source_column, target=target_column)
    
    common_friends = {}
    for node in G.nodes():
        neighbors = set(G.neighbors(node))
        for neighbor in neighbors:
            common = neighbors.intersection(set(G.neighbors(neighbor)))
            common_friends[(node, neighbor)] = len(common)
    
    output_file = 'common_friends.csv'
    pd.DataFrame(list(common_friends.items()), columns=['Pair', 'CommonFriends']).to_csv(output_file, index=False)
    print(f"Common friends graph analysis completed. Output saved to {output_file}")

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python common_friends_graph_analysis.py <input_file> <source_column> <target_column>")
        sys.exit(1)
    input_file, source_column, target_column = sys.argv[1], sys.argv[2], sys.argv[3]
    common_friends_graph_analysis(input_file, source_column, target_column)
