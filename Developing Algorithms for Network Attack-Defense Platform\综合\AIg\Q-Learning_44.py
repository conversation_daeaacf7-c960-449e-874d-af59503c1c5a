import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import random
from collections import deque
import gymnasium as gym
from tqdm.auto import tqdm
import os

class GridWorldEnv(gym.Env):
    """
    简单的网格世界环境
    目标是从起点到达终点，同时避开障碍物
    """
    def __init__(self, size=8):
        super(GridWorldEnv, self).__init__()
        self.size = size
        self.action_space = gym.spaces.Discrete(4)  # 上下左右
        self.observation_space = gym.spaces.Box(low=0, high=1, shape=(size, size), dtype=np.float32)
        
        # 初始化网格世界
        self.reset()
    
    def reset(self, seed=None):
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)
        
        self.grid = np.zeros((self.size, self.size))
        
        # 设置起点(随机)
        self.agent_pos = [0, 0]
        self.grid[tuple(self.agent_pos)] = 1
        
        # 设置终点(随机)
        self.goal = [self.size-1, self.size-1]
        
        # 随机放置障碍物
        num_obstacles = self.size
        for _ in range(num_obstacles):
            pos = [random.randint(0, self.size-1), random.randint(0, self.size-1)]
            if pos != self.agent_pos and pos != self.goal:
                self.grid[tuple(pos)] = -1
        
        return self.get_state(), {}
    
    def get_state(self):
        state = np.zeros_like(self.grid)
        state[tuple(self.agent_pos)] = 1
        return state
    
    def step(self, action):
        # 动作映射: 0=上, 1=右, 2=下, 3=左
        action_map = [(-1, 0), (0, 1), (1, 0), (0, -1)]
        delta = action_map[action]
        
        # 计算新位置
        new_pos = [
            self.agent_pos[0] + delta[0],
            self.agent_pos[1] + delta[1]
        ]
        
        # 检查是否超出边界
        if (0 <= new_pos[0] < self.size and 
            0 <= new_pos[1] < self.size and 
            self.grid[tuple(new_pos)] != -1):
            self.agent_pos = new_pos
        
        # 计算奖励和是否结束
        done = False
        if self.agent_pos == self.goal:
            reward = 100  # 到达目标
            done = True
        elif self.grid[tuple(self.agent_pos)] == -1:
            reward = -50  # 碰到障碍物
            done = True
        else:
            reward = -1  # 每步的成本
        
        return self.get_state(), reward, done, False, {}

class QNetwork(nn.Module):
    def __init__(self, input_dim, output_dim):
        super(QNetwork, self).__init__()
        
        self.network = nn.Sequential(
            nn.Conv2d(1, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(32, 64, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Flatten(),
            nn.Linear(64 * input_dim * input_dim, 256),
            nn.ReLU(),
            nn.Linear(256, output_dim)
        )
    
    def forward(self, x):
        if len(x.shape) == 3:
            x = x.unsqueeze(1)  # 添加通道维度
        return self.network(x)

class ReplayBuffer:
    def __init__(self, capacity):
        self.buffer = deque(maxlen=capacity)
    
    def push(self, state, action, reward, next_state, done):
        self.buffer.append((state, action, reward, next_state, done))
    
    def sample(self, batch_size):
        return random.sample(self.buffer, batch_size)
    
    def __len__(self):
        return len(self.buffer)

class QLearningAgent:
    def __init__(self, state_dim, action_dim, device):
        self.device = device
        self.q_network = QNetwork(state_dim, action_dim).to(device)
        self.target_network = QNetwork(state_dim, action_dim).to(device)
        self.target_network.load_state_dict(self.q_network.state_dict())
        
        self.optimizer = optim.Adam(self.q_network.parameters())
        self.memory = ReplayBuffer(10000)
        
        self.batch_size = 64
        self.gamma = 0.99
        self.epsilon = 1.0
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.target_update = 10
        
    def select_action(self, state, training=True):
        if training and random.random() < self.epsilon:
            return random.randint(0, 3)
        
        state = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        with torch.no_grad():
            q_values = self.q_network(state)
        return q_values.argmax().item()
    
    def train(self, experiences):
        states, actions, rewards, next_states, dones = experiences
        
        # 转换为tensor
        states = torch.FloatTensor(states).to(self.device)
        actions = torch.LongTensor(actions).to(self.device)
        rewards = torch.FloatTensor(rewards).to(self.device)
        next_states = torch.FloatTensor(next_states).to(self.device)
        dones = torch.FloatTensor(dones).to(self.device)
        
        # 计算当前Q值
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        
        # 计算目标Q值
        with torch.no_grad():
            next_q_values = self.target_network(next_states).max(1)[0]
            target_q_values = rewards + (1 - dones) * self.gamma * next_q_values
        
        # 计算损失并更新
        loss = nn.MSELoss()(current_q_values.squeeze(), target_q_values)
        
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()
        
        return loss.item()
    
    def update_target_network(self):
        self.target_network.load_state_dict(self.q_network.state_dict())
    
    def decay_epsilon(self):
        self.epsilon = max(self.epsilon_min, self.epsilon * self.epsilon_decay)

def train_agent(env, agent, num_episodes, device):
    rewards_history = []
    
    for episode in tqdm(range(num_episodes), desc="Training"):
        state, _ = env.reset()
        episode_reward = 0
        done = False
        
        while not done:
            # 选择动作
            action = agent.select_action(state)
            
            # 执行动作
            next_state, reward, done, _, _ = env.step(action)
            
            # 存储经验
            agent.memory.push(state, action, reward, next_state, done)
            episode_reward += reward
            
            # 训练
            if len(agent.memory) >= agent.batch_size:
                batch = agent.memory.sample(agent.batch_size)
                experiences = list(zip(*batch))
                loss = agent.train(experiences)
            
            state = next_state
        
        # 更新目标网络
        if episode % agent.target_update == 0:
            agent.update_target_network()
        
        # 衰减探索率
        agent.decay_epsilon()
        
        rewards_history.append(episode_reward)
        
        if (episode + 1) % 100 == 0:
            avg_reward = np.mean(rewards_history[-100:])
            print(f"Episode {episode + 1}, Average Reward: {avg_reward:.2f}, Epsilon: {agent.epsilon:.3f}")
    
    return rewards_history

def test_agent(env, agent, num_episodes):
    total_rewards = []
    success_count = 0
    
    for episode in range(num_episodes):
        state, _ = env.reset()
        episode_reward = 0
        done = False
        
        while not done:
            action = agent.select_action(state, training=False)
            next_state, reward, done, _, _ = env.step(action)
            episode_reward += reward
            state = next_state
            
            if reward == 100:  # 到达目标
                success_count += 1
        
        total_rewards.append(episode_reward)
    
    avg_reward = np.mean(total_rewards)
    success_rate = success_count / num_episodes
    print(f"\nTest Results:")
    print(f"Average Reward: {avg_reward:.2f}")
    print(f"Success Rate: {success_rate:.2%}")

def main():
    # 环境参数
    grid_size = 8
    env = GridWorldEnv(size=grid_size)
    
    # 训练参数
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    num_episodes = 1000
    test_episodes = 100
    
    # 创建智能体
    agent = QLearningAgent(grid_size, env.action_space.n, device)
    
    # 训练智能体
    print("Starting training...")
    rewards_history = train_agent(env, agent, num_episodes, device)
    
    # 测试智能体
    print("\nStarting testing...")
    test_agent(env, agent, test_episodes)
    
    # 保存模型
    result_dir = 'E:/data/VOCdevkit/model'
    os.makedirs(result_dir, exist_ok=True)
    torch.save(agent.q_network.state_dict(), os.path.join(result_dir, 'q_learning_model.pth'))
    print("\nModel saved successfully!")

if __name__ == "__main__":
    main()