import tensorflow as tf
import numpy as np
import uuid
import os
import requests
import json
import sys
import argparse
from minio import Minio
from PIL import Image
import pandas as pd
from tqdm.auto import tqdm

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class VOC2007Dataset:
    def __init__(self, voc_root, image_size=64):
        """
        VOC2007数据集加载器的TensorFlow实现
        """
        self.voc_root = voc_root
        self.image_size = image_size
        self.images_dir = os.path.join(voc_root, "JPEGImages")
        
        # 获取所有图像文件路径
        self.image_paths = []
        for img_name in os.listdir(self.images_dir):
            if img_name.endswith(('.jpg', '.jpeg', '.png')):
                self.image_paths.append(os.path.join(self.images_dir, img_name))
                
        print(f"找到 {len(self.image_paths)} 张图像")

    def load_and_preprocess_image(self, path):
        image = tf.io.read_file(path)
        image = tf.image.decode_jpeg(image, channels=3)
        image = tf.image.resize(image, [self.image_size, self.image_size])
        image = tf.image.random_flip_left_right(image)
        image = (image / 127.5) - 1  # 归一化到 [-1, 1]
        return image

    def get_dataset(self, batch_size):
        dataset = tf.data.Dataset.from_tensor_slices(self.image_paths)
        dataset = dataset.map(self.load_and_preprocess_image, 
                            num_parallel_calls=tf.data.AUTOTUNE)
        dataset = dataset.shuffle(1000).batch(batch_size).prefetch(tf.data.AUTOTUNE)
        return dataset

class DGANGenerator(tf.keras.Model):
    def __init__(self, latent_dim):
        super(DGANGenerator, self).__init__()
        self.latent_dim = latent_dim
        
        # 初始特征图大小为 4x4
        self.init_size = 4
        self.dense = tf.keras.layers.Dense(256 * self.init_size ** 2)
        
        self.conv_blocks = tf.keras.Sequential([
            # 4x4 -> 8x8
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.UpSampling2D(),
            tf.keras.layers.Conv2D(256, 3, padding='same'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.LeakyReLU(0.2),
            tf.keras.layers.Conv2D(256, 3, padding='same'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.LeakyReLU(0.2),
            
            # 8x8 -> 16x16
            tf.keras.layers.UpSampling2D(),
            tf.keras.layers.Conv2D(128, 3, padding='same'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.LeakyReLU(0.2),
            tf.keras.layers.Conv2D(128, 3, padding='same'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.LeakyReLU(0.2),
            
            # 16x16 -> 32x32
            tf.keras.layers.UpSampling2D(),
            tf.keras.layers.Conv2D(64, 3, padding='same'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.LeakyReLU(0.2),
            tf.keras.layers.Conv2D(64, 3, padding='same'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.LeakyReLU(0.2),
            
            # 32x32 -> 64x64
            tf.keras.layers.UpSampling2D(),
            tf.keras.layers.Conv2D(32, 3, padding='same'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.LeakyReLU(0.2),
            tf.keras.layers.Conv2D(3, 3, padding='same', activation='tanh')
        ])

    def call(self, z):
        out = self.dense(z)
        out = tf.reshape(out, [-1, self.init_size, self.init_size, 256])
        return self.conv_blocks(out)

class DGANDiscriminator(tf.keras.Model):
    def __init__(self):
        super(DGANDiscriminator, self).__init__()
        
        def discriminator_block(filters, bn=True):
            block = [
                tf.keras.layers.Conv2D(filters, 4, strides=2, padding='same'),
                tf.keras.layers.LeakyReLU(0.2),
                tf.keras.layers.Dropout(0.25)
            ]
            if bn:
                block.append(tf.keras.layers.BatchNormalization())
            return block

        self.model = tf.keras.Sequential([
            *discriminator_block(32, bn=False),  # 64x64 -> 32x32
            *discriminator_block(64),            # 32x32 -> 16x16
            *discriminator_block(128),           # 16x16 -> 8x8
            *discriminator_block(256),           # 8x8 -> 4x4
            tf.keras.layers.Flatten()
        ])

        self.features = tf.keras.Sequential([
            tf.keras.layers.Dense(1024),
            tf.keras.layers.LeakyReLU(0.2),
            tf.keras.layers.Dropout(0.3)
        ])
        
        self.adv_layer = tf.keras.Sequential([
            tf.keras.layers.Dense(1, activation='sigmoid')
        ])

    def call(self, img):
        features = self.model(img)
        features = self.features(features)
        validity = self.adv_layer(features)
        return validity, features

class DGANLoss:
    def __init__(self, lambda_fm=10.0):
        self.lambda_fm = lambda_fm
        
    def add_noise(self, tensor, noise_factor=0.1):
        noise = tf.random.normal(tf.shape(tensor)) * noise_factor
        return tensor + noise
        
    def discriminator_loss(self, real_validity, fake_validity, real_features, fake_features):
        real_loss = tf.reduce_mean(tf.keras.losses.binary_crossentropy(
            tf.ones_like(real_validity) * 0.9, real_validity))
        fake_loss = tf.reduce_mean(tf.keras.losses.binary_crossentropy(
            tf.zeros_like(fake_validity), fake_validity))
        
        fm_loss = tf.reduce_mean(tf.keras.losses.mse(real_features, fake_features))
        
        return (real_loss + fake_loss) / 2 + self.lambda_fm * fm_loss
    
    def generator_loss(self, fake_validity, real_features, fake_features):
        g_loss = tf.reduce_mean(tf.keras.losses.binary_crossentropy(
            tf.ones_like(fake_validity), fake_validity))
        
        fm_loss = tf.reduce_mean(tf.keras.losses.mse(real_features, fake_features))
        
        return g_loss + self.lambda_fm * fm_loss

@tf.function
def train_step(real_imgs, generator, discriminator, criterion, g_optimizer, d_optimizer, latent_dim):
    batch_size = tf.shape(real_imgs)[0]
    
    # 生成随机噪声
    z = tf.random.normal([batch_size, latent_dim])
    
    with tf.GradientTape() as g_tape, tf.GradientTape() as d_tape:
        # 生成假图像
        fake_imgs = generator(z, training=True)
        
        # 添加噪声到真实图像
        noisy_real_imgs = criterion.add_noise(real_imgs)
        
        # 判别器预测
        real_validity, real_features = discriminator(noisy_real_imgs, training=True)
        fake_validity, fake_features = discriminator(fake_imgs, training=True)
        
        # 计算损失
        d_loss = criterion.discriminator_loss(real_validity, fake_validity, 
                                            real_features, fake_features)
        g_loss = criterion.generator_loss(fake_validity, real_features, fake_features)
    
    # 计算梯度并应用
    d_gradients = d_tape.gradient(d_loss, discriminator.trainable_variables)
    g_gradients = g_tape.gradient(g_loss, generator.trainable_variables)
    
    d_optimizer.apply_gradients(zip(d_gradients, discriminator.trainable_variables))
    g_optimizer.apply_gradients(zip(g_gradients, generator.trainable_variables))
    
    return d_loss, g_loss

def save_sample_images(gen_imgs, epoch, batches_done, result_dir, n_row=4):
    """保存生成的样本图像"""
    gen_imgs = (gen_imgs + 1) / 2.0  # 反归一化到 [0, 1]
    gen_imgs = tf.clip_by_value(gen_imgs, 0, 1)
    
    fig_size = n_row
    gen_imgs = gen_imgs.numpy()
    
    save_path = os.path.join(result_dir, f'samples_epoch_{epoch}_batch_{batches_done}.png')
    
    # 使用PIL保存图像网格
    image_array = np.zeros((fig_size * 64, fig_size * 64, 3))
    for i in range(fig_size):
        for j in range(fig_size):
            idx = i * fig_size + j
            if idx < len(gen_imgs):
                image_array[i*64:(i+1)*64, j*64:(j+1)*64] = gen_imgs[idx]
    
    image = Image.fromarray((image_array * 255).astype(np.uint8))
    image.save(save_path)

# 其余函数（dgan_train, model_upload, model_register, model_push, header_gen）
# 以及main函数与原代码基本相同，只需要修改相关的PyTorch特定代码为TensorFlow代码

def dgan_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """DGAN训练函数的TensorFlow实现"""
    # 解析参数
    latent_dim = job_params.get("latent_dim", 100)
    image_size = job_params.get("image_size", 64)
    num_epochs = job_params.get("num_epochs", 100)
    learning_rate = job_params.get("learning_rate", 0.0002)
    batch_size = job_params.get("batch_size", 64)

    # 准备数据
    training_data_path = "/workspace/" + dataset["training_data_path"]
    voc_dataset = VOC2007Dataset(training_data_path, image_size)
    dataloader = voc_dataset.get_dataset(batch_size)
    
    # 初始化模型
    generator = DGANGenerator(latent_dim)
    discriminator = DGANDiscriminator()
    
    # 损失函数和优化器
    criterion = DGANLoss()
    g_optimizer = tf.keras.optimizers.Adam(learning_rate, beta_1=0.5, beta_2=0.999)
    d_optimizer = tf.keras.optimizers.Adam(learning_rate, beta_1=0.5, beta_2=0.999)
    
    try:
        # 创建结果目录
        os.makedirs(result_dir, exist_ok=True)
        
        # 创建固定噪声用于可视化
        fixed_noise = tf.random.normal([16, latent_dim])
        
        # 训练循环
        for epoch in range(num_epochs):
            pbar = tqdm(enumerate(dataloader), desc=f"Epoch {epoch+1}/{num_epochs}")
            
            for i, real_imgs in pbar:
                d_loss, g_loss = train_step(real_imgs, generator, discriminator,
                                          criterion, g_optimizer, d_optimizer, latent_dim)
                
                pbar.set_postfix({
                    'D_loss': f'{float(d_loss):.4f}',
                    'G_loss': f'{float(g_loss):.4f}'
                })
                
                # 保存采样结果
                batches_done = epoch * len(list(dataloader)) + i
                if batches_done % 100 == 0:
                    fake_imgs = generator(fixed_noise, training=False)
                    save_sample_images(fake_imgs, epoch, batches_done, result_dir)
        
        # 保存最终模型
        generator.save_weights(os.path.join(result_dir, f"{model_name}_generator.h5"))
        discriminator.save_weights(os.path.join(result_dir, f"{model_name}_discriminator.h5"))
        print(f'训练完成，模型保存到 {result_dir}')
        
    except Exception as e:
        print(f"训练过程中发生错误: {e}")
        return str(e), -1

    return None, 0

def model_upload(result_dir, model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".h5"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".h5")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1

def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "TENSORFLOW",
          "file_name": model_name+".h5",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "DGAN",
        "model_usage": "Generation"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='tensorflow DGAN Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='DGAN Job Params, set all params in dict')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='DGAN DataSet, set as a dict')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')
    
    print("Start DGAN training job, params:\n" + str(sys.argv) + "\n")
    args = parser.parse_args()
    
    job_params = args.job_params
    print("DGAN job params:" + str(job_params) + "\n")
    dataset = args.dataset
    print("DGAN dataset:" + str(dataset) + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("DGAN result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("DGAN factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("DGAN fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("DGAN sparkconf params:" + str(sparkconf) + "\n")
    
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    
    if fit_params is None:
        fit_params = {}
    
    device = '/GPU:0' if tf.config.list_physical_devices('GPU') else '/CPU:0'
    
    print("Step 1 DGAN training:\n")
    result, ret_code = dgan_train(dataset, job_params, model["model_name"], result_dir, fit_params,device)
    if ret_code != 0:
        print("FCN train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()