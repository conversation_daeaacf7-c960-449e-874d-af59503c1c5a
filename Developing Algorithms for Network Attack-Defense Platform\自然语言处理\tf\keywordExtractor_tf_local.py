import argparse
import json
import os
import pickle
import tensorflow as tf
from transformers import TFBertModel, BertTokenizer
from sklearn.feature_extraction.text import TfidfVectorizer
from minio import Minio
import uuid
import joblib
import requests
import sys

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class KeywordExtractor:
    def __init__(self, model_name='bert-base-uncased'):
        self.tokenizer = BertTokenizer.from_pretrained(model_name)
        self.model = TFBertModel.from_pretrained(model_name)

    def extract_keywords(self, texts, top_n=10):
        """
        提取的关键词数 (top_n) 默认为 10
        """
        # Tokenize and encode texts
        encodings = [self.tokenizer(text, return_tensors='tf', padding=True, truncation=True) for text in texts]
        
        # Get embeddings using TensorFlow
        embeddings = []
        for encoding in encodings:
            outputs = self.model(encoding)
            # Convert to numpy for compatibility with sklearn
            embedding = tf.reduce_mean(outputs.last_hidden_state, axis=1).numpy()
            embeddings.append(embedding)

        # Compute TF-IDF scores
        vectorizer = TfidfVectorizer()
        tfidf_matrix = vectorizer.fit_transform(texts)
        feature_names = vectorizer.get_feature_names_out()
        scores = tfidf_matrix.sum(axis=0).A1
        sorted_indices = scores.argsort()[::-1]
        
        keywords = [feature_names[i] for i in sorted_indices[:top_n]]
        return keywords

    def save_model(self, result_dir, model_name):
        # Save the model
        model_path = os.path.join(result_dir, model_name + ".pkl")
        with open(model_path, 'wb') as model_file:
            joblib.dump(self, model_file)
        print(f'Model saved to {model_path}')

def upload_model(result_dir, model_name):
    minioClient = Minio(MINIO_URL,
                        access_key='AKIAIOSFODNN7EXAMPLE',
                        secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                        secure=False)
    try:
        obj_name = str(uuid.uuid1())
        upload_path = obj_name + "/" + model_name + ".pkl"
        source = "s3://mlss-mf/" + obj_name
        minioClient.fput_object('mlss-mf', upload_path, os.path.join(result_dir, model_name + ".pkl"))
        result = {"source": source}
        return result, 0
    except Exception as err:
        print(err)
        return None, -1

def register_model(model_name, source, group_id, headers):
    params = {
        "model_name": model_name,
        "model_type": "tensorflow",
        "file_name": model_name + ".pkl",
        "s3_path": source,
        "group_id": int(float(group_id)),
        "training_id": model_name,
        "training_flag": 1,
    }
    r = requests.post(MODEL_FACTORY_URL + MODEL_ADD_URL, data=json.dumps(params), headers=headers)
    res_data = r.content.decode()
    if r.status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def push_model(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0
    params = {
        "factory_name": factory_name,
        "model_type": "Keyword_Extraction",
        "model_usage": "Extraction"
    }
    r = requests.post(MODEL_FACTORY_URL + MODEL_PUSH_URL + "/" + str(model_version_id), data=json.dumps(params), headers=headers)
    res_data = r.content.decode()
    if r.status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: user_id
    }
    return headers

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Keyword Extraction using BERT.')  
    parser.add_argument('--texts', dest='texts', type=json.loads, help='List of texts for keyword extraction')
    parser.add_argument('--result_dir', dest='result_dir', type=str, help='Directory to save the model')
    parser.add_argument('--model', dest='model', type=json.loads, help='Model parameters')
    parser.add_argument('--factory_name', dest='factory_name', type=str, help='Factory name')
    parser.add_argument('--top_n', dest='top_n', type=int, default=10, help='Number of top keywords to extract')
    
    args = parser.parse_args()
    texts = args.texts
    result_dir = args.result_dir
    model = args.model
    factory_name = args.factory_name
    top_n = args.top_n

    print("Starting keyword extraction, params:\n" + str(args) + "\n")

    # Step 1: Extract keywords
    print("Step 1: Extract keywords\n")
    keyword_extractor = KeywordExtractor()
    keywords = keyword_extractor.extract_keywords(texts, top_n)
    print(f'Extracted keywords: {keywords}')
    
    # Save the model
    print("Step 2: Save the model\n")
    keyword_extractor.save_model(result_dir, model["model_name"])

    # Step 3: Upload model to MinIO
    print("Step 3: Upload model to MinIO\n")
    result, ret_code = upload_model(result_dir, model["model_name"])
    if ret_code != 0:
        print("Model upload error, stopping job, error message: " + result + "\n")
        sys.exit(-1)
    print("Model upload finished, starting model registration...\n")

    # Step 4: Register model
    print("Step 4: Register model\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result, ret_code = register_model(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("Model registration error, stopping job, error message: " + result)
        sys.exit(-1)
    print("Model registration finished, starting model push...\n")

    # Step 5: Push model
    print("Step 5: Push model\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result, ret_code = push_model(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("Model push error, stopping job, error message: " + result + "\n")
        sys.exit(-1)
    
    print("Model push finished, job complete.\n")
    sys.exit()