import tensorflow as tf
import numpy as np
import pickle
import requests
import sys
import argparse
import json
from minio import Minio
import uuid
import os
from PIL import Image
import xml.etree.ElementTree as ET
import pandas as pd
from tqdm import tqdm

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class ROIPooling(tf.keras.layers.Layer):
    def __init__(self, output_size, **kwargs):
        self.output_size = output_size
        super(ROIPooling, self).__init__(**kwargs)

    def build(self, input_shape):
        super(ROIPooling, self).build(input_shape)

    def compute_output_shape(self, input_shape):
        feature_shape, rois_shape = input_shape
        return (rois_shape[0], self.output_size[0], self.output_size[1], feature_shape[3])

    def call(self, inputs):
        features, rois = inputs
        batch_size = tf.shape(features)[0]
        
        # Scale ROIs from pixel coordinates to feature map coordinates
        scale = tf.constant(1/16, dtype=tf.float32)
        scaled_rois = rois * scale
        
        def roi_pool_for_batch(args):
            feature_map, roi = args
            roi = tf.cast(roi, tf.int32)
            pooled = tf.image.crop_and_resize(
                tf.expand_dims(feature_map, 0),
                tf.expand_dims(tf.cast(roi, tf.float32), 0),
                [0],
                self.output_size
            )
            return tf.squeeze(pooled, axis=0)
        
        pooled_regions = tf.map_fn(
            roi_pool_for_batch,
            (features, scaled_rois),
            dtype=tf.float32
        )
        
        return pooled_regions

class FastRCNNForDetection(tf.keras.Model):
    def __init__(self, num_classes, pretrained_model_path):
        super(FastRCNNForDetection, self).__init__()
        
        # Load pretrained ResNet50
        base_model = tf.keras.applications.ResNet50(
            include_top=False,
            weights=None,
            input_shape=(None, None, 3)
        )
        
        # Load weights if provided
        if pretrained_model_path:
            base_model.load_weights(pretrained_model_path)
        
        self.features = base_model
        self.roi_pool = ROIPooling(output_size=(7, 7))
        
        # Classifier layers
        self.classifier = tf.keras.Sequential([
            tf.keras.layers.Flatten(),
            tf.keras.layers.Dense(4096, activation='relu'),
            tf.keras.layers.Dropout(0.5),
            tf.keras.layers.Dense(4096, activation='relu'),
            tf.keras.layers.Dropout(0.5)
        ])
        
        self.cls_score = tf.keras.layers.Dense(num_classes)
        self.bbox_pred = tf.keras.layers.Dense(num_classes * 4)

    def call(self, inputs):
        images, rois = inputs
        
        # Extract features
        features = self.features(images)
        
        # ROI pooling
        pooled_features = self.roi_pool([features, rois])
        
        # Classification head
        fc_features = self.classifier(pooled_features)
        class_scores = self.cls_score(fc_features)
        bbox_preds = self.bbox_pred(fc_features)
        
        return class_scores, bbox_preds

class UniversalImageDataset(tf.keras.utils.Sequence):
    def __init__(self, data_dir, batch_size=32, image_size=(224, 224), 
                 dataset_type='folder', annotations_file=None):
        self.data_dir = data_dir
        self.batch_size = batch_size
        self.image_size = image_size
        self.dataset_type = dataset_type
        
        self.classes = ['__background__', 'aeroplane', 'bicycle', 'bird', 'boat', 
                       'bottle', 'bus', 'car', 'cat', 'chair', 'cow', 'diningtable', 
                       'dog', 'horse', 'motorbike', 'person', 'pottedplant', 'sheep', 
                       'sofa', 'train', 'tvmonitor']
        self.class_to_idx = {cls: idx for idx, cls in enumerate(self.classes)}
        
        # Load dataset based on type
        if dataset_type in ['folder', 'imagenet']:
            self.image_paths, self.labels = self._load_from_folder()
        elif dataset_type == 'coco':
            self.image_paths, self.labels = self._load_coco(annotations_file)
        elif dataset_type == 'voc':
            self.image_paths, self.labels = self._load_voc(annotations_file)
        elif dataset_type == 'yolo':
            self.image_paths, self.labels = self._load_yolo(annotations_file)
        elif dataset_type == 'pickle':
            self.image_paths, self.labels = self._load_pickle(annotations_file)
        
        self.indexes = np.arange(len(self.image_paths))

    def __len__(self):
        return int(np.ceil(len(self.image_paths) / self.batch_size))

    def __getitem__(self, idx):
        batch_indexes = self.indexes[idx * self.batch_size:(idx + 1) * self.batch_size]
        
        batch_images = []
        batch_boxes = []
        batch_labels = []
        
        for i in batch_indexes:
            # Load and preprocess image
            image = tf.keras.preprocessing.image.load_img(
                self.image_paths[i],
                target_size=self.image_size
            )
            image = tf.keras.preprocessing.image.img_to_array(image)
            image = tf.keras.applications.resnet50.preprocess_input(image)
            
            # Process labels and boxes
            label_data = self.labels[i]
            boxes = [box[1:] for box in label_data]
            labels = [self.class_to_idx[box[0]] for box in label_data]
            
            batch_images.append(image)
            batch_boxes.append(boxes)
            batch_labels.append(labels)
        
        return (
            tf.convert_to_tensor(batch_images, dtype=tf.float32),
            {
                'boxes': tf.ragged.constant(batch_boxes, dtype=tf.float32),
                'labels': tf.ragged.constant(batch_labels, dtype=tf.int32)
            }
        )

    def on_epoch_end(self):
        np.random.shuffle(self.indexes)

    def load_from_folder(self):
        classes = os.listdir(self.data_dir)
        class_to_idx = {cls: idx for idx, cls in enumerate(classes)}
        
        image_paths = []
        labels = []

        for cls in classes:
            class_dir = os.path.join(self.data_dir, cls)
            if os.path.isdir(class_dir):
                for img_file in os.listdir(class_dir):
                    if img_file.endswith(('.jpg', '.jpeg', '.png')):
                        img_path = os.path.join(class_dir, img_file)
                        image_paths.append(img_path)
                        labels.append(class_to_idx[cls])

        return image_paths, labels

    def load_coco(self, annotations_file):
        with open(annotations_file) as f:
            annotations = json.load(f)

        image_paths = []
        labels = []
        for item in annotations['images']:
            img_id = item['id']
            img_file = os.path.join(self.data_dir, item['file_name'])
            image_paths.append(img_file)
            label = self.get_label_for_image(img_id, annotations)
            labels.append(label)

        return image_paths, labels

    def load_voc(self, annotations_file):
        image_paths = []
        labels = []

        # 读取所有 XML 文件
        with open(annotations_file, 'r') as file:
            xml_files = file.readlines()

        for xml_file in xml_files:
            xml_file = xml_file.strip()
            tree = ET.parse(xml_file)
            root = tree.getroot()

            # 获取图像文件路径
            image_name = root.find('filename').text
            img_path = os.path.join(self.data_dir, image_name)
            image_paths.append(img_path)

            # 提取标签
            objects = root.findall('object')
            boxes = []
            for obj in objects:
                class_name = obj.find('name').text
                bbox = obj.find('bndbox')
                xmin = float(bbox.find('xmin').text)
                ymin = float(bbox.find('ymin').text)
                xmax = float(bbox.find('xmax').text)
                ymax = float(bbox.find('ymax').text)
                boxes.append((class_name, xmin, ymin, xmax, ymax))

            labels.append(boxes)

        return image_paths, labels

    def load_yolo(self):
        image_paths = []
        labels = []

        for img_file in os.listdir(self.data_dir):
            if img_file.endswith(('.jpg', '.png', '.jpeg')):
                img_path = os.path.join(self.data_dir, img_file)
                image_paths.append(img_path)

                # 加载对应的YOLO标签文件
                label_file = img_file.replace('.jpg', '.txt').replace('.png', '.txt').replace('.jpeg', '.txt')
                label_path = os.path.join(self.data_dir, label_file)

                if os.path.exists(label_path):
                    with open(label_path, 'r') as f:
                        boxes = []
                        for line in f.readlines():
                            class_id, x_center, y_center, width, height = map(float, line.strip().split())
                            boxes.append((class_id, x_center, y_center, width, height))
                        labels.append(boxes)  # 以边界框列表形式存储
                else:
                    labels.append([])  # 无标签时返回空列表

        return image_paths, labels
    
    def load_pickle(self, pkl_file):
        # 从 .pkl 文件加载数据
        with open(pkl_file, 'rb') as f:
            data = pickle.load(f)

        # 假设数据为字典格式，包含特征和标签
        if isinstance(data, dict):
            images = data['images']  # 假设图像数据在 'images' 键下
            labels = data['labels']    # 假设标签在 'labels' 键下
        elif isinstance(data, pd.DataFrame):
            images = data['image_paths'].tolist()  # 假设图像路径在某列
            labels = data['labels'].tolist()        # 假设标签在某列
        else:
            raise ValueError("Unsupported data format in pickle file.")

        return images, labels

class FastRCNNLoss(tf.keras.losses.Loss):
    def __init__(self):
        super(FastRCNNLoss, self).__init__()
        self.cls_loss = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True)
        self.bbox_loss = tf.keras.losses.Huber()

    def call(self, y_true, y_pred):
        class_scores, bbox_preds = y_pred
        true_labels, true_boxes = y_true
        
        # Classification loss
        cls_loss = self.cls_loss(true_labels, class_scores)
        
        # Box regression loss
        positive_indices = tf.where(true_labels > 0)
        if tf.size(positive_indices) > 0:
            bbox_loss = self.bbox_loss(
                tf.gather(true_boxes, positive_indices),
                tf.gather(bbox_preds, positive_indices)
            )
        else:
            bbox_loss = tf.constant(0.0)
        
        return cls_loss + bbox_loss

def train_model(train_dataset, model, num_epochs, learning_rate):
    optimizer = tf.keras.optimizers.Adam(learning_rate=learning_rate)
    loss_fn = FastRCNNLoss()
    
    # Custom training loop
    for epoch in range(num_epochs):
        print(f'Epoch {epoch + 1}/{num_epochs}')
        progbar = tf.keras.utils.Progbar(len(train_dataset))
        
        for step, (images, targets) in enumerate(train_dataset):
            with tf.GradientTape() as tape:
                # Forward pass
                class_scores, bbox_preds = model([images, targets['boxes']])
                
                # Compute loss
                loss = loss_fn([targets['labels'], targets['boxes']], 
                             [class_scores, bbox_preds])
            
            # Compute gradients and update weights
            gradients = tape.gradient(loss, model.trainable_variables)
            optimizer.apply_gradients(zip(gradients, model.trainable_variables))
            
            progbar.update(step + 1)

def test_model(test_dataset, model):
    correct = 0
    total = 0
    
    for images, targets in test_dataset:
        class_scores, _ = model([images, targets['boxes']])
        predictions = tf.argmax(class_scores, axis=1)
        correct += tf.reduce_sum(tf.cast(predictions == targets['labels'], tf.int32))
        total += tf.size(targets['labels'])
    
    accuracy = float(correct) / float(total)
    print(f'Test Accuracy: {accuracy * 100:.2f}%')

def prepare_data(data_dir, dataset_type, batch_size, image_size, annotations_file=None):
    """
    Prepare training and testing datasets.
    
    Args:
        data_dir: Directory containing the dataset
        dataset_type: Type of dataset ('folder', 'coco', 'voc', 'yolo', 'pickle')
        batch_size: Batch size for training
        image_size: Target size for images (height, width)
        annotations_file: Path to annotations file if needed
    
    Returns:
        train_dataset: TensorFlow dataset for training
        test_dataset: TensorFlow dataset for testing
    """
    # Create full dataset
    full_dataset = UniversalImageDataset(
        data_dir=data_dir,
        batch_size=batch_size,
        image_size=(image_size, image_size),
        dataset_type=dataset_type,
        annotations_file=annotations_file
    )
    
    # Calculate split sizes
    total_size = len(full_dataset.image_paths)
    train_size = int(0.8 * total_size)
    test_size = total_size - train_size
    
    # Create train/test splits
    indices = np.random.permutation(total_size)
    train_indices = indices[:train_size]
    test_indices = indices[train_size:]
    
    # Create training dataset
    train_image_paths = [full_dataset.image_paths[i] for i in train_indices]
    train_labels = [full_dataset.labels[i] for i in train_indices]
    train_dataset = UniversalImageDataset(
        data_dir=data_dir,
        batch_size=batch_size,
        image_size=(image_size, image_size),
        dataset_type=dataset_type
    )
    train_dataset.image_paths = train_image_paths
    train_dataset.labels = train_labels
    
    # Create test dataset
    test_image_paths = [full_dataset.image_paths[i] for i in test_indices]
    test_labels = [full_dataset.labels[i] for i in test_indices]
    test_dataset = UniversalImageDataset(
        data_dir=data_dir,
        batch_size=batch_size,
        image_size=(image_size, image_size),
        dataset_type=dataset_type
    )
    test_dataset.image_paths = test_image_paths
    test_dataset.labels = test_labels
    
    print(f"Created datasets with {len(train_dataset)} training batches and {len(test_dataset)} testing batches")
    
    return train_dataset, test_dataset

def fastRCNN_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """
    Main training function using TensorFlow implementation
    """
    input_size = job_params["input_size"]
    dataset_type = job_params["dataset_type"]
    output_size = job_params["output_size"]
    num_epochs = job_params["num_epochs"]
    learning_rate = job_params["learning_rate"]
    batch_size = job_params["batch_size"]
    
    training_data_path = "/workspace/" + dataset["training_data_path"]
    
    # Prepare datasets using the new prepare_data function
    train_dataset, test_dataset = prepare_data(
        data_dir=training_data_path,
        dataset_type=dataset_type,
        batch_size=batch_size,
        image_size=input_size,
        annotations_file=dataset.get("annotations_file")
    )
    
    # Initialize model
    pretrained_model_path = "/workspace/pretrained_model/resnet50_weights.h5"
    model = FastRCNNForDetection(output_size, pretrained_model_path)
    
    try:
        # Train model
        train_model(train_dataset, model, num_epochs, learning_rate)
        
        # Test model
        test_model(test_dataset, model)
        
        # Save model
        model.save_weights(os.path.join(result_dir, f"{model_name}.h5"))
        print(f'Training complete, model saved to {result_dir}/{model_name}.h5')
        
    except Exception as e:
        print(f"Error during training: {e}")
        return None, -1
    
    return None, 0

def model_upload(result_dir,model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".h5"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".h5")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "TENSORFLOW",
          "file_name": model_name+".h5",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "fastRCNN",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

    
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='tensorflow fastRCNN Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='fastRCNN Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='fastRCNN DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start fastRCNN training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("fastRCNN job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("fastRCNN dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("fastRCNN result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("fastRCNN factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("fastRCNN fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("fastRCNN sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    
    device = '/GPU:0' if tf.config.list_physical_devices('GPU') else '/CPU:0'
    print("Step 1 fastRCNN training:\n")
    result,ret_code = fastRCNN_train(dataset,job_params, model["model_name"],result_dir,fit_params,device)
    if ret_code != 0:
        print("fastRCNN train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()