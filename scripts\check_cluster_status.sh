#!/bin/bash

# =======================================================
# 集群状态检查脚本
# 功能: 检查PVE、Hadoop、K8S集群的运行状态
# 作者: 系统管理员
# 版本: 1.0
# =======================================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志文件
LOG_FILE="/var/log/cluster_status_$(date +%Y%m%d_%H%M%S).log"

# 配置文件 - 根据实际环境修改
HADOOP_NODES=("hadoop-node1" "hadoop-node2" "hadoop-node3" "hadoop-node4" "hadoop-node5" "hadoop-node6")
K8S_NODES=("k8s-master" "k8s-worker1" "k8s-worker2" "k8s-worker3" "k8s-worker4")
PVE_HOST="localhost"

# 记录日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 打印标题
print_header() {
    echo -e "${BLUE}=================================${NC}"
    echo -e "${BLUE}    $1${NC}"
    echo -e "${BLUE}=================================${NC}"
}

# 检查服务状态
check_service() {
    local host=$1
    local service=$2
    
    if ssh -o ConnectTimeout=5 "$host" "systemctl is-active $service" >/dev/null 2>&1; then
        echo -e "${GREEN}✓${NC} $host: $service 运行正常"
        log "SUCCESS: $host - $service is running"
        return 0
    else
        echo -e "${RED}✗${NC} $host: $service 状态异常"
        log "ERROR: $host - $service is not running"
        return 1
    fi
}

# 检查端口
check_port() {
    local host=$1
    local port=$2
    local service=$3
    
    if nc -z -w3 "$host" "$port" 2>/dev/null; then
        echo -e "${GREEN}✓${NC} $host:$port ($service) 端口开放"
        log "SUCCESS: $host:$port ($service) port is open"
        return 0
    else
        echo -e "${RED}✗${NC} $host:$port ($service) 端口不可达"
        log "ERROR: $host:$port ($service) port is not reachable"
        return 1
    fi
}

# 检查PVE状态
check_pve_status() {
    print_header "PVE 状态检查"
    
    local pve_services=("pve-cluster" "pveproxy" "pvedaemon" "pvestatd")
    local error_count=0
    
    for service in "${pve_services[@]}"; do
        if ! check_service "$PVE_HOST" "$service"; then
            ((error_count++))
        fi
    done
    
    # 检查虚拟机状态
    echo -e "\n${YELLOW}虚拟机状态:${NC}"
    pvesh get /cluster/resources --type vm | grep -E "vmid|status|name" || true
    
    # 检查存储状态
    echo -e "\n${YELLOW}存储状态:${NC}"
    pvesh get /cluster/resources --type storage | grep -E "storage|total|used|avail" || true
    
    if [ $error_count -eq 0 ]; then
        echo -e "\n${GREEN}PVE 状态: 正常${NC}"
        log "PVE status: All services running normally"
    else
        echo -e "\n${RED}PVE 状态: 发现 $error_count 个问题${NC}"
        log "PVE status: Found $error_count issues"
    fi
    
    return $error_count
}

# 检查Hadoop集群状态
check_hadoop_status() {
    print_header "Hadoop 集群状态检查"
    
    local error_count=0
    
    # 检查HDFS状态
    echo -e "${YELLOW}HDFS 状态:${NC}"
    for node in "${HADOOP_NODES[@]}"; do
        # 检查NameNode
        if [[ "$node" == "hadoop-node1" ]]; then
            check_service "$node" "hadoop-hdfs-namenode" || ((error_count++))
            check_port "$node" 9870 "HDFS NameNode Web UI" || ((error_count++))
        fi
        
        # 检查DataNode
        if [[ "$node" != "hadoop-node1" ]]; then
            check_service "$node" "hadoop-hdfs-datanode" || ((error_count++))
        fi
    done
    
    # 检查YARN状态
    echo -e "\n${YELLOW}YARN 状态:${NC}"
    check_service "hadoop-node1" "hadoop-yarn-resourcemanager" || ((error_count++))
    check_port "hadoop-node1" 8088 "YARN ResourceManager Web UI" || ((error_count++))
    
    for node in "${HADOOP_NODES[@]:1}"; do
        check_service "$node" "hadoop-yarn-nodemanager" || ((error_count++))
    done
    
    # 检查ZooKeeper状态
    echo -e "\n${YELLOW}ZooKeeper 状态:${NC}"
    for node in "${HADOOP_NODES[@]:0:3}"; do
        check_service "$node" "zookeeper" || ((error_count++))
        check_port "$node" 2181 "ZooKeeper" || ((error_count++))
    done
    
    # HDFS健康检查
    echo -e "\n${YELLOW}HDFS 健康检查:${NC}"
    if ssh hadoop-node1 "hdfs dfsadmin -report" >/dev/null 2>&1; then
        echo -e "${GREEN}✓${NC} HDFS 文件系统健康"
        log "SUCCESS: HDFS filesystem is healthy"
    else
        echo -e "${RED}✗${NC} HDFS 文件系统异常"
        log "ERROR: HDFS filesystem has issues"
        ((error_count++))
    fi
    
    if [ $error_count -eq 0 ]; then
        echo -e "\n${GREEN}Hadoop 集群状态: 正常${NC}"
        log "Hadoop cluster status: All services running normally"
    else
        echo -e "\n${RED}Hadoop 集群状态: 发现 $error_count 个问题${NC}"
        log "Hadoop cluster status: Found $error_count issues"
    fi
    
    return $error_count
}

# 检查K8S集群状态
check_k8s_status() {
    print_header "Kubernetes 集群状态检查"
    
    local error_count=0
    
    # 检查Master节点
    echo -e "${YELLOW}Master 节点状态:${NC}"
    local k8s_master_services=("kube-apiserver" "kube-controller-manager" "kube-scheduler" "etcd")
    
    for service in "${k8s_master_services[@]}"; do
        check_service "k8s-master" "$service" || ((error_count++))
    done
    
    # 检查API Server端口
    check_port "k8s-master" 6443 "Kubernetes API Server" || ((error_count++))
    
    # 检查Worker节点
    echo -e "\n${YELLOW}Worker 节点状态:${NC}"
    for node in "${K8S_NODES[@]:1}"; do
        check_service "$node" "kubelet" || ((error_count++))
        check_service "$node" "kube-proxy" || ((error_count++))
    done
    
    # 检查集群状态
    echo -e "\n${YELLOW}集群节点状态:${NC}"
    if kubectl get nodes >/dev/null 2>&1; then
        kubectl get nodes --no-headers | while read -r line; do
            node_name=$(echo "$line" | awk '{print $1}')
            node_status=$(echo "$line" | awk '{print $2}')
            
            if [[ "$node_status" == "Ready" ]]; then
                echo -e "${GREEN}✓${NC} $node_name: Ready"
                log "SUCCESS: K8S node $node_name is Ready"
            else
                echo -e "${RED}✗${NC} $node_name: $node_status"
                log "ERROR: K8S node $node_name status is $node_status"
                ((error_count++))
            fi
        done
    else
        echo -e "${RED}✗${NC} 无法连接到Kubernetes集群"
        log "ERROR: Cannot connect to Kubernetes cluster"
        ((error_count++))
    fi
    
    # 检查关键Pod状态
    echo -e "\n${YELLOW}系统Pod状态:${NC}"
    if kubectl get pods -n kube-system >/dev/null 2>&1; then
        kubectl get pods -n kube-system --no-headers | grep -E "(coredns|calico|flannel)" | while read -r line; do
            pod_name=$(echo "$line" | awk '{print $1}')
            pod_status=$(echo "$line" | awk '{print $3}')
            
            if [[ "$pod_status" == "Running" ]]; then
                echo -e "${GREEN}✓${NC} $pod_name: Running"
                log "SUCCESS: K8S pod $pod_name is Running"
            else
                echo -e "${RED}✗${NC} $pod_name: $pod_status"
                log "ERROR: K8S pod $pod_name status is $pod_status"
                ((error_count++))
            fi
        done
    fi
    
    if [ $error_count -eq 0 ]; then
        echo -e "\n${GREEN}Kubernetes 集群状态: 正常${NC}"
        log "Kubernetes cluster status: All services running normally"
    else
        echo -e "\n${RED}Kubernetes 集群状态: 发现 $error_count 个问题${NC}"
        log "Kubernetes cluster status: Found $error_count issues"
    fi
    
    return $error_count
}

# 检查系统资源
check_system_resources() {
    print_header "系统资源检查"
    
    echo -e "${YELLOW}PVE 物理机资源:${NC}"
    echo "CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
    echo "内存使用: $(free -h | grep Mem | awk '{printf "%.1f%% (%s/%s)\n", $3/$2*100, $3, $2}')"
    echo "磁盘使用: $(df -h / | awk 'NR==2{printf "%s (%s)\n", $5, $4}')"
    
    log "System resources checked for PVE host"
    
    echo -e "\n${YELLOW}各虚拟机资源概况:${NC}"
    for node in "${HADOOP_NODES[@]}" "${K8S_NODES[@]}"; do
        if ping -c1 -W1 "$node" >/dev/null 2>&1; then
            cpu_usage=$(ssh -o ConnectTimeout=5 "$node" "top -bn1 | grep 'Cpu(s)' | awk '{print \$2}' | cut -d'%' -f1" 2>/dev/null || echo "N/A")
            mem_usage=$(ssh -o ConnectTimeout=5 "$node" "free | grep Mem | awk '{printf \"%.1f%%\", \$3/\$2 * 100.0}'" 2>/dev/null || echo "N/A")
            disk_usage=$(ssh -o ConnectTimeout=5 "$node" "df -h / | awk 'NR==2{print \$5}'" 2>/dev/null || echo "N/A")
            
            echo "$node: CPU $cpu_usage%, 内存 $mem_usage, 磁盘 $disk_usage"
        else
            echo -e "${RED}$node: 无法连接${NC}"
        fi
    done
    
    log "System resources checked for all nodes"
}

# 生成报告摘要
generate_summary() {
    print_header "检查结果摘要"
    
    local total_errors=$1
    
    echo "检查时间: $(date)"
    echo "日志文件: $LOG_FILE"
    echo ""
    
    if [ $total_errors -eq 0 ]; then
        echo -e "${GREEN}✓ 所有服务运行正常${NC}"
        log "SUMMARY: All services are running normally"
    else
        echo -e "${RED}✗ 发现 $total_errors 个问题需要处理${NC}"
        log "SUMMARY: Found $total_errors issues that need attention"
    fi
    
    echo ""
    echo "建议操作:"
    echo "1. 查看详细日志: cat $LOG_FILE"
    echo "2. 如有问题,请执行相应的修复脚本"
    echo "3. 重新运行检查确认修复结果"
}

# 主函数
main() {
    echo -e "${BLUE}开始集群状态检查...${NC}"
    log "Starting cluster status check"
    
    local total_errors=0
    
    # 检查PVE
    check_pve_status
    total_errors=$((total_errors + $?))
    
    echo ""
    
    # 检查Hadoop
    check_hadoop_status
    total_errors=$((total_errors + $?))
    
    echo ""
    
    # 检查K8S
    check_k8s_status
    total_errors=$((total_errors + $?))
    
    echo ""
    
    # 检查系统资源
    check_system_resources
    
    echo ""
    
    # 生成摘要
    generate_summary $total_errors
    
    log "Cluster status check completed with $total_errors total issues"
    
    exit $total_errors
}

# 检查依赖工具
check_dependencies() {
    local missing_tools=()
    
    for tool in ssh nc pvesh kubectl hdfs; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            missing_tools+=("$tool")
        fi
    done
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        echo -e "${RED}错误: 缺少必要工具: ${missing_tools[*]}${NC}"
        echo "请安装缺少的工具后重新运行"
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    check_dependencies
    main "$@"
fi 