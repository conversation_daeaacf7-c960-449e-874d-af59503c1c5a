2025-05-19 17:16:20,879 - LSTM_TF_Local_Main - INFO - Starting LSTM TensorFlow Local Mode main script.
2025-05-19 17:16:20,880 - LSTM_TF_Local_Main - INFO - TensorFlow version: 2.13.0
2025-05-19 17:16:20,880 - LSTM_TF_Local_Main - INFO - Received arguments: Namespace(batch_size=64, cv_folds=5, data_format='pkl', handle_imbalance=False, hidden_size=128, input_file='E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', input_size=84, label_column='Label', learning_rate=0.001, model_name='LSTM_TF_Local_Model', normalize=True, num_epochs=1, num_layers=1, output_size=6, result_dir='./results/results_lstm_tf_local', stratify=False, test_split_size=0.3, use_cv=False)
2025-05-19 17:16:20,882 - LSTM_TF_Local_Main - INFO - Effective preprocessing options: {
  "test_size": 0.3,
  "random_state": 42,
  "normalize": true,
  "encode_labels": true,
  "handle_imbalance": false,
  "stratify": false,
  "drop_missing": false,
  "fill_method": "mean",
  "handle_outliers": true
}
2025-05-19 17:16:20,883 - LSTM_TF_Local_Main - INFO - LSTM TensorFlow training (local mode) function started.
2025-05-19 17:16:20,883 - LSTM_TF_Local_Main - INFO - No GPU found, using CPU.
2025-05-19 17:16:20,883 - LSTM_TF_Local_Main - INFO - Run parameters: Input File='E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', Model Name='LSTM_TF_Local_Model'
2025-05-19 17:16:20,884 - LSTM_TF_Local_Main - INFO - Model Hyperparameters: Input Size=84, Hidden Size=128, Output Classes=6, Layers=1
2025-05-19 17:16:20,884 - LSTM_TF_Local_Main - INFO - Training Parameters: Epochs=1, LR=0.001, Batch Size=64, CV Mode=False
2025-05-19 17:16:20,884 - LSTM_TF_Local_Main - INFO - Preprocessing Options: {
  "test_size": 0.3,
  "random_state": 42,
  "normalize": true,
  "encode_labels": true,
  "handle_imbalance": false,
  "stratify": false,
  "drop_missing": false,
  "fill_method": "mean",
  "handle_outliers": true
}
2025-05-19 17:16:20,885 - LSTM_TF_Local_Main - INFO - Loading data from E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl with format pkl
2025-05-19 17:16:21,244 - LSTM_TF_Local_Main - INFO - Data loaded as DataFrame with shape (692703, 85)
2025-05-19 17:16:21,359 - LSTM_TF_Local_Main - INFO - Features shape: (692703, 84), Labels shape: (692703,)
2025-05-19 17:16:21,388 - LSTM_TF_Local_Main - INFO - Label distribution: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-19 17:16:21,428 - LSTM_TF_Local_Main - INFO - Original classes for reporting (from data): ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-19 17:16:21,429 - LSTM_TF_Local_Main - INFO - Using Keras LSTM standard train/test split.
2025-05-19 17:16:21,674 - LSTM_TF_Local_Main - INFO - Handling missing values in features
2025-05-19 17:16:22,007 - LSTM_TF_Local_Main - INFO - Missing values: 1008 -> 0
2025-05-19 17:16:22,008 - LSTM_TF_Local_Main - INFO - Encoding labels
2025-05-19 17:16:22,109 - LSTM_TF_Local_Main - INFO - Encoded 6 classes: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']")
2025-05-19 17:16:22,138 - LSTM_TF_Local_Main - INFO - Encoding 4 categorical features: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-19 17:16:26,027 - LSTM_TF_Local_Main - INFO - Checking and handling infinite values (replacing with NaN, then re-imputing).
2025-05-19 17:16:26,302 - LSTM_TF_Local_Main - INFO - Normalizing features
2025-05-19 17:16:28,470 - LSTM_TF_Local_Main - INFO - Data split into: Train X(484892, 84), y(484892,); Test X(207811, 84), y(207811,)
2025-05-19 17:16:28,533 - LSTM_TF_Local_Main - INFO - LSTM TF Preprocessing info saved to ./results/results_lstm_tf_local\preprocessing_info_lstm_tf.json
2025-05-19 17:16:28,672 - LSTM_TF_Local_Main - INFO - Prepared TF Datasets: Train X(484892, 1, 84), Y(484892,); Test X(207811, 1, 84), Y(207811,). Batch size: 64
2025-05-19 17:16:28,815 - LSTM_TF_Local_Main - INFO - Starting Keras LSTM model training (standard split).
2025-05-19 17:16:28,828 - LSTM_TF_Local_Main - INFO - Best LSTM model weights will be saved to: ./results/results_lstm_tf_local\best_model_lstm.weights.h5 based on val_loss.
2025-05-19 17:16:28,829 - LSTM_TF_Local_Main - INFO - Starting Keras LSTM model training for 1 epochs.
2025-05-19 17:16:55,313 - LSTM_TF_Local_Main - INFO - Saved final LSTM model weights to: ./results/results_lstm_tf_local\final_model_lstm.weights.h5
2025-05-19 17:16:56,222 - LSTM_TF_Local_Main - INFO - Training history plots saved to ./results/results_lstm_tf_local\plots_lstm_tf with suffix '_lstm_tf'
2025-05-19 17:16:56,223 - LSTM_TF_Local_Main - INFO - Evaluating Keras LSTM model on test set.
2025-05-19 17:16:56,223 - LSTM_TF_Local_Main - INFO - Loading best LSTM weights from ./results/results_lstm_tf_local\best_model_lstm.weights.h5 for final testing.
2025-05-19 17:16:56,254 - LSTM_TF_Local_Main - ERROR - Error during LSTM TF training (local_mode): Layer 'output_dense' expected 0 variables, but received 2 variables during loading. Expected: []
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\循环神经网络\LSTM_tf_local_mode.py", line 746, in lstm_train_tf_local
    eval_model_instance.load_weights(best_weights_path)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\base_layer.py", line 3539, in load_own_variables
    raise ValueError(
ValueError: Layer 'output_dense' expected 0 variables, but received 2 variables during loading. Expected: []
2025-05-19 17:16:56,257 - LSTM_TF_Local_Main - ERROR - Critical error in LSTM TF Local Mode main script: Layer 'output_dense' expected 0 variables, but received 2 variables during loading. Expected: []
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\循环神经网络\LSTM_tf_local_mode.py", line 825, in <module>
    training_results = lstm_train_tf_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\循环神经网络\LSTM_tf_local_mode.py", line 746, in lstm_train_tf_local
    eval_model_instance.load_weights(best_weights_path)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\base_layer.py", line 3539, in load_own_variables
    raise ValueError(
ValueError: Layer 'output_dense' expected 0 variables, but received 2 variables during loading. Expected: []
