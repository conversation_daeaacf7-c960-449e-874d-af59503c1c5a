2025-05-23 14:08:21,661 - GraphSAGE_TF_Local - INFO - Logging configured. Log file: ./results/graphsage_tf_local_experiment\GraphSAGE_TF_Local_Model_main_script_training_20250523_140821.log
2025-05-23 14:08:21,662 - GraphSAGE_TF_Local_MainScript - INFO - Starting GraphSAGE TensorFlow Local Mode main script execution.
2025-05-23 14:08:21,662 - GraphSAGE_TF_Local_MainScript - INFO - Command line arguments: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'result_dir': './results/graphsage_tf_local_experiment', 'model_name': 'GraphSAGE_TF_Local_Model', 'hidden_size': 128, 'num_layers': 2, 'dropout_rate': 0.5, 'num_epochs': 3, 'learning_rate': 0.001, 'early_stopping_patience': 10, 'label_column': 'Label', 'data_format': 'pkl', 'feature_columns': None, 'normalize_features': True, 'scaler_type': 'standard', 'fill_missing_method': 'mean', 'stratify_split': True, 'test_split_size': 0.2, 'random_state': 42, 'edge_strategy': 'knn', 'k_neighbors': 10, 'use_cv': False, 'cv_folds': 5}
