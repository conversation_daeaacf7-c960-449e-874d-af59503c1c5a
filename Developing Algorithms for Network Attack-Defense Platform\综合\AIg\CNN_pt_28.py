"""CNN"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler

class CNNModel(nn.Module):
    def __init__(self, input_channels, num_classes):
        super(CNNModel, self).__init__()
        self.conv1 = nn.Conv2d(input_channels, 32, kernel_size=3, stride=1, padding=1)
        self.pool = nn.MaxPool2d(kernel_size=2, stride=2, padding=0)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, stride=1, padding=1)
        self.fc1 = nn.Linear(64 * 7 * 7, 128)
        self.fc2 = nn.Linear(128, num_classes)

    def forward(self, x):
        x = self.pool(F.relu(self.conv1(x)))
        x = self.pool(F.relu(self.conv2(x)))
        x = x.view(-1, 64 * 7 * 7)
        x = F.relu(self.fc1(x))
        x = self.fc2(x)
        return x

def cnn_analysis(input_file, input_column, target_column, input_channels, num_classes, epochs, batch_size):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - input_column: 输入特征列名列表
    - target_column: 目标列名
    - input_channels: 输入通道数
    - num_classes: 类别数
    - epochs: 训练轮数
    - batch_size: 批次大小
    """
    data = pd.read_csv(input_file)
    X = data[input_column].values
    y = data[target_column].values

    scaler = StandardScaler()
    X = scaler.fit_transform(X)

    X = np.reshape(X, (X.shape[0], input_channels, 28, 28))

    X = torch.tensor(X, dtype=torch.float32)
    y = torch.tensor(y, dtype=torch.long)

    dataset = TensorDataset(X, y)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)

    model = CNNModel(input_channels, num_classes)
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    for epoch in range(epochs):
        for inputs, targets in dataloader:
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
        print(f"Epoch {epoch+1}/{epochs}, Loss: {loss.item()}")

    print("CNN training completed")

if __name__ == "__main__":
    input_file = 'data.csv'
    input_column = ['feature1', 'feature2']
    target_column = 'target'
    input_channels = 1
    num_classes = 10
    epochs = 20
    batch_size = 32

    cnn_analysis(input_file, input_column, target_column, input_channels, num_classes, epochs, batch_size)
