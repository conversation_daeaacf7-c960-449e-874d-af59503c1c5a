"""KNN 分类"""

import sys
import pandas as pd
from sklearn.neighbors import KNeighborsClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score

def knn_classifier(input_file, target_column, n_neighbors=5, test_size=0.2):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - target_column: 目标变量的列名
    - n_neighbors: 邻居数量，默认为5
    - test_size: 测试集比例，默认为0.2
    """
    data = pd.read_csv(input_file)
    X = data.drop(target_column, axis=1)
    y = data[target_column]
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size)
    
    model = KNeighborsClassifier(n_neighbors=n_neighbors)
    model.fit(X_train, y_train)
    
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    
    print(f"KNN classification completed. Accuracy: {accuracy:.2f}")

if __name__ == "__main__":
    if len(sys.argv) != 5:
        print("Usage: python knn_classifier.py <input_file> <target_column> <n_neighbors> <test_size>")
        sys.exit(1)
    input_file, target_column, n_neighbors, test_size = sys.argv[1], sys.argv[2], sys.argv[3], sys.argv[4]
    knn_classifier(input_file, target_column, int(n_neighbors), float(test_size))
