{"options_used": {"test_size": 0.2, "random_state": 42, "normalize": true, "scaler_type": "standard", "handle_imbalance": false, "stratify_split": true, "numeric_impute_strategy": "mean", "categorical_impute_strategy": "most_frequent"}, "imputers": {"numeric_strategy": "mean", "categorical_strategy": "most_frequent"}, "label_encoder": {}, "classes": ["BENIGN", "DoS GoldenEye", "DoS Hulk", "DoS Slowhttptest", "DoS slowloris", "Heartbleed"], "categorical_encoder": {"categories": "auto", "dtype": "<class 'numpy.float64'>", "encoded_missing_value": NaN, "handle_unknown": "use_encoded_value", "max_categories": null, "min_frequency": null, "unknown_value": -1}, "categorical_columns_encoded": ["Flow ID", "Source IP", "Destination IP", "Timestamp"], "feature_scaler": {"copy": true, "with_mean": true, "with_std": true}}