{"model_type": "train_test_split_tf", "model_config_path": "results\\results_gcn_tf_local\\models_gcn_tf_local\\GCN_TF_Local_Model_final_config.json", "model_weights_path": "results\\results_gcn_tf_local\\models_gcn_tf_local\\GCN_TF_Local_Model_final_weights.h5", "test_metrics": {"accuracy": 0.8402999833984164, "precision": 0.8201027667626266, "recall": 0.8402999833984164, "f1": 0.8229552772830576}, "training_history": {"train_loss": [2.0799880027770996, 1.4976202249526978, 1.102885127067566], "train_acc": [23.85565936565399, 45.540833473205566, 64.61125016212463], "val_loss": [1.140885591506958, 0.7831538915634155, 0.5703416466712952], "val_acc": [76.92307829856873, 81.4668595790863, 84.02999639511108]}, "class_names": ["BENIGN", "DoS GoldenEye", "DoS Hulk", "DoS Slowhttptest", "DoS slowloris", "Heartbleed"], "preprocessing_info_path": "results\\results_gcn_tf_local\\GCN_TF_Local_Model_preprocessing_info_tf.json"}