"""决策树回归"""

import sys
import pandas as pd
from sklearn.tree import DecisionTreeRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error

def decision_tree_regression(input_file, target_column, test_size=0.2):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - target_column: 目标变量的列名
    - test_size: 测试集比例，默认为0.2
    """
    data = pd.read_csv(input_file)
    X = data.drop(target_column, axis=1)
    y = data[target_column]
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size)
    
    model = DecisionTreeRegressor()
    model.fit(X_train, y_train)
    
    y_pred = model.predict(X_test)
    mse = mean_squared_error(y_test, y_pred)
    
    print(f"Decision tree regression completed. MSE: {mse:.2f}")

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python decision_tree_regression.py <input_file> <target_column> <test_size>")
        sys.exit(1)
    input_file, target_column, test_size = sys.argv[1], sys.argv[2], sys.argv[3]
    decision_tree_regression(input_file, target_column, float(test_size))
