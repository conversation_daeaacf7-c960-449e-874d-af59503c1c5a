import argparse
import tensorflow as tf
from tensorflow.keras.layers import Dense, Dropout
from transformers import TFBertModel, BertTokenizer
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
import os
import joblib
from minio import Minio
import uuid
import json
import requests

# MinIO and Model Factory Configuration
MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class TextDataset:
    def __init__(self, texts, labels, tokenizer, max_len=128):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_len = max_len
        
    def prepare_dataset(self):
        encodings = self.tokenizer(
            self.texts,
            add_special_tokens=True,
            max_length=self.max_len,
            padding='max_length',
            truncation=True,
            return_attention_mask=True,
            return_tensors='tf'
        )
        
        return tf.data.Dataset.from_tensor_slices((
            {
                'input_ids': encodings['input_ids'],
                'attention_mask': encodings['attention_mask']
            },
            self.labels
        ))

class TextClassificationModel(tf.keras.Model):
    def __init__(self, model_name='bert-base-uncased', num_labels=2):
        super().__init__()
        self.bert = TFBertModel.from_pretrained(model_name)
        self.dropout = Dropout(0.1)
        self.classifier = Dense(num_labels, activation='softmax')
        
    def call(self, inputs, training=False):
        input_ids = inputs['input_ids']
        attention_mask = inputs['attention_mask']
        
        outputs = self.bert(input_ids, attention_mask=attention_mask)
        pooled_output = outputs[1]
        pooled_output = self.dropout(pooled_output, training=training)
        logits = self.classifier(pooled_output)
        
        return logits

class TextClassifier:
    def __init__(self, model_name='bert-base-uncased', num_labels=2):
        self.tokenizer = BertTokenizer.from_pretrained(model_name)
        self.model = TextClassificationModel(model_name, num_labels)
        
    def train(self, train_texts, train_labels, val_texts, val_labels, epochs=4, batch_size=16, learning_rate=2e-5):
        # Prepare datasets
        train_dataset = TextDataset(train_texts, train_labels, self.tokenizer)
        val_dataset = TextDataset(val_texts, val_labels, self.tokenizer)
        
        train_ds = train_dataset.prepare_dataset().shuffle(1000).batch(batch_size)
        val_ds = val_dataset.prepare_dataset().batch(batch_size)
        
        # Compile model
        optimizer = tf.keras.optimizers.Adam(learning_rate=learning_rate)
        loss = tf.keras.losses.SparseCategoricalCrossentropy()
        
        self.model.compile(
            optimizer=optimizer,
            loss=loss,
            metrics=['accuracy']
        )
        
        # Train model
        history = self.model.fit(
            train_ds,
            validation_data=val_ds,
            epochs=epochs,
            verbose=1
        )
        
        return history
    
    def evaluate(self, val_texts, val_labels, batch_size=16):
        val_dataset = TextDataset(val_texts, val_labels, self.tokenizer)
        val_ds = val_dataset.prepare_dataset().batch(batch_size)
        
        results = self.model.evaluate(val_ds, verbose=1)
        print(f"Validation Loss: {results[0]}")
        print(f"Validation Accuracy: {results[1]}")
        
    def save_model(self, result_dir, model_name):
        model_path = os.path.join(result_dir, model_name + ".pkl")
        with open(model_path, 'wb') as model_file:
            joblib.dump(self, model_file)
        print(f'Model saved to {model_path}')

def upload_model(result_dir, model_name):
    minioClient = Minio(MINIO_URL,
                       access_key='AKIAIOSFODNN7EXAMPLE',
                       secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                       secure=False)
    try:
        obj_name = str(uuid.uuid1())
        upload_path = obj_name + "/" + model_name + ".pkl"
        source = "s3://mlss-mf/" + obj_name
        minioClient.fput_object('mlss-mf', upload_path, os.path.join(result_dir, model_name + ".pkl"))
        result = {"source": source}
        return result, 0
    except Exception as err:
        print(err)
        return None, -1

def register_model(model_name, source, group_id, headers):
    params = {
        "model_name": model_name,
        "model_type": "tensorflow",
        "file_name": model_name + ".pkl",
        "s3_path": source,
        "group_id": int(float(group_id)),
        "training_id": model_name,
        "training_flag": 1,
    }
    r = requests.post(MODEL_FACTORY_URL + MODEL_ADD_URL, data=json.dumps(params), headers=headers)
    res_data = r.content.decode()
    if r.status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def push_model(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0
    params = {
        "factory_name": factory_name,
        "model_type": "Text_Classification",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL + MODEL_PUSH_URL + "/" + str(model_version_id), data=json.dumps(params), headers=headers)
    res_data = r.content.decode()
    if r.status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: user_id
    }
    return headers

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Text Classification using BERT with TensorFlow.')
    parser.add_argument('--train_texts', dest='train_texts', type=json.loads, help='List of training texts')
    parser.add_argument('--train_labels', dest='train_labels', type=json.loads, help='List of training labels')
    parser.add_argument('--val_texts', dest='val_texts', type=json.loads, help='List of validation texts')
    parser.add_argument('--val_labels', dest='val_labels', type=json.loads, help='List of validation labels')
    parser.add_argument('--result_dir', dest='result_dir', type=str, help='Directory to save the model')
    parser.add_argument('--model', dest='model', type=json.loads, help='Model parameters')
    parser.add_argument('--factory_name', dest='factory_name', type=str, help='Factory name')
    parser.add_argument('--epochs', dest='epochs', type=int, default=4, help='Number of epochs to train')
    parser.add_argument('--batch_size', dest='batch_size', type=int, default=16, help='Batch size')
    parser.add_argument('--learning_rate', dest='learning_rate', type=float, default=2e-5, help='Learning rate')

    args = parser.parse_args()

    print("Starting text classification, params:\n" + str(args) + "\n")

    # Step 1: Train the model
    print("Step 1: Train the model\n")
    classifier = TextClassifier()
    classifier.train(
        args.train_texts, 
        args.train_labels, 
        args.val_texts, 
        args.val_labels, 
        args.epochs, 
        args.batch_size, 
        args.learning_rate
    )

    # Step 2: Save the model
    print("Step 2: Save the model\n")
    classifier.save_model(args.result_dir, args.model["model_name"])

    # Step 3: Upload model to MinIO
    print("Step 3: Upload model to MinIO\n")
    result, ret_code = upload_model(args.result_dir, args.model["model_name"])
    if ret_code != 0:
        exit(-1)

    # Step 4: Register the model in the model factory
    print("Step 4: Register model in model factory\n")
    user_id = os.getenv(MLSS_USER_ID_KEY, "test")
    headers = header_gen(user_id)
    result, ret_code = register_model(args.model["model_name"], result["source"], args.model["group_id"], headers)
    if ret_code != 0:
        exit(-1)

    model_id = result["data"]["model_id"]
    model_version_id = result["data"]["model_version_id"]

    # Step 5: Push model to the factory
    print("Step 5: Push model to the factory\n")
    result, ret_code = push_model(model_id, model_version_id, args.factory_name)
    if ret_code != 0:
        exit(-1)

    print("Model training and deployment pipeline completed successfully!")