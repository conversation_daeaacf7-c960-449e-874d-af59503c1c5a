2025-05-23 10:08:33,489 - GraphSAGE_PT_Local_Mode - INFO - Logging to console and file: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\graphsage_pt_local_mode_training_20250523_100833.log
2025-05-23 10:08:33,490 - GraphSAGE_PT_Local_Mode - INFO - GraphSAGE PyTorch Local Mode training script initialized.
2025-05-23 10:08:33,490 - GraphSAGE_PT_Local_Mode - INFO - PyTorch version: 2.4.1+cpu
2025-05-23 10:08:33,491 - GraphSAGE_PT_Local_Mode - INFO - PyTorch Geometric version: 2.6.1
2025-05-23 10:08:33,491 - GraphSAGE_PT_Local_Mode - INFO - CLI Arguments: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'label_column': 'Label', 'data_format': 'pkl', 'hidden_size': 64, 'output_size': 6, 'num_layers': 2, 'dropout_rate': 0.5, 'edge_strategy': 'fully_connected_per_sample', 'k_neighbors': 5, 'num_epochs': 3, 'learning_rate': 0.001, 'batch_size': 32, 'test_split_size': 0.2, 'random_state': 42, 'normalize': True, 'scaler_type': 'standard', 'handle_imbalance': False, 'stratify_split': True, 'numeric_impute_strategy': 'mean', 'categorical_impute_strategy': 'most_frequent', 'use_cv': False, 'cv_folds': 5, 'result_dir': 'E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\results\\results_graphsage_pt_local', 'model_name': 'GraphSAGE_PT_Local_Model', 'log_level': 'INFO', 'force_cpu': False}
2025-05-23 10:08:33,492 - GraphSAGE_PT_Local_Mode - INFO - Using device: cpu
2025-05-23 10:08:33,493 - GraphSAGE_PT_Local_Mode.DataLoad - INFO - Loading data from E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl, format: pkl
2025-05-23 10:08:36,819 - GraphSAGE_PT_Local_Mode.DataLoad - INFO - Data loaded. X shape: (692703, 84), y shape: (692703,)
2025-05-23 10:08:36,853 - GraphSAGE_PT_Local_Mode.DataLoad - INFO - Label distribution:
Label
BENIGN              440031
DoS Hulk            231073
DoS GoldenEye        10293
DoS slowloris         5796
DoS Slowhttptest      5499
Heartbleed              11
Name: count, dtype: int64
2025-05-23 10:08:36,994 - GraphSAGE_PT_Local_Mode - INFO - Inferred 6 classes from the label column 'Label'. Classes: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-23 10:08:36,995 - GraphSAGE_PT_Local_Mode - INFO - Standard train/test split mode selected for GraphSAGE (PT).
2025-05-23 10:08:43,989 - GraphSAGE_PT_Local_Mode - INFO - Scaler for train/test split saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\GraphSAGE_PT_Local_Model_scaler.joblib
2025-05-23 10:08:43,992 - GraphSAGE_PT_Local_Mode - INFO - Label encoder for train/test split saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\GraphSAGE_PT_Local_Model_label_encoder.joblib
2025-05-23 10:08:44,070 - GraphSAGE_PT_Local_Mode - INFO - Categorical encoder for train/test split saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\GraphSAGE_PT_Local_Model_cat_encoder.joblib
2025-05-23 10:08:46,616 - GraphSAGE_PT_Local_Mode - INFO - Preprocessing info for train/test split saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\GraphSAGE_PT_Local_Model_preproc_info.json
2025-05-23 10:08:46,625 - GraphSAGE_PT_Local_Mode - INFO - Using stratified train/test split.
2025-05-23 10:08:47,644 - GraphSAGE_PT_Local_Mode - INFO - Data split: Train X shape (554162, 84), y shape (554162,). Test X shape (138541, 84), y shape (138541,)
2025-05-23 11:04:03,527 - GraphSAGE_PT_Local_Mode - INFO - Model state dict saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\models\GraphSAGE_PT_Local_Model_best.pth
2025-05-23 11:04:03,754 - GraphSAGE_PT_Local_Mode - INFO - Model configuration saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\models\GraphSAGE_PT_Local_Model_best_config.json
2025-05-23 11:04:03,755 - GraphSAGE_PT_Local_Mode.Train - INFO - Epoch 1: Saved best model with val_loss: 0.3813
2025-05-23 11:04:03,755 - GraphSAGE_PT_Local_Mode.Train - INFO - Epoch 1/3 - Train Loss: 0.4892, Train Acc: 82.27%, Val Loss: 0.3813, Val Acc: 85.39%
2025-05-23 11:56:40,844 - GraphSAGE_PT_Local_Mode - INFO - Model state dict saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\models\GraphSAGE_PT_Local_Model_best.pth
2025-05-23 11:56:40,844 - GraphSAGE_PT_Local_Mode - INFO - Model configuration saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\models\GraphSAGE_PT_Local_Model_best_config.json
2025-05-23 11:56:40,844 - GraphSAGE_PT_Local_Mode.Train - INFO - Epoch 2: Saved best model with val_loss: 0.3728
2025-05-23 11:56:40,848 - GraphSAGE_PT_Local_Mode.Train - INFO - Epoch 2/3 - Train Loss: 0.4425, Train Acc: 83.96%, Val Loss: 0.3728, Val Acc: 85.36%
2025-05-23 12:48:33,908 - GraphSAGE_PT_Local_Mode - INFO - Model state dict saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\models\GraphSAGE_PT_Local_Model_best.pth
2025-05-23 12:48:33,908 - GraphSAGE_PT_Local_Mode - INFO - Model configuration saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\models\GraphSAGE_PT_Local_Model_best_config.json
2025-05-23 12:48:33,908 - GraphSAGE_PT_Local_Mode.Train - INFO - Epoch 3: Saved best model with val_loss: 0.3642
2025-05-23 12:48:33,908 - GraphSAGE_PT_Local_Mode.Train - INFO - Epoch 3/3 - Train Loss: 0.4358, Train Acc: 84.22%, Val Loss: 0.3642, Val Acc: 86.70%
2025-05-23 12:48:33,908 - GraphSAGE_PT_Local_Mode - INFO - Model state dict saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\models\GraphSAGE_PT_Local_Model_final.pth
2025-05-23 12:48:33,921 - GraphSAGE_PT_Local_Mode - INFO - Model configuration saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\models\GraphSAGE_PT_Local_Model_final_config.json
2025-05-23 12:48:34,811 - GraphSAGE_PT_Local_Mode.Plot - INFO - Training history plot saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\plots\GraphSAGE_PT_Local_Model_training_history.png
2025-05-23 12:48:34,840 - GraphSAGE_PT_Local_Mode - INFO - Model loaded from E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\models\GraphSAGE_PT_Local_Model_best.pth and config E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\models\GraphSAGE_PT_Local_Model_best_config.json, moved to cpu
2025-05-23 12:48:34,841 - GraphSAGE_PT_Local_Mode - INFO - Loaded best saved model 'GraphSAGE_PT_Local_Model_best.pth' for final testing.
2025-05-23 12:48:34,845 - GraphSAGE_PT_Local_Mode - INFO - Model state dict saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\models\GraphSAGE_PT_Local_Model_reported.pth
2025-05-23 12:48:34,846 - GraphSAGE_PT_Local_Mode - INFO - Model configuration saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\models\GraphSAGE_PT_Local_Model_reported_config.json
2025-05-23 12:48:34,846 - GraphSAGE_PT_Local_Mode - INFO - Saved reported model state to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\models\GraphSAGE_PT_Local_Model_reported.pth
2025-05-23 12:57:51,922 - GraphSAGE_PT_Local_Mode.Test - INFO - Classification Report (GraphSAGE_PT_Local_Model):
                  precision    recall  f1-score   support

          BENIGN       0.89      0.92      0.90     88006
   DoS GoldenEye       0.00      0.00      0.00      2059
        DoS Hulk       0.83      0.84      0.84     46215
DoS Slowhttptest       0.48      0.58      0.52      1100
   DoS slowloris       0.00      0.00      0.00      1159
      Heartbleed       0.00      0.00      0.00         2

        accuracy                           0.87    138541
       macro avg       0.37      0.39      0.38    138541
    weighted avg       0.85      0.87      0.86    138541

2025-05-23 12:57:52,525 - GraphSAGE_PT_Local_Mode.Plot - INFO - Confusion matrix plot saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\plots\GraphSAGE_PT_Local_Model_test_confusion_matrix.png
2025-05-23 12:57:52,525 - GraphSAGE_PT_Local_Mode.Test - INFO - Test classification report saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\GraphSAGE_PT_Local_Model_test_classification_report.txt
2025-05-23 12:57:52,525 - GraphSAGE_PT_Local_Mode.Test - INFO - GraphSAGE_PT_Local_Model Test Metrics: Accuracy=0.8670, F1 (Weighted)=0.8572
2025-05-23 12:57:52,540 - GraphSAGE_PT_Local_Mode - INFO - GraphSAGE (PT) local mode run summary saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\GraphSAGE_PT_Local_Model_run_summary_pt.json
