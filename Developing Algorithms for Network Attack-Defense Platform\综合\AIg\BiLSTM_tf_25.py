import tensorflow as tf
import pandas as pd
import numpy as np
import pickle
from sklearn.preprocessing import StandardScaler, LabelEncoder, OrdinalEncoder
from sklearn.model_selection import train_test_split
import os

class BiLSTMModel(tf.keras.Model):
    def __init__(self, input_size, hidden_size, num_layers, num_classes):
        super(BiLSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # Bidirectional LSTM layers
        self.bilstm_layers = [
            tf.keras.layers.Bidirectional(
                tf.keras.layers.LSTM(hidden_size, return_sequences=(i < num_layers-1))
            ) for i in range(num_layers)
        ]
        
        # Final dense layer for classification
        self.fc = tf.keras.layers.Dense(num_classes, activation='softmax')

    def call(self, x):
        # Ensure input has correct dimensionality
        if len(x.shape) == 2:
            x = tf.expand_dims(x, axis=1)  # Add time step dimension
        elif len(x.shape) != 3:
            raise ValueError(f"Expected 2D or 3D input, but got {len(x.shape)}D input")

        # Pass through BiLSTM layers
        for bilstm_layer in self.bilstm_layers:
            x = bilstm_layer(x)
        
        # Take the last time step output
        return self.fc(x)

def load_data(file_path):
    # Load data from pkl file
    with open(file_path, 'rb') as file:
        data = pickle.load(file)
    
    # Handle different data formats
    if isinstance(data, pd.DataFrame):
        X = data.drop(['Label'], axis=1)  # Assume 'Label' is the label column
        y = data['Label']
    elif isinstance(data, dict):
        X = data['features']
        y = data['labels']
    
    return X, y

def load_data_preprocess(pkl_file):
    # Load data from pkl file
    with open(pkl_file, 'rb') as file:
        data = pickle.load(file)
    
    # Clean column names
    data.columns = data.columns.str.strip()

    print(type(data))  # Check data type
    print(data.head())  # View first few rows
    print(data.info())  # View data information

    # Handle different data formats
    if isinstance(data, pd.DataFrame):
        print(data.columns)
        X = data.drop(['Label'], axis=1)
        y = data['Label']
    elif isinstance(data, dict):
        X = data['features']
        y = data['labels']
    
    return X, y

def preprocess_data(X, y):
    # 1. Handle missing values
    if X.isnull().any().any() or y.isnull().any():
        X, y = X.align(y, join='inner', axis=0)
        X = X.dropna()
        y = y[X.index]

    # 2. Label encoding
    le = LabelEncoder()
    y = le.fit_transform(y)

    # 3. Encode categorical features
    categorical_cols = X.select_dtypes(include=['object']).columns
    encoder = OrdinalEncoder()
    if not categorical_cols.empty:
        X[categorical_cols] = encoder.fit_transform(X[categorical_cols])
    
    # 4. Handle infinite values
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.mean(), inplace=True)
    
    # 5. Standardize data
    scaler = StandardScaler()
    X = scaler.fit_transform(X)

    # 6. Split into train and test sets
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    return X_train, X_test, y_train, y_test

def bilstm_train(
    input_file, 
    input_size, 
    hidden_size, 
    output_size, 
    num_layers, 
    num_epochs, 
    learning_rate, 
    batch_size, 
    result_dir, 
    model_name
):
    """
    Train BiLSTM model using TensorFlow
    
    Parameters:
    - input_file: Path to input pkl file
    - input_size: Input feature dimension
    - hidden_size: Hidden layer size
    - output_size: Number of output classes
    - num_layers: Number of LSTM layers
    - num_epochs: Number of training epochs
    - learning_rate: Optimizer learning rate
    - batch_size: Batch size for training
    - result_dir: Directory to save model
    - model_name: Name of the model file
    """
    # Load and preprocess data
    X, y = load_data_preprocess(input_file)
    X_train, X_test, y_train, y_test = preprocess_data(X, y)

    # Prepare TensorFlow datasets
    train_dataset = tf.data.Dataset.from_tensor_slices(
        (X_train, y_train)
    ).batch(batch_size)
    
    test_dataset = tf.data.Dataset.from_tensor_slices(
        (X_test, y_test)
    ).batch(batch_size)

    # Initialize BiLSTM model
    # Note: For H5 saving, we'll use a Sequential model instead of custom subclassed model
    model = tf.keras.Sequential([
        tf.keras.layers.Bidirectional(
            tf.keras.layers.LSTM(hidden_size, return_sequences=True), 
            input_shape=(X_train.shape[1], 1)
        ),
        tf.keras.layers.Bidirectional(
            tf.keras.layers.LSTM(hidden_size)
        ),
        tf.keras.layers.Dense(output_size, activation='softmax')
    ])

    # Compile the model
    model.compile(
        optimizer=tf.keras.optimizers.Adam(learning_rate=learning_rate),
        loss=tf.keras.losses.SparseCategoricalCrossentropy(),
        metrics=['accuracy']
    )

    # Train the model
    history = model.fit(
        train_dataset, 
        epochs=num_epochs, 
        validation_data=test_dataset
    )

    # Evaluate the model
    test_loss, test_accuracy = model.evaluate(test_dataset)
    print(f'Test accuracy: {test_accuracy * 100:.2f}%')

    # Save the model in H5 format
    # model_path = f"{result_dir}/{model_name}.h5"
    # model.save(model_path, save_format='h5')
    # print(f'BiLSTM model saved to {model_path}')

    # Save the model in SavedModel format
    model_path = f"{result_dir}/{model_name}"
    print(f"Model will be saved to: {model_path}")
    os.makedirs(model_path, exist_ok=True)  # 确保目录存在
    model.save(model_path, save_format='tf')
    print(f'BiLSTM model saved to {model_path}')

    return history, test_accuracy

# Example usage
if __name__ == "__main__":
    input_file = 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl'
    input_size = 84
    hidden_size = 128
    num_layers = 2
    output_size = 6
    num_epochs = 1
    learning_rate = 0.001
    batch_size = 32
    result_dir = 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_'
    model_name = 'BiLSTM_tf'

    bilstm_train(
        input_file, 
        input_size, 
        hidden_size, 
        output_size, 
        num_layers, 
        num_epochs, 
        learning_rate, 
        batch_size, 
        result_dir, 
        model_name
    )