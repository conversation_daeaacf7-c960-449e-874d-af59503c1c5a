2025-05-23 15:08:31,818 - QLearning_PT_Local - INFO - Logging to: ./results/results_qlearning_pt_local\QLearning_CartPole_training_20250523_150831.log
2025-05-23 15:08:31,818 - QLearning_PT_Local - INFO - Q-Learning PT Local Training (QLearning_CartPole) starting on device: cpu.
2025-05-23 15:08:31,818 - QLearning_PT_Local - INFO - Params:
{
  "env_name": "CartPole-v1",
  "result_dir": "./results/results_qlearning_pt_local",
  "model_name": "QLearning_CartPole",
  "hidden_size": 64,
  "learning_rate": 0.001,
  "gamma": 0.99,
  "epsilon_start": 1.0,
  "epsilon_end": 0.01,
  "epsilon_decay": 0.995,
  "buffer_size": 10000,
  "batch_size": 32,
  "num_episodes": 3,
  "max_steps_per_episode": 200,
  "random_seed": null,
  "device_str": "cpu",
  "save_freq": 100,
  "plot_freq": 50,
  "logger": "<Logger QLearning_PT_Local (INFO)>"
}
2025-05-23 15:08:31,834 - QLearning_PT_Local.Env - INFO - Using Gymnasium API for CartPole-v1
2025-05-23 15:08:31,835 - QLearning_PT_Local - INFO - Env: CartPole-v1, State Dim=4, Action Dim=2
2025-05-23 15:08:33,114 - QLearning_PT_Local - INFO - Ep 1/3 | Score: 10.00 | AvgScore(100): 10.00 | Epsilon: 0.995 | Steps: 10 | Time: 0.00s
2025-05-23 15:08:33,130 - QLearning_PT_Local - INFO - Ep 2/3 | Score: 17.00 | AvgScore(100): 13.50 | Epsilon: 0.990 | Steps: 17 | Time: 0.02s
2025-05-23 15:08:33,152 - QLearning_PT_Local - INFO - Ep 3/3 | Score: 17.00 | AvgScore(100): 14.67 | Epsilon: 0.985 | Steps: 17 | Time: 0.02s
2025-05-23 15:08:34,368 - QLearning_PT_Local.Plot - INFO - Plots saved in ./results/results_qlearning_pt_local\plots
2025-05-23 15:08:34,578 - QLearning_PT_Local - INFO - Training done. Final agent: ./results/results_qlearning_pt_local\models\QLearning_CartPole_final_agent_ep3.pth, Final inference model: ./results/results_qlearning_pt_local\models\QLearning_CartPole_final_inference_ep3.pth. Total time: 1.46s
2025-05-23 15:08:34,581 - QLearning_PT_Local - INFO - Script finished successfully.
