import numpy as np
import caffe
from caffe import layers as L
from caffe import params as P
import pickle
import os
import sys
import json
import uuid
from minio import Minio
import requests
import argparse

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

# Constants for headers
MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

def create_gin_layer(net, bottom, num_output, name_prefix, eps=0.0):
    """Create a GIN layer using Caffe primitives"""
    # Sum of neighbor features
    neighbor_sum = L.Pooling(bottom, pool=P.Pooling.AVE, kernel_size=1, stride=1, 
                           name=f"{name_prefix}_neighbor_pool")
    
    # (1 + eps) * x_i term
    scale = L.Scale(bottom, scale_param={'bias_term': True, 'filler': {'type': 'constant', 'value': 1 + eps}},
                   name=f"{name_prefix}_scale")
    
    # Combine node features with neighbor features
    combined = L.Eltwise(scale, neighbor_sum, operation=P.Eltwise.SUM,
                        name=f"{name_prefix}_combine")
    
    # MLP transformation
    mlp = L.InnerProduct(combined, num_output=num_output,
                        weight_filler=dict(type='xavier'),
                        bias_filler=dict(type='constant'),
                        name=f"{name_prefix}_mlp")
    
    relu = L.ReLU(mlp, in_place=True, name=f"{name_prefix}_relu")
    bn = L.BatchNorm(relu, name=f"{name_prefix}_bn")
    
    return bn

def create_gin_network(input_dim, hidden_dim, output_dim, num_layers, phase='TRAIN'):
    """Create the full GIN network architecture"""
    n = caffe.NetSpec()
    
    # Input layers
    n.data = L.Input(shape={'dim': [1, input_dim]}, name='data')
    n.graph = L.Input(shape={'dim': [1, 1]}, name='graph')  # Adjacency matrix
    n.label = L.Input(shape={'dim': [1, 1]}, name='label')
    
    # Create GIN layers
    current = n.data
    hidden_reps = [current]
    
    for i in range(num_layers - 1):
        layer_input_dim = input_dim if i == 0 else hidden_dim
        current = create_gin_layer(current, hidden_dim, f'gin_layer_{i}')
        hidden_reps.append(current)
    
    # Final prediction layers
    predictions = []
    for i, h in enumerate(hidden_reps):
        pred = L.InnerProduct(h, num_output=output_dim,
                            weight_filler=dict(type='xavier'),
                            bias_filler=dict(type='constant'),
                            name=f'predict_layer_{i}')
        predictions.append(pred)
    
    # Sum all predictions
    n.score = L.Eltwise(*predictions, operation=P.Eltwise.SUM, name='final_score')
    
    if phase == 'TRAIN':
        n.loss = L.SoftmaxWithLoss(n.score, n.label, name='loss')
    else:
        n.prob = L.Softmax(n.score, name='prob')
    
    return n.to_proto()

def gin_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """Train the GIN model using Caffe"""
    # Load data
    training_data_path = "/workspace/" + dataset["training_data_path"]
    with open(training_data_path, 'rb') as f:
        training_data, validation_data, test_data = pickle.load(f, encoding='bytes')
    train_x, train_g, train_y = training_data
    
    # Create network architecture
    train_net = create_gin_network(
        input_dim=job_params["input_dim"],
        hidden_dim=job_params["hidden_dim"],
        output_dim=job_params["output_dim"],
        num_layers=job_params["num_layers"],
        phase='TRAIN'
    )
    
    # Save network architecture
    with open(f"{result_dir}/train.prototxt", 'w') as f:
        f.write(str(train_net))
    
    # Create solver
    solver_param = {
        'train_net': f"{result_dir}/train.prototxt",
        'base_lr': job_params["learning_rate"],
        'max_iter': job_params["num_epochs"] * len(train_x),
        'lr_policy': 'step',
        'gamma': 0.1,
        'stepsize': 1000,
        'display': 100,
        'snapshot': 1000,
        'snapshot_prefix': f"{result_dir}/{model_name}",
        'solver_mode': caffe.SOLVER_MODE_GPU
    }
    
    with open(f"{result_dir}/solver.prototxt", 'w') as f:
        for key, value in solver_param.items():
            f.write(f'{key}: {value}\n')
    
    # Initialize solver
    solver = caffe.SGDSolver(f"{result_dir}/solver.prototxt")
    
    # Training loop
    for _ in range(job_params["num_epochs"]):
        solver.step(len(train_x))
    
    # Save final model
    solver.net.save(f"{result_dir}/{model_name}.caffemodel")
    return None, 0

def model_upload(result_dir, model_name):
    """Upload model to MinIO"""
    minioClient = Minio(
        MINIO_URL,
        access_key='AKIAIOSFODNN7EXAMPLE',
        secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
        secure=False
    )
    
    try:
        obj_name = str(uuid.uuid1())
        upload_path = f"{obj_name}/{model_name}.caffemodel"
        source = f"s3://mlss-mf/{obj_name}"
        
        # Upload both .caffemodel and .prototxt files
        minioClient.fput_object('mlss-mf', upload_path, 
                              f"{result_dir}/{model_name}.caffemodel")
        minioClient.fput_object('mlss-mf', 
                              f"{obj_name}/{model_name}.prototxt",
                              f"{result_dir}/train.prototxt")
        
        return {"source": source}, 0
    except Exception as err:
        print(err)
        return None, -1

def header_gen(user_id):
    """Generate headers for API requests"""
    return {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: user_id
    }

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Caffe GIN Train.')
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                       help='GIN Job Params')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                       help='GIN DataSet')
    parser.add_argument('--model', dest='model', type=json.loads,
                       help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                       help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                       help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                       help='fit params')
    
    args = parser.parse_args()
    
    print("Starting Caffe GIN training...")
    result, ret_code = gin_train(args.dataset, args.job_params, 
                                args.model["model_name"], args.result_dir, 
                                args.fit_params)
    
    if ret_code != 0:
        print(f"Training failed: {result}")
        sys.exit(-1)
    
    print("Training complete, uploading model...")
    result, ret_code = model_upload(args.result_dir, args.model["model_name"])
    
    if ret_code != 0:
        print(f"Upload failed: {result}")
        sys.exit(-1)
    
    print("Model upload complete")
    sys.exit(0)