import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import torchvision.models as models
import numpy as np
import pickle
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.preprocessing import LabelEncoder
from sklearn.preprocessing import OrdinalEncoder
from sklearn.model_selection import train_test_split

class ResNetForTabular(nn.Module):
    def __init__(self, input_channels, num_classes):
        super(ResNetForTabular, self).__init__()
        # 使用预训练的ResNet18，但替换第一层和最后一层
        self.resnet = models.resnet18(pretrained=True)
        self.resnet.conv1 = nn.Conv2d(input_channels, 64, kernel_size=7, stride=2, padding=3, bias=False)
        num_ftrs = self.resnet.fc.in_features
        self.resnet.fc = nn.Linear(num_ftrs, num_classes)

    def forward(self, x):
        return self.resnet(x)

# 加载数据集
def load_data(pkl_file):
    with open(pkl_file, 'rb') as f:
        data = pickle.load(f, encoding='bytes')
    train_x, train_y = data['train']
    test_x, test_y = data['test']
    return train_x, train_y, test_x, test_y

#读取 .pkl 文件之后加入预处理逻辑，需要先从 .pkl 文件中反序列化数据，然后执行相应的预处理步骤。
def load_data_preprocess(pkl_file):
    # 从pkl文件中加载数据
    with open(pkl_file, 'rb') as file:
        data = pickle.load(file)
    
    # 清理列名，去除可能的空格
    data.columns = data.columns.str.strip()

    print(type(data))  # 检查数据类型
    print(data.head())  # 查看前几行数据
    print(data.info())  # 查看数据信息


    # 假设数据是一个DataFrame格式，或者是特征和标签分别存储的形式
    if isinstance(data, pd.DataFrame):
        print(data.columns)
        X = data.drop(['Label'], axis=1)  # 假设 'Label' 是标签列
        y = data['Label']
    elif isinstance(data, dict):  # 如果是字典形式
        X = data['features']  # 假设特征存储在 'features' 键下
        y = data['labels']    # 假设标签存储在 'labels' 键下
    
    return X, y

# def preprocess_data(X, y):
#     # 1. 处理缺失值（删除缺失值）
#     if X.isnull().any().any() or y.isnull().any():
#         # 删除包含缺失值的行
#         X, y = X.align(y, join='inner', axis=0)
#         X = X.dropna()
#         y = y[X.index]  # 保证 y 和 X 同步
    

#     # 2. 标签编码
#     le = LabelEncoder()
#     y = le.fit_transform(y)

#     # 3. 分类特征的编码（如果存在分类特征）
#     categorical_cols = X.select_dtypes(include=['object']).columns
#     if not categorical_cols.empty:
#         encoder = OrdinalEncoder()
#         X[categorical_cols] = encoder.fit_transform(X[categorical_cols])
    
#     # 4. 检查并处理无限值
#     X.replace([np.inf, -np.inf], np.nan, inplace=True)  # 替换为 NaN
#     X.fillna(X.mean(), inplace=True)  # 用均值填充 NaN
    
#     # 5. 数据标准化
#     scaler = StandardScaler()
#     X = scaler.fit_transform(X)

#     # 6. 划分训练集和测试集
#     X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
#     return X_train, X_test, y_train, y_test

def preprocess_data(X, y):
    if X.isnull().any().any():
        X = X.dropna()
        y = y[X.index]
    
    le = LabelEncoder()
    y = le.fit_transform(y)

    categorical_cols = X.select_dtypes(include=['object']).columns
    if len(categorical_cols) > 0:
        encoder = OrdinalEncoder()
        X[categorical_cols] = encoder.fit_transform(X[categorical_cols])
    
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.mean(), inplace=True)
    
    scaler = StandardScaler()
    X = scaler.fit_transform(X)
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    return X_train, X_test, y_train, y_test

# def reshape_to_image(X, y, input_channels):
#     # 将2D数据重塑为3D（类似图像）
#     # X: 输入特征，假设形状为 (样本数, 特征数)
#     # y: 标签
#     # input_channels: 通道数，比如图像的 RGB 会是 3，灰度图为 1

#     # 检查输入维度
#     if X.ndim != 2:
#         raise ValueError(f"Expected 2D input for X, but got {X.ndim}D input")

#     # 获取样本数量和特征数量
#     num_samples, num_features = X.shape
    
#     # 计算要重塑的图像边长（确保是最接近的正方形大小）
#     side_len = int(np.ceil(np.sqrt(num_features / input_channels)))
    
#     # 计算填充量以使特征可以填充成正方形
#     padding = side_len ** 2 * input_channels - num_features

#     # 填充特征以适应新的形状
#     X_padded = np.pad(X, ((0, 0), (0, padding)), mode='constant')

#     # 检查填充后数组的大小是否匹配
#     total_elements = X_padded.shape[1]
#     expected_elements = side_len * side_len * input_channels

#     if total_elements != expected_elements:
#         raise ValueError(f"Mismatch in reshaping: got {total_elements}, expected {expected_elements}")

#     # 重新调整形状为 (batch_size, input_channels, side_len, side_len)
#     X_reshaped = X_padded.reshape(num_samples, input_channels, side_len, side_len)

#     # 转换为 PyTorch 张量
#     X_tensor = torch.FloatTensor(X_reshaped)
#     y_tensor = torch.LongTensor(y)
    
#     # X_tensor = torch.tensor(X_reshaped, dtype=torch.float32)
#     # y_tensor = torch.tensor(y, dtype=torch.long)

#     return X_tensor, y_tensor

def reshape_to_image(X, input_channels):
    num_samples, num_features = X.shape
    side_len = int(np.ceil(np.sqrt(num_features / input_channels)))
    padding = side_len ** 2 * input_channels - num_features
    X_padded = np.pad(X, ((0, 0), (0, padding)), mode='constant')
    X_reshaped = X_padded.reshape(num_samples, input_channels, side_len, side_len)
    return torch.FloatTensor(X_reshaped)

# 数据准备
# def prepare_dataset(X_train, y_train, X_test, y_test, input_channels):
#     X_train = reshape_to_image(X_train, y_train,input_channels)
#     X_test = reshape_to_image(X_test, y_test,input_channels)
#     y_train = torch.tensor(y_train, dtype=torch.long)
#     y_test = torch.tensor(y_test, dtype=torch.long)

#     # 确保 X_train, y_train, X_test, y_test 都是 torch.Tensor
#     print(type(X_train), type(y_train))  # 确认是否是 tensor 类型
#     print(type(X_test), type(y_test))
    
#     train_dataset = TensorDataset(X_train, y_train)
#     test_dataset = TensorDataset(X_test, y_test)
    
#     return train_dataset, test_dataset

def prepare_dataset(X_train, y_train, X_test, y_test, input_channels):
    X_train = reshape_to_image(X_train, input_channels)
    X_test = reshape_to_image(X_test, input_channels)
    y_train = torch.LongTensor(y_train)
    y_test = torch.LongTensor(y_test)
    train_dataset = TensorDataset(X_train, y_train)
    test_dataset = TensorDataset(X_test, y_test)
    return train_dataset, test_dataset

def train_model(train_loader, model, criterion, optimizer, num_epochs, device):
    model.to(device)

    for epoch in range(num_epochs):
        model.train()
        running_loss = 0.0

        for inputs, labels in train_loader:
            inputs, labels = inputs.to(device), labels.to(device)

            optimizer.zero_grad()
            outputs = model(inputs)
            
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()

            running_loss += loss.item()

        print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {running_loss/len(train_loader):.4f}')

# 测试函数
def test_model(test_loader, model, device):
    model.to(device)  # 将模型移到指定设备
    model.eval()  # 设置模型为评估模式
    correct, total = 0, 0

    with torch.no_grad():
        for inputs, labels in test_loader:
            inputs, labels = inputs.to(device), labels.to(device)  # 将数据移到指定设备
            outputs = model(inputs)
            _, predicted = torch.max(outputs, 1)  # 使用 softmax 输出中最大的值作为预测类别
            total += labels.size(0)
            correct += (predicted == labels).sum().item()

    print(f'Accuracy: {100 * correct / total:.2f}%')

# def resnet18_train(input_file, input_channels,num_classes,epochs,learning_rate,batch_size,result_dir,model_name,device):

#     # 加载数据 (pkl文件)
#     X, y = load_data_preprocess(input_file)
    
#     # 数据预处理
#     X_train, X_test, y_train, y_test = preprocess_data(X, y)
    
#     # 准备数据
#     train_dataset, test_dataset = prepare_dataset(X_train, y_train, X_test, y_test,input_channels)
#     train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
#     test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
#     # 创建模型实例
#     model = ResNetForTabular(input_channels, num_classes)

#     # 定义损失函数和优化器
#     criterion = nn.CrossEntropyLoss()
#     optimizer = optim.Adam(model.parameters(),lr=learning_rate)

#     # 训练和测试模型
#     train_model(train_loader, model, criterion, optimizer, epochs, device)
#     test_model(test_loader, model,device)
    
#     # 保存模型
#     model_file = open(result_dir + "/" + model_name + ".pth", "wb")
#     torch.save(model.state_dict(), model_file)
#     model_file.close()
#     print(f'ResNet18训练完成，模型保存到 {result_dir}/{model_name}.pth')
#     return None, 0
    
def resnet18_train(input_file, input_channels, num_classes, epochs, learning_rate, batch_size, result_dir, model_name, device):
    X, y = load_data_preprocess(input_file)
    X_train, X_test, y_train, y_test = preprocess_data(X, y)
    train_dataset, test_dataset = prepare_dataset(X_train, y_train, X_test, y_test, input_channels)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    model = ResNetForTabular(input_channels, num_classes)
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    
    train_model(train_loader, model, criterion, optimizer, epochs, device)
    test_model(test_loader, model, device)
    
    # 模型保存
    os.makedirs(result_dir, exist_ok=True)
    torch.save(model.state_dict(), os.path.join(result_dir, f"{model_name}.pth"))
    print(f'Model saved to {os.path.join(result_dir, model_name)}')

if __name__ == "__main__":
    # 设置参数
    input_file = 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl'
    input_channels = 84  # 如果你的数据是单通道的
    num_classes = 6
    epochs = 10
    learning_rate = 0.001
    batch_size = 32
    result_dir = 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_'
    model_name = 'resnet18_pt'
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    resnet18_train(input_file, input_channels,num_classes,epochs,learning_rate,batch_size,result_dir,model_name,device)