import pickle
import requests
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import tensorflow as tf
import numpy as np
from PIL import Image
import xml.etree.ElementTree as ET
import pandas as pd
from tqdm.auto import tqdm

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class DoubleConv(tf.keras.layers.Layer):
    """U-Net中的双重卷积块"""
    def __init__(self, filters):
        super().__init__()
        self.double_conv = tf.keras.Sequential([
            tf.keras.layers.Conv2D(filters, 3, padding='same'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.ReLU(),
            tf.keras.layers.Conv2D(filters, 3, padding='same'),
            tf.keras.layers.BatchNormalization(),
            tf.keras.layers.ReLU()
        ])

    def call(self, x):
        return self.double_conv(x)

class UNet(tf.keras.Model):
    def __init__(self, num_classes):
        super(UNet, self).__init__()
        
        # 编码器部分
        self.enc1 = DoubleConv(64)
        self.enc2 = DoubleConv(128)
        self.enc3 = DoubleConv(256)
        self.enc4 = DoubleConv(512)
        self.enc5 = DoubleConv(1024)
        
        # 池化层
        self.pool = tf.keras.layers.MaxPooling2D(pool_size=(2, 2))
        
        # 解码器部分
        self.up1 = tf.keras.layers.Conv2DTranspose(512, 2, strides=2, padding='same')
        self.dec1 = DoubleConv(512)
        
        self.up2 = tf.keras.layers.Conv2DTranspose(256, 2, strides=2, padding='same')
        self.dec2 = DoubleConv(256)
        
        self.up3 = tf.keras.layers.Conv2DTranspose(128, 2, strides=2, padding='same')
        self.dec3 = DoubleConv(128)
        
        self.up4 = tf.keras.layers.Conv2DTranspose(64, 2, strides=2, padding='same')
        self.dec4 = DoubleConv(64)
        
        # 最终输出层
        self.final = tf.keras.layers.Conv2D(num_classes, 1)

    def call(self, x):
        # 编码路径
        enc1 = self.enc1(x)
        x = self.pool(enc1)
        
        enc2 = self.enc2(x)
        x = self.pool(enc2)
        
        enc3 = self.enc3(x)
        x = self.pool(enc3)
        
        enc4 = self.enc4(x)
        x = self.pool(enc4)
        
        # 瓶颈层
        x = self.enc5(x)
        
        # 解码路径
        x = self.up1(x)
        x = tf.concat([enc4, x], axis=-1)
        x = self.dec1(x)
        
        x = self.up2(x)
        x = tf.concat([enc3, x], axis=-1)
        x = self.dec2(x)
        
        x = self.up3(x)
        x = tf.concat([enc2, x], axis=-1)
        x = self.dec3(x)
        
        x = self.up4(x)
        x = tf.concat([enc1, x], axis=-1)
        x = self.dec4(x)
        
        return self.final(x)

class UniversalImageDataset(tf.keras.utils.Sequence):
    def __init__(self, data_dir, batch_size, image_size, dataset_type='folder', annotations_file=None, is_training=True):
        self.data_dir = data_dir
        self.batch_size = batch_size
        self.image_size = image_size
        self.dataset_type = dataset_type
        self.annotations_file = annotations_file
        self.is_training = is_training

        self.classes = ['background'] + [f'class_{i}' for i in range(1, 21)]
        self.class_to_idx = {cls: idx for idx, cls in enumerate(self.classes)}

        if dataset_type == 'pickle':
            self.image_data, self.mask_data = self.load_pickle_data()
        elif dataset_type == 'coco':
            self.image_paths, self.mask_data = self.load_coco_data()
        elif dataset_type == 'yolo':
            self.image_paths, self.mask_data = self.load_yolo_data()
        elif dataset_type in ['folder', 'voc']:
            self.image_paths, self.mask_paths = self.load_segmentation_data()
        else:
            raise ValueError("Unsupported dataset type for segmentation.")
            
        self.indexes = np.arange(len(self.image_paths))
        if self.is_training:
            np.random.shuffle(self.indexes)

    def load_segmentation_data(self):
        image_paths = []
        mask_paths = []
        
        images_dir = os.path.join(self.data_dir, 'JPEGImages')
        masks_dir = os.path.join(self.data_dir, 'SegmentationClass')
        
        for img_name in os.listdir(images_dir):
            if img_name.endswith(('.jpg', '.jpeg', '.png')):
                img_path = os.path.join(images_dir, img_name)
                mask_name = img_name.replace('.jpg', '.png').replace('.jpeg', '.png')
                mask_path = os.path.join(masks_dir, mask_name)
                
                if os.path.exists(mask_path):
                    image_paths.append(img_path)
                    mask_paths.append(mask_path)

        return image_paths, mask_paths

    def __len__(self):
        return int(np.ceil(len(self.indexes) / self.batch_size))

    def __getitem__(self, idx):
        start_idx = idx * self.batch_size
        end_idx = min((idx + 1) * self.batch_size, len(self.indexes))
        batch_indexes = self.indexes[start_idx:end_idx]

        batch_images = []
        batch_masks = []

        for i in batch_indexes:
            # 加载和预处理图像
            img = tf.keras.preprocessing.image.load_img(self.image_paths[i], target_size=(self.image_size, self.image_size))
            img = tf.keras.preprocessing.image.img_to_array(img)
            img = img / 255.0  # 归一化
            
            # 加载和预处理掩码
            mask = tf.keras.preprocessing.image.load_img(self.mask_paths[i], target_size=(self.image_size, self.image_size), color_mode='grayscale')
            mask = tf.keras.preprocessing.image.img_to_array(mask)
            mask = mask.astype('int32')

            batch_images.append(img)
            batch_masks.append(mask)

        return np.array(batch_images), np.array(batch_masks)

def prepare_data(data_dir, dataset_type, batch_size, image_size, annotations_file=None):
    train_dataset = UniversalImageDataset(
        data_dir,
        batch_size=batch_size,
        image_size=image_size,
        dataset_type=dataset_type,
        annotations_file=annotations_file,
        is_training=True
    )
    
    test_dataset = UniversalImageDataset(
        data_dir,
        batch_size=batch_size,
        image_size=image_size,
        dataset_type=dataset_type,
        annotations_file=annotations_file,
        is_training=False
    )
    
    return train_dataset, test_dataset

def compute_iou(y_true, y_pred):
    intersection = tf.reduce_sum(y_true * y_pred)
    union = tf.reduce_sum(y_true) + tf.reduce_sum(y_pred) - intersection
    return intersection / (union + tf.keras.backend.epsilon())

@tf.function
def train_step(model, images, masks, optimizer, loss_fn):
    with tf.GradientTape() as tape:
        predictions = model(images, training=True)
        loss = loss_fn(masks, predictions)
    
    gradients = tape.gradient(loss, model.trainable_variables)
    optimizer.apply_gradients(zip(gradients, model.trainable_variables))
    
    return loss, predictions

def train_model(train_dataset, model, num_epochs, learning_rate):
    optimizer = tf.keras.optimizers.Adam(learning_rate=learning_rate)
    loss_fn = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True)
    
    for epoch in range(num_epochs):
        print(f'\nEpoch {epoch+1}/{num_epochs}')
        progbar = tf.keras.utils.Progbar(len(train_dataset))
        
        for i, (images, masks) in enumerate(train_dataset):
            loss, predictions = train_step(model, images, masks, optimizer, loss_fn)
            progbar.update(i+1, [('loss', loss)])

def test_model(test_dataset, model):
    total_iou = 0
    num_batches = 0
    
    for images, masks in test_dataset:
        predictions = model(images, training=False)
        predictions = tf.argmax(predictions, axis=-1)
        
        for pred, mask in zip(predictions, masks):
            iou = compute_iou(mask, pred)
            total_iou += iou
            num_batches += 1
    
    mean_iou = total_iou / num_batches
    print(f'Mean IoU: {mean_iou:.4f}')

def unet_train(dataset, job_params, model_name, result_dir, fit_params=None):
    input_size = job_params["input_size"]
    num_classes = job_params["num_classes"]
    dataset_type = job_params["dataset_type"]
    num_epochs = job_params["num_epochs"]
    learning_rate = job_params["learning_rate"]
    batch_size = job_params["batch_size"]
    
    training_data_path = "/workspace/" + dataset["training_data_path"]
    train_dataset, test_dataset = prepare_data(
        training_data_path, 
        dataset_type,
        batch_size, 
        input_size,
        annotations_file=None
    )

    # 初始化模型
    model = UNet(num_classes)
    
    try:
        train_model(train_dataset, model, num_epochs, learning_rate)
        test_model(test_dataset, model)
        
        # 保存模型
        model_path = os.path.join(result_dir, f"{model_name}.h5")
        model.save(model_path)
        print(f'训练完成，模型保存到 {model_path}')
    except Exception as e:
        print(f"训练过程中发生错误: {e}")

    return None, 0

def model_upload(result_dir, model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".h5"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".h5")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1

def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "TENSORFLOW",
          "file_name": model_name+".h5",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "UNet",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='tensorflow UNet Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='UNet Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='UNet DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start UNet training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("UNet job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("UNet dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("UNet result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("UNet factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("UNet fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("UNet sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    
    device = '/GPU:0' if tf.config.list_physical_devices('GPU') else '/CPU:0'
    print("Step 1 UNet training:\n")
    result,ret_code = unet_train(dataset,job_params, model["model_name"],result_dir,fit_params)
    if ret_code != 0:
        print("UNet train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()