2025-05-23 16:44:22,722 - GAN_PT_Local - INFO - Logging to file: ./results_gan_pt_local\GAN_PT_Local_training_20250523_164422.log
2025-05-23 16:44:22,723 - GAN_PT_Local - INFO - GAN PyTorch Local Mode started. Args: {'data_path': 'E:/data/Flowers Recognition/flowers', 'dataset_type': 'folder', 'annotations_file': None, 'image_size': 224, 'image_channels': 3, 'model_name': 'GAN_PT_Local', 'latent_dim': 100, 'num_epochs': 2, 'batch_size': 64, 'g_lr': 0.0002, 'd_lr': 0.0002, 'beta1': 0.5, 'sample_interval': 400, 'fixed_noise_samples': 16, 'save_epoch_interval': 5, 'result_dir': './results_gan_pt_local', 'num_workers': 0, 'pin_memory': False, 'random_seed': None, 'force_cpu': False, 'log_level': 'INFO'}
2025-05-23 16:44:22,724 - GAN_PT_Local - INFO - Using device: cpu
2025-05-23 16:44:22,724 - GAN_PT_Local - INFO - Preparing dataset from: E:/data/Flowers Recognition/flowers, type: folder
2025-05-23 16:44:22,725 - GAN_PT_Local - INFO - Loading images for dataset type 'folder' from source: 'E:/data/Flowers Recognition/flowers'
2025-05-23 16:44:22,760 - GAN_PT_Local - INFO - Found 4317 images.
2025-05-23 16:44:22,761 - GAN_PT_Local - WARNING - Target image_size 224 is not perfectly achievable by power-of-2 upsampling from 4x4. Final layer might produce slighly different or require adjustment.
2025-05-23 16:44:22,784 - GAN_PT_Local - INFO - Generator configured for 224x224 output with 6 upsampling stages from 4x4.
2025-05-23 16:44:22,790 - GAN_PT_Local - INFO - Discriminator configured for 224x224 input, downsampled to 14x14 with 4 stages. Final conv channels: 128
2025-05-23 16:44:22,793 - GAN_PT_Local - INFO - --- Training GAN ---
