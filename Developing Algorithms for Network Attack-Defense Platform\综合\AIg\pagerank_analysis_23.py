"""PageRank"""

import sys
import pandas as pd
import networkx as nx

def pagerank_analysis(input_file, source_column, target_column):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - source_column: 源节点列名
    - target_column: 目标节点列名
    """
    data = pd.read_csv(input_file)
    
    G = nx.from_pandas_edgelist(data, source=source_column, target=target_column)
    
    pagerank = nx.pagerank(G)
    
    output_file = 'pagerank.csv'
    pd.DataFrame(list(pagerank.items()), columns=['Node', 'PageRank']).to_csv(output_file, index=False)
    print(f"PageRank analysis completed. Output saved to {output_file}")

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python pagerank_analysis.py <input_file> <source_column> <target_column>")
        sys.exit(1)
    input_file, source_column, target_column = sys.argv[1], sys.argv[2], sys.argv[3]
    pagerank_analysis(input_file, source_column, target_column)
