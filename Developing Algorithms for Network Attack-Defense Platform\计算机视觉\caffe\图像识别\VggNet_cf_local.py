import os
import sys
import argparse
import json
import uuid
import pickle
import numpy as np
import caffe
from caffe import layers as L
from caffe import params as P
# from caffe.proto import caffe_pb2
from caffe import proto
import lmdb
from PIL import Image
import xml.etree.ElementTree as ET
from minio import Minio
import requests
import pandas as pd

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

def create_vgg_net(lmdb_path, batch_size, input_size, num_classes):
    """Create VGG network architecture"""
    n = caffe.NetSpec()
    
    # Data layer
    n.data, n.label = L.Data(batch_size=batch_size,
                            backend=P.Data.LMDB,
                            source=lmdb_path,
                            transform_param=dict(scale=1./255),
                            ntop=2)
    
    # VGG16 architecture
    n.conv1_1 = L.Convolution(n.data, num_output=64, kernel_size=3, pad=1)
    n.relu1_1 = L.ReLU(n.conv1_1, in_place=True)
    n.conv1_2 = L.Convolution(n.relu1_1, num_output=64, kernel_size=3, pad=1)
    n.relu1_2 = L.ReLU(n.conv1_2, in_place=True)
    n.pool1 = L.Pooling(n.relu1_2, kernel_size=2, stride=2, pool=P.Pooling.MAX)

    n.conv2_1 = L.Convolution(n.pool1, num_output=128, kernel_size=3, pad=1)
    n.relu2_1 = L.ReLU(n.conv2_1, in_place=True)
    n.conv2_2 = L.Convolution(n.relu2_1, num_output=128, kernel_size=3, pad=1)
    n.relu2_2 = L.ReLU(n.conv2_2, in_place=True)
    n.pool2 = L.Pooling(n.relu2_2, kernel_size=2, stride=2, pool=P.Pooling.MAX)

    n.conv3_1 = L.Convolution(n.pool2, num_output=256, kernel_size=3, pad=1)
    n.relu3_1 = L.ReLU(n.conv3_1, in_place=True)
    n.conv3_2 = L.Convolution(n.relu3_1, num_output=256, kernel_size=3, pad=1)
    n.relu3_2 = L.ReLU(n.conv3_2, in_place=True)
    n.conv3_3 = L.Convolution(n.relu3_2, num_output=256, kernel_size=3, pad=1)
    n.relu3_3 = L.ReLU(n.conv3_3, in_place=True)
    n.pool3 = L.Pooling(n.relu3_3, kernel_size=2, stride=2, pool=P.Pooling.MAX)

    n.conv4_1 = L.Convolution(n.pool3, num_output=512, kernel_size=3, pad=1)
    n.relu4_1 = L.ReLU(n.conv4_1, in_place=True)
    n.conv4_2 = L.Convolution(n.relu4_1, num_output=512, kernel_size=3, pad=1)
    n.relu4_2 = L.ReLU(n.conv4_2, in_place=True)
    n.conv4_3 = L.Convolution(n.relu4_2, num_output=512, kernel_size=3, pad=1)
    n.relu4_3 = L.ReLU(n.conv4_3, in_place=True)
    n.pool4 = L.Pooling(n.relu4_3, kernel_size=2, stride=2, pool=P.Pooling.MAX)

    n.conv5_1 = L.Convolution(n.pool4, num_output=512, kernel_size=3, pad=1)
    n.relu5_1 = L.ReLU(n.conv5_1, in_place=True)
    n.conv5_2 = L.Convolution(n.relu5_1, num_output=512, kernel_size=3, pad=1)
    n.relu5_2 = L.ReLU(n.conv5_2, in_place=True)
    n.conv5_3 = L.Convolution(n.relu5_2, num_output=512, kernel_size=3, pad=1)
    n.relu5_3 = L.ReLU(n.conv5_3, in_place=True)
    n.pool5 = L.Pooling(n.relu5_3, kernel_size=2, stride=2, pool=P.Pooling.MAX)

    n.fc6 = L.InnerProduct(n.pool5, num_output=4096)
    n.relu6 = L.ReLU(n.fc6, in_place=True)
    n.drop6 = L.Dropout(n.relu6, in_place=True)

    n.fc7 = L.InnerProduct(n.drop6, num_output=4096)
    n.relu7 = L.ReLU(n.fc7, in_place=True)
    n.drop7 = L.Dropout(n.relu7, in_place=True)

    n.fc8 = L.InnerProduct(n.drop7, num_output=num_classes)
    n.loss = L.SoftmaxWithLoss(n.fc8, n.label)
    n.accuracy = L.Accuracy(n.fc8, n.label)

    return n.to_proto()

def create_solver(model_name, train_net_path, test_net_path, base_lr):
    """Create solver configuration"""
    s = proto.caffe_pb2.SolverParameter()
    
    s.train_net = train_net_path
    if test_net_path:
        s.test_net.append(test_net_path)
        s.test_interval = 500
        s.test_iter.append(100)
    
    s.max_iter = 10000
    s.type = "Adam"
    s.base_lr = base_lr
    s.lr_policy = "step"
    s.gamma = 0.1
    s.stepsize = 3000
    s.momentum = 0.9
    s.weight_decay = 0.0005
    s.display = 100
    s.snapshot = 1000
    s.snapshot_prefix = model_name
    s.solver_mode = proto.caffe_pb2.SolverParameter.GPU
    
    return s

def prepare_lmdb(data_dir, dataset_type, output_path, image_size):
    """Convert dataset to LMDB format"""
    env = lmdb.open(output_path, map_size=1099511627776)
    
    with env.begin(write=True) as txn:
        for idx, (image_path, label) in enumerate(get_dataset_items(data_dir, dataset_type)):
            # Read and preprocess image
            with open(image_path, 'rb') as f:
                img = Image.open(f)
                img = img.convert('RGB')
                img = img.resize((image_size, image_size))
                img = np.array(img, dtype=np.uint8)
                img = img.transpose((2, 0, 1))  # HWC to CHW
            
            # Create datum
            datum = proto.caffe_pb2.Datum()
            datum.channels = img.shape[0]
            datum.height = img.shape[1]
            datum.width = img.shape[2]
            datum.data = img.tobytes()
            datum.label = label
            
            # Store in LMDB
            str_id = '{:08}'.format(idx)
            txn.put(str_id.encode('ascii'), datum.SerializeToString())

def get_folder_items(data_dir):
    """
    Generator for folder/ImageNet style dataset where images are organized in class folders.
    Yields tuples of (image_path, label_index).
    
    Args:
        data_dir: Root directory containing class folders
    """
    classes = sorted([d for d in os.listdir(data_dir) if os.path.isdir(os.path.join(data_dir, d))])
    class_to_idx = {cls: idx for idx, cls in enumerate(classes)}
    
    for class_name in classes:
        class_dir = os.path.join(data_dir, class_name)
        if not os.path.isdir(class_dir):
            continue
            
        for img_name in os.listdir(class_dir):
            if img_name.lower().endswith(('.png', '.jpg', '.jpeg')):
                img_path = os.path.join(class_dir, img_name)
                yield img_path, class_to_idx[class_name]

def get_coco_items(data_dir):
    """
    Generator for COCO format dataset.
    Yields tuples of (image_path, annotations).
    
    Args:
        data_dir: Directory containing COCO dataset with images and annotations
    """
    annotation_file = os.path.join(data_dir, 'annotations', 'instances_train2017.json')
    with open(annotation_file) as f:
        coco_data = json.load(f)
    
    # Create lookup dictionaries
    image_dict = {img['id']: img for img in coco_data['images']}
    categories = {cat['id']: cat['name'] for cat in coco_data['categories']}
    
    # Group annotations by image
    image_annotations = {}
    for ann in coco_data['annotations']:
        img_id = ann['image_id']
        if img_id not in image_annotations:
            image_annotations[img_id] = []
        image_annotations[img_id].append({
            'category_id': ann['category_id'],
            'category_name': categories[ann['category_id']],
            'bbox': ann['bbox'],  # [x, y, width, height]
            'area': ann['area'],
            'iscrowd': ann['iscrowd']
        })
    
    # Yield image paths and their annotations
    for img_id, annotations in image_annotations.items():
        img_info = image_dict[img_id]
        img_path = os.path.join(data_dir, 'images', img_info['file_name'])
        yield img_path, annotations

def get_voc_items(data_dir):
    """
    Generator for Pascal VOC format dataset.
    Yields tuples of (image_path, annotations).
    
    Args:
        data_dir: Directory containing VOC dataset
    """
    annotations_dir = os.path.join(data_dir, 'Annotations')
    images_dir = os.path.join(data_dir, 'JPEGImages')
    
    for xml_file in os.listdir(annotations_dir):
        if not xml_file.endswith('.xml'):
            continue
            
        xml_path = os.path.join(annotations_dir, xml_file)
        tree = ET.parse(xml_path)
        root = tree.getroot()
        
        # Get image path
        img_name = root.find('filename').text
        img_path = os.path.join(images_dir, img_name)
        
        # Parse annotations
        boxes = []
        for obj in root.findall('object'):
            name = obj.find('name').text
            bbox = obj.find('bndbox')
            xmin = float(bbox.find('xmin').text)
            ymin = float(bbox.find('ymin').text)
            xmax = float(bbox.find('xmax').text)
            ymax = float(bbox.find('ymax').text)
            
            boxes.append({
                'class': name,
                'bbox': [xmin, ymin, xmax, ymax]
            })
            
        yield img_path, boxes

def get_yolo_items(data_dir):
    """
    Generator for YOLO format dataset.
    Yields tuples of (image_path, annotations).
    
    Args:
        data_dir: Directory containing YOLO dataset
    """
    # First load class names if available
    classes_file = os.path.join(data_dir, 'classes.txt')
    classes = {}
    if os.path.exists(classes_file):
        with open(classes_file, 'r') as f:
            classes = {i: line.strip() for i, line in enumerate(f)}
    
    for img_file in os.listdir(data_dir):
        if not img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
            continue
            
        img_path = os.path.join(data_dir, img_file)
        label_file = os.path.join(data_dir, os.path.splitext(img_file)[0] + '.txt')
        
        boxes = []
        if os.path.exists(label_file):
            with open(label_file, 'r') as f:
                for line in f:
                    values = line.strip().split()
                    class_id = int(values[0])
                    # YOLO format: class_id, x_center, y_center, width, height (normalized)
                    box = {
                        'class_id': class_id,
                        'class_name': classes.get(class_id, str(class_id)),
                        'bbox': [float(x) for x in values[1:]]  # normalized coordinates
                    }
                    boxes.append(box)
                    
        yield img_path, boxes

def get_dataset_items(data_dir, dataset_type):
    """Generator for dataset items based on type"""
    if dataset_type in ['folder', 'imagenet']:
        return get_folder_items(data_dir)
    elif dataset_type == 'coco':
        return get_coco_items(data_dir)
    elif dataset_type == 'voc':
        return get_voc_items(data_dir)
    elif dataset_type == 'yolo':
        return get_yolo_items(data_dir)
    else:
        raise ValueError(f"Unsupported dataset type: {dataset_type}")

def vggnet_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """Main training function"""
    input_size = job_params["input_size"]
    dataset_type = job_params["dataset_type"]
    output_size = job_params["output_size"]
    learning_rate = job_params["learning_rate"]
    batch_size = job_params["batch_size"]
    
    # Prepare data
    training_data_path = "/workspace/" + dataset["training_data_path"]
    train_lmdb = os.path.join(result_dir, 'train_lmdb')
    prepare_lmdb(training_data_path, dataset_type, train_lmdb, input_size)
    
    # Create network
    train_net_path = os.path.join(result_dir, 'train_net.prototxt')
    with open(train_net_path, 'w') as f:
        f.write(str(create_vgg_net(train_lmdb, batch_size, input_size, output_size)))
    
    # Create solver
    solver_path = os.path.join(result_dir, 'solver.prototxt')
    solver_param = create_solver(model_name, train_net_path, None, learning_rate)
    with open(solver_path, 'w') as f:
        f.write(str(solver_param))
    
    # Train model
    caffe.set_mode_gpu()
    solver = caffe.get_solver(solver_path)
    
    if os.path.exists("/workspace/pretrained_model/VggNet121-a639ec97.caffemodel"):
        solver.net.copy_from("/workspace/pretrained_model/VggNet121-a639ec97.caffemodel")
    
    solver.solve()
    
    # Save final model
    model_path = os.path.join(result_dir, f"{model_name}.caffemodel")
    solver.net.save(model_path)
    
    return None, 0

def model_upload(result_dir, model_name):
    """Upload model to MinIO"""
    minioClient = Minio(MINIO_URL,
                       access_key='AKIAIOSFODNN7EXAMPLE',
                       secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                       secure=False)
    try:
        obj_name = str(uuid.uuid1())
        upload_path = f"{obj_name}/{model_name}.caffemodel"
        source = f"s3://mlss-mf/{obj_name}"
        res = minioClient.fput_object('mlss-mf', upload_path, 
                                    f"{result_dir}/{model_name}.caffemodel")
        return {"source": source}, 0
    except Exception as err:
        print(err)
        return None, -1

def header_gen(user_id):
    """Generate headers for API requests"""
    return {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: user_id
    }

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Caffe VGGNet Train.')
    parser.add_argument('--job_params', type=json.loads, help='VGGNet Job Params')
    parser.add_argument('--dataset', type=json.loads, help='VGGNet DataSet')
    parser.add_argument('--model', type=json.loads, help='Model info')
    parser.add_argument('--factory_name', type=str, help='Factory name')
    parser.add_argument('--result_dir', type=str, help='Result directory')
    parser.add_argument('--fit_params', type=json.loads, help='Fit params')
    
    args = parser.parse_args()
    job_params = args.job_params
    print("VggNet job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("VggNet dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("VggNet result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("VggNet factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("VggNet fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("VggNet sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    
    print("Starting VGGNet training...")
    result, ret_code = vggnet_train(args.dataset, args.job_params, 
                                  args.model["model_name"], args.result_dir, 
                                  args.fit_params)
    
    if ret_code != 0:
        print(f"Training failed: {result}")
        sys.exit(-1)
        
    print("Training complete, uploading model...")
    result, ret_code = model_upload(args.result_dir, args.model["model_name"])
    
    if ret_code != 0:
        print(f"Upload failed: {result}")
        sys.exit(-1)
        
    print("Job completed successfully")