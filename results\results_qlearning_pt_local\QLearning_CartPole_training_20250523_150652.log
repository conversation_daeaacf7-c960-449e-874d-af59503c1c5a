2025-05-23 15:06:52,632 - QLearning_PT_Local - INFO - Logging to: ./results/results_qlearning_pt_local\QLearning_CartPole_training_20250523_150652.log
2025-05-23 15:06:52,632 - QLearning_PT_Local - INFO - Q-Learning PT Local Training (QLearning_CartPole) starting on device: cpu.
2025-05-23 15:06:52,632 - QLearning_PT_Local - INFO - Params:
{
  "env_name": "CartPole-v1",
  "result_dir": "./results/results_qlearning_pt_local",
  "model_name": "QLearning_CartPole",
  "hidden_size": 64,
  "learning_rate": 0.001,
  "gamma": 0.99,
  "epsilon_start": 1.0,
  "epsilon_end": 0.01,
  "epsilon_decay": 0.995,
  "buffer_size": 10000,
  "batch_size": 32,
  "num_episodes": 3,
  "max_steps_per_episode": 200,
  "random_seed": null,
  "device_str": "cpu",
  "save_freq": 100,
  "plot_freq": 50,
  "logger": "<Logger QLearning_PT_Local (INFO)>"
}
2025-05-23 15:06:52,654 - QLearning_PT_Local.Env - INFO - Using Gymnasium API for CartPole-v1
2025-05-23 15:06:52,654 - QLearning_PT_Local - INFO - Env: CartPole-v1, State Dim=4, Action Dim=2
2025-05-23 15:06:54,075 - QLearning_PT_Local - INFO - Ep 1/3 | Score: 64.00 | AvgScore(100): 64.00 | Epsilon: 0.995 | Steps: 64 | Time: 0.08s
2025-05-23 15:06:54,143 - QLearning_PT_Local - INFO - Ep 2/3 | Score: 28.00 | AvgScore(100): 46.00 | Epsilon: 0.990 | Steps: 28 | Time: 0.07s
2025-05-23 15:06:54,206 - QLearning_PT_Local - INFO - Ep 3/3 | Score: 30.00 | AvgScore(100): 40.67 | Epsilon: 0.985 | Steps: 30 | Time: 0.06s
2025-05-23 15:06:55,355 - QLearning_PT_Local.Plot - INFO - Plots saved in ./results/results_qlearning_pt_local\plots
2025-05-23 15:06:55,370 - QLearning_PT_Local - INFO - Training done. Final agent: ./results/results_qlearning_pt_local\models\QLearning_CartPole_final_agent_ep3.pth, Final inference model: ./results/results_qlearning_pt_local\models\QLearning_CartPole_final_inference_ep3.pth. Total time: 1.40s
2025-05-23 15:06:55,377 - QLearning_PT_Local - ERROR - Critical error: [enforce fail at inline_container.cc:642] . invalid file name: 
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\Q-Learning_pt_local_mode.py", line 445, in <module>
    q_learning_train_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\Q-Learning_pt_local_mode.py", line 355, in q_learning_train_local
    'hyperparameters': agent.training_info.get('hyperparameters', agent.save_checkpoint_local('').get('hyperparameters', {})), # Get from checkpoint structure
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\Q-Learning_pt_local_mode.py", line 201, in save_checkpoint_local
    torch.save(checkpoint, filepath)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\serialization.py", line 651, in save
    with _open_zipfile_writer(f) as opened_zipfile:
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\serialization.py", line 525, in _open_zipfile_writer
    return container(name_or_buffer)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\serialization.py", line 496, in __init__
    super().__init__(torch._C.PyTorchFileWriter(self.name))
RuntimeError: [enforce fail at inline_container.cc:642] . invalid file name: 
