2025-06-13 14:27:59,665 - YOLOv8_local - INFO - Logging to file: ./results/results_yolov8_pt_local\YOLOv8_local_training_20250613_142759.log
2025-06-13 14:27:59,665 - YOLOv8_local - INFO - YOLOv8 PyTorch Local Mode. Args: {'data_path': './test_data', 'dataset_type': 'voc', 'annotations_file': None, 'input_size': 640, 'model_name': 'YOLOv8_local', 'model_type': 'nano', 'pretrained_model_path': None, 'mode': 'train', 'num_epochs': 1, 'batch_size': 16, 'conf_thres': 0.25, 'iou_thres': 0.45, 'early_stopping_patience': 30, 'use_cv': False, 'cv_folds': 5, 'trained_model_path': None, 'test_data_path': None, 'annotations_file_test': None, 'result_dir': './results/results_yolov8_pt_local', 'random_seed': 42, 'force_cpu': False, 'log_level': 'INFO'}
2025-06-13 14:27:59,665 - YOLOv8_local - ERROR - An error occurred in yolov8_main_local: Data path does not exist: ./test_data
Traceback (most recent call last):
  File "卷积神经网络/yolov8_pt_local_mode.py", line 885, in yolov8_main_local
    validate_params_local(args)
  File "卷积神经网络/yolov8_pt_local_mode.py", line 855, in validate_params_local
    raise ValueError(f"Data path does not exist: {args.data_path}")
ValueError: Data path does not exist: ./test_data
2025-06-13 14:27:59,665 - YOLOv8_local - INFO - Failure report saved to ./results/results_yolov8_pt_local\YOLOv8_local_FAILURE_REPORT.json
