"""高斯混合聚类"""

import pickle
import requests
import joblib
import sys
import argparse
import json
from minio import Minio
import uuid
import os
from sklearn.mixture import GaussianMixture
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score, calinski_harabasz_score
from datetime import datetime
from sklearn.model_selection import KFold
import seaborn as sns

MINIO_URL = "minio-prophecis.prophecis:9000"

MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"


MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"


def gaussian_mixture_clustering(dataset, job_params, model_name, result_dir, fit_params=None):
    """
    使用高斯混合模型进行聚类分析
    
    参数:
    dataset (dict): 包含训练数据路径的字典
    job_params (dict): 高斯混合模型参数，包含n_components等
    model_name (str): 模型名称
    result_dir (str): 结果保存目录
    fit_params (dict, optional): 拟合参数
    
    返回:
    dict: 包含聚类结果的字典
    """
    try:
        # 初始化日志
        logger = logging.getLogger("GaussianMixture")
        logger.info("开始高斯混合聚类分析")
        
        # 参数验证
        if not dataset or 'training_data_path' not in dataset:
            raise ValueError("无效的数据集参数，必须包含'training_data_path'")
        
        if not result_dir:
            result_dir = "./results"
            logger.warning(f"未提供结果目录，默认使用 {result_dir}")
            
        # 创建结果目录
        os.makedirs(result_dir, exist_ok=True)
            
        # 灵活数据加载
        data_path = dataset.get('training_data_path')
        feature_cols = dataset.get('feature_cols', None)
        data_format = dataset.get('data_format', 'pkl')
        
        logger.info(f"从 {data_path} 加载 {data_format} 格式数据")
        
        # 兼容旧格式路径
        if not data_path.startswith("/workspace/"):
            # 添加/workspace/前缀
            data_path = "/workspace/" + data_path
            logger.info(f"转换为绝对路径: {data_path}")
        
        # 检查文件是否存在
        if not os.path.exists(data_path):
            logger.error(f"文件不存在: {data_path}")
            raise FileNotFoundError(f"文件不存在: {data_path}")
        
        # 尝试使用旧的数据加载方式保持兼容性
        try:
            logger.info("尝试使用旧格式加载数据...")
            with open(data_path, 'rb') as f:
                loaded_data = pickle.load(f, encoding='bytes')
                
            # 处理旧格式数据
            if isinstance(loaded_data, tuple) and len(loaded_data) == 3:
                training_data, validation_data, test_data = loaded_data
                X = training_data[0]
                logger.info(f"成功加载数据，形状: {X.shape}")
            else:
                # 尝试使用灵活加载
                logger.info("旧格式加载失败，尝试灵活加载...")
                X = flexible_data_load(data_path, feature_cols, data_format)
        except Exception as e:
            logger.warning(f"旧格式加载失败：{str(e)}，使用灵活加载...")
            X = flexible_data_load(data_path, feature_cols, data_format)
        
        # 参数设置
        n_components = job_params.get('n_components', 2)
        covariance_type = job_params.get('covariance_type', 'full')
        tol = job_params.get('tol', 0.001)
        max_iter = job_params.get('max_iter', 100)
        init_params = job_params.get('init_params', 'kmeans')
        
        logger.info("初始化高斯混合模型")
        logger.info(f"参数: n_components={n_components}, covariance_type={covariance_type}")
        
        # 数据预处理 - 标准化
        if job_params.get('standardize', True):
            logger.info("对数据进行标准化")
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
        else:
            X_scaled = X
        
        # 初始化并训练高斯混合模型
        start_time = datetime.now()
        gmm = GaussianMixture(
            n_components=n_components,
            covariance_type=covariance_type,
            tol=tol,
            max_iter=max_iter,
            init_params=init_params,
            random_state=job_params.get('random_state', 42)
        )
        
        # 拟合模型
        logger.info("开始拟合高斯混合模型")
        gmm.fit(X_scaled)
        training_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"高斯混合模型拟合完成，用时 {training_time:.2f} 秒")
        
        # 预测聚类标签
        labels = gmm.predict(X_scaled)
        
        # 计算聚类数量和BIC、AIC
        n_clusters = n_components
        bic = gmm.bic(X_scaled)
        aic = gmm.aic(X_scaled)
        
        logger.info(f"聚类数量: {n_clusters}")
        logger.info(f"BIC: {bic:.2f}")
        logger.info(f"AIC: {aic:.2f}")
        
        # 计算评估指标
        cluster_metrics = {}
        
        # 只有当聚类数量 >= 2 时才计算轮廓系数
        if n_clusters >= 2:
            try:
                silhouette = silhouette_score(X_scaled, labels)
                calinski = calinski_harabasz_score(X_scaled, labels)
                
                cluster_metrics['silhouette_score'] = silhouette
                cluster_metrics['calinski_harabasz_score'] = calinski
                
                logger.info(f"轮廓系数: {silhouette:.4f}")
                logger.info(f"Calinski-Harabasz指数: {calinski:.4f}")
            except Exception as e:
                logger.warning(f"无法计算聚类指标: {str(e)}")
        
        # 保存模型
        model_path = os.path.join(result_dir, f"{model_name}.joblib")
        joblib.dump(gmm, model_path)
        
        # 为了兼容性，也保存pickle格式
        pickle_path = os.path.join(result_dir, f"{model_name}.pickle")
        with open(pickle_path, "wb") as model_file:
            pickle.dump(gmm, model_file)
        
        logger.info(f"模型已保存至 {model_path} 和 {pickle_path}")
        
        # 保存聚类结果
        results_df = pd.DataFrame({
            'cluster_label': labels
        })
        results_path = os.path.join(result_dir, f"{model_name}_labels.csv")
        results_df.to_csv(results_path, index=True)
        logger.info(f"聚类标签已保存至 {results_path}")
        
        # 可视化聚类结果
        if X.shape[1] > 2:
            logger.info("使用PCA降维以便可视化")
            pca = PCA(n_components=2)
            X_pca = pca.fit_transform(X_scaled)
            explained_variance = pca.explained_variance_ratio_
            
            logger.info(f"PCA解释方差: {explained_variance[0]:.4f}, {explained_variance[1]:.4f}")
            visualization_data = X_pca
        else:
            visualization_data = X_scaled
        
        # 绘制聚类散点图
        plt.figure(figsize=(12, 10))
        unique_labels = set(labels)
        colors = plt.cm.rainbow(np.linspace(0, 1, len(unique_labels)))
        
        for k, col in zip(unique_labels, colors):
            class_member_mask = (labels == k)
            xy = visualization_data[class_member_mask]
            plt.plot(
                xy[:, 0], xy[:, 1], 'o', 
                markerfacecolor=tuple(col),
                markeredgecolor='k',
                markersize=6
            )
        
        plt.title(f'高斯混合聚类 (n_components={n_components})')
        plt.grid(True)
        
        if X.shape[1] > 2:
            plt.xlabel(f'第一主成分 ({explained_variance[0]:.2%})')
            plt.ylabel(f'第二主成分 ({explained_variance[1]:.2%})')
        else:
            plt.xlabel('特征1')
            plt.ylabel('特征2')
        
        # 保存聚类散点图
        cluster_plot_path = os.path.join(result_dir, f"{model_name}_clusters.png")
        plt.savefig(cluster_plot_path, dpi=300)
        plt.close()
        logger.info(f"聚类散点图已保存至 {cluster_plot_path}")
        
        # 绘制聚类分布条形图
        plt.figure(figsize=(12, 6))
        cluster_counts = pd.Series(labels).value_counts().sort_index()
        
        # 设置合适的颜色
        bar_colors = plt.cm.rainbow(np.linspace(0, 1, len(cluster_counts)))
        
        cluster_counts.plot(
            kind='bar',
            color=bar_colors
        )
        
        plt.title('聚类分布')
        plt.xlabel('聚类标签')
        plt.ylabel('样本数量')
        plt.grid(True, axis='y')
        
        # 保存聚类分布图
        dist_plot_path = os.path.join(result_dir, f"{model_name}_distribution.png")
        plt.savefig(dist_plot_path, dpi=300)
        plt.close()
        logger.info(f"聚类分布图已保存至 {dist_plot_path}")
        
        # 绘制BIC/AIC曲线（如果通过交叉验证设置了多个聚类数）
        if 'bic_aic_data' in job_params:
            bic_aic_data = job_params['bic_aic_data']
            plt.figure(figsize=(12, 6))
            
            if 'n_components_range' in bic_aic_data and 'bic' in bic_aic_data and 'aic' in bic_aic_data:
                plt.plot(bic_aic_data['n_components_range'], bic_aic_data['bic'], 'o-', label='BIC')
                plt.plot(bic_aic_data['n_components_range'], bic_aic_data['aic'], 'o-', label='AIC')
                plt.axvline(x=n_components, color='r', linestyle='--', label=f'选择的聚类数 ({n_components})')
                plt.xlabel('聚类数量')
                plt.ylabel('信息准则值')
                plt.title('BIC和AIC随聚类数量的变化')
                plt.legend()
                plt.grid(True)
                
                # 保存BIC/AIC曲线
                bic_aic_path = os.path.join(result_dir, f"{model_name}_bic_aic.png")
                plt.savefig(bic_aic_path, dpi=300)
                plt.close()
                logger.info(f"BIC/AIC曲线已保存至 {bic_aic_path}")
        
        # 准备结果字典
        results = {
            "model_name": model_name,
            "n_clusters": n_clusters,
            "bic": bic,
            "aic": aic,
            "training_time": training_time,
            "model_path": model_path,
            "pickle_path": pickle_path,
            "cluster_plot": cluster_plot_path,
            "distribution_plot": dist_plot_path,
            "labels_csv": results_path
        }
        
        # 添加聚类评估指标（如果有）
        if cluster_metrics:
            results.update(cluster_metrics)
        
        # 保存结果摘要
        results_json_path = os.path.join(result_dir, f"{model_name}_results.json")
        with open(results_json_path, 'w') as f:
            json.dump(results, f, indent=4)
        
        logger.info(f"高斯混合聚类分析完成，发现 {n_clusters} 个聚类")
        return results
        
    except Exception as e:
        logger.error(f"高斯混合聚类分析失败: {str(e)}")
        logger.exception("详细错误信息:")
        raise

def cross_validate_gaussian_mixture(X, param_grid, cv=5, random_state=42):
    """
    对高斯混合模型进行参数交叉验证
    
    参数:
    X: 特征数据
    param_grid: 参数网格，包含n_components和covariance_type的不同组合
    cv: 交叉验证折数
    random_state: 随机种子
    
    返回:
    best_params: 最佳参数
    cv_results: 交叉验证结果
    bic_aic_data: BIC和AIC数据，用于绘图
    """
    logger = logging.getLogger("GaussianMixture")
    logger.info(f"开始高斯混合模型参数交叉验证")
    
    # 准备存储结果的数据结构
    cv_results = []
    
    # 创建交叉验证分割器
    kf = KFold(n_splits=cv, shuffle=True, random_state=random_state)
    
    # 记录开始时间
    start_time = datetime.now()
    
    # 存储BIC和AIC值，用于绘图
    bic_values = []
    aic_values = []
    n_components_range = param_grid.get('n_components', [2, 3, 4, 5])
    
    # 对每个参数组合进行交叉验证
    for n_components in n_components_range:
        for covariance_type in param_grid.get('covariance_type', ['full']):
            # 记录当前参数组合
            param_dict = {'n_components': n_components, 'covariance_type': covariance_type}
            logger.info(f"评估参数: n_components={n_components}, covariance_type={covariance_type}")
            
            # 存储每个折叠的指标
            fold_metrics = []
            bic_scores = []
            aic_scores = []
            
            # 对数据进行K折交叉验证
            for fold, (train_idx, val_idx) in enumerate(kf.split(X)):
                X_train, X_val = X[train_idx], X[val_idx]
                
                # 在训练集上训练高斯混合模型
                gmm = GaussianMixture(
                    n_components=n_components,
                    covariance_type=covariance_type,
                    random_state=random_state
                )
                gmm.fit(X_train)
                
                # 计算BIC和AIC
                bic = gmm.bic(X_train)
                aic = gmm.aic(X_train)
                bic_scores.append(bic)
                aic_scores.append(aic)
                
                # 计算验证集的对数似然
                val_log_likelihood = gmm.score(X_val) * len(X_val)
                
                # 计算训练集上的聚类标签
                train_labels = gmm.predict(X_train)
                
                # 计算验证集上的聚类标签
                val_labels = gmm.predict(X_val)
                
                # 计算聚类有效性指标
                train_metrics = {}
                val_metrics = {}
                
                # 只有当聚类数量 >= 2 时才计算轮廓系数
                if n_components >= 2:
                    try:
                        train_silhouette = silhouette_score(X_train, train_labels)
                        train_calinski = calinski_harabasz_score(X_train, train_labels)
                        
                        train_metrics['silhouette_score'] = train_silhouette
                        train_metrics['calinski_harabasz_score'] = train_calinski
                    except Exception as e:
                        logger.warning(f"计算训练集评估指标异常: {str(e)}")
                    
                    try:
                        val_silhouette = silhouette_score(X_val, val_labels)
                        val_calinski = calinski_harabasz_score(X_val, val_labels)
                        
                        val_metrics['silhouette_score'] = val_silhouette
                        val_metrics['calinski_harabasz_score'] = val_calinski
                    except Exception as e:
                        logger.warning(f"计算验证集评估指标异常: {str(e)}")
                
                # 合并指标
                fold_result = {
                    'params': param_dict.copy(),
                    'fold': fold,
                    'bic': bic,
                    'aic': aic,
                    'val_log_likelihood': val_log_likelihood,
                    'train_metrics': train_metrics,
                    'val_metrics': val_metrics
                }
                
                fold_metrics.append(fold_result)
                logger.debug(f"折叠 {fold+1}/{cv} 完成, BIC: {bic:.2f}, AIC: {aic:.2f}")
            
            # 计算该参数组合的平均指标
            avg_bic = np.mean(bic_scores)
            avg_aic = np.mean(aic_scores)
            
            # 计算平均轮廓系数（如果存在）
            silhouette_list = [fold['train_metrics'].get('silhouette_score', 0) for fold in fold_metrics 
                              if 'silhouette_score' in fold['train_metrics']]
            
            # 平均验证集指标
            val_silhouette_list = [fold['val_metrics'].get('silhouette_score', 0) for fold in fold_metrics 
                                  if 'silhouette_score' in fold['val_metrics']]
            
            # 汇总结果
            param_result = {
                'params': param_dict,
                'avg_bic': avg_bic,
                'std_bic': np.std(bic_scores),
                'avg_aic': avg_aic,
                'std_aic': np.std(aic_scores),
                'fold_metrics': fold_metrics
            }
            
            # 添加轮廓系数指标（如果有）
            if silhouette_list:
                param_result['avg_silhouette'] = np.mean(silhouette_list)
                param_result['std_silhouette'] = np.std(silhouette_list)
            
            if val_silhouette_list:
                param_result['avg_val_silhouette'] = np.mean(val_silhouette_list)
            
            # 添加结果到列表
            cv_results.append(param_result)
            
            # 记录每个n_components的BIC和AIC
            if covariance_type == 'full':  # 只记录一种协方差类型
                bic_values.append(avg_bic)
                aic_values.append(avg_aic)
            
            # 记录当前参数组合结果
            logger.info(f"参数 n_components={n_components}, covariance_type={covariance_type}: BIC={avg_bic:.2f}, AIC={avg_aic:.2f}")
            if 'avg_silhouette' in param_result:
                logger.info(f"平均轮廓系数: {param_result['avg_silhouette']:.4f}")
    
    # 计算总耗时
    total_time = (datetime.now() - start_time).total_seconds()
    logger.info(f"交叉验证完成，总耗时: {total_time:.2f}秒")
    
    # 确定最佳参数，以BIC较小值为优先
    best_result = min(cv_results, key=lambda r: r['avg_bic'])
    
    # 如果BIC相差不大，考虑使用轮廓系数
    if any('avg_silhouette' in r for r in cv_results):
        # 找出BIC接近最小值的结果
        bic_threshold = best_result['avg_bic'] * 1.05  # 允许5%的差异
        potential_results = [r for r in cv_results if r['avg_bic'] <= bic_threshold]
        
        if len(potential_results) > 1:
            # 在这些结果中找出轮廓系数最大的
            silhouette_results = [r for r in potential_results if 'avg_silhouette' in r]
            if silhouette_results:
                best_result = max(silhouette_results, key=lambda r: r['avg_silhouette'])
    
    logger.info(f"最佳参数: n_components={best_result['params']['n_components']}, covariance_type={best_result['params']['covariance_type']}")
    logger.info(f"最佳BIC: {best_result['avg_bic']:.2f}, AIC: {best_result['avg_aic']:.2f}")
    
    # 准备BIC和AIC数据用于绘图
    bic_aic_data = {
        'n_components_range': n_components_range,
        'bic': bic_values,
        'aic': aic_values
    }
    
    # 返回最佳参数、所有结果和BIC/AIC数据
    return best_result['params'], cv_results, bic_aic_data

def plot_confusion_matrix(cm, class_names, result_dir, model_name):
    """
    绘制混淆矩阵
    
    参数:
    cm: 混淆矩阵
    class_names: 类别名称
    result_dir: 结果保存目录
    model_name: 模型名称
    """
    plt.figure(figsize=(10, 8))
    sns.heatmap(cm, annot=True, fmt="d", cmap="Blues", xticklabels=class_names, yticklabels=class_names)
    plt.title('混淆矩阵')
    plt.xlabel('预测标签')
    plt.ylabel('真实标签')
    
    # 保存混淆矩阵图
    cm_plot_path = os.path.join(result_dir, f"{model_name}_confusion_matrix.png")
    plt.savefig(cm_plot_path, dpi=300)
    plt.close()
    
    return cm_plot_path

def convert_to_serializable(obj):
    """
    将对象转换为可序列化的格式
    
    参数:
    obj: 需要转换的对象
    
    返回:
    转换后的对象
    """
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, dict):
        return {k: convert_to_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_serializable(item) for item in obj]
    else:
        return obj

def model_upload(result_dir, model_name):    
    """
    将模型上传到 MinIO 存储
    
    参数:
    result_dir: 结果保存目录
    model_name: 模型名称
    
    返回:
    result: 上传结果
    ret_code: 返回代码
    """
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".pickle"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".pickle")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    """
    注册模型到模型工厂
    
    参数:
    model_name: 模型名称
    source: 模型源路径
    group_id: 模型组ID
    headers: 请求头
    
    返回:
    result: 注册结果
    ret_code: 返回代码
    """
    params = {
          "model_name": model_name,
          "model_type": "MLPipeline",
          "file_name": model_name+".pickle",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name, headers):
    """
    推送模型到工厂
    
    参数:
    model_id: 模型ID
    model_version_id: 模型版本ID
    factory_name: 工厂名称
    headers: 请求头
    
    返回:
    result: 推送结果
    ret_code: 返回代码
    """
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "GaussianMixture",
        "model_usage": "Clustering"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    """
    生成请求头
    
    参数:
    user_id: 用户ID
    
    返回:
    headers: 请求头
    """
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

# 添加缺失的灵活数据加载函数
def flexible_data_load(file_path, feature_cols=None, data_format='pkl'):
    """
    灵活加载不同格式的数据集
    
    参数:
    file_path: 数据文件路径
    feature_cols: 特征列名列表，None表示使用所有列
    data_format: 数据格式，支持'pkl'、'csv'、'json'等
    
    返回:
    X: 特征数据
    """
    logger = logging.getLogger("GaussianMixture")
    logger.info(f"加载数据，文件路径: {file_path}，格式: {data_format}")
    
    # 加载不同格式的数据
    if data_format.lower() == 'pkl':
        with open(file_path, 'rb') as file:
            data = pickle.load(file, encoding='bytes')
    elif data_format.lower() == 'csv':
        data = pd.read_csv(file_path)
    elif data_format.lower() == 'json':
        data = pd.read_json(file_path)
    else:
        raise ValueError(f"不支持的数据格式: {data_format}")
    
    # 处理不同类型的数据结构
    if isinstance(data, pd.DataFrame):
        logger.info(f"数据加载为DataFrame，形状: {data.shape}")
        
        # 清理列名，去除可能的空格
        data.columns = data.columns.str.strip()
        
        # 根据特征列选择特征
        if feature_cols is not None:
            missing_cols = [col for col in feature_cols if col not in data.columns]
            if missing_cols:
                logger.warning(f"部分特征列未在数据中找到: {missing_cols}")
            X = data[feature_cols]
        else:
            X = data
        
        logger.info(f"特征形状: {X.shape}")
    
    elif isinstance(data, tuple) and len(data) >= 2:
        # 处理已经分割好的数据集，用于聚类只需要X部分
        logger.info("数据加载为元组（已分割）")
        # 假设前两个元素是训练数据和训练标签
        X = data[0]
        
        # 检查数据类型，确保可以处理
        logger.info(f"特征类型: {type(X)}, 形状: {X.shape if hasattr(X, 'shape') else '未知'}")
    
    elif isinstance(data, dict):
        logger.info("数据加载为字典")
        if 'features' in data:
            X = data['features']
        elif 'data' in data:
            X = data['data']
        else:
            # 检查是否包含训练、测试数据
            if 'train' in data and 'test' in data:
                # 只返回训练数据
                if isinstance(data['train'], tuple) and len(data['train']) >= 1:
                    X = data['train'][0]
                else:
                    raise ValueError("无法识别的训练数据格式")
            else:
                raise ValueError("字典数据应包含'features'或'data'键")
    else:
        # 尝试处理tuple类型的特殊情况：(train_data, validation_data, test_data)
        if isinstance(data, tuple) and len(data) == 3:
            # 处理原始代码中的特殊格式
            training_data = data[0]
            if isinstance(training_data, tuple) and len(training_data) >= 1:
                X = training_data[0]
                logger.info(f"从元组格式加载数据，形状: X={X.shape if hasattr(X, 'shape') else '未知'}")
                return X
            else:
                raise TypeError(f"训练数据格式不符合预期: {type(training_data)}")
        else:
            raise TypeError(f"不支持的数据类型: {type(data)}")
    
    # 确保X是二维的
    if hasattr(X, 'ndim') and X.ndim == 1:
        X = X.reshape(-1, 1)
        logger.info(f"将一维数据转换为二维，新形状: {X.shape}")
    
    logger.info(f"处理后的特征形状: {X.shape if hasattr(X, 'shape') else '未知'}")
    return X

    
if __name__ == "__main__":
    # 设置日志
    if not os.path.exists("./logs"):
        os.makedirs("./logs")
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("./logs/gaussian_mixture.log"),
            logging.StreamHandler()
        ]
    )
    logger = logging.getLogger("GaussianMixture")

    parser = argparse.ArgumentParser(description='MLPipeline  gaussian_mixture_clustering Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help=' gaussian_mixture_clustering Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help=' gaussian_mixture_clustering DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start  gaussian_mixture_clustering training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print(" gaussian_mixture_clustering job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print(" gaussian_mixture_clustering dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print(" gaussian_mixture_clustering result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print(" gaussian_mixture_clustering factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print(" gaussian_mixture_clustering fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("gaussian_mixture_clustering sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
     
    print("Step 1  gaussian_mixture_clustering training:\n")
    try:
        # 执行高斯混合聚类
        result = gaussian_mixture_clustering(dataset, job_params, model["model_name"], result_dir, fit_params)
        logger.info(f"高斯混合聚类完成，结果: {result}")
        print("Training finished successfully")
    except Exception as e:
        logger.error(f"高斯混合聚类失败: {str(e)}")
        print(f"gaussian_mixture_clustering train error: {str(e)}\n")
        sys.exit(-1)
    
    print("Training finish, start storage model...\n")
    
    print("Step 2 Model Upload to MinIO: \n")
    result_upload, ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + str(result_upload) + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result_upload["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result_register, ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ str(result_register))
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result_register["result"]["model_id"]
    model_version_id = result_register["result"]["model_version_id"]
    result_push, ret_code = model_push(model_id, model_version_id, factory_name, headers)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ str(result_push) +"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit(0)
   
  
