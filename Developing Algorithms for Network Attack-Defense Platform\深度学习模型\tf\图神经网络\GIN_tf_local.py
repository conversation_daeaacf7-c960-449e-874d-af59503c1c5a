"""图同构网络 (Graph Isomorphism Network, GIN)"""


import pickle
import requests
import joblib
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import tensorflow as tf
from tensorflow import keras
import numpy as np

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class ApplyNodeFunc(keras.layers.Layer):
    def __init__(self, mlp):
        super(ApplyNodeFunc, self).__init__()
        self.mlp = mlp
    
    def call(self, inputs):
        return self.mlp(inputs)

class GINConv(keras.layers.Layer):
    def __init__(self, apply_func, eps=0, train_eps=False):
        super(GINConv, self).__init__()
        self.apply_func = apply_func
        if train_eps:
            self.eps = tf.Variable(initial_value=eps, dtype=tf.float32, trainable=True)
        else:
            self.eps = tf.constant(eps, dtype=tf.float32)
        self.train_eps = train_eps

    def call(self, inputs):
        g, feat = inputs
        # Convert graph to adjacency matrix if not already
        if isinstance(g, tf.sparse.SparseTensor):
            adj = g
        else:
            adj = tf.sparse.from_dense(g)
        
        # Aggregate neighbors
        neigh = tf.sparse.sparse_dense_matmul(adj, feat)
        # Update node features
        rst = (1 + self.eps) * feat + neigh
        
        if self.apply_func is not None:
            rst = self.apply_func(rst)
        return rst

class GIN(keras.Model):
    def __init__(self, num_layers, num_mlp_layers, input_dim, hidden_dim, 
                 output_dim, final_dropout, learn_eps, neighbor_pooling_type):
        super(GIN, self).__init__()
        self.num_layers = num_layers
        self.learn_eps = learn_eps
        
        # Initialize GIN layers
        self.gins = []
        self.batch_norms = []
        for layer in range(self.num_layers - 1):
            if layer == 0:
                mlp = self.build_mlp(num_mlp_layers, input_dim, hidden_dim, hidden_dim)
            else:
                mlp = self.build_mlp(num_mlp_layers, hidden_dim, hidden_dim, hidden_dim)
            
            self.gins.append(GINConv(
                ApplyNodeFunc(mlp), 
                eps=0, 
                train_eps=self.learn_eps
            ))
            self.batch_norms.append(keras.layers.BatchNormalization())
        
        # Initialize prediction layers
        self.linears_prediction = []
        for layer in range(self.num_layers):
            if layer == 0:
                self.linears_prediction.append(keras.layers.Dense(output_dim))
            else:
                self.linears_prediction.append(keras.layers.Dense(output_dim))
        
        self.dropout = keras.layers.Dropout(final_dropout)

    def build_mlp(self, num_mlp_layers, input_dim, hidden_dim, output_dim):
        layers = []
        for i in range(num_mlp_layers):
            if i == 0:
                layers.append(keras.layers.Dense(hidden_dim, input_dim=input_dim))
            else:
                layers.append(keras.layers.Dense(hidden_dim))
            layers.append(keras.layers.ReLU())
        layers.append(keras.layers.Dense(output_dim))
        return keras.Sequential(layers)

    def call(self, inputs):
        g, h = inputs
        hidden_rep = [h]
        
        for i in range(self.num_layers - 1):
            h = self.gins[i]([g, h])
            h = self.batch_norms[i](h)
            h = tf.nn.relu(h)
            hidden_rep.append(h)
        
        score_over_layer = 0
        for i, h in enumerate(hidden_rep):
            score_over_layer += self.dropout(self.linears_prediction[i](h))
        
        return score_over_layer

def gin_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """
    TensorFlow implementation of GIN training
    
    Parameters:
    - num_layers: Number of GIN layers
    - num_mlp_layers: Number of layers in each MLP
    - input_dim: Input feature dimension
    - hidden_dim: Hidden layer dimension
    - output_dim: Output dimension
    - final_dropout: Dropout rate for final layer
    - learn_eps: Whether to train epsilon parameter
    - neighbor_pooling_type: Neighbor pooling type (sum, mean, max)
    - num_epochs: Number of training epochs
    - learning_rate: Learning rate
    """
    training_data_path = "/workspace/" + dataset["training_data_path"]
    with open(training_data_path, 'rb') as f:
        training_data, validation_data, test_data = pickle.load(f, encoding='bytes')
    
    train_x, train_g, train_y = training_data
    test_x, test_g, test_y = test_data

    # Convert data to TensorFlow format
    train_x = tf.convert_to_tensor(train_x, dtype=tf.float32)
    train_g = tf.convert_to_tensor(train_g, dtype=tf.float32)
    train_y = tf.convert_to_tensor(train_y, dtype=tf.int64)

    # Initialize model
    model = GIN(
        num_layers=job_params["num_layers"],
        num_mlp_layers=job_params["num_mlp_layers"],
        input_dim=job_params["input_dim"],
        hidden_dim=job_params["hidden_dim"],
        output_dim=job_params["output_dim"],
        final_dropout=job_params["final_dropout"],
        learn_eps=job_params["learn_eps"],
        neighbor_pooling_type=job_params["neighbor_pooling_type"]
    )

    # Define optimizer and loss
    optimizer = keras.optimizers.Adam(learning_rate=job_params["learning_rate"])
    loss_fn = keras.losses.SparseCategoricalCrossentropy(from_logits=True)

    # Training loop
    @tf.function
    def train_step(g, x, y):
        with tf.GradientTape() as tape:
            predictions = model([g, x])
            loss = loss_fn(y, predictions)
        gradients = tape.gradient(loss, model.trainable_variables)
        optimizer.apply_gradients(zip(gradients, model.trainable_variables))
        return loss

    for epoch in range(job_params["num_epochs"]):
        loss = train_step(train_g, train_x, train_y)
        if epoch % 10 == 0:
            print(f"Epoch {epoch}, Loss: {loss:.4f}")

    # Save model
    model_path = f"{result_dir}/{model_name}.h5"
    model.save(model_path)
    print(f'GIN training completed, model saved to {model_path}')

    return None, 0

def model_upload(result_dir, model_name):
    minioClient = Minio(MINIO_URL,
                       access_key='AKIAIOSFODNN7EXAMPLE',
                       secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                       secure=False)
    try:
        obj_name = str(uuid.uuid1())
        upload_path = f"{obj_name}/{model_name}.h5"
        source = f"s3://mlss-mf/{obj_name}"
        res = minioClient.fput_object('mlss-mf', upload_path, f"{result_dir}/{model_name}.h5")
        result = {"source": source}
        return result, 0
    except Exception as err:
        print(err)
        return None, -1

def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "TENSORFLOW",
          "file_name": model_name+".h5",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "Logistic_Regression",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

    
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='tensorflow GIN Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='GIN Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='GIN DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start GIN training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("GIN job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("GIN dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("GIN result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("GIN factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("GIN fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("GIN sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
     
    print("Step 1 GIN training:\n")
    result,ret_code = gin_train(dataset,job_params, model["model_name"],result_dir,fit_params)
    if ret_code != 0:
        print("GIN train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()
   
  
