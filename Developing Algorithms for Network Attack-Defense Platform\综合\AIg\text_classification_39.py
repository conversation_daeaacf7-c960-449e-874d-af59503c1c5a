"""文本分类"""

from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
from sklearn.pipeline import make_pipeline

def text_classification(train_texts, train_labels, test_texts):
    """
    参数说明：
    - train_texts: 训练文本列表
    - train_labels: 训练标签列表
    - test_texts: 测试文本列表
    """
    model = make_pipeline(TfidfVectorizer(), MultinomialNB())
    model.fit(train_texts, train_labels)
    predictions = model.predict(test_texts)
    return predictions

if __name__ == "__main__":
    train_texts = ["这是一个例子", "这是另一个例子"]
    train_labels = ["类别1", "类别2"]
    test_texts = ["这是测试文本"]
    predictions = text_classification(train_texts, train_labels, test_texts)
    print(predictions)
