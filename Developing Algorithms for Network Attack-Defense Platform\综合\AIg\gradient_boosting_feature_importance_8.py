"""基于梯度提升分类的特征权重分析"""

import sys
import pandas as pd
import numpy as np
from sklearn.ensemble import GradientBoostingClassifier

def gradient_boosting_feature_importance(input_file, target_column, n_estimators=100, learning_rate=0.1, max_depth=3):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - target_column: 目标变量的列名
    - n_estimators: 弱学习器的数量，整数，默认为100
    - learning_rate: 学习率，浮点数，默认为0.1
    - max_depth: 树的最大深度，整数，默认为3
    """
    data = pd.read_csv(input_file)
    X = data.drop(target_column, axis=1)
    y = data[target_column]
    
    gb = GradientBoostingClassifier(n_estimators=n_estimators, learning_rate=learning_rate, max_depth=max_depth)
    gb.fit(X, y)
    
    feature_importance = pd.DataFrame({
        'feature': X.columns,
        'importance': gb.feature_importances_
    }).sort_values('importance', ascending=False)
    
    output_file = 'gradient_boosting_feature_importance.csv'
    feature_importance.to_csv(output_file, index=False)
    print(f"Gradient Boosting feature importance analysis completed. Output saved to {output_file}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python gradient_boosting_feature_importance.py <input_file> <target_column>")
        sys.exit(1)
    input_file, target_column = sys.argv[1], sys.argv[2]
    gradient_boosting_feature_importance(input_file, target_column)