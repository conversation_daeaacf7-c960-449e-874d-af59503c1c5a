2025-05-21 16:04:42,255 - GCN_TF_Local - INFO - 日志将记录到控制台和文件: results\results_gcn_tf_local\gcn_tf_local_training_20250521_160442.log
2025-05-21 16:04:42,256 - GCN_TF_Local - INFO - GCN TensorFlow 本地模式训练脚本已初始化。
2025-05-21 16:04:42,256 - GCN_TF_Local - INFO - TensorFlow 版本: 2.13.0
2025-05-21 16:04:42,256 - GCN_TF_Local - INFO - 参数: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'result_dir': 'results\\results_gcn_tf_local', 'model_name': 'GCN_TF_Local_Model', 'label_column': 'Label', 'data_format': 'pkl', 'hidden_size': 64, 'num_layers': 2, 'dropout_rate': 0.5, 'num_epochs': 3, 'learning_rate': 0.005, 'optimizer': 'adam', 'loss_function': 'sparsecategoricalcrossentropy', 'edge_strategy': 'fully_connected', 'test_split_size': 0.2, 'random_state': 42, 'normalize': True, 'scaler_type': 'standard', 'handle_imbalance': False, 'stratify_split': True, 'numeric_impute_strategy': 'mean', 'categorical_impute_strategy': 'most_frequent', 'use_cv': False, 'cv_folds': 5, 'log_level': 'INFO', 'force_cpu': False}
2025-05-21 16:04:42,258 - GCN_TF_Local - INFO - 未找到GPU或强制使用CPU。
2025-05-21 16:04:42,258 - GCN_TF_Local - INFO - 从 E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl (格式: pkl) 加载数据
2025-05-21 16:04:42,623 - GCN_TF_Local - INFO - 数据加载为 DataFrame，形状: (692703, 85)
2025-05-21 16:04:42,758 - GCN_TF_Local - INFO - 特征形状: (692703, 84), 标签形状: (692703,)
2025-05-21 16:04:42,788 - GCN_TF_Local - INFO - 标签分布: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-21 16:04:42,815 - GCN_TF_Local - INFO - 选择标准训练/测试分割模式。
2025-05-21 16:04:43,285 - GCN_TF_Local - INFO - 已将特征中的无限值替换为NaN。
2025-05-21 16:04:43,369 - GCN_TF_Local - INFO - 处理特征中的缺失值。
2025-05-21 16:04:45,277 - GCN_TF_Local - INFO - 使用 'mean' 填充了数值型缺失值。
2025-05-21 16:04:45,760 - GCN_TF_Local - INFO - 使用 'most_frequent' 填充了类别型缺失值。
2025-05-21 16:04:45,867 - GCN_TF_Local - INFO - 标签已编码。6 个类别: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-21 16:04:45,951 - GCN_TF_Local - INFO - 编码类别特征: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-21 16:04:49,852 - GCN_TF_Local - INFO - 使用 standard 缩放器标准化特征。
2025-05-21 16:04:52,142 - GCN_TF_Local - INFO - 数据分割: X_train (554162, 84), y_train (554162,), X_test (138541, 84), y_test (138541,)
2025-05-21 16:04:52,192 - GCN_TF_Local - INFO - 训练/测试分割的预处理信息已保存: results\results_gcn_tf_local\GCN_TF_Local_Model_preprocessing_info_tf.json
2025-05-21 16:04:52,193 - GCN_TF_Local - INFO - 预处理后数据 (训练/测试分割): 输入大小=84, 类别数=6
2025-05-21 16:04:52,193 - GCN_TF_Local - WARNING - 节点数 (554162) 超过阈值 (20000)，全连接策略将使用等效单位矩阵进行归一化 (仅自环)。
2025-05-21 16:04:52,237 - GCN_TF_Local - INFO - 请求为 554162 个节点创建基于标识的邻接信息。
2025-05-21 16:04:52,238 - GCN_TF_Local - INFO - normalize_adjacency_matrix_tf_local: Identity requested. Returning placeholder and True flag.
2025-05-21 16:04:52,239 - GCN_TF_Local - INFO - 创建图数据字典: 特征 (554162, 84), 处理后的邻接张量 ((1, 1)), 是否为单位邻接: True, 标签 (554162,)
2025-05-21 16:04:52,239 - GCN_TF_Local - INFO - 训练图数据准备完成. 节点数: 554162, 边策略: fully_connected
2025-05-21 16:04:52,240 - GCN_TF_Local - WARNING - 节点数 (138541) 超过阈值 (20000)，全连接策略将使用等效单位矩阵进行归一化 (仅自环)。
2025-05-21 16:04:52,253 - GCN_TF_Local - INFO - 请求为 138541 个节点创建基于标识的邻接信息。
2025-05-21 16:04:52,253 - GCN_TF_Local - INFO - normalize_adjacency_matrix_tf_local: Identity requested. Returning placeholder and True flag.
2025-05-21 16:04:52,254 - GCN_TF_Local - INFO - 创建图数据字典: 特征 (138541, 84), 处理后的邻接张量 ((1, 1)), 是否为单位邻接: True, 标签 (138541,)
2025-05-21 16:04:52,254 - GCN_TF_Local - INFO - 测试图数据准备完成. 节点数: 138541, 边策略: fully_connected
2025-05-21 16:04:52,619 - GCN_TF_Local - INFO - Model: "GCNNetwork_TF_Local"
2025-05-21 16:04:52,620 - GCN_TF_Local - INFO - _________________________________________________________________
2025-05-21 16:04:52,620 - GCN_TF_Local - INFO -  Layer (type)                Output Shape              Param #   
2025-05-21 16:04:52,621 - GCN_TF_Local - INFO - =================================================================
2025-05-21 16:04:52,622 - GCN_TF_Local - INFO -  input_projection (Dense)    multiple                  5440      
2025-05-21 16:04:52,622 - GCN_TF_Local - INFO -                                                                  
2025-05-21 16:04:52,622 - GCN_TF_Local - INFO -  gcn_hidden_1 (GCNLayer)     multiple                  4160      
2025-05-21 16:04:52,623 - GCN_TF_Local - INFO -                                                                  
2025-05-21 16:04:52,623 - GCN_TF_Local - INFO -  gcn_output (GCNLayer)       multiple                  390       
2025-05-21 16:04:52,623 - GCN_TF_Local - INFO -                                                                  
2025-05-21 16:04:52,623 - GCN_TF_Local - INFO -  dropout (Dropout)           multiple                  0         
2025-05-21 16:04:52,624 - GCN_TF_Local - INFO -                                                                  
2025-05-21 16:04:52,624 - GCN_TF_Local - INFO - =================================================================
2025-05-21 16:04:52,626 - GCN_TF_Local - INFO - Total params: 9990 (39.02 KB)
2025-05-21 16:04:52,626 - GCN_TF_Local - INFO - Trainable params: 9990 (39.02 KB)
2025-05-21 16:04:52,626 - GCN_TF_Local - INFO - Non-trainable params: 0 (0.00 Byte)
2025-05-21 16:04:52,626 - GCN_TF_Local - INFO - _________________________________________________________________
2025-05-21 16:04:52,626 - GCN_TF_Local - INFO - 开始 GCN (TF) 模型训练 (标准分割)。
2025-05-21 16:04:52,647 - GCN_TF_Local - INFO - 最佳模型权重将基于 val_loss 保存到: results\results_gcn_tf_local\models_gcn_tf_local\GCN_TF_Local_Model_best.weights.h5
2025-05-21 16:04:52,647 - GCN_TF_Local - INFO - 开始 Keras GCN 模型训练，共 3 轮
2025-05-21 16:04:54,196 - GCN_TF_Local - CRITICAL - 发生意外严重错误: in user code:

    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 1338, in train_function  *
        return step_function(self, iterator)
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 1322, in step_function  **
        outputs = model.distribute_strategy.run(run_step, args=(data,))
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 1303, in run_step  **
        outputs = model.train_step(data)
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 1080, in train_step
        y_pred = self(x, training=True)
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler
        raise e.with_traceback(filtered_tb) from None
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_filesj6l3e8o.py", line 27, in tf__call
        ag__.for_stmt(ag__.ld(self).gcn_layers_list, None, loop_body, get_state, set_state, ('x',), {'iterate_names': 'gcn_layer_obj'})
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_filesj6l3e8o.py", line 24, in loop_body
        x = ag__.converted_call(ag__.ld(gcn_layer_obj), ([ag__.ld(x), ag__.ld(adj_tensor_for_layers), ag__.ld(is_adj_identity_flag_for_layers)],), dict(training=ag__.ld(training)), fscope)
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 164, in tf__call
        support = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_adj_identity_flag), ag__.ld(true_branch_fn), ag__.ld(false_branch_fn)), None, fscope)
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 156, in false_branch_fn
        effective_adj_for_matmul = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_actually_placeholder_in_false_branch), ag__.ld(adj_for_tracing_placeholder_case), ag__.ld(adj_for_real_adjacency_case)), None, fscope_2)
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 148, in adj_for_real_adjacency_case
        ag__.converted_call(ag__.ld(tf).debugging.assert_logical_or, (ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 2), None, fscope_6), ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 3), None, fscope_6)), dict(message=f'GCNLayer {ag__.ld(self).name} false_branch: Real adjacency matrix has unexpected rank {ag__.ld(adj_rank)}'), fscope_6)

    AttributeError: Exception encountered when calling layer 'GCNNetwork_TF_Local' (type GCNNetwork).
    
    in user code:
    
        File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 253, in call  *
            x = gcn_layer_obj([x, adj_tensor_for_layers, is_adj_identity_flag_for_layers], training=training)
        File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler  **
            raise e.with_traceback(filtered_tb) from None
        File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 164, in tf__call
            support = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_adj_identity_flag), ag__.ld(true_branch_fn), ag__.ld(false_branch_fn)), None, fscope)
        File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 156, in false_branch_fn
            effective_adj_for_matmul = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_actually_placeholder_in_false_branch), ag__.ld(adj_for_tracing_placeholder_case), ag__.ld(adj_for_real_adjacency_case)), None, fscope_2)
        File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 148, in adj_for_real_adjacency_case
            ag__.converted_call(ag__.ld(tf).debugging.assert_logical_or, (ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 2), None, fscope_6), ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 3), None, fscope_6)), dict(message=f'GCNLayer {ag__.ld(self).name} false_branch: Real adjacency matrix has unexpected rank {ag__.ld(adj_rank)}'), fscope_6)
    
        AttributeError: Exception encountered when calling layer 'gcn_hidden_1' (type GCNLayer).
        
        in user code:
        
            File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 175, in adj_for_real_adjacency_case  *
                tf.debugging.assert_logical_or(
        
            AttributeError: module 'tensorflow._api.v2.debugging' has no attribute 'assert_logical_or'
        
        
        Call arguments received by layer 'gcn_hidden_1' (type GCNLayer):
          • inputs=['tf.Tensor(shape=(554162, 64), dtype=float32)', 'tf.Tensor(shape=(1, 1), dtype=float32)', 'tf.Tensor(shape=(), dtype=bool)']
          • training=True
    
    
    Call arguments received by layer 'GCNNetwork_TF_Local' (type GCNNetwork):
      • inputs=('tf.Tensor(shape=(554162, 84), dtype=float32)', 'tf.Tensor(shape=(1, 1), dtype=float32)', 'tf.Tensor(shape=(), dtype=bool)')
      • training=True
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 1132, in gcn_train_tf_local_mode
    train_history = train_gcn_model_tf_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 688, in train_gcn_model_tf_local
    history = model.fit(
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_filerbz4qexl.py", line 15, in tf__train_function
    retval_ = ag__.converted_call(ag__.ld(step_function), (ag__.ld(self), ag__.ld(iterator)), None, fscope)
  File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_filesj6l3e8o.py", line 27, in tf__call
    ag__.for_stmt(ag__.ld(self).gcn_layers_list, None, loop_body, get_state, set_state, ('x',), {'iterate_names': 'gcn_layer_obj'})
  File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_filesj6l3e8o.py", line 24, in loop_body
    x = ag__.converted_call(ag__.ld(gcn_layer_obj), ([ag__.ld(x), ag__.ld(adj_tensor_for_layers), ag__.ld(is_adj_identity_flag_for_layers)],), dict(training=ag__.ld(training)), fscope)
  File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 164, in tf__call
    support = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_adj_identity_flag), ag__.ld(true_branch_fn), ag__.ld(false_branch_fn)), None, fscope)
  File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 156, in false_branch_fn
    effective_adj_for_matmul = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_actually_placeholder_in_false_branch), ag__.ld(adj_for_tracing_placeholder_case), ag__.ld(adj_for_real_adjacency_case)), None, fscope_2)
  File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 148, in adj_for_real_adjacency_case
    ag__.converted_call(ag__.ld(tf).debugging.assert_logical_or, (ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 2), None, fscope_6), ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 3), None, fscope_6)), dict(message=f'GCNLayer {ag__.ld(self).name} false_branch: Real adjacency matrix has unexpected rank {ag__.ld(adj_rank)}'), fscope_6)
AttributeError: in user code:

    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 1338, in train_function  *
        return step_function(self, iterator)
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 1322, in step_function  **
        outputs = model.distribute_strategy.run(run_step, args=(data,))
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 1303, in run_step  **
        outputs = model.train_step(data)
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 1080, in train_step
        y_pred = self(x, training=True)
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler
        raise e.with_traceback(filtered_tb) from None
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_filesj6l3e8o.py", line 27, in tf__call
        ag__.for_stmt(ag__.ld(self).gcn_layers_list, None, loop_body, get_state, set_state, ('x',), {'iterate_names': 'gcn_layer_obj'})
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_filesj6l3e8o.py", line 24, in loop_body
        x = ag__.converted_call(ag__.ld(gcn_layer_obj), ([ag__.ld(x), ag__.ld(adj_tensor_for_layers), ag__.ld(is_adj_identity_flag_for_layers)],), dict(training=ag__.ld(training)), fscope)
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 164, in tf__call
        support = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_adj_identity_flag), ag__.ld(true_branch_fn), ag__.ld(false_branch_fn)), None, fscope)
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 156, in false_branch_fn
        effective_adj_for_matmul = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_actually_placeholder_in_false_branch), ag__.ld(adj_for_tracing_placeholder_case), ag__.ld(adj_for_real_adjacency_case)), None, fscope_2)
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 148, in adj_for_real_adjacency_case
        ag__.converted_call(ag__.ld(tf).debugging.assert_logical_or, (ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 2), None, fscope_6), ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 3), None, fscope_6)), dict(message=f'GCNLayer {ag__.ld(self).name} false_branch: Real adjacency matrix has unexpected rank {ag__.ld(adj_rank)}'), fscope_6)

    AttributeError: Exception encountered when calling layer 'GCNNetwork_TF_Local' (type GCNNetwork).
    
    in user code:
    
        File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 253, in call  *
            x = gcn_layer_obj([x, adj_tensor_for_layers, is_adj_identity_flag_for_layers], training=training)
        File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler  **
            raise e.with_traceback(filtered_tb) from None
        File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 164, in tf__call
            support = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_adj_identity_flag), ag__.ld(true_branch_fn), ag__.ld(false_branch_fn)), None, fscope)
        File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 156, in false_branch_fn
            effective_adj_for_matmul = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_actually_placeholder_in_false_branch), ag__.ld(adj_for_tracing_placeholder_case), ag__.ld(adj_for_real_adjacency_case)), None, fscope_2)
        File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 148, in adj_for_real_adjacency_case
            ag__.converted_call(ag__.ld(tf).debugging.assert_logical_or, (ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 2), None, fscope_6), ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 3), None, fscope_6)), dict(message=f'GCNLayer {ag__.ld(self).name} false_branch: Real adjacency matrix has unexpected rank {ag__.ld(adj_rank)}'), fscope_6)
    
        AttributeError: Exception encountered when calling layer 'gcn_hidden_1' (type GCNLayer).
        
        in user code:
        
            File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 175, in adj_for_real_adjacency_case  *
                tf.debugging.assert_logical_or(
        
            AttributeError: module 'tensorflow._api.v2.debugging' has no attribute 'assert_logical_or'
        
        
        Call arguments received by layer 'gcn_hidden_1' (type GCNLayer):
          • inputs=['tf.Tensor(shape=(554162, 64), dtype=float32)', 'tf.Tensor(shape=(1, 1), dtype=float32)', 'tf.Tensor(shape=(), dtype=bool)']
          • training=True
    
    
    Call arguments received by layer 'GCNNetwork_TF_Local' (type GCNNetwork):
      • inputs=('tf.Tensor(shape=(554162, 84), dtype=float32)', 'tf.Tensor(shape=(1, 1), dtype=float32)', 'tf.Tensor(shape=(), dtype=bool)')
      • training=True

2025-05-21 16:04:54,219 - GCN_TF_Local - CRITICAL - 主程序中发生严重错误: in user code:

    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 1338, in train_function  *
        return step_function(self, iterator)
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 1322, in step_function  **
        outputs = model.distribute_strategy.run(run_step, args=(data,))
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 1303, in run_step  **
        outputs = model.train_step(data)
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 1080, in train_step
        y_pred = self(x, training=True)
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler
        raise e.with_traceback(filtered_tb) from None
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_filesj6l3e8o.py", line 27, in tf__call
        ag__.for_stmt(ag__.ld(self).gcn_layers_list, None, loop_body, get_state, set_state, ('x',), {'iterate_names': 'gcn_layer_obj'})
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_filesj6l3e8o.py", line 24, in loop_body
        x = ag__.converted_call(ag__.ld(gcn_layer_obj), ([ag__.ld(x), ag__.ld(adj_tensor_for_layers), ag__.ld(is_adj_identity_flag_for_layers)],), dict(training=ag__.ld(training)), fscope)
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 164, in tf__call
        support = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_adj_identity_flag), ag__.ld(true_branch_fn), ag__.ld(false_branch_fn)), None, fscope)
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 156, in false_branch_fn
        effective_adj_for_matmul = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_actually_placeholder_in_false_branch), ag__.ld(adj_for_tracing_placeholder_case), ag__.ld(adj_for_real_adjacency_case)), None, fscope_2)
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 148, in adj_for_real_adjacency_case
        ag__.converted_call(ag__.ld(tf).debugging.assert_logical_or, (ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 2), None, fscope_6), ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 3), None, fscope_6)), dict(message=f'GCNLayer {ag__.ld(self).name} false_branch: Real adjacency matrix has unexpected rank {ag__.ld(adj_rank)}'), fscope_6)

    AttributeError: Exception encountered when calling layer 'GCNNetwork_TF_Local' (type GCNNetwork).
    
    in user code:
    
        File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 253, in call  *
            x = gcn_layer_obj([x, adj_tensor_for_layers, is_adj_identity_flag_for_layers], training=training)
        File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler  **
            raise e.with_traceback(filtered_tb) from None
        File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 164, in tf__call
            support = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_adj_identity_flag), ag__.ld(true_branch_fn), ag__.ld(false_branch_fn)), None, fscope)
        File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 156, in false_branch_fn
            effective_adj_for_matmul = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_actually_placeholder_in_false_branch), ag__.ld(adj_for_tracing_placeholder_case), ag__.ld(adj_for_real_adjacency_case)), None, fscope_2)
        File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 148, in adj_for_real_adjacency_case
            ag__.converted_call(ag__.ld(tf).debugging.assert_logical_or, (ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 2), None, fscope_6), ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 3), None, fscope_6)), dict(message=f'GCNLayer {ag__.ld(self).name} false_branch: Real adjacency matrix has unexpected rank {ag__.ld(adj_rank)}'), fscope_6)
    
        AttributeError: Exception encountered when calling layer 'gcn_hidden_1' (type GCNLayer).
        
        in user code:
        
            File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 175, in adj_for_real_adjacency_case  *
                tf.debugging.assert_logical_or(
        
            AttributeError: module 'tensorflow._api.v2.debugging' has no attribute 'assert_logical_or'
        
        
        Call arguments received by layer 'gcn_hidden_1' (type GCNLayer):
          • inputs=['tf.Tensor(shape=(554162, 64), dtype=float32)', 'tf.Tensor(shape=(1, 1), dtype=float32)', 'tf.Tensor(shape=(), dtype=bool)']
          • training=True
    
    
    Call arguments received by layer 'GCNNetwork_TF_Local' (type GCNNetwork):
      • inputs=('tf.Tensor(shape=(554162, 84), dtype=float32)', 'tf.Tensor(shape=(1, 1), dtype=float32)', 'tf.Tensor(shape=(), dtype=bool)')
      • training=True
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 1246, in <module>
    gcn_train_tf_local_mode(parsed_args)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 1132, in gcn_train_tf_local_mode
    train_history = train_gcn_model_tf_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 688, in train_gcn_model_tf_local
    history = model.fit(
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_filerbz4qexl.py", line 15, in tf__train_function
    retval_ = ag__.converted_call(ag__.ld(step_function), (ag__.ld(self), ag__.ld(iterator)), None, fscope)
  File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_filesj6l3e8o.py", line 27, in tf__call
    ag__.for_stmt(ag__.ld(self).gcn_layers_list, None, loop_body, get_state, set_state, ('x',), {'iterate_names': 'gcn_layer_obj'})
  File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_filesj6l3e8o.py", line 24, in loop_body
    x = ag__.converted_call(ag__.ld(gcn_layer_obj), ([ag__.ld(x), ag__.ld(adj_tensor_for_layers), ag__.ld(is_adj_identity_flag_for_layers)],), dict(training=ag__.ld(training)), fscope)
  File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 164, in tf__call
    support = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_adj_identity_flag), ag__.ld(true_branch_fn), ag__.ld(false_branch_fn)), None, fscope)
  File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 156, in false_branch_fn
    effective_adj_for_matmul = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_actually_placeholder_in_false_branch), ag__.ld(adj_for_tracing_placeholder_case), ag__.ld(adj_for_real_adjacency_case)), None, fscope_2)
  File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 148, in adj_for_real_adjacency_case
    ag__.converted_call(ag__.ld(tf).debugging.assert_logical_or, (ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 2), None, fscope_6), ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 3), None, fscope_6)), dict(message=f'GCNLayer {ag__.ld(self).name} false_branch: Real adjacency matrix has unexpected rank {ag__.ld(adj_rank)}'), fscope_6)
AttributeError: in user code:

    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 1338, in train_function  *
        return step_function(self, iterator)
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 1322, in step_function  **
        outputs = model.distribute_strategy.run(run_step, args=(data,))
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 1303, in run_step  **
        outputs = model.train_step(data)
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 1080, in train_step
        y_pred = self(x, training=True)
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler
        raise e.with_traceback(filtered_tb) from None
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_filesj6l3e8o.py", line 27, in tf__call
        ag__.for_stmt(ag__.ld(self).gcn_layers_list, None, loop_body, get_state, set_state, ('x',), {'iterate_names': 'gcn_layer_obj'})
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_filesj6l3e8o.py", line 24, in loop_body
        x = ag__.converted_call(ag__.ld(gcn_layer_obj), ([ag__.ld(x), ag__.ld(adj_tensor_for_layers), ag__.ld(is_adj_identity_flag_for_layers)],), dict(training=ag__.ld(training)), fscope)
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 164, in tf__call
        support = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_adj_identity_flag), ag__.ld(true_branch_fn), ag__.ld(false_branch_fn)), None, fscope)
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 156, in false_branch_fn
        effective_adj_for_matmul = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_actually_placeholder_in_false_branch), ag__.ld(adj_for_tracing_placeholder_case), ag__.ld(adj_for_real_adjacency_case)), None, fscope_2)
    File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 148, in adj_for_real_adjacency_case
        ag__.converted_call(ag__.ld(tf).debugging.assert_logical_or, (ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 2), None, fscope_6), ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 3), None, fscope_6)), dict(message=f'GCNLayer {ag__.ld(self).name} false_branch: Real adjacency matrix has unexpected rank {ag__.ld(adj_rank)}'), fscope_6)

    AttributeError: Exception encountered when calling layer 'GCNNetwork_TF_Local' (type GCNNetwork).
    
    in user code:
    
        File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 253, in call  *
            x = gcn_layer_obj([x, adj_tensor_for_layers, is_adj_identity_flag_for_layers], training=training)
        File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler  **
            raise e.with_traceback(filtered_tb) from None
        File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 164, in tf__call
            support = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_adj_identity_flag), ag__.ld(true_branch_fn), ag__.ld(false_branch_fn)), None, fscope)
        File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 156, in false_branch_fn
            effective_adj_for_matmul = ag__.converted_call(ag__.ld(tf).cond, (ag__.ld(is_actually_placeholder_in_false_branch), ag__.ld(adj_for_tracing_placeholder_case), ag__.ld(adj_for_real_adjacency_case)), None, fscope_2)
        File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_fileoxh1zh7n.py", line 148, in adj_for_real_adjacency_case
            ag__.converted_call(ag__.ld(tf).debugging.assert_logical_or, (ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 2), None, fscope_6), ag__.converted_call(ag__.ld(tf).equal, (ag__.ld(adj_rank), 3), None, fscope_6)), dict(message=f'GCNLayer {ag__.ld(self).name} false_branch: Real adjacency matrix has unexpected rank {ag__.ld(adj_rank)}'), fscope_6)
    
        AttributeError: Exception encountered when calling layer 'gcn_hidden_1' (type GCNLayer).
        
        in user code:
        
            File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 175, in adj_for_real_adjacency_case  *
                tf.debugging.assert_logical_or(
        
            AttributeError: module 'tensorflow._api.v2.debugging' has no attribute 'assert_logical_or'
        
        
        Call arguments received by layer 'gcn_hidden_1' (type GCNLayer):
          • inputs=['tf.Tensor(shape=(554162, 64), dtype=float32)', 'tf.Tensor(shape=(1, 1), dtype=float32)', 'tf.Tensor(shape=(), dtype=bool)']
          • training=True
    
    
    Call arguments received by layer 'GCNNetwork_TF_Local' (type GCNNetwork):
      • inputs=('tf.Tensor(shape=(554162, 84), dtype=float32)', 'tf.Tensor(shape=(1, 1), dtype=float32)', 'tf.Tensor(shape=(), dtype=bool)')
      • training=True

