2025-05-22 10:17:42,119 - GIN_PT_Local_Mode - INFO - 日志将记录到控制台和文件: results\results_gin_pt_local\gin_pt_local_mode_training_20250522_101742.log
2025-05-22 10:17:42,120 - GIN_PT_Local_Mode - INFO - GIN PyTorch 本地模式训练脚本已初始化。
2025-05-22 10:17:42,120 - GIN_PT_Local_Mode - INFO - PyTorch 版本: 2.4.1+cpu
2025-05-22 10:17:42,121 - GIN_PT_Local_Mode - INFO - 参数: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'result_dir': 'results\\results_gin_pt_local', 'model_name': 'GIN_PT_Local_Model', 'input_size': 84, 'hidden_size': 64, 'output_size': 6, 'num_layers': 2, 'dropout_rate': 0.5, 'num_epochs': 3, 'learning_rate': 0.001, 'batch_size': 32, 'edge_strategy': 'fully_connected', 'k_neighbors': 5, 'radius': 1.0, 'label_column': 'Label', 'data_format': 'pkl', 'test_split_size': 0.2, 'random_state': 42, 'normalize': True, 'scaler_type': 'standard', 'handle_imbalance': False, 'stratify_split': True, 'numeric_impute_strategy': 'mean', 'categorical_impute_strategy': 'most_frequent', 'use_cv': False, 'cv_folds': 5, 'log_level': 'INFO', 'force_cpu': False}
2025-05-22 10:17:42,132 - GIN_PT_Local_Mode - INFO - 使用设备: cpu
2025-05-22 10:17:42,139 - GIN_PT_Local_Mode - INFO - 从 E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl (格式: pkl) 加载数据
2025-05-22 10:17:45,344 - GIN_PT_Local_Mode - INFO - 数据加载为 DataFrame，形状: (692703, 85)
2025-05-22 10:17:45,628 - GIN_PT_Local_Mode - INFO - 特征形状: (692703, 84), 标签形状: (692703,)
2025-05-22 10:17:45,660 - GIN_PT_Local_Mode - INFO - 标签分布: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-22 10:17:45,803 - GIN_PT_Local_Mode - INFO - 模型将使用 input_size=84, output_size(num_classes)=6
2025-05-22 10:17:45,803 - GIN_PT_Local_Mode - INFO - 选择标准训练/测试分割模式。
2025-05-22 10:17:46,301 - GIN_PT_Local_Mode - INFO - 已将特征中的无限值替换为NaN。
2025-05-22 10:17:46,413 - GIN_PT_Local_Mode - INFO - 处理特征中的缺失值。
2025-05-22 10:17:46,673 - GIN_PT_Local_Mode - INFO - 标签已编码。6个类别: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-22 10:17:46,705 - GIN_PT_Local_Mode - INFO - 编码类别特征: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-22 10:17:51,615 - GIN_PT_Local_Mode - INFO - 使用 standard 缩放器标准化/缩放特征。
2025-05-22 10:17:52,826 - GIN_PT_Local_Mode - INFO - 数据分割: X_train (554162, 84), y_train (554162,), X_test (138541, 84), y_test (138541,)
2025-05-22 10:17:52,906 - GIN_PT_Local_Mode - WARNING - GIN_PT: 'fully_connected' edge strategy is selected and training data size (554162) exceeds the limit (10000). Subsampling training data to 10000 samples to prevent OOM errors. For processing all data with 'fully_connected', ensure sufficient memory. Consider using '--edge_strategy knn' or '--edge_strategy identity_like' for large datasets.
2025-05-22 10:17:52,931 - GIN_PT_Local_Mode - WARNING - GIN_PT: 'fully_connected' edge strategy is selected and test data size (138541) exceeds the limit (2000). Subsampling test data to 2000 samples. 
2025-05-22 10:17:52,962 - GIN_PT_Local_Mode - INFO - 训练/测试分割的预处理信息已保存: results\results_gin_pt_local\GIN_PT_Local_Model_preprocessing_info_pt.json
2025-05-22 10:17:52,963 - GIN_PT_Local_Mode - INFO - 预处理后数据 (训练/测试分割): input_size=84, num_classes=6
2025-05-22 10:17:52,963 - GIN_PT_Local_Mode - INFO - 将 10000 个样本转换为图列表，样本内边策略: 'fully_connected'
2025-05-22 10:17:54,591 - GIN_PT_Local_Mode - INFO - 创建了 10000 个图数据对象。
2025-05-22 10:17:54,597 - GIN_PT_Local_Mode - INFO - PyG DataLoader创建成功。批大小: 32, Shuffle: True。包含 313 个批次。
2025-05-22 10:17:54,597 - GIN_PT_Local_Mode - INFO - 将 2000 个样本转换为图列表，样本内边策略: 'fully_connected'
2025-05-22 10:17:55,009 - GIN_PT_Local_Mode - INFO - 创建了 2000 个图数据对象。
2025-05-22 10:17:55,010 - GIN_PT_Local_Mode - INFO - PyG DataLoader创建成功。批大小: 32, Shuffle: False。包含 63 个批次。
2025-05-22 10:17:55,153 - GIN_PT_Local_Mode - INFO - 开始 GIN (PyTorch) 模型训练 (标准分割)。
2025-05-22 10:17:55,188 - GIN_PT_Local_Mode - ERROR - 训练期间发生运行时错误: mat1 and mat2 shapes cannot be multiplied (2688x1 and 84x64)
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GIN_pt_local_mode.py", line 467, in train_gin_model_local
    out = model(batch_data)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\nn\modules\module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\nn\modules\module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GIN_pt_local_mode.py", line 104, in forward
    x = F.relu(self.initial_transform(x))
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\nn\modules\module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\nn\modules\module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\nn\modules\linear.py", line 117, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (2688x1 and 84x64)
2025-05-22 10:17:55,193 - GIN_PT_Local_Mode - CRITICAL - 发生意外严重错误: mat1 and mat2 shapes cannot be multiplied (2688x1 and 84x64)
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GIN_pt_local_mode.py", line 1075, in gin_train_local_mode
    train_history = train_gin_model_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GIN_pt_local_mode.py", line 467, in train_gin_model_local
    out = model(batch_data)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\nn\modules\module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\nn\modules\module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GIN_pt_local_mode.py", line 104, in forward
    x = F.relu(self.initial_transform(x))
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\nn\modules\module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\nn\modules\module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\nn\modules\linear.py", line 117, in forward
    return F.linear(input, self.weight, self.bias)
RuntimeError: mat1 and mat2 shapes cannot be multiplied (2688x1 and 84x64)
