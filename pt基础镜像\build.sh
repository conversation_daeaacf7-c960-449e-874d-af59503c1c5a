#!/bin/bash
# BiLSTM推理服务构建脚本 - 优化版
# 基于BiLSTMPredictor.py的简化构建流程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
🚀 BiLSTM推理服务构建脚本 - 优化版

用法: $0 [选项]

选项:
  -h, --help          显示此帮助信息
  -b, --base          只构建基础镜像
  -i, --inference     只构建推理镜像
  -a, --all          构建所有镜像 (默认)
  -t, --test         构建后运行测试
  --no-cache         不使用Docker缓存
  --push             构建后推送到仓库
  --tag TAG          指定镜像标签 (默认: latest)

示例:
  $0                  # 构建所有镜像
  $0 -b               # 只构建基础镜像
  $0 -i               # 只构建推理镜像
  $0 --test           # 构建并测试
  $0 --tag v1.0.0     # 构建并打标签
EOF
}

# 默认参数
BUILD_BASE=false
BUILD_INFERENCE=false
BUILD_ALL=true
RUN_TEST=false
NO_CACHE=""
PUSH_IMAGES=false
TAG="latest"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -b|--base)
            BUILD_BASE=true
            BUILD_ALL=false
            shift
            ;;
        -i|--inference)
            BUILD_INFERENCE=true
            BUILD_ALL=false
            shift
            ;;
        -a|--all)
            BUILD_ALL=true
            shift
            ;;
        -t|--test)
            RUN_TEST=true
            shift
            ;;
        --no-cache)
            NO_CACHE="--no-cache"
            shift
            ;;
        --push)
            PUSH_IMAGES=true
            shift
            ;;
        --tag)
            TAG="$2"
            shift 2
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查Docker
if ! command -v docker &> /dev/null; then
    log_error "Docker未安装或不在PATH中"
    exit 1
fi

# 显示构建信息
echo "=================================="
echo "🚀 BiLSTM推理服务构建开始"
echo "=================================="
echo "📅 时间: $(date)"
echo "🏷️  标签: $TAG"
echo "🔧 模式: $([ "$BUILD_ALL" == "true" ] && echo "全部构建" || echo "部分构建")"
echo "🧪 测试: $([ "$RUN_TEST" == "true" ] && echo "是" || echo "否")"
echo "📤 推送: $([ "$PUSH_IMAGES" == "true" ] && echo "是" || echo "否")"
echo "=================================="

# 记录开始时间
START_TIME=$(date +%s)

# 构建基础镜像
if [[ "$BUILD_ALL" == "true" || "$BUILD_BASE" == "true" ]]; then
    log_info "开始构建基础镜像..."
    
    BASE_IMAGE="bilstm-inference:base-$TAG"
    
    # 检查必要文件
    if [[ ! -f "BiLSTMPredictor.py" ]]; then
        log_error "找不到 BiLSTMPredictor.py 文件"
        exit 1
    fi
    
    if [[ ! -f "requirements.txt" ]]; then
        log_error "找不到 requirements.txt 文件"
        exit 1
    fi
    
    # 构建基础镜像
    log_info "构建基础镜像: $BASE_IMAGE"
    if docker build $NO_CACHE -t "$BASE_IMAGE" .; then
        log_success "基础镜像构建完成: $BASE_IMAGE"
    else
        log_error "基础镜像构建失败"
        exit 1
    fi
fi

# 构建推理镜像
if [[ "$BUILD_ALL" == "true" || "$BUILD_INFERENCE" == "true" ]]; then
    log_info "开始构建推理镜像..."
    
    INFERENCE_IMAGE="bilstm-inference:$TAG"
    BASE_IMAGE="bilstm-inference:base-$TAG"
    
    # 检查inference-image目录
    if [[ ! -d "inference-image" ]]; then
        log_error "找不到 inference-image 目录"
        exit 1
    fi
    
    cd inference-image
    
    # 检查必要文件
    if [[ ! -f "Dockerfile" ]]; then
        log_error "找不到 inference-image/Dockerfile 文件"
        exit 1
    fi
    
    # 构建推理镜像
    log_info "构建推理镜像: $INFERENCE_IMAGE"
    if docker build $NO_CACHE --build-arg BASE_IMAGE="$BASE_IMAGE" -t "$INFERENCE_IMAGE" .; then
        log_success "推理镜像构建完成: $INFERENCE_IMAGE"
    else
        log_error "推理镜像构建失败"
        exit 1
    fi
    
    cd ..
fi

# 运行测试
if [[ "$RUN_TEST" == "true" ]]; then
    log_info "开始运行测试..."
    
    INFERENCE_IMAGE="bilstm-inference:$TAG"
    
    # 健康检查测试
    log_info "执行健康检查..."
    if docker run --rm "$INFERENCE_IMAGE" health; then
        log_success "健康检查通过"
    else
        log_warning "健康检查失败"
    fi
    
    # 功能测试
    log_info "执行功能测试..."
    if docker run --rm "$INFERENCE_IMAGE" test; then
        log_success "功能测试通过"
    else
        log_warning "功能测试失败"
    fi
fi

# 推送镜像
if [[ "$PUSH_IMAGES" == "true" ]]; then
    log_info "开始推送镜像..."
    
    # 推送基础镜像
    if [[ "$BUILD_ALL" == "true" || "$BUILD_BASE" == "true" ]]; then
        BASE_IMAGE="bilstm-inference:base-$TAG"
        log_info "推送基础镜像: $BASE_IMAGE"
        if docker push "$BASE_IMAGE"; then
            log_success "基础镜像推送完成"
        else
            log_error "基础镜像推送失败"
        fi
    fi
    
    # 推送推理镜像
    if [[ "$BUILD_ALL" == "true" || "$BUILD_INFERENCE" == "true" ]]; then
        INFERENCE_IMAGE="bilstm-inference:$TAG"
        log_info "推送推理镜像: $INFERENCE_IMAGE"
        if docker push "$INFERENCE_IMAGE"; then
            log_success "推理镜像推送完成"
        else
            log_error "推理镜像推送失败"
        fi
    fi
fi

# 计算耗时
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))
MINUTES=$((DURATION / 60))
SECONDS=$((DURATION % 60))

# 显示结果
echo "=================================="
echo "🎉 构建完成!"
echo "=================================="
echo "⏱️  总耗时: ${MINUTES}分${SECONDS}秒"

# 显示构建的镜像
echo "📦 构建的镜像:"
if [[ "$BUILD_ALL" == "true" || "$BUILD_BASE" == "true" ]]; then
    echo "  🎯 基础镜像: bilstm-inference:base-$TAG"
fi
if [[ "$BUILD_ALL" == "true" || "$BUILD_INFERENCE" == "true" ]]; then
    echo "  🚀 推理镜像: bilstm-inference:$TAG"
fi

# 显示使用方法
echo ""
echo "🚀 快速启动:"
if [[ "$BUILD_ALL" == "true" || "$BUILD_INFERENCE" == "true" ]]; then
    echo "  docker run -p 5000:5000 bilstm-inference:$TAG"
fi

echo ""
echo "🧪 测试命令:"
if [[ "$BUILD_ALL" == "true" || "$BUILD_INFERENCE" == "true" ]]; then
    echo "  docker run --rm bilstm-inference:$TAG test"
    echo "  docker run --rm bilstm-inference:$TAG health"
fi

echo "==================================" 