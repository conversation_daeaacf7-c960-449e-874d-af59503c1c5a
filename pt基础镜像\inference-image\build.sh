#!/bin/bash

# BiLSTM推理镜像构建脚本
# 支持构建基础镜像和推理镜像

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
BASE_IMAGE_NAME="pytorch-inference"
BASE_IMAGE_TAG="base-latest"
INFERENCE_IMAGE_NAME="bilstm-inference"
INFERENCE_IMAGE_TAG="latest"
BUILD_MODE="inference"  # base, inference, both

# 显示帮助
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -m, --mode MODE        构建模式: base, inference, both (默认: inference)"
    echo "  -b, --base-tag TAG     基础镜像标签 (默认: base-latest)"
    echo "  -i, --inference-tag TAG 推理镜像标签 (默认: latest)"
    echo "  --no-cache             不使用缓存构建"
    echo "  --push                 构建后推送到仓库"
    echo "  -h, --help             显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 -m base             # 只构建基础镜像"
    echo "  $0 -m inference        # 只构建推理镜像"
    echo "  $0 -m both             # 构建两个镜像"
    echo "  $0 --no-cache --push   # 无缓存构建并推送"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -m|--mode)
                BUILD_MODE="$2"
                shift 2
                ;;
            -b|--base-tag)
                BASE_IMAGE_TAG="$2"
                shift 2
                ;;
            -i|--inference-tag)
                INFERENCE_IMAGE_TAG="$2"
                shift 2
                ;;
            --no-cache)
                NO_CACHE="--no-cache"
                shift
                ;;
            --push)
                PUSH_IMAGES="true"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                echo "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查Docker
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker未安装或未启动${NC}"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        echo -e "${RED}❌ Docker daemon未运行${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Docker检查通过${NC}"
}

# 检查文件
check_files() {
    local missing_files=()
    
    if [[ "$BUILD_MODE" == "base" || "$BUILD_MODE" == "both" ]]; then
        if [[ ! -f "../Dockerfile" ]]; then
            missing_files+=("../Dockerfile (基础镜像)")
        fi
    fi
    
    if [[ "$BUILD_MODE" == "inference" || "$BUILD_MODE" == "both" ]]; then
        if [[ ! -f "Dockerfile" ]]; then
            missing_files+=("Dockerfile (推理镜像)")
        fi
        if [[ ! -f "../bilstm_predictor.py" ]]; then
            missing_files+=("../bilstm_predictor.py")
        fi
        if [[ ! -f "start.py" ]]; then
            missing_files+=("start.py")
        fi
    fi
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        echo -e "${RED}❌ 缺少必要文件:${NC}"
        for file in "${missing_files[@]}"; do
            echo -e "   ${RED}✗${NC} $file"
        done
        exit 1
    fi
    
    echo -e "${GREEN}✅ 文件检查通过${NC}"
}

# 构建基础镜像
build_base_image() {
    local full_name="${BASE_IMAGE_NAME}:${BASE_IMAGE_TAG}"
    
    echo -e "${BLUE}🏗️ 构建基础镜像: $full_name${NC}"
    echo "===========================================""
    
    cd ..
    
    # 构建命令
    local build_cmd="docker build $NO_CACHE -t $full_name -f Dockerfile ."
    echo -e "${YELLOW}执行: $build_cmd${NC}"
    
    if eval $build_cmd; then
        echo -e "${GREEN}✅ 基础镜像构建成功: $full_name${NC}"
        
        # 显示镜像信息
        docker images $full_name
        
        # 推送镜像
        if [[ "$PUSH_IMAGES" == "true" ]]; then
            echo -e "${BLUE}📤 推送基础镜像...${NC}"
            docker push $full_name
        fi
        
        cd inference-image
        return 0
    else
        echo -e "${RED}❌ 基础镜像构建失败${NC}"
        cd inference-image
        return 1
    fi
}

# 构建推理镜像
build_inference_image() {
    local base_full_name="${BASE_IMAGE_NAME}:${BASE_IMAGE_TAG}"
    local inference_full_name="${INFERENCE_IMAGE_NAME}:${INFERENCE_IMAGE_TAG}"
    
    echo -e "${BLUE}🚀 构建推理镜像: $inference_full_name${NC}"
    echo -e "${BLUE}📦 模型文件将内嵌到镜像中${NC}"
    echo "=========================================="
    
    # 检查基础镜像是否存在
    if ! docker image inspect $base_full_name &> /dev/null; then
        echo -e "${YELLOW}⚠️ 基础镜像 $base_full_name 不存在，尝试构建...${NC}"
        if ! build_base_image; then
            echo -e "${RED}❌ 无法构建基础镜像${NC}"
            return 1
        fi
    fi
    
    # 构建命令
    local build_cmd="docker build $NO_CACHE \
        --build-arg BASE_IMAGE=$base_full_name \
        -t $inference_full_name \
        -f Dockerfile ."
    
    echo -e "${YELLOW}执行: $build_cmd${NC}"
    
    if eval $build_cmd; then
        echo -e "${GREEN}✅ 推理镜像构建成功: $inference_full_name${NC}"
        
        # 显示镜像信息
        docker images $inference_full_name
        
        # 推送镜像
        if [[ "$PUSH_IMAGES" == "true" ]]; then
            echo -e "${BLUE}📤 推送推理镜像...${NC}"
            docker push $inference_full_name
        fi
        
        return 0
    else
        echo -e "${RED}❌ 推理镜像构建失败${NC}"
        return 1
    fi
}

# 主函数
main() {
    echo -e "${BLUE}🏗️ BiLSTM推理镜像构建工具${NC}"
    echo "=========================================="
    
    # 解析参数
    parse_args "$@"
    
    # 显示配置
    echo -e "${YELLOW}📋 构建配置:${NC}"
    echo "   构建模式: $BUILD_MODE"
    echo "   基础镜像: ${BASE_IMAGE_NAME}:${BASE_IMAGE_TAG}"
    echo "   推理镜像: ${INFERENCE_IMAGE_NAME}:${INFERENCE_IMAGE_TAG}"
    [[ "$NO_CACHE" ]] && echo "   缓存: 禁用"
    [[ "$PUSH_IMAGES" ]] && echo "   推送: 启用"
    echo ""
    
    # 检查环境
    check_docker
    check_files
    
    # 执行构建
    case $BUILD_MODE in
        "base")
            build_base_image
            ;;
        "inference")
            build_inference_image
            ;;
        "both")
            if build_base_image; then
                build_inference_image
            else
                echo -e "${RED}❌ 基础镜像构建失败，跳过推理镜像构建${NC}"
                exit 1
            fi
            ;;
        *)
            echo -e "${RED}❌ 未知构建模式: $BUILD_MODE${NC}"
            show_help
            exit 1
            ;;
    esac
    
    echo ""
    echo -e "${GREEN}🎉 构建完成！${NC}"
    echo ""
    echo -e "${YELLOW}💡 下一步操作:${NC}"
    echo "   1. 测试镜像: docker run --rm ${INFERENCE_IMAGE_NAME}:${INFERENCE_IMAGE_TAG} test"
    echo "   2. 启动服务: docker run -p 5000:5000 ${INFERENCE_IMAGE_NAME}:${INFERENCE_IMAGE_TAG} server"
    echo "   3. Seldon部署: kubectl apply -f seldon-deployment.yaml"
}

# 运行主函数
main "$@" 