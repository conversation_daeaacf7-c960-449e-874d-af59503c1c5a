2025-05-21 16:02:35,478 - GCN_TF_Local - INFO - 日志将记录到控制台和文件: results\results_gcn_tf_local\gcn_tf_local_training_20250521_160235.log
2025-05-21 16:02:35,479 - GCN_TF_Local - INFO - GCN TensorFlow 本地模式训练脚本已初始化。
2025-05-21 16:02:35,479 - GCN_TF_Local - INFO - TensorFlow 版本: 2.13.0
2025-05-21 16:02:35,479 - GCN_TF_Local - INFO - 参数: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'result_dir': 'results\\results_gcn_tf_local', 'model_name': 'GCN_TF_Local_Model', 'label_column': 'Label', 'data_format': 'pkl', 'hidden_size': 64, 'num_layers': 2, 'dropout_rate': 0.5, 'num_epochs': 3, 'learning_rate': 0.005, 'optimizer': 'adam', 'loss_function': 'sparsecategoricalcrossentropy', 'edge_strategy': 'fully_connected', 'test_split_size': 0.2, 'random_state': 42, 'normalize': True, 'scaler_type': 'standard', 'handle_imbalance': False, 'stratify_split': True, 'numeric_impute_strategy': 'mean', 'categorical_impute_strategy': 'most_frequent', 'use_cv': False, 'cv_folds': 5, 'log_level': 'INFO', 'force_cpu': False}
2025-05-21 16:02:35,480 - GCN_TF_Local - INFO - 未找到GPU或强制使用CPU。
2025-05-21 16:02:35,481 - GCN_TF_Local - INFO - 从 E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl (格式: pkl) 加载数据
2025-05-21 16:02:35,873 - GCN_TF_Local - INFO - 数据加载为 DataFrame，形状: (692703, 85)
2025-05-21 16:02:35,986 - GCN_TF_Local - INFO - 特征形状: (692703, 84), 标签形状: (692703,)
2025-05-21 16:02:36,016 - GCN_TF_Local - INFO - 标签分布: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-21 16:02:36,043 - GCN_TF_Local - INFO - 选择标准训练/测试分割模式。
2025-05-21 16:02:36,502 - GCN_TF_Local - INFO - 已将特征中的无限值替换为NaN。
2025-05-21 16:02:36,621 - GCN_TF_Local - INFO - 处理特征中的缺失值。
2025-05-21 16:02:38,626 - GCN_TF_Local - INFO - 使用 'mean' 填充了数值型缺失值。
2025-05-21 16:02:39,082 - GCN_TF_Local - INFO - 使用 'most_frequent' 填充了类别型缺失值。
2025-05-21 16:02:39,184 - GCN_TF_Local - INFO - 标签已编码。6 个类别: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-21 16:02:39,261 - GCN_TF_Local - INFO - 编码类别特征: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-21 16:02:43,121 - GCN_TF_Local - INFO - 使用 standard 缩放器标准化特征。
2025-05-21 16:02:45,224 - GCN_TF_Local - INFO - 数据分割: X_train (554162, 84), y_train (554162,), X_test (138541, 84), y_test (138541,)
2025-05-21 16:02:45,272 - GCN_TF_Local - INFO - 训练/测试分割的预处理信息已保存: results\results_gcn_tf_local\GCN_TF_Local_Model_preprocessing_info_tf.json
2025-05-21 16:02:45,273 - GCN_TF_Local - INFO - 预处理后数据 (训练/测试分割): 输入大小=84, 类别数=6
2025-05-21 16:02:45,273 - GCN_TF_Local - WARNING - 节点数 (554162) 超过阈值 (20000)，全连接策略将使用等效单位矩阵进行归一化 (仅自环)。
2025-05-21 16:02:45,316 - GCN_TF_Local - INFO - 请求为 554162 个节点创建基于标识的邻接信息。
2025-05-21 16:02:45,317 - GCN_TF_Local - INFO - normalize_adjacency_matrix_tf_local: Identity requested. Returning placeholder and True flag.
2025-05-21 16:02:45,318 - GCN_TF_Local - INFO - 创建图数据字典: 特征 (554162, 84), 处理后的邻接张量 ((1, 1)), 是否为单位邻接: True, 标签 (554162,)
2025-05-21 16:02:45,318 - GCN_TF_Local - INFO - 训练图数据准备完成. 节点数: 554162, 边策略: fully_connected
2025-05-21 16:02:45,319 - GCN_TF_Local - WARNING - 节点数 (138541) 超过阈值 (20000)，全连接策略将使用等效单位矩阵进行归一化 (仅自环)。
2025-05-21 16:02:45,329 - GCN_TF_Local - INFO - 请求为 138541 个节点创建基于标识的邻接信息。
2025-05-21 16:02:45,330 - GCN_TF_Local - INFO - normalize_adjacency_matrix_tf_local: Identity requested. Returning placeholder and True flag.
2025-05-21 16:02:45,330 - GCN_TF_Local - INFO - 创建图数据字典: 特征 (138541, 84), 处理后的邻接张量 ((1, 1)), 是否为单位邻接: True, 标签 (138541,)
2025-05-21 16:02:45,331 - GCN_TF_Local - INFO - 测试图数据准备完成. 节点数: 138541, 边策略: fully_connected
2025-05-21 16:02:45,660 - GCN_TF_Local - CRITICAL - 发生意外严重错误: Exception encountered when calling layer 'gcn_hidden_1' (type GCNLayer).

name 'input_shape' is not defined

Call arguments received by layer 'gcn_hidden_1' (type GCNLayer):
  • inputs=['tf.Tensor(shape=(554162, 64), dtype=float32)', 'tf.Tensor(shape=(1, 1), dtype=float32)', 'tf.Tensor(shape=(), dtype=bool)']
  • training=False
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 1157, in gcn_train_tf_local_mode
    _ = model([train_graph_dict['x'], train_graph_dict['adj_tensor'], train_graph_dict['is_adj_identity']], training=False)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 285, in call
    x = gcn_layer_obj([x, adj_tensor_for_layers, is_adj_identity_flag_for_layers], training=training)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 228, in call
    if self._input_features_static_rank == 2 and tf.TensorShape(input_shape[0])[0] is not None:
NameError: Exception encountered when calling layer 'gcn_hidden_1' (type GCNLayer).

name 'input_shape' is not defined

Call arguments received by layer 'gcn_hidden_1' (type GCNLayer):
  • inputs=['tf.Tensor(shape=(554162, 64), dtype=float32)', 'tf.Tensor(shape=(1, 1), dtype=float32)', 'tf.Tensor(shape=(), dtype=bool)']
  • training=False
2025-05-21 16:02:45,665 - GCN_TF_Local - CRITICAL - 主程序中发生严重错误: Exception encountered when calling layer 'gcn_hidden_1' (type GCNLayer).

name 'input_shape' is not defined

Call arguments received by layer 'gcn_hidden_1' (type GCNLayer):
  • inputs=['tf.Tensor(shape=(554162, 64), dtype=float32)', 'tf.Tensor(shape=(1, 1), dtype=float32)', 'tf.Tensor(shape=(), dtype=bool)']
  • training=False
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 1278, in <module>
    gcn_train_tf_local_mode(parsed_args)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 1157, in gcn_train_tf_local_mode
    _ = model([train_graph_dict['x'], train_graph_dict['adj_tensor'], train_graph_dict['is_adj_identity']], training=False)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 285, in call
    x = gcn_layer_obj([x, adj_tensor_for_layers, is_adj_identity_flag_for_layers], training=training)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 228, in call
    if self._input_features_static_rank == 2 and tf.TensorShape(input_shape[0])[0] is not None:
NameError: Exception encountered when calling layer 'gcn_hidden_1' (type GCNLayer).

name 'input_shape' is not defined

Call arguments received by layer 'gcn_hidden_1' (type GCNLayer):
  • inputs=['tf.Tensor(shape=(554162, 64), dtype=float32)', 'tf.Tensor(shape=(1, 1), dtype=float32)', 'tf.Tensor(shape=(), dtype=bool)']
  • training=False
