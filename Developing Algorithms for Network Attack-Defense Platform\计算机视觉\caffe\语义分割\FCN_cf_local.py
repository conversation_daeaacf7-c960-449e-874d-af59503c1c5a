import os
import sys
import numpy as np
import caffe
from caffe import layers as L
from caffe import params as P
import json
import argparse
import pickle
from PIL import Image
import uuid
from minio import Minio
import requests
from tqdm import tqdm

# Constants
MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

def create_fcn_architecture(net, num_classes):
    """
    Create FCN architecture using Caffe layers
    """
    # VGG-16 base network
    net.conv1_1, relu1_1 = vgg_conv_relu(net.data, 64, pad=1)
    net.conv1_2, relu1_2 = vgg_conv_relu(relu1_1, 64, pad=1)
    net.pool1 = L.Pooling(relu1_2, pool=P.Pooling.MAX, kernel_size=2, stride=2)

    net.conv2_1, relu2_1 = vgg_conv_relu(net.pool1, 128, pad=1)
    net.conv2_2, relu2_2 = vgg_conv_relu(relu2_1, 128, pad=1)
    net.pool2 = L.Pooling(relu2_2, pool=P.Pooling.MAX, kernel_size=2, stride=2)

    net.conv3_1, relu3_1 = vgg_conv_relu(net.pool2, 256, pad=1)
    net.conv3_2, relu3_2 = vgg_conv_relu(relu3_1, 256, pad=1)
    net.conv3_3, relu3_3 = vgg_conv_relu(relu3_2, 256, pad=1)
    net.pool3 = L.Pooling(relu3_3, pool=P.Pooling.MAX, kernel_size=2, stride=2)

    net.conv4_1, relu4_1 = vgg_conv_relu(net.pool3, 512, pad=1)
    net.conv4_2, relu4_2 = vgg_conv_relu(relu4_1, 512, pad=1)
    net.conv4_3, relu4_3 = vgg_conv_relu(relu4_2, 512, pad=1)
    net.pool4 = L.Pooling(relu4_3, pool=P.Pooling.MAX, kernel_size=2, stride=2)

    net.conv5_1, relu5_1 = vgg_conv_relu(net.pool4, 512, pad=1)
    net.conv5_2, relu5_2 = vgg_conv_relu(relu5_1, 512, pad=1)
    net.conv5_3, relu5_3 = vgg_conv_relu(relu5_2, 512, pad=1)
    net.pool5 = L.Pooling(relu5_3, pool=P.Pooling.MAX, kernel_size=2, stride=2)

    # FCN-specific layers
    net.fc6 = L.Convolution(net.pool5, num_output=4096, kernel_size=7, pad=3)
    net.relu6 = L.ReLU(net.fc6, in_place=True)
    net.drop6 = L.Dropout(net.relu6, dropout_ratio=0.5, in_place=True)
    
    net.fc7 = L.Convolution(net.drop6, num_output=4096, kernel_size=1)
    net.relu7 = L.ReLU(net.fc7, in_place=True)
    net.drop7 = L.Dropout(net.relu7, dropout_ratio=0.5, in_place=True)
    
    net.score_fr = L.Convolution(net.drop7, num_output=num_classes, kernel_size=1)

    # Deconvolution and skip layers
    net.upscore2 = L.Deconvolution(net.score_fr,
        convolution_param=dict(num_output=num_classes, kernel_size=4, stride=2,
                             bias_term=False))
    
    net.score_pool4 = L.Convolution(net.pool4, num_output=num_classes, kernel_size=1)
    net.score_pool4c = L.Crop(net.score_pool4, net.upscore2)
    net.fuse_pool4 = L.Eltwise(net.upscore2, net.score_pool4c)

    net.upscore_pool4 = L.Deconvolution(net.fuse_pool4,
        convolution_param=dict(num_output=num_classes, kernel_size=4, stride=2,
                             bias_term=False))
    
    net.score_pool3 = L.Convolution(net.pool3, num_output=num_classes, kernel_size=1)
    net.score_pool3c = L.Crop(net.score_pool3, net.upscore_pool4)
    net.fuse_pool3 = L.Eltwise(net.upscore_pool4, net.score_pool3c)

    net.upscore8 = L.Deconvolution(net.fuse_pool3,
        convolution_param=dict(num_output=num_classes, kernel_size=16, stride=8,
                             bias_term=False))
    
    net.score = L.Crop(net.upscore8, net.data)
    
    # Loss layer
    net.loss = L.SoftmaxWithLoss(net.score, net.label)

    return net

def vgg_conv_relu(bottom, num_output, pad=0):
    """Helper for creating VGG-style conv + relu layers"""
    conv = L.Convolution(bottom, num_output=num_output, kernel_size=3, pad=pad,
                        param=[dict(lr_mult=1, decay_mult=1), dict(lr_mult=2, decay_mult=0)])
    relu = L.ReLU(conv, in_place=True)
    return conv, relu

class CaffeSegmentationDataLayer:
    def __init__(self, data_dir, batch_size, phase, input_size):
        self.data_dir = data_dir
        self.batch_size = batch_size
        self.phase = phase
        self.input_size = input_size
        
        # Setup paths
        self.images_dir = os.path.join(data_dir, 'JPEGImages')
        self.masks_dir = os.path.join(data_dir, 'SegmentationClass')
        
        # Get image list
        self.image_list = [f for f in os.listdir(self.images_dir) 
                          if f.endswith(('.jpg', '.jpeg', '.png'))]
        
        if phase == 'train':
            self.image_list = self.image_list[:int(0.8 * len(self.image_list))]
        else:
            self.image_list = self.image_list[int(0.8 * len(self.image_list)):]
            
    def reshape(self, bottom, top):
        # Reshape tops to fit batch_size
        top[0].reshape(self.batch_size, 3, self.input_size, self.input_size)  # data
        top[1].reshape(self.batch_size, 1, self.input_size, self.input_size)  # label

    def forward(self, bottom, top):
        # Load batch_size number of images and labels
        for i in range(self.batch_size):
            # Load image
            img_path = os.path.join(self.images_dir, self.image_list[i])
            img = Image.open(img_path).convert('RGB')
            img = img.resize((self.input_size, self.input_size))
            img_data = np.array(img).transpose((2, 0, 1))  # HWC -> CHW
            
            # Normalize image
            img_data = img_data / 255.0
            img_data = (img_data - np.array([0.485, 0.456, 0.406])[:, None, None]) / \
                      np.array([0.229, 0.224, 0.225])[:, None, None]
            
            # Load mask
            mask_name = self.image_list[i].replace('.jpg', '.png').replace('.jpeg', '.png')
            mask_path = os.path.join(self.masks_dir, mask_name)
            mask = Image.open(mask_path)
            mask = mask.resize((self.input_size, self.input_size), Image.NEAREST)
            mask_data = np.array(mask)[None, :, :]  # Add channel dimension
            
            # Assign to top blobs
            top[0].data[i] = img_data
            top[1].data[i] = mask_data

def create_solver(model_name, train_net_path, test_net_path):
    """Create Caffe solver prototxt"""
    solver_str = """
    train_net: "{train_net_path}"
    test_net: "{test_net_path}"
    test_iter: 50
    test_interval: 1000
    
    base_lr: 0.0001
    lr_policy: "step"
    gamma: 0.1
    stepsize: 20000
    max_iter: 100000
    
    display: 20
    momentum: 0.9
    weight_decay: 0.0005
    snapshot: 5000
    snapshot_prefix: "{model_name}"
    solver_mode: GPU
    """.format(train_net_path=train_net_path,
              test_net_path=test_net_path,
              model_name=model_name)
    
    return solver_str

def fcn_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """Train FCN model using Caffe"""
    input_size = job_params["input_size"]
    num_classes = job_params["num_classes"]
    batch_size = job_params["batch_size"]
    
    # Create network architecture
    train_net = caffe.NetSpec()
    train_net.data, train_net.label = L.Python(
        module='segmentation_data_layer',
        layer='CaffeSegmentationDataLayer',
        param_str=str(dict(data_dir=dataset["training_data_path"],
                          batch_size=batch_size,
                          phase='train',
                          input_size=input_size)),
        ntop=2)
    
    train_net = create_fcn_architecture(train_net, num_classes)
    
    # Create test network
    test_net = caffe.NetSpec()
    test_net.data, test_net.label = L.Python(
        module='segmentation_data_layer',
        layer='CaffeSegmentationDataLayer',
        param_str=str(dict(data_dir=dataset["training_data_path"],
                          batch_size=batch_size,
                          phase='test',
                          input_size=input_size)),
        ntop=2)
    
    test_net = create_fcn_architecture(test_net, num_classes)
    
    # Write network definitions to prototxt files
    with open(os.path.join(result_dir, 'train.prototxt'), 'w') as f:
        f.write(str(train_net.to_proto()))
    with open(os.path.join(result_dir, 'test.prototxt'), 'w') as f:
        f.write(str(test_net.to_proto()))
    
    # Create solver
    solver_path = os.path.join(result_dir, 'solver.prototxt')
    with open(solver_path, 'w') as f:
        f.write(create_solver(model_name,
                            os.path.join(result_dir, 'train.prototxt'),
                            os.path.join(result_dir, 'test.prototxt')))
    
    # Initialize solver
    caffe.set_mode_gpu()
    solver = caffe.SGDSolver(solver_path)
    
    # Copy weights from pretrained model if available
    if os.path.exists('/workspace/pretrained_model/vgg16.caffemodel'):
        solver.net.copy_from('/workspace/pretrained_model/vgg16.caffemodel')
    
    # Training loop
    print("Starting training...")
    for _ in tqdm(range(job_params["num_epochs"])):
        solver.step(1)
    
    # Save final model
    solver.net.save(os.path.join(result_dir, model_name + '.caffemodel'))
    return None, 0

def model_upload(result_dir, model_name):    
    minioClient = Minio(MINIO_URL,
                       access_key='AKIAIOSFODNN7EXAMPLE',
                       secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                       secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".caffemodel"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, 
                                    result_dir + "/" + model_name + ".caffemodel")
        result = {"source": source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1

def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "pytorch",
          "file_name": model_name+".pickle",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "FCN",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

    
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='pytorch FCN Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='FCN Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='FCN DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start FCN training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("FCN job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("FCN dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("FCN result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("FCN factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("FCN fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("FCN sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    
    # device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print("Step 1 FCN training:\n")
    result,ret_code = fcn_train(dataset,job_params, model["model_name"],result_dir,fit_params)
    if ret_code != 0:
        print("FCN train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()