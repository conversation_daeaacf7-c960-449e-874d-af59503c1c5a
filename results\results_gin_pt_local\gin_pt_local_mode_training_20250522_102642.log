2025-05-22 10:26:42,243 - GIN_PT_Local_Mode - INFO - 日志将记录到控制台和文件: results\results_gin_pt_local\gin_pt_local_mode_training_20250522_102642.log
2025-05-22 10:26:42,243 - GIN_PT_Local_Mode - INFO - GIN PyTorch 本地模式训练脚本已初始化。
2025-05-22 10:26:42,244 - GIN_PT_Local_Mode - INFO - PyTorch 版本: 2.4.1+cpu
2025-05-22 10:26:42,244 - GIN_PT_Local_Mode - INFO - 参数: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'result_dir': 'results\\results_gin_pt_local', 'model_name': 'GIN_PT_Local_Model', 'input_size': 84, 'hidden_size': 64, 'output_size': 6, 'num_layers': 2, 'dropout_rate': 0.5, 'num_epochs': 3, 'learning_rate': 0.001, 'batch_size': 32, 'edge_strategy': 'fully_connected', 'k_neighbors': 5, 'radius': 1.0, 'label_column': 'Label', 'data_format': 'pkl', 'test_split_size': 0.2, 'random_state': 42, 'normalize': True, 'scaler_type': 'standard', 'handle_imbalance': False, 'stratify_split': True, 'numeric_impute_strategy': 'mean', 'categorical_impute_strategy': 'most_frequent', 'use_cv': False, 'cv_folds': 5, 'log_level': 'INFO', 'force_cpu': False}
2025-05-22 10:26:42,245 - GIN_PT_Local_Mode - INFO - 使用设备: cpu
2025-05-22 10:26:42,249 - GIN_PT_Local_Mode - INFO - 从 E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl (格式: pkl) 加载数据
2025-05-22 10:26:42,634 - GIN_PT_Local_Mode - INFO - 数据加载为 DataFrame，形状: (692703, 85)
2025-05-22 10:26:42,928 - GIN_PT_Local_Mode - INFO - 特征形状: (692703, 84), 标签形状: (692703,)
2025-05-22 10:26:42,959 - GIN_PT_Local_Mode - INFO - 标签分布: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-22 10:26:43,118 - GIN_PT_Local_Mode - INFO - 模型将使用 GIN model input_size=1 (each node's feature dim), output_size(num_classes)=6
2025-05-22 10:26:43,118 - GIN_PT_Local_Mode - INFO - Original sample feature count (number of nodes per graph) is 84. User provided --input_size was 84
2025-05-22 10:26:43,119 - GIN_PT_Local_Mode - INFO - 选择标准训练/测试分割模式。
2025-05-22 10:26:43,600 - GIN_PT_Local_Mode - INFO - 已将特征中的无限值替换为NaN。
2025-05-22 10:26:43,690 - GIN_PT_Local_Mode - INFO - 处理特征中的缺失值。
2025-05-22 10:26:43,954 - GIN_PT_Local_Mode - INFO - 标签已编码。6个类别: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-22 10:26:43,995 - GIN_PT_Local_Mode - INFO - 编码类别特征: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-22 10:26:49,139 - GIN_PT_Local_Mode - INFO - 使用 standard 缩放器标准化/缩放特征。
2025-05-22 10:26:50,331 - GIN_PT_Local_Mode - INFO - 数据分割: X_train (554162, 84), y_train (554162,), X_test (138541, 84), y_test (138541,)
2025-05-22 10:26:50,415 - GIN_PT_Local_Mode - WARNING - GIN_PT: 'fully_connected' edge strategy is selected and training data size (554162) exceeds the limit (10000). Subsampling training data to 10000 samples to prevent OOM errors. For processing all data with 'fully_connected', ensure sufficient memory. Consider using '--edge_strategy knn' or '--edge_strategy identity_like' for large datasets.
2025-05-22 10:26:50,444 - GIN_PT_Local_Mode - WARNING - GIN_PT: 'fully_connected' edge strategy is selected and test data size (138541) exceeds the limit (2000). Subsampling test data to 2000 samples. 
2025-05-22 10:26:50,453 - GIN_PT_Local_Mode - INFO - 训练/测试分割的预处理信息已保存: results\results_gin_pt_local\GIN_PT_Local_Model_preprocessing_info_pt.json
2025-05-22 10:26:50,454 - GIN_PT_Local_Mode - INFO - 预处理后数据 (训练/测试分割): GIN model node feature dim=1, num_classes=6
2025-05-22 10:26:50,455 - GIN_PT_Local_Mode - INFO - 将 10000 个样本转换为图列表，样本内边策略: 'fully_connected'
2025-05-22 10:26:52,138 - GIN_PT_Local_Mode - INFO - 创建了 10000 个图数据对象。
2025-05-22 10:26:52,141 - GIN_PT_Local_Mode - INFO - PyG DataLoader创建成功。批大小: 32, Shuffle: True。包含 313 个批次。
2025-05-22 10:26:52,142 - GIN_PT_Local_Mode - INFO - 将 2000 个样本转换为图列表，样本内边策略: 'fully_connected'
2025-05-22 10:26:52,592 - GIN_PT_Local_Mode - INFO - 创建了 2000 个图数据对象。
2025-05-22 10:26:52,593 - GIN_PT_Local_Mode - INFO - PyG DataLoader创建成功。批大小: 32, Shuffle: False。包含 63 个批次。
2025-05-22 10:26:52,725 - GIN_PT_Local_Mode - INFO - 开始 GIN (PyTorch) 模型训练 (标准分割)。
2025-05-22 10:27:22,361 - GIN_PT_Local_Mode - INFO - 模型配置已保存: results\results_gin_pt_local\models\GIN_PT_Local_Model_best_config.json
2025-05-22 10:27:22,362 - GIN_PT_Local_Mode - INFO - 模型状态字典已保存: results\results_gin_pt_local\models\GIN_PT_Local_Model_best.pth
2025-05-22 10:27:22,362 - GIN_PT_Local_Mode - INFO - Epoch 1: 保存了新的最佳模型，验证损失: 0.2618
2025-05-22 10:27:22,362 - GIN_PT_Local_Mode - INFO - Epoch 1/3 - 训练损失: 0.5273, 训练准确率: 83.05%, 验证损失: 0.2618, 验证准确率: 91.80%
2025-05-22 10:27:51,511 - GIN_PT_Local_Mode - INFO - 模型配置已保存: results\results_gin_pt_local\models\GIN_PT_Local_Model_best_config.json
2025-05-22 10:27:51,512 - GIN_PT_Local_Mode - INFO - 模型状态字典已保存: results\results_gin_pt_local\models\GIN_PT_Local_Model_best.pth
2025-05-22 10:27:51,512 - GIN_PT_Local_Mode - INFO - Epoch 2: 保存了新的最佳模型，验证损失: 0.2528
2025-05-22 10:27:51,512 - GIN_PT_Local_Mode - INFO - Epoch 2/3 - 训练损失: 0.3359, 训练准确率: 88.41%, 验证损失: 0.2528, 验证准确率: 91.80%
2025-05-22 10:28:20,355 - GIN_PT_Local_Mode - INFO - 模型配置已保存: results\results_gin_pt_local\models\GIN_PT_Local_Model_best_config.json
2025-05-22 10:28:20,355 - GIN_PT_Local_Mode - INFO - 模型状态字典已保存: results\results_gin_pt_local\models\GIN_PT_Local_Model_best.pth
2025-05-22 10:28:20,356 - GIN_PT_Local_Mode - INFO - Epoch 3: 保存了新的最佳模型，验证损失: 0.1956
2025-05-22 10:28:20,356 - GIN_PT_Local_Mode - INFO - Epoch 3/3 - 训练损失: 0.3029, 训练准确率: 89.57%, 验证损失: 0.1956, 验证准确率: 93.55%
2025-05-22 10:28:20,371 - GIN_PT_Local_Mode - INFO - 模型配置已保存: results\results_gin_pt_local\models\GIN_PT_Local_Model_final_config.json
2025-05-22 10:28:20,371 - GIN_PT_Local_Mode - INFO - 模型状态字典已保存: results\results_gin_pt_local\models\GIN_PT_Local_Model_final.pth
2025-05-22 10:28:20,372 - GIN_PT_Local_Mode - INFO - 最终模型已保存到目录: results\results_gin_pt_local\models
2025-05-22 10:28:20,403 - GIN_PT_Local_Mode - INFO - 模型状态字典从 results\results_gin_pt_local\models\GIN_PT_Local_Model_best.pth 加载成功.
2025-05-22 10:28:20,404 - GIN_PT_Local_Mode - INFO - 已加载最佳保存模型进行测试: results\results_gin_pt_local\models\GIN_PT_Local_Model_best.pth
2025-05-22 10:28:20,404 - GIN_PT_Local_Mode - INFO - 评估 GIN (PyTorch) 模型在测试集上的表现。
2025-05-22 10:28:23,148 - GIN_PT_Local_Mode - INFO - 分类报告:
                  precision    recall  f1-score   support

          BENIGN       0.93      0.98      0.95      1269
   DoS GoldenEye       0.00      0.00      0.00        32
        DoS Hulk       0.96      0.95      0.95       661
DoS Slowhttptest       0.20      0.11      0.14        18
   DoS slowloris       0.00      0.00      0.00        20

        accuracy                           0.94      2000
       macro avg       0.42      0.41      0.41      2000
    weighted avg       0.91      0.94      0.92      2000

2025-05-22 10:28:24,022 - GIN_PT_Local_Mode - INFO - 混淆矩阵图已保存到 results\results_gin_pt_local\plots\GIN_PT_Local_Model_confusion_matrix.png
2025-05-22 10:28:24,023 - GIN_PT_Local_Mode - INFO - 详细报告已保存到: results\results_gin_pt_local\GIN_PT_Local_Model_classification_report.txt
2025-05-22 10:28:24,024 - GIN_PT_Local_Mode - INFO - 最终测试指标: 准确率=0.9355, 精确率=0.9084, 召回率=0.9355, F1=0.9215
2025-05-22 10:28:25,079 - GIN_PT_Local_Mode - INFO - 训练历史图表已保存到 results\results_gin_pt_local\plots\GIN_PT_Local_Model_training_history.png
2025-05-22 10:28:25,081 - GIN_PT_Local_Mode - INFO - GIN (PyTorch) 训练摘要已保存: results\results_gin_pt_local\GIN_PT_Local_Model_training_summary_pt.json
2025-05-22 10:28:25,081 - GIN_PT_Local_Mode - INFO - GIN PyTorch 本地模式训练脚本成功完成。
