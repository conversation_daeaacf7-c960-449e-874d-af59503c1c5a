{"model_type": "train_test_split", "model_path": "./results/rnn_local_results\\RNN_local_model_final.pth", "test_metrics": {"accuracy": 0.9993720270533633, "precision": 0.9993790232112396, "recall": 0.9993720270533633, "f1": 0.9993740840255871}, "training_history": {"train_loss": [0.008745065637567736, 0.0019845689858972934], "train_acc": [99.78147184397342, 99.94604465842119], "val_loss": [0.0035934349625193754, 0.0032335772867705345], "val_acc": [99.91627027378176, 99.93720270533633]}, "class_names": ["BENIGN", "DoS GoldenEye", "DoS Hulk", "DoS Slowhttptest", "DoS slowloris", "Heartbleed"], "preprocessing_info_path": "./results/rnn_local_results\\RNN_local_model_preprocessing_info.json"}