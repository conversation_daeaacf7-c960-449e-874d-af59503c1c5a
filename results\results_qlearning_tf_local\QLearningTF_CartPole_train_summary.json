{"env_name": "CartPole-v1", "model_name": "QLearningTF_CartPole", "num_episodes": 3, "final_avg_score_100": 22.0, "final_epsilon": 0.985074875, "hyperparameters_used": {"state_size": 4, "action_size": 2, "hidden_size": 64, "learning_rate": 0.001, "gamma": 0.99, "epsilon_current": 0.985074875, "epsilon_start": 1.0, "epsilon_end": 0.01, "epsilon_decay": 0.995, "buffer_size": 10000, "batch_size": 64, "random_seed": null, "training_info_summary": {"episodes_completed": 3, "total_steps": 66}}, "hyperparameters_file_path": "./results/qlearning_tf_local_run\\models_tf_local\\QLearningTF_CartPole_final_agent_ep3_hyperparams.json", "qnetwork_weights_path": "./results/qlearning_tf_local_run\\models_tf_local\\QLearningTF_CartPole_final_agent_ep3_qnetwork.weights.h5", "total_time_sec": 2.5521819591522217, "best_agent_weights_path_prefix": null}