{"args": {"env_name": "Pendulum-v1", "result_dir": "./results/ddpg_tf_local_run", "model_name": "DDPG_TF_Pendulum", "hidden_size": 256, "actor_lr": 0.0001, "critic_lr": 0.001, "gamma": 0.99, "tau": 0.001, "buffer_size": 50000, "batch_size": 64, "noise_sigma": 0.1, "num_episodes": 3, "max_steps_per_episode": 200, "random_seed": null, "save_freq": 20, "plot_freq": 10, "tf_log_level": "ERROR", "mode": "train", "test_model_prefix": null, "test_episodes": 5, "render_test": false}, "error": "Object of type float32 is not JSON serializable", "traceback": "Traceback (most recent call last):\n  File \"E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\强化学习\\DDPG_tf_local_mode.py\", line 486, in <module>\n    ddpg_train_tf_local(\n  File \"E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\强化学习\\DDPG_tf_local_mode.py\", line 410, in ddpg_train_tf_local\n    json.dump({'scores': scores_history, 'actor_losses': actor_loss_history, 'critic_losses': critic_loss_history}, f)\n  File \"c:\\ProgramData\\Anaconda3\\envs\\bypt\\lib\\json\\__init__.py\", line 179, in dump\n    for chunk in iterable:\n  File \"c:\\ProgramData\\Anaconda3\\envs\\bypt\\lib\\json\\encoder.py\", line 431, in _iterencode\n    yield from _iterencode_dict(o, _current_indent_level)\n  File \"c:\\ProgramData\\Anaconda3\\envs\\bypt\\lib\\json\\encoder.py\", line 405, in _iterencode_dict\n    yield from chunks\n  File \"c:\\ProgramData\\Anaconda3\\envs\\bypt\\lib\\json\\encoder.py\", line 325, in _iterencode_list\n    yield from chunks\n  File \"c:\\ProgramData\\Anaconda3\\envs\\bypt\\lib\\json\\encoder.py\", line 438, in _iterencode\n    o = _default(o)\n  File \"c:\\ProgramData\\Anaconda3\\envs\\bypt\\lib\\json\\encoder.py\", line 179, in default\n    raise TypeError(f'Object of type {o.__class__.__name__} '\nTypeError: Object of type float32 is not JSON serializable\n", "timestamp": "2025-05-23T14:58:44.613795"}