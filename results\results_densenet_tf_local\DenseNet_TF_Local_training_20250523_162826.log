2025-05-23 16:28:26,545 - DenseNet_TF_Local - INFO - Logging to file: ./results/results_densenet_tf_local\DenseNet_TF_Local_training_20250523_162826.log
2025-05-23 16:28:26,546 - DenseNet_TF_Local - INFO - DenseNet TensorFlow Local Mode started. Args: {'input_data_path': 'E:/data/Flowers Recognition/flowers', 'data_format': 'folder', 'pickle_image_path_col': 'image_path', 'pickle_label_col': 'label', 'pickle_base_data_dir': None, 'image_size': 224, 'model_name': 'DenseNet_TF_Local', 'densenet_variant': 'DenseNet121', 'num_classes': 0, 'use_imagenet_pretrained': False, 'pretrained_weights_path': None, 'mode': 'train', 'num_epochs': 3, 'batch_size': 32, 'learning_rate': 0.001, 'test_split_size': 0.2, 'use_cv': False, 'cv_folds': 5, 'trained_model_path': None, 'result_dir': './results/results_densenet_tf_local', 'random_seed': 42, 'log_level': 'INFO'}
2025-05-23 16:28:26,547 - DenseNet_TF_Local - INFO - No GPU found, using CPU.
2025-05-23 16:28:26,547 - DenseNet_TF_Local - INFO - Random seed set to: 42
2025-05-23 16:28:26,548 - DenseNet_TF_Local - INFO - Found classes from folder structure: ['daisy', 'dandelion', 'rose', 'sunflower', 'tulip']
2025-05-23 16:28:26,582 - DenseNet_TF_Local - INFO - Loaded 4317 image paths from 5 classes. Label mapping: {'daisy': 0, 'dandelion': 1, 'rose': 2, 'sunflower': 3, 'tulip': 4}
2025-05-23 16:28:26,583 - DenseNet_TF_Local - INFO - Inferred number of classes: 5 (['daisy', 'dandelion', 'rose', 'sunflower', 'tulip'])
2025-05-23 16:28:26,583 - DenseNet_TF_Local - INFO - --- Training Phase ---
2025-05-23 16:28:26,583 - DenseNet_TF_Local - INFO - Starting standard train/validation split training.
2025-05-23 16:28:26,588 - DenseNet_TF_Local - INFO - Data split: 3453 train, 864 validation samples.
2025-05-23 16:28:31,350 - DenseNet_TF_Local - INFO - Using Keras DenseNet variant: DenseNet121
2025-05-23 16:28:31,358 - DenseNet_TF_Local - INFO - Best model weights (based on val_loss) will be saved to: ./results/results_densenet_tf_local\DenseNet_TF_Local_best.weights.h5
2025-05-23 16:28:31,379 - DenseNet_TF_Local - INFO - Starting TensorFlow training for 3 epochs (initial_epoch=0).
