"""基于主成分分析的特征降维"""

import sys
import pandas as pd
from sklearn.decomposition import PCA

def pca_feature_reduction(input_file, target_column, n_components='all'):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - target_column: 目标变量的列名
    - n_components: 选择的主成分数量，可以是整数或'all'（表示选择所有主成分），默认为'all'
    """
    data = pd.read_csv(input_file)
    X = data.drop(target_column, axis=1)
    
    if n_components == 'all':
        n_components = X.shape[1]
    
    pca = PCA(n_components=int(n_components))
    X_new = pca.fit_transform(X)
    
    output_file = 'pca_reduced_features.csv'
    pd.DataFrame(X_new).to_csv(output_file, index=False)
    print(f"PCA feature reduction completed. Output saved to {output_file}")

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python pca_feature_reduction.py <input_file> <target_column> <n_components>")
        sys.exit(1)
    input_file, target_column, n_components = sys.argv[1], sys.argv[2], sys.argv[3]
    pca_feature_reduction(input_file, target_column, n_components)
