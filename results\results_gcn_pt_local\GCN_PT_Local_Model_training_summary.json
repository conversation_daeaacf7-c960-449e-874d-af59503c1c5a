{"model_type": "train_test_split", "model_config_path": "results\\results_gcn_pt_local\\models_gcn_local\\GCN_PT_Local_Model_best_config.json", "model_weights_path": "results\\results_gcn_pt_local\\models_gcn_local\\GCN_PT_Local_Model_best_weights.pth", "test_metrics": {"accuracy": 0.8562375036992659, "precision": 0.8351785842661797, "recall": 0.8562375036992659, "f1": 0.8380804167295607}, "training_history": {"train_loss": [1.8343530893325806, 1.098463773727417, 0.6119117736816406], "train_acc": [20.623933073722124, 76.7941504469812, 82.33296400691495], "val_loss": [1.136316180229187, 0.5208112597465515, 0.4165276288986206], "val_acc": [85.20365812286616, 85.06579279779993, 85.62375036992658]}, "class_names": ["BENIGN", "DoS GoldenEye", "DoS Hulk", "DoS Slowhttptest", "DoS slowloris", "Heartbleed"], "preprocessing_info_path": "results\\results_gcn_pt_local\\GCN_PT_Local_Model_preprocessing_info.json"}