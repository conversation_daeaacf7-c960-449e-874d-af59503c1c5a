import pickle
import requests
import joblib
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import numpy as np
import caffe
from caffe import layers as L
from caffe import params as P
from caffe import proto

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

def create_gru_net(batch_size, input_size, hidden_size, output_size, num_layers):
    """创建GRU网络结构"""
    net = caffe.NetSpec()
    
    # 输入层
    net.data = L.Input(shape=dict(dim=[batch_size, 1, input_size]))
    
    # GRU层
    prev_layer = net.data
    for i in range(num_layers):
        gru_name = f'gru{i+1}'
        net[gru_name] = L.GRU(prev_layer,
                             num_output=hidden_size,
                             weight_filler=dict(type='xavier'),
                             bias_filler=dict(type='constant', value=0),
                             name=gru_name)
        prev_layer = net[gru_name]
    
    # 全连接层
    net.fc = L.InnerProduct(prev_layer,
                           num_output=output_size,
                           weight_filler=dict(type='xavier'),
                           bias_filler=dict(type='constant', value=0))
    
    # 损失层
    net.label = L.Input(shape=dict(dim=[batch_size, output_size]))
    net.loss = L.EuclideanLoss(net.fc, net.label)
    
    return net.to_proto()

def write_net_to_file(net_proto, filename):
    """将网络结构写入文件"""
    with open(filename, 'w') as f:
        f.write(str(net_proto))

def write_solver_to_file(solver_proto, filename):
    """将solver配置写入文件"""
    with open(filename, 'w') as f:
        f.write(str(solver_proto))

def create_solver(model_file, learning_rate):
    """创建solver配置"""
    solver = proto.caffe_pb2.SolverParameter()
    solver.train_net = model_file
    solver.base_lr = learning_rate
    solver.lr_policy = "step"
    solver.gamma = 0.1
    solver.stepsize = 1000
    solver.max_iter = 4000
    solver.momentum = 0.9
    solver.weight_decay = 0.0005
    solver.display = 100
    solver.snapshot = 1000
    solver.snapshot_prefix = "gru_snapshot"
    solver.solver_type = proto.caffe_pb2.SolverParameter.ADAM
    return solver

def gru_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """GRU模型训练函数"""
    # 加载数据
    training_data_path = "/workspace/" + dataset["training_data_path"]
    with open(training_data_path, 'rb') as f:
        training_data, validation_data, test_data = pickle.load(f, encoding='bytes')
    train_x, train_y = training_data
    
    # 获取参数
    input_size = job_params["input_size"]
    hidden_size = job_params["hidden_size"]
    output_size = job_params["output_size"]
    num_layers = job_params["num_layers"]
    learning_rate = job_params["learning_rate"]
    batch_size = 32  # 设置批次大小
    
    # 创建网络结构
    net_proto = create_gru_net(batch_size, input_size, hidden_size, output_size, num_layers)
    model_file = os.path.join(result_dir, f"{model_name}_net.prototxt")
    write_net_to_file(net_proto, model_file)
    
    # 创建solver
    solver_proto = create_solver(model_file, learning_rate)
    solver_file = os.path.join(result_dir, f"{model_name}_solver.prototxt")
    write_solver_to_file(solver_proto, solver_file)
    
    # 初始化solver
    solver = caffe.AdamSolver(solver_file)
    
    # 训练模型
    num_samples = len(train_x)
    num_batches = num_samples // batch_size
    
    for _ in range(job_params["num_epochs"]):
        for i in range(num_batches):
            batch_x = train_x[i*batch_size:(i+1)*batch_size]
            batch_y = train_y[i*batch_size:(i+1)*batch_size]
            
            solver.net.blobs['data'].data[...] = batch_x.reshape(batch_size, 1, -1)
            solver.net.blobs['label'].data[...] = batch_y
            solver.step(1)
    
    # 保存模型
    model_path = os.path.join(result_dir, f"{model_name}.caffemodel")
    solver.net.save(model_path)
    print(f'GRU训练完成，模型保存到 {model_path}')
    
    return None, 0

def model_upload(result_dir, model_name):    
    minioClient = Minio(MINIO_URL,
                       access_key='AKIAIOSFODNN7EXAMPLE',
                       secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                       secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = f"{obj_name}/{model_name}.caffemodel"
        source = f"s3://mlss-mf/{obj_name}"
        res = minioClient.fput_object('mlss-mf', upload_path, f"{result_dir}/{model_name}.caffemodel")
        result = {"source": source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1

def model_register(model_name, source, group_id, headers):    
    params = {
        "model_name": model_name,
        "model_type": "caffe",
        "file_name": f"{model_name}.caffemodel",
        "s3_path": source,
        "group_id": int(float(group_id)),
        "training_id": model_name,
        "training_flag": 1,
    }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL, 
                     data=json.dumps(params),
                     headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
    
    params = {
        "factory_name": factory_name,
        "model_type": "GRU",
        "model_usage": "Regression"
    }
    r = requests.post(f"{MODEL_FACTORY_URL}{MODEL_PUSH_URL}/{model_version_id}",
                     data=json.dumps(params),
                     headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: user_id
    }
    return headers

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Caffe GRU Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                       help='GRU Job Params, set all params in dict')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                       help='GRU DataSet, set as a dict')
    parser.add_argument('--model', dest='model', type=json.loads,
                       help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                       help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                       help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                       help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,
                       default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                       help='sparkconf params')
    parser.add_argument('--nodehost', nargs='?', const=None, dest='nodehost', 
                       type=str, default="**************",
                       help='nodehost params')
    
    print("Start GRU training job, params:\n" + str(sys.argv) + "\n")
    args = parser.parse_args()
    
    job_params = args.job_params
    print("GRU job params:" + str(job_params) + "\n")
    dataset = args.dataset
    print("GRU dataSet:" + str(dataset) + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("GRU result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("GRU factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("GRU fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("GRU sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    
    if fit_params is None:
        fit_params = {}
     
    print("Step 1 GRU training:\n")
    result, ret_code = gru_train(dataset, job_params, model["model_name"], 
                                result_dir, fit_params)
    if ret_code != 0:
        print("GRU train err, stop job....\n")
        print("Error Msg:" + result + "\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    print("Step 2 Model Upload to MinIO:\n")
    result, ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model register...\n")
    
    print("Step 3 Model Register:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result, ret_code = model_register(model["model_name"], source, 
                                    model["group_id"], headers)
    if ret_code != 0:
        print("model register error, stop job..., err msg: " + result)
        sys.exit(-1)
    print("Register model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result, ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: " + result + "\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()