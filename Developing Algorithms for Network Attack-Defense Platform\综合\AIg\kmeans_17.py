"""K-Means 聚类"""

import sys
import pandas as pd
from sklearn.cluster import KMeans

def k_means_clustering(input_file, n_clusters=8):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - n_clusters: 聚类数量，默认为8
    """
    data = pd.read_csv(input_file)
    
    model = KMeans(n_clusters=n_clusters)
    model.fit(data)
    
    clusters = model.predict(data)
    
    output_file = 'k_means_clusters.csv'
    pd.DataFrame({'Cluster': clusters}).to_csv(output_file, index=False)
    print(f"K-Means clustering completed. Output saved to {output_file}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python k_means_clustering.py <input_file> <n_clusters>")
        sys.exit(1)
    input_file, n_clusters = sys.argv[1], sys.argv[2]
    k_means_clustering(input_file, int(n_clusters))
