import numpy as np
import caffe
import pickle
import requests
import joblib
import sys
import argparse
import json
from minio import Minio
import uuid
import os

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

def create_lstm_prototxt(input_size, hidden_size, output_size, num_layers, model_path):
    """
    创建LSTM的网络结构定义文件(train.prototxt)
    """
    with open(model_path + '/train.prototxt', 'w') as f:
        f.write("""name: "LSTM"
layer {
  name: "data"
  type: "Input"
  top: "data"
  input_param { shape: { dim: 1 dim: %d dim: %d } }
}
""" % (1, input_size))

        # 添加LSTM层
        for i in range(num_layers):
            bottom = "data" if i == 0 else f"lstm{i-1}"
            f.write("""
layer {
  name: "lstm%d"
  type: "LSTM"
  bottom: "%s"
  top: "lstm%d"
  recurrent_param {
    num_output: %d
    weight_filler {
      type: "xavier"
    }
    bias_filler {
      type: "constant"
    }
  }
}""" % (i, bottom, i, hidden_size))

        # 添加全连接层
        f.write("""
layer {
  name: "ip1"
  type: "InnerProduct"
  bottom: "lstm%d"
  top: "ip1"
  inner_product_param {
    num_output: %d
    weight_filler {
      type: "xavier"
    }
    bias_filler {
      type: "constant"
    }
  }
}
layer {
  name: "loss"
  type: "EuclideanLoss"
  bottom: "ip1"
  bottom: "label"
  top: "loss"
}""" % (num_layers-1, output_size))

def create_solver_prototxt(model_path, num_epochs, learning_rate):
    """
    创建solver配置文件
    """
    with open(model_path + '/solver.prototxt', 'w') as f:
        f.write("""net: "train.prototxt"
test_iter: 100
test_interval: 500
base_lr: %f
momentum: 0.9
weight_decay: 0.0005
lr_policy: "step"
gamma: 0.1
stepsize: 5000
display: 100
max_iter: %d
snapshot: 5000
snapshot_prefix: "lstm"
solver_mode: GPU""" % (learning_rate, num_epochs))

def lstm_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """
    训练LSTM模型
    """
    training_data_path = "/workspace/" + dataset["training_data_path"]
    with open(training_data_path, 'rb') as f:
        training_data, validation_data, test_data = pickle.load(f, encoding='bytes')
    
    train_x, train_y = training_data
    test_x, test_y = test_data

    # 获取参数
    input_size = job_params["input_size"]
    hidden_size = job_params["hidden_size"]
    output_size = job_params["output_size"]
    num_layers = job_params["num_layers"]
    num_epochs = job_params["num_epochs"]
    learning_rate = job_params["learning_rate"]

    # 创建模型定义和solver文件
    create_lstm_prototxt(input_size, hidden_size, output_size, num_layers, result_dir)
    create_solver_prototxt(result_dir, num_epochs, learning_rate)

    # 初始化solver
    solver = caffe.get_solver(result_dir + '/solver.prototxt')

    # 训练模型
    for _ in range(num_epochs):
        solver.step(1)

    # 保存模型
    solver.snapshot()
    
    # 将最终模型复制到指定位置
    final_model = result_dir + "/" + model_name + ".caffemodel"
    os.rename(result_dir + "/lstm_iter_" + str(num_epochs) + ".caffemodel", final_model)
    
    print(f'LSTM训练完成，模型保存到 {final_model}')
    return None, 0

def model_upload(result_dir, model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".caffemodel"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".caffemodel")
        result = {"source": source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1

def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "caffe",
          "file_name": model_name+".caffemodel",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
    }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL, data=json.dumps(params), headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "LSTM",
        "model_usage": "Sequence"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id), data=json.dumps(params), headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: user_id
    }
    return headers

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Caffe LSTM Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='LSTM Job Params, set all params in dict')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='LSTM DataSet, set as a dict')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,
                    default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost', nargs='?', const=None, dest='nodehost', 
                    type=str, default="**************",
                    help='nodehost params')     
    
    args = parser.parse_args()
    print("Start LSTM training job, params:\n" + str(sys.argv) + "\n")
    
    job_params = args.job_params
    dataset = args.dataset
    model = args.model
    result_dir = args.result_dir
    factory_name = args.factory_name
    fit_params = args.fit_params or {}
    sparkconf = json.loads(args.sparkconf)
    nodehost = args.nodehost
    
    print("Step 1 LSTM training:\n")
    result, ret_code = lstm_train(dataset, job_params, model["model_name"], 
                                result_dir, fit_params)
    if ret_code != 0:
        print("LSTM train err, stop job....\n")
        print("Error Msg:" + result + "\n")
        sys.exit(-1)
    
    print("Step 2 Model Upload to MinIO:\n")
    result, ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    
    print("Step 3 Model Register:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result, ret_code = model_register(model["model_name"], source, 
                                    model["group_id"], headers)
    if ret_code != 0:
        print("model register err, stop job....error msg: " + result)
        sys.exit(-1)
    
    print("Step 4 Model Push:\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result, ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....error msg: " + result + "\n")
    
    print("Job completed successfully\n")
    sys.exit(0)