import os
import sys
import numpy as np
import pickle
import caffe
from caffe import layers as L
from caffe import params as P
from caffe import proto
import json
from minio import Minio
import sys
import argparse
import uuid
import requests


MINIO_URL = "minio-prophecis.prophecis:9000"

MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"


MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

# 自定义GCN层的实现
class GCNLayer(caffe.Layer):
    def setup(self, bottom, top):
        # 检查输入blob的数量
        if len(bottom) != 2:
            raise Exception("Need two inputs: features and adjacency matrix")
            
        params = eval(self.param_str)
        self.in_features = params['in_features']
        self.out_features = params['out_features']
        
        # 初始化权重
        self.weights = np.random.normal(0, 0.01, (self.in_features, self.out_features))
        self.bias = np.zeros(self.out_features)

    def reshape(self, bottom, top):
        # 特征矩阵形状
        batch_size = bottom[0].data.shape[0]
        # 输出形状
        top[0].reshape(batch_size, self.out_features)

    def forward(self, bottom, top):
        features = bottom[0].data
        adj = bottom[1].data
        
        # GCN forward计算: ReLU(AXW + b)
        out = np.dot(adj, features)
        out = np.dot(out, self.weights) + self.bias
        top[0].data[...] = np.maximum(0, out)  # ReLU激活

    def backward(self, top, propagate_down, bottom):
        if propagate_down[0]:
            features = bottom[0].data
            adj = bottom[1].data
            
            # 计算梯度
            delta = top[0].diff
            delta_relu = delta * (bottom[0].data > 0)
            
            # 特征梯度
            bottom[0].diff[...] = np.dot(adj.T, np.dot(delta_relu, self.weights.T))
            # 权重梯度
            self.weights -= np.dot(features.T, delta_relu)
            self.bias -= np.sum(delta_relu, axis=0)

def create_gcn_model(model_name, in_features, hidden_features, out_features):
    # 创建模型定义
    model = proto.caffe_pb2.NetParameter()
    model.name = model_name

    # 数据层
    data_layer = model.layer.add()
    data_layer.name = 'data'
    data_layer.type = 'Input'
    data_layer.top.append('features')
    data_layer.input_param.shape.add()
    data_layer.input_param.shape[0].dim.extend([1, in_features])

    adj_layer = model.layer.add()
    adj_layer.name = 'adj'
    adj_layer.type = 'Input'
    adj_layer.top.append('adj')
    adj_layer.input_param.shape.add()
    adj_layer.input_param.shape[0].dim.extend([1, 1])

    # 第一个GCN层
    gcn1 = model.layer.add()
    gcn1.name = 'gcn1'
    gcn1.type = 'Python'
    gcn1.bottom.extend(['features', 'adj'])
    gcn1.top.append('gcn1_out')
    gcn1.python_param.module = 'gcn_layers'
    gcn1.python_param.layer = 'GCNLayer'
    gcn1.python_param.param_str = str({
        'in_features': in_features,
        'out_features': hidden_features
    })

    # 第二个GCN层
    gcn2 = model.layer.add()
    gcn2.name = 'gcn2'
    gcn2.type = 'Python'
    gcn2.bottom.extend(['gcn1_out', 'adj'])
    gcn2.top.append('gcn2_out')
    gcn2.python_param.module = 'gcn_layers'
    gcn2.python_param.layer = 'GCNLayer'
    gcn2.python_param.param_str = str({
        'in_features': hidden_features,
        'out_features': out_features
    })

    # Softmax层
    softmax = model.layer.add()
    softmax.name = 'prob'
    softmax.type = 'Softmax'
    softmax.bottom.append('gcn2_out')
    softmax.top.append('prob')

    return model

def gcn_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """
    Caffe版本的GCN训练函数
    """
    # 加载数据
    training_data_path = "/workspace/" + dataset["training_data_path"]
    with open(training_data_path, 'rb') as f:
        training_data, validation_data, test_data = pickle.load(f, encoding='bytes')
    train_x, train_adj, train_y = training_data
    test_x, test_adj, test_y = test_data

    # 创建模型
    in_features = job_params["in_features"]
    hidden_features = job_params["hidden_features"]
    out_features = job_params["out_features"]
    learning_rate = job_params["learning_rate"]
    num_epochs = job_params["num_epochs"]

    # 创建网络定义
    net = create_gcn_model(model_name, in_features, hidden_features, out_features)
    
    # 保存模型定义
    model_def_path = os.path.join(result_dir, f"{model_name}.prototxt")
    model_path = os.path.join(result_dir, f"{model_name}.caffemodel")
    
    with open(model_def_path, 'w') as f:
        f.write(str(net))
    
    # 创建求解器配置
    solver = proto.caffe_pb2.SolverParameter()
    solver.net = model_def_path
    solver.test_iter.append(1)
    solver.test_interval = 100
    solver.base_lr = learning_rate
    solver.momentum = 0.9
    solver.weight_decay = 0.0005
    solver.lr_policy = 'step'
    solver.gamma = 0.1
    solver.stepsize = 1000
    solver.display = 100
    solver.max_iter = num_epochs
    solver.snapshot = num_epochs
    solver.snapshot_prefix = os.path.join(result_dir, model_name)
    
    # 保存求解器配置
    solver_path = os.path.join(result_dir, f"{model_name}_solver.prototxt")
    with open(solver_path, 'w') as f:
        f.write(str(solver))
    
    # 初始化求解器
    caffe.set_mode_gpu()
    solver = caffe.get_solver(solver_path)
    
    # 训练模型
    for _ in range(num_epochs):
        solver.step(1)
    
    # 保存模型
    solver.net.save(model_path)
    print(f'GCN训练完成，模型保存到 {model_path}')
    
    return None, 0

def model_upload(result_dir,model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".pickle"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".pickle")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "pytorch",
          "file_name": model_name+".pickle",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "Logistic_Regression",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

    
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='pytorch GCN Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='GCN Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='GCN DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start GCN training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("GCN job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("GCN dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("GCN result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("GCN factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("GCN fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("GCN sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
     
    print("Step 1 GCN training:\n")
    result,ret_code = gcn_train(dataset,job_params, model["model_name"],result_dir,fit_params)
    if ret_code != 0:
        print("GCN train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()