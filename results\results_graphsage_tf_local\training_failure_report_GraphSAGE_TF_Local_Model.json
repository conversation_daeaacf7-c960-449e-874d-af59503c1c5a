{"model_name": "GraphSAGE_TF_Local_Model", "status": "failed", "error": "ufunc 'isinf' not supported for the input types, and the inputs could not be safely coerced to any supported types according to the casting rule ''safe''", "traceback": "Traceback (most recent call last):\n  File \"E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\图神经网络\\GraphSAGE_tf_local_mode.py\", line 985, in graphsage_train_tf_local_mode\n    X_train_p, X_test_p, y_train_p, y_test_p, preprocessing_info_main = advanced_preprocess(\n  File \"E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\图神经网络\\GraphSAGE_tf_local_mode.py\", line 238, in advanced_preprocess\n    initial_missing = X_df_proc.isnull().sum().sum() + np.isinf(X_df_proc.values).sum()\nTypeError: ufunc 'isinf' not supported for the input types, and the inputs could not be safely coerced to any supported types according to the casting rule ''safe''\n", "timestamp": "2025-05-23T14:08:24.718868"}