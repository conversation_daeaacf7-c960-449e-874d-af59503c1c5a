# BiLSTM推理服务基础镜像
# 基于BiLSTMPredictor.py优化版本
# 
# 镜像标签: bilstm-inference:base-latest
# 作者: AI算法团队
# 更新: 2025-06-26

FROM python:3.8.18-slim

# 设置镜像标签
LABEL maintainer="AI Algorithm Team" \
      version="2.0.0" \
      description="BiLSTM inference base image - optimized version" \
      purpose="bilstm-inference-service" \
      python_version="3.8.18"

# 设置工作目录
WORKDIR /app

# 配置阿里云镜像源
RUN echo "deb https://mirrors.aliyun.com/debian/ bullseye main" > /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian/ bullseye-updates main" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian-security bullseye-security main" >> /etc/apt/sources.list

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# 配置pip
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set global.trusted-host "mirrors.aliyun.com pypi.org files.pythonhosted.org"

# 升级pip
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制推理服务代码
COPY BiLSTMPredictor.py .

# 创建模型目录
RUN mkdir -p /app/models

# 暴露服务端口
EXPOSE 5000 9000

# 设置环境变量
ENV MODEL_NAME=PyTorchModel_v1
ENV API_TYPE=REST 
ENV SERVICE_TYPE=MODEL 
ENV MODEL_DIR=/app
ENV PYTHONPATH=/app:$PYTHONPATH

# 创建非root用户（安全最佳实践）
RUN groupadd -r aiuser && useradd -r -g aiuser aiuser \
    && chown -R aiuser:aiuser /app

# 切换到非root用户
USER aiuser

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import torch; print('🚀 BiLSTM服务就绪')" || exit 1

# 默认启动命令
CMD ["python", "BiLSTMPredictor.py", "--mode", "server"]
 