2025-06-13 14:37:24,401 - YOLOv8_local - INFO - Logging to file: ./results/results_yolov8_pt_local\YOLOv8_local_training_20250613_143724.log
2025-06-13 14:37:24,401 - YOLOv8_local - INFO - YOLOv8 PyTorch Local Mode. Args: {'data_path': './demo_data', 'dataset_type': 'folder', 'annotations_file': None, 'input_size': 640, 'model_name': 'YOLOv8_local', 'model_type': 'nano', 'pretrained_model_path': None, 'mode': 'train', 'num_epochs': 1, 'batch_size': 16, 'conf_thres': 0.25, 'iou_thres': 0.45, 'early_stopping_patience': 30, 'use_cv': False, 'cv_folds': 5, 'trained_model_path': None, 'test_data_path': None, 'annotations_file_test': None, 'result_dir': './results/results_yolov8_pt_local', 'random_seed': 42, 'force_cpu': False, 'log_level': 'INFO'}
2025-06-13 14:37:24,403 - YOLOv8_local - INFO - Using device: cpu
2025-06-13 14:37:24,405 - YOLOv8_local - INFO - Random seed set to: 42
2025-06-13 14:37:24,406 - YOLOv8_local - INFO - --- Training Mode ---
2025-06-13 14:37:24,406 - YOLOv8_local - INFO - Starting Standard Training...
2025-06-13 14:37:29,880 - YOLOv8_local - ERROR - Training failed: {'error': "Dataset 'Folder_YOLO/dataset.yaml' error  Dataset 'Folder_YOLO/dataset.yaml' images not found, missing path 'E:\\work\\\\\\2025.3\\bypt\\desin\\datasets\\Folder_YOLO\\images\\val'\nNote dataset download directory is 'E:\\work\\\\\\2025.3\\bypt\\desin\\datasets'. You can update this in 'C:\\Users\\<USER>\\AppData\\Roaming\\Ultralytics\\settings.json'"}
2025-06-13 14:37:29,881 - YOLOv8_local - ERROR - An error occurred in yolov8_main_local: Training failed: {'error': "Dataset 'Folder_YOLO/dataset.yaml' error  Dataset 'Folder_YOLO/dataset.yaml' images not found, missing path 'E:\\work\\\\\\2025.3\\bypt\\desin\\datasets\\Folder_YOLO\\images\\val'\nNote dataset download directory is 'E:\\work\\\\\\2025.3\\bypt\\desin\\datasets'. You can update this in 'C:\\Users\\<USER>\\AppData\\Roaming\\Ultralytics\\settings.json'"}
Traceback (most recent call last):
  File "卷积神经网络/yolov8_pt_local_mode.py", line 1171, in yolov8_main_local
    raise RuntimeError(f"Training failed: {train_results}")
RuntimeError: Training failed: {'error': "Dataset 'Folder_YOLO/dataset.yaml' error  Dataset 'Folder_YOLO/dataset.yaml' images not found, missing path 'E:\\work\\\\\\2025.3\\bypt\\desin\\datasets\\Folder_YOLO\\images\\val'\nNote dataset download directory is 'E:\\work\\\\\\2025.3\\bypt\\desin\\datasets'. You can update this in 'C:\\Users\\<USER>\\AppData\\Roaming\\Ultralytics\\settings.json'"}
2025-06-13 14:37:29,883 - YOLOv8_local - INFO - Failure report saved to ./results/results_yolov8_pt_local\YOLOv8_local_FAILURE_REPORT.json
