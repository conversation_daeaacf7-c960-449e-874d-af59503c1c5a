2025-05-23 14:08:21,664 - GraphSAGE_TF_Local_MainScript - INFO - Logging configured. Log file: ./results/graphsage_tf_local_experiment\GraphSAGE_TF_Local_Model_training_20250523_140821.log
2025-05-23 14:08:21,664 - GraphSAGE_TF_Local_MainScript - INFO - GraphSAGE TensorFlow Local Mode training started for model: GraphSAGE_TF_Local_Model
2025-05-23 14:08:21,665 - GraphSAGE_TF_Local_MainScript - INFO - Training Parameters:
{
  "input_file": "E:/data/\u7f51\u7edc\u653b\u9632\u535a\u5f08\u5e73\u53f0\u6570\u636e\u96c6/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl",
  "result_dir": "./results/graphsage_tf_local_experiment",
  "model_name": "GraphSAGE_TF_Local_Model",
  "hidden_size": 128,
  "num_layers": 2,
  "dropout_rate": 0.5,
  "num_epochs": 3,
  "learning_rate": 0.001,
  "early_stopping_patience": 10,
  "label_column": "Label",
  "data_format": "pkl",
  "feature_columns": null,
  "normalize_features": true,
  "scaler_type": "standard",
  "fill_missing_method": "mean",
  "stratify_split": true,
  "test_split_size": 0.2,
  "random_state": 42,
  "edge_strategy": "knn",
  "k_neighbors": 10,
  "use_cross_validation": false,
  "cv_folds": 5
}
2025-05-23 14:08:21,670 - GraphSAGE_TF_Local_MainScript - INFO - No GPU found, using CPU.
2025-05-23 14:08:21,670 - GraphSAGE_TF_Local_MainScript - INFO - TensorFlow version: 2.13.0
2025-05-23 14:08:21,670 - GraphSAGE_TF_Local.DataLoad - INFO - Loading data from E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl with format pkl
2025-05-23 14:08:22,079 - GraphSAGE_TF_Local.DataLoad - INFO - Data loaded as DataFrame with shape (692703, 85)
2025-05-23 14:08:23,558 - GraphSAGE_TF_Local.DataLoad - INFO - Features shape: (692703, 84), Labels shape: (692703,)
2025-05-23 14:08:23,589 - GraphSAGE_TF_Local.DataLoad - INFO - Label distribution: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-23 14:08:23,669 - GraphSAGE_TF_Local_MainScript - INFO - Data loaded: X shape (692703, 84), y shape (692703,)
2025-05-23 14:08:23,669 - GraphSAGE_TF_Local_MainScript - INFO - Loaded feature names: ['Flow ID', 'Source IP', 'Source Port', 'Destination IP', 'Destination Port', 'Protocol', 'Timestamp', 'Flow Duration', 'Total Fwd Packets', 'Total Backward Packets']...
2025-05-23 14:08:23,670 - GraphSAGE_TF_Local_MainScript - INFO - Inferred class names from data load: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-23 14:08:23,670 - GraphSAGE_TF_Local_MainScript - INFO - Using standard Train/Test split.
2025-05-23 14:08:24,716 - GraphSAGE_TF_Local_MainScript - ERROR - Error during GraphSAGE TF training (local_mode) for 'GraphSAGE_TF_Local_Model': ufunc 'isinf' not supported for the input types, and the inputs could not be safely coerced to any supported types according to the casting rule ''safe''
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GraphSAGE_tf_local_mode.py", line 985, in graphsage_train_tf_local_mode
    X_train_p, X_test_p, y_train_p, y_test_p, preprocessing_info_main = advanced_preprocess(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GraphSAGE_tf_local_mode.py", line 238, in advanced_preprocess
    initial_missing = X_df_proc.isnull().sum().sum() + np.isinf(X_df_proc.values).sum()
TypeError: ufunc 'isinf' not supported for the input types, and the inputs could not be safely coerced to any supported types according to the casting rule ''safe''
2025-05-23 14:08:24,720 - GraphSAGE_TF_Local_MainScript - ERROR - Critical error in GraphSAGE TF main script for 'GraphSAGE_TF_Local_Model': ufunc 'isinf' not supported for the input types, and the inputs could not be safely coerced to any supported types according to the casting rule ''safe''
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GraphSAGE_tf_local_mode.py", line 1131, in <module>
    results_data = graphsage_train_tf_local_mode(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GraphSAGE_tf_local_mode.py", line 985, in graphsage_train_tf_local_mode
    X_train_p, X_test_p, y_train_p, y_test_p, preprocessing_info_main = advanced_preprocess(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GraphSAGE_tf_local_mode.py", line 238, in advanced_preprocess
    initial_missing = X_df_proc.isnull().sum().sum() + np.isinf(X_df_proc.values).sum()
TypeError: ufunc 'isinf' not supported for the input types, and the inputs could not be safely coerced to any supported types according to the casting rule ''safe''
