2025-05-23 14:29:20,647 - DDPG_PT_Local_MainScript - INFO - Logging configured. Log file: ./results/ddpg_pt_local_experiment\DDPG_Pendulum_Local_training_20250523_142920.log
2025-05-23 14:29:20,647 - DDPG_PT_Local_MainScript - INFO - DDPG Local Training (DDPG_Pendulum_Local) Started on device: cpu
2025-05-23 14:29:20,647 - DDPG_PT_Local_MainScript - INFO - Training Parameters:
{
  "env_name": "Pendulum-v1",
  "result_dir": "./results/ddpg_pt_local_experiment",
  "model_name": "DDPG_Pendulum_Local",
  "hidden_size": 256,
  "actor_lr": 0.0001,
  "critic_lr": 0.001,
  "gamma": 0.99,
  "tau": 0.001,
  "buffer_size": 100000,
  "batch_size": 32,
  "noise_sigma": 0.1,
  "num_episodes": 2,
  "max_steps_per_episode": 500,
  "random_seed": null,
  "device_str": "cpu",
  "save_checkpoint_freq": 50,
  "eval_freq": 20,
  "logger": "<Logger DDPG_PT_Local_MainScript (INFO)>"
}
2025-05-23 14:29:20,661 - DDPG_PT_Local.Env - INFO - Using new Gymnasium API for Pendulum-v1
2025-05-23 14:29:20,661 - DDPG_PT_Local_MainScript - INFO - Environment 'Pendulum-v1': State Size=3, Action Size=1, Action Range=[-2.0, 2.0]
2025-05-23 14:29:23,888 - DDPG_PT_Local_MainScript - INFO - Episode 1/2 | Score: -976.71 | Avg Score (last 100): -976.71 | Duration: 1.39s
2025-05-23 14:29:25,496 - DDPG_PT_Local_MainScript - INFO - Episode 2/2 | Score: -1370.96 | Avg Score (last 100): -1173.84 | Duration: 1.61s
2025-05-23 14:29:25,937 - DDPG_PT_Local.Plot - INFO - Saved scores plot to ./results/ddpg_pt_local_experiment\plots\DDPG_Pendulum_Local_scores.png
2025-05-23 14:29:25,971 - DDPG_PT_Local_MainScript - INFO - Training completed. Final model saved to ./results/ddpg_pt_local_experiment\models\DDPG_Pendulum_Local_final_ep2.pth
2025-05-23 14:29:25,984 - DDPG_PT_Local_MainScript - ERROR - Critical error in DDPG main script for 'DDPG_Pendulum_Local': 'int' object has no attribute 'tolist'
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\DDPG_pt_local_mode.py", line 707, in <module>
    training_summary = ddpg_train_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\DDPG_pt_local_mode.py", line 546, in ddpg_train_local
    json.dump(convert_to_serializable_local(training_info), f, indent=4)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\DDPG_pt_local_mode.py", line 379, in convert_to_serializable_local
    return {convert_to_serializable_local(k): convert_to_serializable_local(v) for k, v in obj.items()}
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\DDPG_pt_local_mode.py", line 379, in <dictcomp>
    return {convert_to_serializable_local(k): convert_to_serializable_local(v) for k, v in obj.items()}
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\DDPG_pt_local_mode.py", line 386, in convert_to_serializable_local
    elif isinstance(obj, pd.Index if 'pd' in sys.modules else int): return obj.tolist()
AttributeError: 'int' object has no attribute 'tolist'
2025-05-23 14:29:25,987 - DDPG_PT_Local_MainScript - ERROR - Could not save failure report: 'int' object has no attribute 'tolist'
