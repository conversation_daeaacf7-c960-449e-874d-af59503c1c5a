import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

public class AlgorithmManager {
    private Map<String, String> algorithms;
    private String pythonPath;

    public AlgorithmManager(String pythonPath) {
        this.algorithms = new HashMap<>();
        this.pythonPath = pythonPath;
    }

    public void loadAlgorithm(String name, String scriptPath) {
        algorithms.put(name, scriptPath);
    }

    public String runAlgorithm(String name, String... args) throws Exception {
        String scriptPath = algorithms.get(name);
        if (scriptPath == null) {
            throw new IllegalArgumentException("Algorithm not found: " + name);
        }

        ProcessBuilder pb = new ProcessBuilder(pythonPath, scriptPath);
        for (String arg : args) {
            pb.command().add(arg);
        }

        Process process = pb.start();
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        StringBuilder output = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            output.append(line).append("\n");
        }

        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new RuntimeException("Algorithm execution failed with exit code: " + exitCode);
        }

        return output.toString();
    }
}