2025-05-21 16:28:28,150 - GCN_TF_Local - INFO - 日志将记录到控制台和文件: results\results_gcn_tf_local\gcn_tf_local_training_20250521_162828.log
2025-05-21 16:28:28,151 - GCN_TF_Local - INFO - GCN TensorFlow 本地模式训练脚本已初始化。
2025-05-21 16:28:28,151 - GCN_TF_Local - INFO - TensorFlow 版本: 2.13.0
2025-05-21 16:28:28,151 - GCN_TF_Local - INFO - 参数: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'result_dir': 'results\\results_gcn_tf_local', 'model_name': 'GCN_TF_Local_Model', 'label_column': 'Label', 'data_format': 'pkl', 'hidden_size': 64, 'num_layers': 2, 'dropout_rate': 0.5, 'num_epochs': 3, 'learning_rate': 0.005, 'optimizer': 'adam', 'loss_function': 'sparsecategoricalcrossentropy', 'edge_strategy': 'fully_connected', 'test_split_size': 0.2, 'random_state': 42, 'normalize': True, 'scaler_type': 'standard', 'handle_imbalance': False, 'stratify_split': True, 'numeric_impute_strategy': 'mean', 'categorical_impute_strategy': 'most_frequent', 'use_cv': False, 'cv_folds': 5, 'log_level': 'INFO', 'force_cpu': False}
2025-05-21 16:28:28,153 - GCN_TF_Local - INFO - 未找到GPU或强制使用CPU。
2025-05-21 16:28:28,153 - GCN_TF_Local - INFO - 从 E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl (格式: pkl) 加载数据
2025-05-21 16:28:28,584 - GCN_TF_Local - INFO - 数据加载为 DataFrame，形状: (692703, 85)
2025-05-21 16:28:28,707 - GCN_TF_Local - INFO - 特征形状: (692703, 84), 标签形状: (692703,)
2025-05-21 16:28:28,737 - GCN_TF_Local - INFO - 标签分布: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-21 16:28:28,766 - GCN_TF_Local - INFO - 选择标准训练/测试分割模式。
2025-05-21 16:28:29,266 - GCN_TF_Local - INFO - 已将特征中的无限值替换为NaN。
2025-05-21 16:28:29,376 - GCN_TF_Local - INFO - 处理特征中的缺失值。
2025-05-21 16:28:31,423 - GCN_TF_Local - INFO - 使用 'mean' 填充了数值型缺失值。
2025-05-21 16:28:31,915 - GCN_TF_Local - INFO - 使用 'most_frequent' 填充了类别型缺失值。
2025-05-21 16:28:32,017 - GCN_TF_Local - INFO - 标签已编码。6 个类别: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-21 16:28:32,103 - GCN_TF_Local - INFO - 编码类别特征: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-21 16:28:36,091 - GCN_TF_Local - INFO - 使用 standard 缩放器标准化特征。
2025-05-21 16:28:38,351 - GCN_TF_Local - INFO - 数据分割: X_train (554162, 84), y_train (554162,), X_test (138541, 84), y_test (138541,)
2025-05-21 16:28:38,421 - GCN_TF_Local - INFO - 训练/测试分割的预处理信息已保存: results\results_gcn_tf_local\GCN_TF_Local_Model_preprocessing_info_tf.json
2025-05-21 16:28:38,421 - GCN_TF_Local - INFO - 预处理后数据 (训练/测试分割): 输入大小=84, 类别数=6
2025-05-21 16:28:38,422 - GCN_TF_Local - WARNING - 节点数 (554162) 超过阈值 (20000)，全连接策略将使用等效单位矩阵进行归一化 (仅自环)。
2025-05-21 16:28:38,467 - GCN_TF_Local - INFO - 请求为 554162 个节点创建基于标识的邻接信息。
2025-05-21 16:28:38,468 - GCN_TF_Local - INFO - normalize_adjacency_matrix_tf_local: Identity requested. Returning placeholder and True flag.
2025-05-21 16:28:38,471 - GCN_TF_Local - INFO - 创建图数据字典: 特征 (554162, 84), 处理后的邻接张量 ((1, 1)), 是否为单位邻接: True, 标签 (554162,)
2025-05-21 16:28:38,472 - GCN_TF_Local - INFO - 训练图数据准备完成. 节点数: 554162, 边策略: fully_connected
2025-05-21 16:28:38,472 - GCN_TF_Local - WARNING - 节点数 (138541) 超过阈值 (20000)，全连接策略将使用等效单位矩阵进行归一化 (仅自环)。
2025-05-21 16:28:38,482 - GCN_TF_Local - INFO - 请求为 138541 个节点创建基于标识的邻接信息。
2025-05-21 16:28:38,483 - GCN_TF_Local - INFO - normalize_adjacency_matrix_tf_local: Identity requested. Returning placeholder and True flag.
2025-05-21 16:28:38,483 - GCN_TF_Local - INFO - 创建图数据字典: 特征 (138541, 84), 处理后的邻接张量 ((1, 1)), 是否为单位邻接: True, 标签 (138541,)
2025-05-21 16:28:38,484 - GCN_TF_Local - INFO - 测试图数据准备完成. 节点数: 138541, 边策略: fully_connected
2025-05-21 16:28:38,882 - GCN_TF_Local - INFO - Model: "GCNNetwork_TF_Local"
2025-05-21 16:28:38,882 - GCN_TF_Local - INFO - _________________________________________________________________
2025-05-21 16:28:38,883 - GCN_TF_Local - INFO -  Layer (type)                Output Shape              Param #   
2025-05-21 16:28:38,883 - GCN_TF_Local - INFO - =================================================================
2025-05-21 16:28:38,884 - GCN_TF_Local - INFO -  input_projection (Dense)    multiple                  5440      
2025-05-21 16:28:38,884 - GCN_TF_Local - INFO -                                                                  
2025-05-21 16:28:38,884 - GCN_TF_Local - INFO -  gcn_hidden_1 (GCNLayer)     multiple                  4160      
2025-05-21 16:28:38,884 - GCN_TF_Local - INFO -                                                                  
2025-05-21 16:28:38,885 - GCN_TF_Local - INFO -  gcn_output (GCNLayer)       multiple                  390       
2025-05-21 16:28:38,885 - GCN_TF_Local - INFO -                                                                  
2025-05-21 16:28:38,885 - GCN_TF_Local - INFO -  dropout (Dropout)           multiple                  0         
2025-05-21 16:28:38,886 - GCN_TF_Local - INFO -                                                                  
2025-05-21 16:28:38,886 - GCN_TF_Local - INFO - =================================================================
2025-05-21 16:28:38,888 - GCN_TF_Local - INFO - Total params: 9990 (39.02 KB)
2025-05-21 16:28:38,888 - GCN_TF_Local - INFO - Trainable params: 9990 (39.02 KB)
2025-05-21 16:28:38,888 - GCN_TF_Local - INFO - Non-trainable params: 0 (0.00 Byte)
2025-05-21 16:28:38,888 - GCN_TF_Local - INFO - _________________________________________________________________
2025-05-21 16:28:38,888 - GCN_TF_Local - INFO - 开始 GCN (TF) 模型训练 (标准分割)。
2025-05-21 16:28:38,909 - GCN_TF_Local - INFO - 最佳模型权重将基于 val_loss 保存到: results\results_gcn_tf_local\models_gcn_tf_local\GCN_TF_Local_Model_best.weights.h5
2025-05-21 16:28:38,909 - GCN_TF_Local - INFO - 开始 Keras GCN 模型训练，共 3 轮
2025-05-21 16:28:38,910 - GCN_TF_Local - ERROR - 值错误: Models passed to `fit` can only have `training` and the first argument in `call()` as positional arguments, found: ['adj_tensor_for_layers', 'is_adj_identity_flag_for_layers'].
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 1169, in gcn_train_tf_local_mode
    train_history = train_gcn_model_tf_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 718, in train_gcn_model_tf_local
    history = model.fit(
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 3751, in _check_call_args
    raise ValueError(
ValueError: Models passed to `fit` can only have `training` and the first argument in `call()` as positional arguments, found: ['adj_tensor_for_layers', 'is_adj_identity_flag_for_layers'].
2025-05-21 16:28:38,914 - GCN_TF_Local - CRITICAL - 主程序中发生严重错误: Models passed to `fit` can only have `training` and the first argument in `call()` as positional arguments, found: ['adj_tensor_for_layers', 'is_adj_identity_flag_for_layers'].
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 1283, in <module>
    gcn_train_tf_local_mode(parsed_args)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 1169, in gcn_train_tf_local_mode
    train_history = train_gcn_model_tf_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 718, in train_gcn_model_tf_local
    history = model.fit(
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 3751, in _check_call_args
    raise ValueError(
ValueError: Models passed to `fit` can only have `training` and the first argument in `call()` as positional arguments, found: ['adj_tensor_for_layers', 'is_adj_identity_flag_for_layers'].
