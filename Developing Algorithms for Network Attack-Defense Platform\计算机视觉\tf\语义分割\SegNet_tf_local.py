import tensorflow as tf
import numpy as np
import os
import pickle
import requests
import sys
import argparse
import json
from minio import Minio
import uuid
from PIL import Image
import xml.etree.ElementTree as ET
import pandas as pd
from tqdm.auto import tqdm

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class SegNet(tf.keras.Model):
    def __init__(self, num_classes, pretrained_model_path):
        super(SegNet, self).__init__()
        
        # 编码器层
        self.enc1 = self._make_encoder_block(3, 64, 2)
        self.enc2 = self._make_encoder_block(64, 128, 2)
        self.enc3 = self._make_encoder_block(128, 256, 3)
        self.enc4 = self._make_encoder_block(256, 512, 3)
        self.enc5 = self._make_encoder_block(512, 512, 3)
        
        # 解码器层
        self.dec5 = self._make_decoder_block(512, 512, 3)
        self.dec4 = self._make_decoder_block(512, 256, 3)
        self.dec3 = self._make_decoder_block(256, 128, 3)
        self.dec2 = self._make_decoder_block(128, 64, 2)
        self.dec1 = self._make_decoder_block(64, num_classes, 2, final_layer=True)
        
        # 加载预训练权重
        self._load_pretrained_weights(pretrained_model_path)
        
    def _make_encoder_block(self, in_channels, out_channels, n_convs):
        layers = []
        for i in range(n_convs):
            input_ch = in_channels if i == 0 else out_channels
            layers.extend([
                tf.keras.layers.Conv2D(out_channels, 3, padding='same'),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.ReLU()
            ])
        return tf.keras.Sequential(layers)
    
    def _make_decoder_block(self, in_channels, out_channels, n_convs, final_layer=False):
        layers = []
        for i in range(n_convs):
            input_ch = in_channels if i == 0 else out_channels
            output_ch = out_channels
            layers.extend([
                tf.keras.layers.Conv2DTranspose(output_ch, 3, padding='same'),
                tf.keras.layers.BatchNormalization(),
                tf.keras.layers.ReLU() if not final_layer or i < n_convs-1 else tf.keras.layers.Activation('linear')
            ])
        return tf.keras.Sequential(layers)
    
    def _load_pretrained_weights(self, pretrained_model_path):
        # 加载VGG16预训练权重
        vgg16 = tf.keras.applications.VGG16(weights=None)
        vgg16.load_weights(pretrained_model_path)
        
        # 将VGG16权重复制到编码器
        vgg_layers = [layer for layer in vgg16.layers if isinstance(layer, tf.keras.layers.Conv2D)]
        encoder_blocks = [self.enc1, self.enc2, self.enc3, self.enc4, self.enc5]
        
        layer_idx = 0
        for block in encoder_blocks:
            for layer in block.layers:
                if isinstance(layer, tf.keras.layers.Conv2D):
                    layer.set_weights(vgg_layers[layer_idx].get_weights())
                    layer_idx += 1
    
    def call(self, x):
        # 保存编码器的池化索引
        indices = []
        unpool_shapes = []
        
        # 编码过程
        def encode_block(x, block):
            x = block(x)
            unpool_shapes.append(tf.shape(x))
            x = tf.keras.layers.MaxPooling2D(padding='same')(x)
            return x
        
        # 编码器前向传播
        x = encode_block(x, self.enc1)
        x = encode_block(x, self.enc2)
        x = encode_block(x, self.enc3)
        x = encode_block(x, self.enc4)
        x = encode_block(x, self.enc5)
        
        # 解码过程
        def decode_block(x, block, output_shape):
            x = tf.keras.layers.UpSampling2D()(x)
            x = block(x)
            return x
        
        # 解码器前向传播
        x = decode_block(x, self.dec5, unpool_shapes.pop())
        x = decode_block(x, self.dec4, unpool_shapes.pop())
        x = decode_block(x, self.dec3, unpool_shapes.pop())
        x = decode_block(x, self.dec2, unpool_shapes.pop())
        x = decode_block(x, self.dec1, unpool_shapes.pop())
        
        return x

class UniversalImageDataset(tf.keras.utils.Sequence):
    def __init__(self, data_dir, batch_size, image_size, dataset_type='folder', annotations_file=None, is_training=True):
        self.data_dir = data_dir
        self.batch_size = batch_size
        self.image_size = image_size
        self.dataset_type = dataset_type
        self.annotations_file = annotations_file
        self.is_training = is_training
        
        if dataset_type == 'pickle':
            self.image_data, self.mask_data = self.load_pickle_data()
        elif dataset_type == 'folder':
            self.image_paths, self.mask_paths = self.load_segmentation_data()
        else:
            raise ValueError("Unsupported dataset type")
            
        self.on_epoch_end()
    
    def load_pickle_data(self):
        with open(self.annotations_file, 'rb') as f:
            data = pickle.load(f)
        return data['images'], data['masks']
    
    def load_segmentation_data(self):
        image_paths = []
        mask_paths = []
        
        images_dir = os.path.join(self.data_dir, 'JPEGImages')
        masks_dir = os.path.join(self.data_dir, 'SegmentationClass')
        
        for img_name in os.listdir(images_dir):
            if img_name.endswith(('.jpg', '.jpeg', '.png')):
                img_path = os.path.join(images_dir, img_name)
                mask_name = img_name.replace('.jpg', '.png').replace('.jpeg', '.png')
                mask_path = os.path.join(masks_dir, mask_name)
                
                if os.path.exists(mask_path):
                    image_paths.append(img_path)
                    mask_paths.append(mask_path)
        
        return image_paths, mask_paths
    
    def __len__(self):
        return len(self.image_paths) // self.batch_size
    
    def on_epoch_end(self):
        if self.is_training:
            indices = np.arange(len(self.image_paths))
            np.random.shuffle(indices)
            self.indices = indices
    
    def __getitem__(self, idx):
        batch_indices = self.indices[idx * self.batch_size:(idx + 1) * self.batch_size]
        
        batch_images = []
        batch_masks = []
        
        for i in batch_indices:
            # 加载和预处理图像
            if self.dataset_type == 'pickle':
                image = self.image_data[i]
                mask = self.mask_data[i]
            else:
                image = tf.keras.preprocessing.image.load_img(
                    self.image_paths[i],
                    target_size=(self.image_size, self.image_size)
                )
                mask = tf.keras.preprocessing.image.load_img(
                    self.mask_paths[i],
                    target_size=(self.image_size, self.image_size),
                    color_mode='grayscale'
                )
            
            # 转换为数组
            image = tf.keras.preprocessing.image.img_to_array(image)
            mask = tf.keras.preprocessing.image.img_to_array(mask)
            
            # 标准化
            image = image / 255.0
            mask = mask / 255.0
            
            batch_images.append(image)
            batch_masks.append(mask)
        
        return np.array(batch_images), np.array(batch_masks)

def prepare_data(data_dir, dataset_type, batch_size, image_size, annotations_file=None):
    # 确保输入图像尺寸是32的倍数
    if image_size % 32 != 0:
        image_size = ((image_size // 32) + 1) * 32
        print(f"调整输入图像尺寸为: {image_size}x{image_size}")
    
    # 创建训练集和测试集
    train_dataset = UniversalImageDataset(
        data_dir,
        batch_size,
        image_size,
        dataset_type=dataset_type,
        annotations_file=annotations_file,
        is_training=True
    )
    
    test_dataset = UniversalImageDataset(
        data_dir,
        batch_size,
        image_size,
        dataset_type=dataset_type,
        annotations_file=annotations_file,
        is_training=False
    )
    
    return train_dataset, test_dataset

def train_model(train_dataset, model, num_epochs, learning_rate):
    # 配置优化器和损失函数
    optimizer = tf.keras.optimizers.Adam(learning_rate=learning_rate)
    loss_fn = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True)
    
    # 设置训练指标
    train_loss = tf.keras.metrics.Mean(name='train_loss')
    train_accuracy = tf.keras.metrics.SparseCategoricalAccuracy(name='train_accuracy')
    
    @tf.function
    def train_step(images, masks):
        with tf.GradientTape() as tape:
            predictions = model(images, training=True)
            loss = loss_fn(masks, predictions)
        
        gradients = tape.gradient(loss, model.trainable_variables)
        optimizer.apply_gradients(zip(gradients, model.trainable_variables))
        
        train_loss(loss)
        train_accuracy(masks, predictions)
    
    for epoch in range(num_epochs):
        train_loss.reset_states()
        train_accuracy.reset_states()
        
        for images, masks in tqdm(train_dataset, desc=f"Epoch {epoch+1}/{num_epochs}"):
            train_step(images, masks)
        
        print(f'Epoch {epoch+1}, Loss: {train_loss.result():.4f}, Accuracy: {train_accuracy.result():.4f}')

def test_model(test_dataset, model):
    test_loss = tf.keras.metrics.Mean(name='test_loss')
    test_accuracy = tf.keras.metrics.SparseCategoricalAccuracy(name='test_accuracy')
    loss_fn = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True)
    
    for images, masks in tqdm(test_dataset, desc="Testing"):
        predictions = model(images, training=False)
        loss = loss_fn(masks, predictions)
        
        test_loss(loss)
        test_accuracy(masks, predictions)
    
    print(f'Test Loss: {test_loss.result():.4f}, Test Accuracy: {test_accuracy.result():.4f}')

def segnet_train(dataset, job_params, model_name, result_dir, fit_params=None):
    # 准备训练参数
    input_size = job_params["input_size"]
    num_classes = job_params["num_classes"]
    dataset_type = job_params["dataset_type"]
    num_epochs = job_params["num_epochs"]
    learning_rate = job_params["learning_rate"]
    batch_size = job_params["batch_size"]
    
    # 准备数据
    training_data_path = "/workspace/" + dataset["training_data_path"]
    train_dataset, test_dataset = prepare_data(
        training_data_path,
        dataset_type,
        batch_size,
        input_size,
        annotations_file=None
    )
    
    # 创建模型
    pretrained_model_path = "/workspace/pretrained_model/vgg16_weights.h5"
    model = SegNet(num_classes, pretrained_model_path)
    
    try:
        # 训练模型
        train_model(train_dataset, model, num_epochs, learning_rate)
        test_model(test_dataset, model)
        
        # 保存模型
        model.save_weights(os.path.join(result_dir, f"{model_name}.h5"))
        print(f'训练完成，模型保存到 {result_dir}/{model_name}.h5')
        
    except Exception as e:
        print(f"训练过程中发生错误: {e}")
        raise e
    
    return None, 0

def model_upload(result_dir,model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".h5"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".h5")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "TENSORFLOW",
          "file_name": model_name+".h5",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "SegNet",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

    
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='tensorflow SegNet Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='SegNet Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='SegNet DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start SegNet training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("SegNet job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("SegNet dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("SegNet result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("SegNet factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("SegNet fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("SegNet sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    
    device = '/GPU:0' if tf.config.list_physical_devices('GPU') else '/CPU:0'
    print("Step 1 SegNet training:\n")
    result,ret_code = segnet_train(dataset,job_params, model["model_name"],result_dir,fit_params,device)
    if ret_code != 0:
        print("SegNet train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()