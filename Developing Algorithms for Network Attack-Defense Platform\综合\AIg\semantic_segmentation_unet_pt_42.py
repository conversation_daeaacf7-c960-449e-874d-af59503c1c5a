"""语义分割 (U-Net)"""

import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import datasets, transforms
from torchvision.models.segmentation import fcn_resnet50

def semantic_segmentation_unet(data_dir, epochs, batch_size):
    """
    参数说明：
    - data_dir: 数据目录
    - epochs: 训练轮数
    - batch_size: 批次大小
    """
    transform = transforms.Compose([
        transforms.Resize((256, 256)),
        transforms.ToTensor(),
    ])
    train_dataset = datasets.ImageFolder(data_dir, transform=transform)
    train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, shuffle=True)

    model = fcn_resnet50(pretrained=True)
    model.classifier[4] = nn.Conv2d(512, len(train_dataset.classes), kernel_size=1)
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    for epoch in range(epochs):
        for inputs, labels in train_loader:
            outputs = model(inputs)['out']
            loss = criterion(outputs, labels)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
        print(f"Epoch {epoch+1}/{epochs}, Loss: {loss.item()}")

    print("U-Net training completed")

if __name__ == "__main__":
    data_dir = 'E:/data/Flowers Recognition/flowers'
    epochs = 10
    batch_size = 32

    semantic_segmentation_unet(data_dir, epochs, batch_size)
