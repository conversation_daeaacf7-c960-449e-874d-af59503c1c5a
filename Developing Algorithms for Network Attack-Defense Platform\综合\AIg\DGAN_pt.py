import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
from torchvision import datasets, transforms
import torchvision.transforms.functional as TF
from torchvision import models
import torchvision
from PIL import Image
import os
import numpy as np
from tqdm.auto import tqdm
import xml.etree.ElementTree as ET

class VOC2007Dataset(Dataset):
    def __init__(self, voc_root, image_size=64, transform=None):
        """
        VOC2007数据集加载器
        Args:
            voc_root: VOC2007数据集根目录
            image_size: 目标图像大小
            transform: 图像变换
        """
        self.voc_root = voc_root
        self.image_size = image_size
        self.images_dir = os.path.join(voc_root, "JPEGImages")
        
        if transform is None:
            self.transform = transforms.Compose([
                transforms.Resize((image_size, image_size)),
                transforms.RandomHorizontalFlip(),
                transforms.ToTensor(),
                transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])
            ])
        else:
            self.transform = transform
            
        self.image_paths = []
        for img_name in os.listdir(self.images_dir):
            if img_name.endswith(('.jpg', '.jpeg', '.png')):
                self.image_paths.append(os.path.join(self.images_dir, img_name))
                
        print(f"找到 {len(self.image_paths)} 张图像")

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        image = Image.open(img_path).convert('RGB')
        
        if self.transform:
            image = self.transform(image)
            
        return image

class DGANGenerator(nn.Module):
    def __init__(self, latent_dim, image_channels=3):
        super(DGANGenerator, self).__init__()
        self.latent_dim = latent_dim
        
        # 初始特征图大小为 4x4
        self.init_size = 4
        self.l1 = nn.Sequential(
            nn.Linear(latent_dim, 256 * self.init_size ** 2)
        )

        # 使用残差块和更深的架构
        self.conv_blocks = nn.Sequential(
            # 4x4 -> 8x8
            nn.BatchNorm2d(256),
            nn.Upsample(scale_factor=2),
            nn.Conv2d(256, 256, 3, stride=1, padding=1),
            nn.BatchNorm2d(256),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Conv2d(256, 256, 3, stride=1, padding=1),
            nn.BatchNorm2d(256),
            nn.LeakyReLU(0.2, inplace=True),
            
            # 8x8 -> 16x16
            nn.Upsample(scale_factor=2),
            nn.Conv2d(256, 128, 3, stride=1, padding=1),
            nn.BatchNorm2d(128),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Conv2d(128, 128, 3, stride=1, padding=1),
            nn.BatchNorm2d(128),
            nn.LeakyReLU(0.2, inplace=True),
            
            # 16x16 -> 32x32
            nn.Upsample(scale_factor=2),
            nn.Conv2d(128, 64, 3, stride=1, padding=1),
            nn.BatchNorm2d(64),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Conv2d(64, 64, 3, stride=1, padding=1),
            nn.BatchNorm2d(64),
            nn.LeakyReLU(0.2, inplace=True),
            
            # 32x32 -> 64x64
            nn.Upsample(scale_factor=2),
            nn.Conv2d(64, 32, 3, stride=1, padding=1),
            nn.BatchNorm2d(32),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Conv2d(32, image_channels, 3, stride=1, padding=1),
            nn.Tanh()
        )

    def forward(self, z):
        out = self.l1(z)
        out = out.view(out.shape[0], 256, self.init_size, self.init_size)
        img = self.conv_blocks(out)
        return img

class DGANDiscriminator(nn.Module):
    def __init__(self, image_channels=3):
        super(DGANDiscriminator, self).__init__()

        def discriminator_block(in_filters, out_filters, bn=True, kernel_size=4):
            block = [
                nn.Conv2d(in_filters, out_filters, kernel_size, 2, 1),
                nn.LeakyReLU(0.2, inplace=True),
                nn.Dropout2d(0.25)
            ]
            if bn:
                block.append(nn.BatchNorm2d(out_filters))
            return block

        self.model = nn.Sequential(
            *discriminator_block(image_channels, 32, bn=False),  # 64x64 -> 32x32
            *discriminator_block(32, 64),                        # 32x32 -> 16x16
            *discriminator_block(64, 128),                       # 16x16 -> 8x8
            *discriminator_block(128, 256),                      # 8x8 -> 4x4
        )

        # 特征匹配层
        self.features = nn.Sequential(
            nn.Linear(256 * 4 * 4, 1024),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout(0.3)
        )
        
        # 判别器输出层
        self.adv_layer = nn.Sequential(
            nn.Linear(1024, 1),
            nn.Sigmoid()
        )

    def forward(self, img):
        features = self.model(img)
        features = features.view(features.shape[0], -1)
        features = self.features(features)
        validity = self.adv_layer(features)
        return validity, features
    

def prepare_voc_data(voc_root, batch_size, image_size):
    """准备VOC2007数据集"""
    dataset = VOC2007Dataset(voc_root, image_size=image_size)
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=2,
        pin_memory=True
    )
    return dataloader

class DGANLoss:
    def __init__(self, device, lambda_fm=10.0):
        self.adversarial_loss = nn.BCELoss()
        self.feature_matching_loss = nn.MSELoss()
        self.device = device
        self.lambda_fm = lambda_fm
        
    def add_noise(self, tensor, noise_factor=0.1):
        noise = torch.randn_like(tensor) * noise_factor
        return tensor + noise
        
    def discriminator_loss(self, real_validity, fake_validity, real_features, fake_features):
        real_label = torch.ones(real_validity.size()).to(self.device)
        fake_label = torch.zeros(fake_validity.size()).to(self.device)
        
        # 添加标签平滑
        real_label = real_label * 0.9
        
        real_loss = self.adversarial_loss(real_validity, real_label)
        fake_loss = self.adversarial_loss(fake_validity, fake_label)
        
        # 特征匹配损失
        fm_loss = self.feature_matching_loss(real_features, fake_features)
        
        d_loss = (real_loss + fake_loss) / 2 + self.lambda_fm * fm_loss
        return d_loss
    
    def generator_loss(self, fake_validity, real_features, fake_features):
        real_label = torch.ones(fake_validity.size()).to(self.device)
        
        # 生成器对抗损失
        g_loss = self.adversarial_loss(fake_validity, real_label)
        
        # 特征匹配损失
        fm_loss = self.feature_matching_loss(real_features, fake_features)
        
        return g_loss + self.lambda_fm * fm_loss

def train_dgan(dataloader, generator, discriminator, criterion, g_optimizer, d_optimizer, 
               num_epochs, latent_dim, device, sample_interval=100, result_dir='results'):
    os.makedirs(result_dir, exist_ok=True)
    
    # 创建固定噪声用于可视化训练进程
    fixed_noise = torch.randn(16, latent_dim).to(device)
    
    for epoch in range(num_epochs):
        pbar = tqdm(enumerate(dataloader), desc=f"Epoch {epoch+1}/{num_epochs}")
        
        for i, real_imgs in pbar:
            batch_size = real_imgs.size(0)
            
            # 配置真实图像和噪声向量
            real_imgs = real_imgs.to(device)
            
            # 添加噪声到真实图像
            noisy_real_imgs = criterion.add_noise(real_imgs)
            z = torch.randn(batch_size, latent_dim).to(device)
            
            # -----------------
            #  训练判别器
            # -----------------
            d_optimizer.zero_grad()
            
            # 生成一批假图像
            fake_imgs = generator(z)
            
            # 判别器对真实和生成图像的判断
            real_validity, real_features = discriminator(noisy_real_imgs)
            fake_validity, fake_features = discriminator(fake_imgs.detach())
            
            # 计算判别器损失
            d_loss = criterion.discriminator_loss(real_validity, fake_validity, 
                                                real_features, fake_features)
            
            # 反向传播和优化
            d_loss.backward()
            d_optimizer.step()
            
            # -----------------
            #  训练生成器
            # -----------------
            g_optimizer.zero_grad()
            
            # 重新生成假图像和获取特征（因为之前的计算图已经分离）
            fake_imgs = generator(z)
            fake_validity, fake_features = discriminator(fake_imgs)
            
            # 保存real_features的副本，避免在backward时访问已释放的张量
            with torch.no_grad():
                real_features_copy = real_features.clone()
            
            # 计算生成器损失
            g_loss = criterion.generator_loss(fake_validity, real_features_copy, fake_features)
            
            # 反向传播和优化
            g_loss.backward()
            g_optimizer.step()
            
            # 更新进度条
            pbar.set_postfix({
                'D_loss': f'{d_loss.item():.4f}',
                'G_loss': f'{g_loss.item():.4f}'
            })
            
            # 保存采样结果
            batches_done = epoch * len(dataloader) + i
            if batches_done % sample_interval == 0:
                with torch.no_grad():
                    fake_imgs = generator(fixed_noise)
                    save_sample_images(fake_imgs, epoch, batches_done, result_dir)

def save_sample_images(gen_imgs, epoch, batches_done, result_dir, n_row=4):
    """保存生成的样本图像"""
    gen_imgs = 0.5 * (gen_imgs + 1.0)  # 反归一化
    gen_imgs = gen_imgs.clamp(0, 1)
    
    save_path = os.path.join(result_dir, f'samples_epoch_{epoch}_batch_{batches_done}.png')
    torchvision.utils.save_image(gen_imgs, save_path, nrow=n_row, normalize=False)

def dgan_train(voc_root, image_size, num_epochs, learning_rate, batch_size, result_dir, model_name, device):
    # 超参数
    latent_dim = 100
    
    # 准备数据
    dataloader = prepare_voc_data(voc_root, batch_size, image_size)
    
    # 初始化模型
    generator = DGANGenerator(latent_dim).to(device)
    discriminator = DGANDiscriminator().to(device)
    
    # 损失函数
    criterion = DGANLoss(device)
    
    # 优化器
    g_optimizer = optim.Adam(generator.parameters(), lr=learning_rate, betas=(0.5, 0.999))
    d_optimizer = optim.Adam(discriminator.parameters(), lr=learning_rate, betas=(0.5, 0.999))
    
    try:
        # 创建结果目录
        os.makedirs(result_dir, exist_ok=True)
        
        # 训练模型
        train_dgan(dataloader, generator, discriminator, criterion, 
                  g_optimizer, d_optimizer, num_epochs, latent_dim, 
                  device, sample_interval=100, result_dir=result_dir)
        
        # 保存最终模型
        torch.save(generator.state_dict(), os.path.join(result_dir, f"{model_name}_generator.pth"))
        torch.save(discriminator.state_dict(), os.path.join(result_dir, f"{model_name}_discriminator.pth"))
        print(f'训练完成，模型保存到 {result_dir}')
        
    except Exception as e:
        print(f"训练过程中发生错误: {e}")
        raise e

if __name__ == "__main__":
    # 设置参数
    voc_root = 'E:/data/VOCdevkit/VOC2007'  # VOC2007数据集根目录
    image_size = 64  # 生成图像的大小
    epochs = 1    # 训练轮数
    learning_rate = 0.0002
    batch_size = 64
    result_dir = 'E:/data/VOCdevkit/dgan_results'
    model_name = 'voc_dgan'
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 设置随机种子以确保可重复性
    torch.manual_seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(42)
    
    # 开始训练
    dgan_train(voc_root, image_size, epochs, learning_rate, batch_size, 
              result_dir, model_name, device)