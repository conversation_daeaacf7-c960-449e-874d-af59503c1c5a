"""DenseNet TensorFlow Implementation"""

import pickle
import requests
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import tensorflow as tf
import numpy as np
from PIL import Image
import xml.etree.ElementTree as ET
import pandas as pd

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"
MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class DenseNetForTabular(tf.keras.Model):
    def __init__(self, input_size, num_classes, pretrained_model_path):
        super(DenseNetForTabular, self).__init__()
        # 加载预训练的DenseNet121
        base_model = tf.keras.applications.DenseNet121(
            include_top=False,
            weights=None,
            input_shape=(input_size, input_size, 3)
        )
        
        # 加载预训练权重
        base_model.load_weights(pretrained_model_path)
        
        self.base_model = base_model
        self.global_pool = tf.keras.layers.GlobalAveragePooling2D()
        self.classifier = tf.keras.layers.Dense(num_classes, activation='softmax')

    def call(self, inputs):
        x = self.base_model(inputs)
        x = self.global_pool(x)
        return self.classifier(x)

class UniversalImageDataset(tf.keras.utils.Sequence):
    def __init__(self, data_dir, batch_size=32, image_size=224, dataset_type='folder', annotations_file=None):
        self.data_dir = data_dir
        self.batch_size = batch_size
        self.image_size = image_size
        self.dataset_type = dataset_type

        if dataset_type in ['folder', 'imagenet']:
            self.image_paths, self.labels = self.load_from_folder()
        elif dataset_type == 'coco':
            self.image_paths, self.labels = self.load_coco(annotations_file)
        elif dataset_type == 'voc':
            self.image_paths, self.labels = self.load_voc(annotations_file)
        elif dataset_type == 'yolo':
            self.image_paths, self.labels = self.load_yolo(annotations_file)
        elif dataset_type == 'pickle':
            self.image_paths, self.labels = self.load_pickle(annotations_file)
        else:
            raise ValueError("Unsupported dataset type.")

    def load_from_folder(self):
        classes = os.listdir(self.data_dir)
        class_to_idx = {cls: idx for idx, cls in enumerate(classes)}
        
        image_paths = []
        labels = []

        for cls in classes:
            class_dir = os.path.join(self.data_dir, cls)
            if os.path.isdir(class_dir):
                for img_file in os.listdir(class_dir):
                    if img_file.endswith(('.jpg', '.jpeg', '.png')):
                        img_path = os.path.join(class_dir, img_file)
                        image_paths.append(img_path)
                        labels.append(class_to_idx[cls])

        return image_paths, labels

    def load_coco(self, annotations_file):
        with open(annotations_file) as f:
            annotations = json.load(f)

        image_paths = []
        labels = []
        for item in annotations['images']:
            img_id = item['id']
            img_file = os.path.join(self.data_dir, item['file_name'])
            image_paths.append(img_file)
            label = self.get_label_for_image(img_id, annotations)
            labels.append(label)

        return image_paths, labels

    def load_voc(self, annotations_file):
        image_paths = []
        labels = []

        # 读取所有 XML 文件
        with open(annotations_file, 'r') as file:
            xml_files = file.readlines()

        for xml_file in xml_files:
            xml_file = xml_file.strip()
            tree = ET.parse(xml_file)
            root = tree.getroot()

            # 获取图像文件路径
            image_name = root.find('filename').text
            img_path = os.path.join(self.data_dir, image_name)
            image_paths.append(img_path)

            # 提取标签
            objects = root.findall('object')
            boxes = []
            for obj in objects:
                class_name = obj.find('name').text
                bbox = obj.find('bndbox')
                xmin = float(bbox.find('xmin').text)
                ymin = float(bbox.find('ymin').text)
                xmax = float(bbox.find('xmax').text)
                ymax = float(bbox.find('ymax').text)
                boxes.append((class_name, xmin, ymin, xmax, ymax))

            labels.append(boxes)

        return image_paths, labels

    def load_yolo(self):
        image_paths = []
        labels = []

        for img_file in os.listdir(self.data_dir):
            if img_file.endswith(('.jpg', '.png', '.jpeg')):
                img_path = os.path.join(self.data_dir, img_file)
                image_paths.append(img_path)

                # 加载对应的YOLO标签文件
                label_file = img_file.replace('.jpg', '.txt').replace('.png', '.txt').replace('.jpeg', '.txt')
                label_path = os.path.join(self.data_dir, label_file)

                if os.path.exists(label_path):
                    with open(label_path, 'r') as f:
                        boxes = []
                        for line in f.readlines():
                            class_id, x_center, y_center, width, height = map(float, line.strip().split())
                            boxes.append((class_id, x_center, y_center, width, height))
                        labels.append(boxes)  # 以边界框列表形式存储
                else:
                    labels.append([])  # 无标签时返回空列表

        return image_paths, labels
    
    def load_pickle(self, pkl_file):
        # 从 .pkl 文件加载数据
        with open(pkl_file, 'rb') as f:
            data = pickle.load(f)

        # 假设数据为字典格式，包含特征和标签
        if isinstance(data, dict):
            images = data['images']  # 假设图像数据在 'images' 键下
            labels = data['labels']    # 假设标签在 'labels' 键下
        elif isinstance(data, pd.DataFrame):
            images = data['image_paths'].tolist()  # 假设图像路径在某列
            labels = data['labels'].tolist()        # 假设标签在某列
        else:
            raise ValueError("Unsupported data format in pickle file.")

        return images, labels
    
    def __len__(self):
        return len(self.image_paths) // self.batch_size

    def __getitem__(self, idx):
        batch_x = self.image_paths[idx * self.batch_size:(idx + 1) * self.batch_size]
        batch_y = self.labels[idx * self.batch_size:(idx + 1) * self.batch_size]

        # 加载和预处理图像
        images = np.zeros((self.batch_size, self.image_size, self.image_size, 3))
        for i, path in enumerate(batch_x):
            img = Image.open(path).convert("RGB")
            img = img.resize((self.image_size, self.image_size))
            img = np.array(img) / 255.0  # 归一化到 [0,1]
            images[i] = img

        return images, np.array(batch_y)

def prepare_data(data_dir, dataset_type, batch_size, image_size, annotations_file=None):
    dataset = UniversalImageDataset(
        data_dir, 
        batch_size=batch_size,
        image_size=image_size,
        dataset_type=dataset_type,
        annotations_file=annotations_file
    )
    
    # 划分训练集和测试集
    total_size = len(dataset.image_paths)
    train_size = int(0.8 * total_size)
    
    indices = np.random.permutation(total_size)
    train_indices = indices[:train_size]
    test_indices = indices[train_size:]
    
    train_dataset = tf.data.Dataset.from_tensor_slices(
        (np.array(dataset.image_paths)[train_indices],
         np.array(dataset.labels)[train_indices])
    ).batch(batch_size)
    
    test_dataset = tf.data.Dataset.from_tensor_slices(
        (np.array(dataset.image_paths)[test_indices],
         np.array(dataset.labels)[test_indices])
    ).batch(batch_size)
    
    return train_dataset, test_dataset

def test_model(test_dataset, model,device):
    model.to(device)
    accuracy = tf.keras.metrics.SparseCategoricalAccuracy()
    
    for images, labels in test_dataset:
        predictions = model(images, training=False)
        accuracy.update_state(labels, predictions)
    
    print(f'Accuracy: {accuracy.result().numpy() * 100:.2f}%')

def set_device(device):
    """设置TensorFlow运行设备"""
    if device.lower() == "gpu":
        # 检查是否有可用的GPU
        if len(tf.config.list_physical_devices('GPU')) > 0:
            # 允许GPU内存动态增长
            for gpu in tf.config.list_physical_devices('GPU'):
                tf.config.experimental.set_memory_growth(gpu, True)
            device = "/device:GPU:0"
        else:
            print("Warning: GPU requested but no GPU available. Using CPU instead.")
            device = "/device:CPU:0"
    else:
        device = "/device:CPU:0"
    
    return device

def densenet_train(dataset, job_params, model_name, result_dir, fit_params=None, device="cpu"):
    # 设置设备
    tf_device = set_device(device)
    
    # 提取参数
    input_size = job_params["input_size"]
    dataset_type = job_params["dataset_type"]
    output_size = job_params["output_size"]
    num_epochs = job_params["num_epochs"]
    learning_rate = job_params["learning_rate"]
    batch_size = job_params["batch_size"]
    
    training_data_path = "/workspace/" + dataset["training_data_path"]
    
    with tf.device(tf_device):
        train_dataset, test_dataset = prepare_data(training_data_path, dataset_type, batch_size, input_size)
        
        pretrained_model_path = "/workspace/pretrained_model/densenet121.h5"
        
        model = DenseNetForTabular(input_size, output_size, pretrained_model_path)
        optimizer = tf.keras.optimizers.Adam(learning_rate=learning_rate)
        
        train_model(train_dataset, model, optimizer, num_epochs)
        test_model(test_dataset, model)
        
        # 保存模型
        model.save(os.path.join(result_dir, f"{model_name}.h5"))
        print(f'DenseNet训练完成，模型保存到 {result_dir}/{model_name}.h5')
    
    return None, 0

def train_model(train_dataset, model, optimizer, num_epochs):
    loss_object = tf.keras.losses.SparseCategoricalCrossentropy()
    
    # 获取当前设备
    device = tf.device(None)
    print(f"Training on device: {device}")
    
    for epoch in range(num_epochs):
        total_loss = 0
        num_batches = 0
        
        for images, labels in train_dataset:
            with tf.GradientTape() as tape:
                predictions = model(images, training=True)
                loss = loss_object(labels, predictions)
            
            gradients = tape.gradient(loss, model.trainable_variables)
            optimizer.apply_gradients(zip(gradients, model.trainable_variables))
            
            total_loss += loss
            num_batches += 1
        
        print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {total_loss/num_batches:.4f}')

def model_upload(result_dir,model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".h5"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".h5")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "TENSORFLOW",
          "file_name": model_name+".h5",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "Logistic_Regression",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

# main函数部分基本保持不变，只需要移除PyTorch设备相关代码
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='tensorflow DenseNet Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='DenseNet Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='DenseNet DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start DenseNet training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("DenseNet job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("DenseNet dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("DenseNet result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("DenseNet factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("DenseNet fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("DenseNet sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    
    device = '/GPU:0' if tf.config.list_physical_devices('GPU') else '/CPU:0'
    
    print("Step 1 DenseNet training:\n")
    result,ret_code = densenet_train(dataset, job_params, model["model_name"], result_dir, fit_params,device)
    
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()