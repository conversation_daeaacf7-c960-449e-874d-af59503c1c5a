"""
基于梯度提升分类的特征权重分析 - 增强版

此脚本使用梯度提升分类器(GradientBoostingClassifier)对数据集进行分析，
计算特征重要性，并通过可视化方式展示结果。支持多种数据格式和高级预处理选项。

功能:
- 灵活数据加载(支持pkl、csv、json格式)
- 特征重要性分析与可视化
- 模型保存与管理(MinIO存储)
- 完整的日志记录
- 错误处理机制

使用方法:
python gradient_boosting_feature_importance_local.py --job_params '{"n_estimators": 100}' --dataset '{"training_data_path": "path/to/data"}' --model '{"model_name": "gb_model", "group_id": 1}' --result_dir "./results" --factory_name "factory1"
"""

import pickle
import requests
import joblib
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import logging
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 非交互式后端，适用于无显示环境
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix, classification_report
from sklearn.model_selection import train_test_split, KFold, StratifiedKFold, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder, OrdinalEncoder
import traceback

# 常量定义
MINIO_URL = "minio-prophecis.prophecis:9000"

MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

# 请求头常量
MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

# 使用常量定义Minio凭证，便于维护
MINIO_ACCESS_KEY = 'AKIAIOSFODNN7EXAMPLE'
MINIO_SECRET_KEY = 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY'

def setup_logging(log_dir, log_level=logging.INFO):
    """
    设置日志记录器
    
    参数:
        log_dir: 日志目录
        log_level: 日志级别
        
    返回:
        logger: 日志记录器
    """
    # 创建日志目录
    os.makedirs(log_dir, exist_ok=True)
    
    # 获取当前时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 创建日志文件名
    log_file = os.path.join(log_dir, f"gradient_boosting_{timestamp}.log")
    
    # 配置根日志记录器
    logger = logging.getLogger("GradientBoosting")
    logger.setLevel(log_level)
    
    # 清除现有处理程序
    if logger.handlers:
        logger.handlers.clear()
    
    # 创建文件处理程序
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(log_level)
    
    # 创建控制台处理程序
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    
    # 创建格式化程序
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加处理程序到记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

# 灵活加载数据集，支持不同的数据格式和标签列
def flexible_data_load(file_path, label_col='Label', feature_cols=None, data_format='pkl'):
    """
    灵活加载不同格式的数据集
    
    参数:
    file_path: 数据文件路径
    label_col: 标签列名，默认为'Label'
    feature_cols: 特征列名列表，None表示使用除标签列外的所有列
    data_format: 数据格式，支持'pkl'、'csv'等
    
    返回:
    X: 特征数据
    y: 标签数据
    """
    logger = logging.getLogger("GradientBoosting")
    logger.info(f"加载数据，文件路径: {file_path}，格式: {data_format}")
    
    # 加载不同格式的数据
    if data_format.lower() == 'pkl':
        with open(file_path, 'rb') as file:
            data = pickle.load(file)
    elif data_format.lower() == 'csv':
        data = pd.read_csv(file_path)
    elif data_format.lower() == 'json':
        data = pd.read_json(file_path)
    else:
        raise ValueError(f"不支持的数据格式: {data_format}")
    
    # 处理不同类型的数据结构
    if isinstance(data, pd.DataFrame):
        logger.info(f"数据加载为DataFrame，形状: {data.shape}")
        
        # 清理列名，去除可能的空格
        data.columns = data.columns.str.strip()
        
        # 检查标签列是否存在
        if label_col not in data.columns:
            available_cols = ', '.join(data.columns.tolist())
            logger.error(f"未找到标签列 '{label_col}'。可用列: {available_cols}")
            raise ValueError(f"未找到标签列 '{label_col}'")
        
        # 根据特征列选择特征
        if feature_cols is not None:
            missing_cols = [col for col in feature_cols if col not in data.columns]
            if missing_cols:
                logger.warning(f"部分特征列未在数据中找到: {missing_cols}")
            X = data[feature_cols]
        else:
            X = data.drop([label_col], axis=1)
            
        y = data[label_col]
        
        logger.info(f"特征形状: {X.shape}, 标签形状: {y.shape}")
        logger.info(f"标签分布: {y.value_counts().to_dict()}")
    
    elif isinstance(data, tuple) and len(data) >= 2:
        # 处理已经分割好的数据集
        logger.info("数据加载为元组（已分割）")
        # 假设前两个元素是训练数据和训练标签
        X, y = data[0], data[1]
        
        # 检查数据类型，确保可以处理
        logger.info(f"特征类型: {type(X)}, 形状: {X.shape if hasattr(X, 'shape') else '未知'}")
        logger.info(f"标签类型: {type(y)}, 形状: {y.shape if hasattr(y, 'shape') else '未知'}")
    
    elif isinstance(data, dict):
        logger.info("数据加载为字典")
        if 'features' in data and 'labels' in data:
            X = data['features']
            y = data['labels']
        else:
            # 检查是否包含训练、测试数据
            if 'train' in data and 'test' in data:
                # 只返回训练数据
                if isinstance(data['train'], tuple) and len(data['train']) >= 2:
                    X, y = data['train'][0], data['train'][1]
                else:
                    raise ValueError("无法识别的训练数据格式")
            else:
                raise ValueError("字典数据应包含'features'和'labels'键或'train'和'test'键")
    else:
        # 尝试处理tuple类型的特殊情况：(train_data, validation_data, test_data)
        if isinstance(data, tuple) and len(data) == 3:
            # 处理原始代码中的特殊格式
            training_data, validation_data, test_data = data
            if isinstance(training_data, tuple) and len(training_data) >= 2:
                X, y = training_data
                logger.info(f"从元组格式加载数据，形状: X={X.shape if hasattr(X, 'shape') else '未知'}, y={y.shape if hasattr(y, 'shape') else '未知'}")
                return X, y
            else:
                raise TypeError(f"训练数据格式不符合预期: {type(training_data)}")
        else:
            raise TypeError(f"不支持的数据类型: {type(data)}")
    
    return X, y

def feature_importance_with_gb(dataset, job_params, model_name, result_dir, fit_params=None):
    """
    使用梯度提升进行特征重要性分析

    参数:
    dataset (dict): 包含训练数据路径的字典
    job_params (dict): 梯度提升模型参数
        n_estimators: 树的数量，默认为100
        learning_rate: 学习率，默认为0.1
        max_depth: 树的最大深度，默认为3
        subsample: 样本子集比例，默认为1.0
        max_features: 特征子集选择方式，默认为'sqrt'
        random_state: 随机数种子，默认为42
    model_name (str): 模型名称
    result_dir (str): 结果保存目录
    fit_params (dict, optional): 训练配置字典
        use_cross_validation: 是否使用交叉验证 (布尔值)
        cv_folds: 交叉验证折数 (整数)
        label_column: 标签列名 (字符串)
        data_format: 数据格式 (字符串)

    返回:
    training_results: 训练结果字典
    ret_code: 返回码(0表示成功)
    """
    logger = logging.getLogger("GradientBoosting")
    logger.info("梯度提升特征重要性分析开始")
    logger.info(f"模型参数: {job_params}")
    logger.info(f"数据集配置: {dataset}")
    
    # 提取模型参数
    n_estimators = job_params.get('n_estimators', 100)
    learning_rate = job_params.get('learning_rate', 0.1)
    max_depth = job_params.get('max_depth', 3)
    subsample = job_params.get('subsample', 1.0)
    max_features = job_params.get('max_features', 'sqrt')
    random_state = job_params.get('random_state', 42)
    
    # 提取训练配置，如果没有提供则使用默认值
    if fit_params is None:
        fit_params = {}
    
    # 提取基本训练参数
    use_cross_validation = fit_params.get("use_cross_validation", False)
    label_column = fit_params.get("label_column", "Label")
    data_format = fit_params.get("data_format", "pkl")
    
    # 提取交叉验证特定参数
    cv_params = {
        "n_splits": fit_params.get("cv_folds", 5),
        "shuffle": True  # 总是打乱数据
    }
    
    # 准备数据集
    training_data_path = "/workspace/" + dataset["training_data_path"]
    logger.info(f"从 {training_data_path} 加载数据集")
    
    try:
        # 使用灵活数据加载函数
        X, y = flexible_data_load(training_data_path, label_col=label_column, data_format=data_format)
        
        # 获取特征名称（如果X是DataFrame）
        feature_names = X.columns.tolist() if hasattr(X, 'columns') else None
        if feature_names:
            logger.info(f"加载了 {len(feature_names)} 个特征")
        
        # 检查是否需要标签编码
        if not np.issubdtype(y.dtype, np.number):
            logger.info("将目标变量进行标签编码")
            label_encoder = LabelEncoder()
            y = label_encoder.fit_transform(y)
            # 保存标签编码器
            label_encoder_path = os.path.join(result_dir, f"{model_name}_label_encoder.joblib")
            joblib.dump(label_encoder, label_encoder_path)
            logger.info(f"标签编码器已保存至 {label_encoder_path}")
        
        # 根据是否使用交叉验证选择训练方式
        if use_cross_validation:
            logger.info(f"使用交叉验证，参数: {cv_params}")
            
            # 数据预处理但不拆分
            X_processed, y_processed = X, y
            if hasattr(X, 'values'):
                X_processed = X.values
            if hasattr(y, 'values'):
                y_processed = y.values
                
            # 运行交叉验证训练
            final_model, cv_results = cross_validation_train(
                X_processed, y_processed, job_params, model_name, 
                result_dir, cv_params=cv_params
            )
            
            # 获取特征重要性
            importances = final_model.feature_importances_
            indices = np.argsort(importances)[::-1]
            
            # 创建特征重要性数据框
            importance_df = pd.DataFrame({
                'feature': [feature_names[i] for i in indices] if feature_names else [f"feature_{i}" for i in indices],
                'importance': [importances[i] for i in indices]
            })
            
            # 保存特征重要性到CSV
            importance_csv_path = os.path.join(result_dir, f"{model_name}_feature_importance.csv")
            importance_df.to_csv(importance_csv_path, index=False)
            logger.info(f"特征重要性已保存至 {importance_csv_path}")
            
            # 可视化特征重要性
            plt.figure(figsize=(12, 8))
            plt.barh(range(len(indices)), importances[indices], align='center')
            plt.yticks(range(len(indices)), [feature_names[i] if feature_names else f"feature_{i}" for i in indices])
            plt.title('梯度提升 - 特征重要性')
            plt.xlabel('相对重要性')
            plt.tight_layout()
            
            # 保存可视化图表
            plot_path = os.path.join(result_dir, f"{model_name}_feature_importance.png")
            plt.savefig(plot_path, dpi=300)
            plt.close()
            logger.info(f"特征重要性可视化已保存至 {plot_path}")
            
            training_results = {
                "model_path": os.path.join(result_dir, f"{model_name}.joblib"),
                "cv_results": cv_results,
                "importance_csv": importance_csv_path,
                "importance_plot": plot_path
            }
            
        else:
            logger.info("使用标准训练/测试集分割")
            
            # 简单数据预处理
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.3, random_state=42, stratify=y if len(np.unique(y)) > 1 else None
            )
            
            # 初始化梯度提升分类器
        model = GradientBoostingClassifier(
            n_estimators=n_estimators,
            learning_rate=learning_rate,
            max_depth=max_depth,
            subsample=subsample,
            max_features=max_features,
            random_state=random_state
        )
        
        # 训练模型
            logger.info(f"训练梯度提升分类器，参数: n_estimators={n_estimators}, learning_rate={learning_rate}, max_depth={max_depth}")
        start_time = datetime.now()
        model.fit(X_train, y_train)
        training_time = (datetime.now() - start_time).total_seconds()
            
            # 评估模型
            logger.info("在测试集上评估模型")
            y_pred = model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            precision, recall, f1, _ = precision_recall_fscore_support(y_test, y_pred, average='weighted')
        
        # 计算特征重要性
        importances = model.feature_importances_
        indices = np.argsort(importances)[::-1]
        
        # 创建特征重要性数据框
        importance_df = pd.DataFrame({
                'feature': [feature_names[i] for i in indices] if feature_names else [f"feature_{i}" for i in indices],
            'importance': [importances[i] for i in indices]
        })
        
        # 保存特征重要性到CSV
        importance_csv_path = os.path.join(result_dir, f"{model_name}_feature_importance.csv")
        importance_df.to_csv(importance_csv_path, index=False)
        logger.info(f"特征重要性已保存至 {importance_csv_path}")
        
        # 可视化特征重要性
        plt.figure(figsize=(12, 8))
        plt.barh(range(len(indices)), importances[indices], align='center')
            plt.yticks(range(len(indices)), [feature_names[i] if feature_names else f"feature_{i}" for i in indices])
        plt.title('梯度提升 - 特征重要性')
        plt.xlabel('相对重要性')
        plt.tight_layout()
        
        # 保存可视化图表
        plot_path = os.path.join(result_dir, f"{model_name}_feature_importance.png")
        plt.savefig(plot_path, dpi=300)
        plt.close()
        logger.info(f"特征重要性可视化已保存至 {plot_path}")
        
        # 保存模型
        model_path = os.path.join(result_dir, f"{model_name}.joblib")
        joblib.dump(model, model_path)
        logger.info(f"模型已保存至 {model_path}")
        
            # 记录所有训练结果
            training_results = {
                "model_path": model_path,
            "accuracy": accuracy,
            "precision": precision,
            "recall": recall,
            "f1_score": f1,
            "training_time": training_time,
            "feature_importance": importance_df.to_dict(orient='records'),
            "importance_csv": importance_csv_path,
            "importance_plot": plot_path
        }
        
        # 保存训练结果到JSON文件
        results_file = os.path.join(result_dir, 'training_results.json')
        with open(results_file, 'w') as f:
            # 确保所有值都是JSON可序列化的
            serializable_results = convert_to_serializable(training_results)
            json.dump(serializable_results, f, indent=4)
        
        logger.info(f"训练结果已保存到 {results_file}")
        logger.info("梯度提升特征重要性分析成功完成")
        
        return training_results, 0
        
    except Exception as e:
        logger.error(f"梯度提升特征重要性分析中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        return {"error": str(e)}, 1

def model_upload(model_name, model_path):
    """
    上传模型到MinIO存储
    
    参数:
        model_name: 模型名称
        model_path: 模型文件路径
        
    返回:
        source: MinIO源路径
    """
    logger = logging.getLogger("GradientBoosting")
    logger.info(f"上传模型 {model_name} 到MinIO")
    
    try:
        # 初始化MinIO客户端
        minio_client = Minio(
            MINIO_URL,
            access_key=MINIO_ACCESS_KEY,
            secret_key=MINIO_SECRET_KEY,
            secure=False
        )
        
        # 生成唯一对象名
        obj_name = str(uuid.uuid1())
        upload_path = f"{obj_name}/{model_name}.joblib"
        source = f"s3://mlss-mf/{obj_name}"
        
        # 上传模型文件
        minio_client.fput_object('mlss-mf', upload_path, model_path)
        logger.info(f"模型已上传到 {source}")
        
        return source
    except Exception as e:
        logger.error(f"模型上传失败: {str(e)}")
        logger.error(traceback.format_exc())
        return None

def model_register(model_name, user_id, source, model_type="GradientBoostingFeatureImportance"):
    """
    注册模型到模型工厂
    
    参数:
        model_name: 模型名称
        user_id: 用户ID
        source: MinIO源路径
        model_type: 模型类型
        
    返回:
        model_id: 模型ID
        model_version_id: 模型版本ID
    """
    logger = logging.getLogger("GradientBoosting")
    logger.info(f"注册模型 {model_name}")
    
    try:
        # 生成请求头
        headers = header_gen(user_id)
        headers["Content-Type"] = "application/json"
        
        # 构建请求参数
    params = {
        "model_name": model_name,
            "model_type": model_type,
            "file_name": f"{model_name}.joblib",
        "s3_path": source,
            "group_id": 1,  # 默认组ID
        "training_id": model_name,
        "training_flag": 1,
    }
    
        # 发送注册请求
        url = f"{MODEL_FACTORY_URL}{MODEL_ADD_URL}"
        response = requests.post(url, data=json.dumps(params), headers=headers)
        
        # 处理响应
        if response.status_code == 200:
            result = response.json()
            model_id = result.get("result", {}).get("model_id")
            model_version_id = result.get("result", {}).get("model_version_id")
            
            if model_id and model_version_id:
                logger.info(f"模型注册成功，模型ID: {model_id}, 版本ID: {model_version_id}")
                return model_id, model_version_id
            else:
                logger.error(f"模型注册响应缺少ID信息: {result}")
                return None, None
    else:
            logger.error(f"模型注册失败，状态码: {response.status_code}, 响应: {response.text}")
            return None, None
    except Exception as e:
        logger.error(f"模型注册过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        return None, None

def model_push(model_name, factory_name, model_version, model_path, model_type="Gradient_Boosting_Feature_Importance"):
    """
    将模型推送到模型工厂
    
    参数:
        model_name: 模型名称
        factory_name: 工厂名称
        model_version: 模型版本
        model_path: 模型路径
        model_type: 模型类型
    
    返回:
        response: 响应对象
    """
    # 如果工厂名称为"None"，则跳过推送
    if factory_name == "None":
        logger = logging.getLogger("GradientBoosting")
        logger.info("工厂名称为None，跳过模型推送")
        return None
        
    # 生成请求头
    headers = {
        "accept": "application/json",
        "Content-Type": "application/json"
    }
    
    # 构建请求URL和数据
    url = f"http://prophecis-model-factory-service.prophecis:8080/api/v1/mlss-mf/model/push"
    data = {
        "modelName": model_name,
        "factoryName": factory_name,
        "modelVersion": model_version,
        "modelPath": model_path,
        "modelType": model_type
    }
    
    # 发送请求
    response = requests.post(url, headers=headers, json=data)
    
    # 检查响应状态
    if response.status_code == 200:
        return response.json()
    else:
        logger = logging.getLogger("GradientBoosting")
        logger.error(f"模型推送失败: {response.text}")
        return None

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

def cross_validation_split(X, y, n_splits=5, shuffle=True, random_state=42):
    """
    创建交叉验证的数据分割，使用标准K折交叉验证
    
    参数:
    X: 特征数据
    y: 标签数据
    n_splits: 折数
    shuffle: 是否打乱数据
    random_state: 随机种子
    
    返回:
    splits: 包含训练和验证索引的列表
    """
    logger = logging.getLogger("GradientBoosting")
    logger.info(f"执行 {n_splits} 折交叉验证")
    
    # 使用K折交叉验证
    kf = KFold(n_splits=n_splits, shuffle=shuffle, random_state=random_state)
    logger.info("使用标准k折交叉验证")
    
    splits = list(kf.split(X, y))
    return splits

def cross_validation_train(X, y, job_params, model_name, result_dir, cv_params=None):
    """
    使用交叉验证训练梯度提升模型
    
    参数:
    X: 特征数据
    y: 标签数据
    job_params: 模型参数
    model_name: 模型名称
    result_dir: 结果保存目录
    cv_params: 交叉验证参数字典
        n_splits: 折数 (默认5)
        shuffle: 是否打乱数据 (默认True)
    
    返回:
    trained_model: 在全部数据上训练的最终模型
    cv_results: 交叉验证结果
    """
    logger = logging.getLogger("GradientBoosting")
    
    # 设置默认交叉验证参数
    if cv_params is None:
        cv_params = {}
    
    n_splits = cv_params.get("n_splits", 5)
    shuffle = cv_params.get("shuffle", True)
    
    logger.info(f"开始 {n_splits} 折交叉验证训练")
    logger.info(f"交叉验证参数: {cv_params}")
    
    # 提取模型参数
    n_estimators = job_params.get('n_estimators', 100)
    learning_rate = job_params.get('learning_rate', 0.1)
    max_depth = job_params.get('max_depth', 3)
    subsample = job_params.get('subsample', 1.0)
    max_features = job_params.get('max_features', 'sqrt')
    random_state = job_params.get('random_state', 42)
    
    # 获取交叉验证分割
    splits = cross_validation_split(X, y, n_splits=n_splits, shuffle=shuffle, 
                                   random_state=42)  # 使用固定的随机种子
    
    # 用于存储每个折的性能
    fold_metrics = []
    
    # 对每个折进行训练和评估
    for fold, (train_idx, val_idx) in enumerate(splits):
        logger.info(f"训练折 {fold+1}/{n_splits}")
        
        # 分割数据
        X_train, X_val = X[train_idx], X[val_idx]
        y_train, y_val = y[train_idx], y[val_idx]
        
        # 创建梯度提升模型
        gb_model = GradientBoostingClassifier(
            n_estimators=n_estimators,
            learning_rate=learning_rate,
            max_depth=max_depth,
            subsample=subsample,
            max_features=max_features,
            random_state=random_state
        )
        
        # 训练模型
        gb_model.fit(X_train, y_train)
        
        # 验证模型
        y_pred = gb_model.predict(X_val)
        accuracy = accuracy_score(y_val, y_pred)
        precision, recall, f1, _ = precision_recall_fscore_support(y_val, y_pred, average='weighted')
        
        # 保存本折的模型
        fold_dir = os.path.join(result_dir, f'fold_{fold+1}')
        if not os.path.exists(fold_dir):
            os.makedirs(fold_dir)
            
        model_path = os.path.join(fold_dir, f"{model_name}_fold_{fold+1}.joblib")
        joblib.dump(gb_model, model_path)
        
        # 计算特征重要性
        importances = gb_model.feature_importances_
        
        # 记录性能
        fold_metrics.append({
            'fold': fold + 1,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1': f1,
            'feature_importances': importances.tolist() if hasattr(importances, 'tolist') else list(importances)
        })
        
        logger.info(f"折 {fold+1} 完成，验证准确率: {accuracy:.4f}, F1: {f1:.4f}")
    
    # 汇总交叉验证结果
    cv_accuracy = np.mean([m['accuracy'] for m in fold_metrics])
    cv_precision = np.mean([m['precision'] for m in fold_metrics])
    cv_recall = np.mean([m['recall'] for m in fold_metrics])
    cv_f1 = np.mean([m['f1'] for m in fold_metrics])
    
    logger.info(f"交叉验证完成，平均准确率: {cv_accuracy:.4f}, 精确率: {cv_precision:.4f}, 召回率: {cv_recall:.4f}, F1: {cv_f1:.4f}")
    
    # 在全部数据上训练最终模型
    logger.info("在所有数据上训练最终模型")
    
    final_model = GradientBoostingClassifier(
        n_estimators=n_estimators,
        learning_rate=learning_rate,
        max_depth=max_depth,
        subsample=subsample,
        max_features=max_features,
        random_state=random_state
    )
    
    final_model.fit(X, y)
    
    # 计算最终模型的特征重要性
    final_importances = final_model.feature_importances_
    
    # 保存最终模型
    final_model_path = os.path.join(result_dir, f"{model_name}.joblib")
    joblib.dump(final_model, final_model_path)
    logger.info(f"最终模型已保存到 {final_model_path}")
    
    # 保存交叉验证结果到JSON文件
    cv_results = {
        'folds': fold_metrics,
        'mean_accuracy': float(cv_accuracy),
        'mean_precision': float(cv_precision),
        'mean_recall': float(cv_recall),
        'mean_f1': float(cv_f1),
        'cv_params': {
            'n_splits': n_splits,
            'shuffle': shuffle
        },
        'final_feature_importances': final_importances.tolist() if hasattr(final_importances, 'tolist') else list(final_importances)
    }
    
    with open(os.path.join(result_dir, 'cv_results.json'), 'w') as f:
        # 使用辅助函数转换为可序列化格式
        json.dump(convert_to_serializable(cv_results), f, indent=4)
    
    return final_model, cv_results

def convert_to_serializable(obj):
    """
    递归地将对象转换为JSON可序列化的格式
    
    参数:
    obj: 需要转换的对象
    
    返回:
    转换后的对象
    """
    if isinstance(obj, dict):
        return {convert_to_serializable(k): convert_to_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_serializable(item) for item in obj]
    elif isinstance(obj, (np.integer, np.int64, np.int32)):
        return int(obj)
    elif isinstance(obj, (np.floating, np.float64, np.float32)):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    else:
        return obj

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='梯度提升特征重要性分析工具 - 支持交叉验证和高级预处理')
    
    # 添加参数
    parser.add_argument('--job_params', type=str, required=True,
                        help='模型参数 (JSON格式): n_estimators, learning_rate, max_depth, subsample, max_features, random_state')
    parser.add_argument('--dataset', type=str, required=True,
                        help='数据集配置 (JSON格式): training_data_path')
    parser.add_argument('--result_dir', type=str, required=True,
                        help='结果保存目录')
    parser.add_argument('--model_name', type=str, required=True,
                        help='模型名称')
    parser.add_argument('--fit_params', type=str, required=False, default='{}',
                        help='训练配置 (JSON格式): use_cross_validation (布尔值), cv_folds (整数), '
                             'label_column (字符串), data_format (pkl/csv/excel)')
    parser.add_argument('--user_id', type=str, required=False, default='admin',
                        help='用户ID')
    parser.add_argument('--factory_name', type=str, required=False, default='None',
                        help='工厂名称')
    
    # 解析参数
    args = parser.parse_args()
    
    # 创建结果目录
    os.makedirs(args.result_dir, exist_ok=True)
    
    # 设置日志
    logger = setup_logging(args.result_dir)
    logger.info("梯度提升特征重要性分析脚本启动")
    
    try:
        # 解析JSON参数
        job_params = json.loads(args.job_params)
        dataset = json.loads(args.dataset)
        fit_params = json.loads(args.fit_params) if args.fit_params else {}
        
        # 训练模型
        logger.info("开始梯度提升特征重要性分析")
        training_results, ret_code = feature_importance_with_gb(
            dataset, job_params, args.model_name, args.result_dir, fit_params
        )
        
        if ret_code != 0:
            logger.error(f"特征重要性分析失败: {training_results}")
            sys.exit(1)
            
        logger.info("特征重要性分析成功，准备上传模型")
        
        # 获取模型路径
        model_path = training_results.get("model_path")
        if not model_path:
            logger.error("模型路径未找到")
            sys.exit(1)
            
        # 上传模型
        try:
            logger.info("开始上传模型")
            source = model_upload(args.model_name, model_path)
            if not source:
                logger.error("模型上传失败")
                sys.exit(1)
            logger.info(f"模型上传成功: {source}")
        except Exception as e:
            logger.error(f"模型上传过程中发生错误: {str(e)}")
            logger.error(traceback.format_exc())
            sys.exit(1)
            
        # 注册模型
        try:
            logger.info("开始注册模型")
            model_id, model_version_id = model_register(
                args.model_name, args.user_id, source
            )
            if not model_id or not model_version_id:
                logger.error("模型注册失败")
                sys.exit(1)
            logger.info(f"模型注册成功，模型ID: {model_id}, 版本ID: {model_version_id}")
        except Exception as e:
            logger.error(f"模型注册过程中发生错误: {str(e)}")
            logger.error(traceback.format_exc())
            sys.exit(1)
            
        # 推送模型到工厂
        try:
            logger.info(f"开始推送模型到工厂: {args.factory_name}")
            push_result = model_push(
                args.model_name, args.factory_name, model_version_id, model_path
            )
            if push_result:
                logger.info(f"模型推送成功: {push_result}")
            else:
                logger.info("模型推送跳过或失败")
        except Exception as e:
            logger.error(f"模型推送过程中发生错误: {str(e)}")
            logger.error(traceback.format_exc())
            # 推送失败不影响整体流程，继续执行
            
        logger.info("梯度提升特征重要性分析流程成功完成")
        
    except Exception as e:
        logger.error(f"执行过程中发生错误: {str(e)}")
        logger.error(traceback.format_exc())
        sys.exit(1)
   
  
