{"final_model_weights_path": "E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\results\\results_gin_tf_local\\models\\GIN_TF_Local_Model_reported.weights.h5", "final_model_config_path": "E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\results\\results_gin_tf_local\\models\\GIN_TF_Local_Model_reported_config.json", "model_type": "train_test_split_tf_gin", "test_metrics": {"accuracy": 0.9684642091510817, "precision_weighted": 0.9681451205946155, "recall_weighted": 0.9684642091510817, "f1_weighted": 0.9681717131867639, "classification_report_str": "                  precision    recall  f1-score   support\n\n          BENIGN       0.98      0.97      0.98     88006\n   DoS GoldenEye       0.68      0.59      0.63      2059\n        DoS Hulk       0.97      0.99      0.98     46215\nDoS Slowhttptest       0.66      0.74      0.70      1100\n   DoS slowloris       0.65      0.62      0.63      1159\n      Heartbleed       0.00      0.00      0.00         2\n\n        accuracy                           0.97    138541\n       macro avg       0.66      0.65      0.65    138541\n    weighted avg       0.97      0.97      0.97    138541\n"}, "training_history": {"loss": [0.23479017615318298, 0.16311770677566528, 0.15120325982570648], "accuracy": [0.9188396334648132, 0.9448879361152649, 0.949238657951355], "val_loss": [0.1561700701713562, 0.3686869442462921, 0.10439548641443253], "val_accuracy": [0.9631156325340271, 0.8501959443092346, 0.968464195728302]}, "class_names": ["BENIGN", "DoS GoldenEye", "DoS Hulk", "DoS Slowhttptest", "DoS slowloris", "Heartbleed"], "preprocessing_info_path": "E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\results\\results_gin_tf_local\\GIN_TF_Local_Model_preproc_info.json"}