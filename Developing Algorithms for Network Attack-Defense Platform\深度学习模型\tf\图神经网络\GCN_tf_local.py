import pickle
import requests
import joblib
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import tensorflow as tf
import numpy as np

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class GCNLayer(tf.keras.layers.Layer):
    def __init__(self, out_features):
        super(G<PERSON>NLayer, self).__init__()
        self.dense = tf.keras.layers.Dense(out_features)
    
    def call(self, inputs, adj):
        # 使用tf.sparse.sparse_dense_matmul替代PyTorch的spmm
        x = tf.sparse.sparse_dense_matmul(adj, inputs) if isinstance(adj, tf.SparseTensor) else tf.matmul(adj, inputs)
        x = self.dense(x)
        return tf.nn.relu(x)

class GCN(tf.keras.Model):
    def __init__(self, in_features, hidden_features, out_features):
        super(GCN, self).__init__()
        self.gcn1 = GCNLayer(hidden_features)
        self.gcn2 = GCNLayer(out_features)
    
    def call(self, inputs, adj):
        x = self.gcn1(inputs, adj)
        x = self.gcn2(x, adj)
        return tf.nn.log_softmax(x, axis=1)

def gcn_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """
    参数说明：
    - in_features: 输入特征的维度
    - hidden_features: 隐藏层特征的维度
    - out_features: 输出特征的维度（通常是类别数）
    - num_epochs: 训练轮数
    - learning_rate: 学习率
    """
    training_data_path = "/workspace/" + dataset["training_data_path"]
    with open(training_data_path, 'rb') as f:
        training_data, validation_data, test_data = pickle.load(f, encoding='bytes')
    
    train_x, train_adj, train_y = training_data
    test_x, test_adj, test_y = test_data

    # 转换为TensorFlow张量
    train_x = tf.convert_to_tensor(train_x, dtype=tf.float32)
    train_adj = tf.convert_to_tensor(train_adj, dtype=tf.float32)
    train_y = tf.convert_to_tensor(train_y, dtype=tf.int64)

    # 初始化GCN模型
    in_features = job_params["in_features"]
    hidden_features = job_params["hidden_features"]
    out_features = job_params["out_features"]
    num_epochs = job_params["num_epochs"]
    learning_rate = job_params["learning_rate"]

    model = GCN(in_features, hidden_features, out_features)
    optimizer = tf.keras.optimizers.Adam(learning_rate=learning_rate)
    loss_fn = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True)

    # 定义训练步骤
    @tf.function
    def train_step(x, adj, y):
        with tf.GradientTape() as tape:
            predictions = model(x, adj)
            loss = loss_fn(y, predictions)
        gradients = tape.gradient(loss, model.trainable_variables)
        optimizer.apply_gradients(zip(gradients, model.trainable_variables))
        return loss

    # 训练模型
    for epoch in range(num_epochs):
        loss = train_step(train_x, train_adj, train_y)
        if epoch % 10 == 0:
            print(f"Epoch {epoch}, Loss: {loss:.4f}")

    # 保存模型
    model_path = f"{result_dir}/{model_name}.h5"
    model.save(model_path)
    print(f'GCN训练完成，模型保存到 {model_path}')

    return None, 0

def model_upload(result_dir, model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = f"{obj_name}/{model_name}.h5"
        source = f"s3://mlss-mf/{obj_name}"
        res = minioClient.fput_object('mlss-mf', upload_path, f"{result_dir}/{model_name}.h5")
        result = {"source": source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1

def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "TENSORFLOW",
          "file_name": f"{model_name}.h5",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
    }
    
    r = requests.post(f"{MODEL_FACTORY_URL}{MODEL_ADD_URL}",
                     data=json.dumps(params),
                     headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "Logistic_Regression",
        "model_usage": "Classification"
    }
    r = requests.post(f"{MODEL_FACTORY_URL}{MODEL_PUSH_URL}/{model_version_id}",
                     data=json.dumps(params),
                     headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def header_gen(user_id):
    return {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: user_id
    }

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='tensorflow GCN Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='GCN Job Params, set all params in dict')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='GCN DataSet, set as a dict')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,
                    default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost', nargs='?', const=None, dest='nodehost',
                    type=str, default="**************",
                    help='nodehost params')     

    print("Start GCN training job, params:\n" + str(sys.argv) + "\n")
    args = parser.parse_args()
    
    job_params = args.job_params
    print("GCN job params:" + str(job_params) + "\n")
    
    dataset = args.dataset
    print("GCN dataSet:" + str(dataset) + "\n")
    
    model = args.model
    print(model)
    
    result_dir = args.result_dir
    print("GCN result dir:" + result_dir + "\n")    
    
    factory_name = args.factory_name
    print("GCN factory name:" + factory_name + "\n")    
    
    fit_params = args.fit_params
    print("GCN fit params:" + str(fit_params) + "\n")  
    
    sparkconf = json.loads(args.sparkconf)
    print("GCN sparkconf params:" + str(sparkconf) + "\n")
    
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
        
    if fit_params is None:
        fit_params = {}
     
    print("Step 1 GCN training:\n")
    result, ret_code = gcn_train(dataset, job_params, model["model_name"], result_dir, fit_params)
    if ret_code != 0:
        print("GCN train err, stop job....\n")
        print("Error Msg:" + result + "\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    print("Step 2 Model Upload to MinIO: \n")
    result, ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Register:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result, ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: " + result)
        sys.exit(-1)
    print("Register model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result, ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: " + result + "\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()