2025-05-23 14:59:48,650 - DDPG_TF_Local - INFO - Logging to: ./results/ddpg_tf_local_run\DDPG_TF_Pendulum_training_20250523_145948.log
2025-05-23 14:59:48,650 - DDPG_TF_Local - INFO - DDPG TF Local Training (DDPG_TF_Pendulum) starting.
2025-05-23 14:59:48,650 - DDPG_TF_Local - INFO - Params:
{
  "env_name": "Pendulum-v1",
  "result_dir": "./results/ddpg_tf_local_run",
  "model_name": "DDPG_TF_Pendulum",
  "hidden_size": 256,
  "actor_lr": 0.0001,
  "critic_lr": 0.001,
  "gamma": 0.99,
  "tau": 0.001,
  "buffer_size": 50000,
  "batch_size": 64,
  "noise_sigma": 0.1,
  "num_episodes": 3,
  "max_steps_per_episode": 200,
  "random_seed": null,
  "save_freq": 20,
  "plot_freq": 10,
  "tf_log_level": "ERROR",
  "logger": "<Logger DDPG_TF_Local (INFO)>"
}
2025-05-23 14:59:48,673 - DDPG_TF_Local.Env - INFO - Gymnasium API for Pendulum-v1
2025-05-23 14:59:48,674 - DDPG_TF_Local - INFO - Env: Pendulum-v1, State=3, Action=1, Bound=2.0
2025-05-23 14:59:55,419 - DDPG_TF_Local - INFO - Ep 1/3 | Score: -1443.66 | AvgScore(100): -1443.66 | Steps: 200 | Time: 6.52s
2025-05-23 14:59:58,371 - DDPG_TF_Local - INFO - Ep 2/3 | Score: -1267.43 | AvgScore(100): -1355.55 | Steps: 200 | Time: 2.95s
2025-05-23 15:00:01,271 - DDPG_TF_Local - INFO - Ep 3/3 | Score: -1066.01 | AvgScore(100): -1259.03 | Steps: 200 | Time: 2.90s
2025-05-23 15:00:02,462 - DDPG_TF_Local.Plot - INFO - Plots saved in ./results/ddpg_tf_local_run\plots
2025-05-23 15:00:02,644 - DDPG_TF_Local - INFO - Training done. Final model: ./results/ddpg_tf_local_run\models\DDPG_TF_Pendulum_final_ep3. Total time: 13.74s
2025-05-23 15:00:02,666 - DDPG_TF_Local - INFO - Script finished successfully.
