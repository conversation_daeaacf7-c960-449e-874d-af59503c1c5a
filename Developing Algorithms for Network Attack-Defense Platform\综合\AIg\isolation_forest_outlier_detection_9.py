"""基于孤立森林的异常值检测 """

import sys
import pandas as pd
import numpy as np
from sklearn.ensemble import IsolationForest

def isolation_forest_outlier_detection(input_file, contamination=0.1, n_estimators=100, max_samples='auto', random_state=None):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - contamination: 数据集中异常值的预期比例，浮点数，默认为0.1
    - n_estimators: 树的数量，整数，默认为100
    - max_samples: 用于训练每棵树的样本数，可以是整数或浮点数，默认为'auto'
    - random_state: 随机数种子，整数或None，默认为None
    """
    data = pd.read_csv(input_file)
    
    iso_forest = IsolationForest(contamination=contamination, n_estimators=n_estimators, 
                                 max_samples=max_samples, random_state=random_state)
    outliers = iso_forest.fit_predict(data)
    
    output_file = 'isolation_forest_outliers.csv'
    data[outliers == -1].to_csv(output_file, index=False)
    print(f"Isolation Forest outlier detection completed. Outliers saved to {output_file}")
    print(f"Number of outliers detected: {(outliers == -1).sum()}")

if __name__ == "__main__":
    if len(sys.argv) not in [2, 3]:
        print("Usage: python isolation_forest_outlier_detection.py <input_file> [contamination]")
        sys.exit(1)
    input_file = sys.argv[1]
    contamination = float(sys.argv[2]) if len(sys.argv) == 3 else 0.1
    isolation_forest_outlier_detection(input_file, contamination)