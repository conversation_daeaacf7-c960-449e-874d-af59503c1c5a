"""yolov8_caffe"""

import pickle
import requests
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import numpy as np
import caffe
from caffe import layers as L
from caffe import params as P
import xml.etree.ElementTree as ET
import pandas as pd
from tqdm.auto import tqdm
import yaml
import shutil
import cv2

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class CaffeDataLayer(caffe.Layer):
    """Custom data layer for YOLO training in Caffe"""
    
    def setup(self, bottom, top):
        self.batch_size = 32
        self.transform_param = {
            'mean_value': [104, 117, 123],
            'scale': 0.017
        }
        params = eval(self.param_str)
        self.data_root = params['data_root']
        self.anno_path = params['anno_path']
        self._load_dataset()
        
    def _load_dataset(self):
        self.classes = ['aeroplane', 'bicycle', 'bird', 'boat', 'bottle', 'bus', 'car', 
                       'cat', 'chair', 'cow', 'diningtable', 'dog', 'horse', 'motorbike', 
                       'person', 'pottedplant', 'sheep', 'sofa', 'train', 'tvmonitor']
        self.class_to_idx = {cls: idx for idx, cls in enumerate(self.classes)}
        self.images, self.labels = self._load_annotations()
        self.idx = 0
        
    def _load_annotations(self):
        images = []
        labels = []
        for xml_file in os.listdir(self.anno_path):
            if xml_file.endswith('.xml'):
                xml_path = os.path.join(self.anno_path, xml_file)
                tree = ET.parse(xml_path)
                root = tree.getroot()
                
                size = root.find('size')
                width = float(size.find('width').text)
                height = float(size.find('height').text)
                
                image_name = root.find('filename').text
                img_path = os.path.join(self.data_root, image_name)
                images.append(img_path)
                
                label = []
                for obj in root.findall('object'):
                    class_name = obj.find('name').text
                    class_idx = self.class_to_idx[class_name]
                    
                    bbox = obj.find('bndbox')
                    xmin = float(bbox.find('xmin').text)
                    ymin = float(bbox.find('ymin').text)
                    xmax = float(bbox.find('xmax').text)
                    ymax = float(bbox.find('ymax').text)
                    
                    x_center = ((xmin + xmax) / 2) / width
                    y_center = ((ymin + ymax) / 2) / height
                    box_width = (xmax - xmin) / width
                    box_height = (ymax - ymin) / height
                    
                    label.append([class_idx, x_center, y_center, box_width, box_height])
                
                labels.append(label)
        return images, labels
    
    def reshape(self, bottom, top):
        top[0].reshape(self.batch_size, 3, 416, 416)  # image data
        top[1].reshape(self.batch_size, 100, 5)  # labels (max 100 objects per image)
        
    def forward(self, bottom, top):
        for i in range(self.batch_size):
            img = cv2.imread(self.images[self.idx])
            img = cv2.resize(img, (416, 416))
            img = img.transpose((2, 0, 1))  # HWC to CHW
            img = img.astype(np.float32)
            
            # Normalize image
            for c in range(3):
                img[c] = (img[c] - self.transform_param['mean_value'][c]) * self.transform_param['scale']
            
            # Prepare labels
            label_data = np.zeros((100, 5))
            curr_labels = self.labels[self.idx]
            for j, label in enumerate(curr_labels[:100]):  # limit to 100 objects
                label_data[j] = label
            
            top[0].data[i] = img
            top[1].data[i] = label_data
            
            self.idx = (self.idx + 1) % len(self.images)
            
    def backward(self, top, propagate_down, bottom):
        pass

def create_yolo_prototxt(input_size, num_classes, batch_size):
    """Create YOLO network architecture in Caffe prototxt format"""
    net = caffe.NetSpec()
    
    # Data layer
    net.data, net.label = L.Python(
        module='yolov8_caffe',
        layer='CaffeDataLayer',
        param_str=str({
            'data_root': '${DATA_ROOT}',
            'anno_path': '${ANNO_PATH}'
        }),
        ntop=2
    )
    
    # Backbone
    net.conv1 = L.Convolution(net.data, kernel_size=3, num_output=32, pad=1, stride=1)
    net.relu1 = L.ReLU(net.conv1, in_place=True)
    net.pool1 = L.Pooling(net.relu1, pool=P.Pooling.MAX, kernel_size=2, stride=2)
    
    # Add more layers following YOLOv8 architecture...
    # This is a simplified version - you would need to add the complete
    # YOLOv8 architecture here
    
    # Detection heads
    net.bbox_pred = L.Convolution(net.pool1, kernel_size=1, num_output=num_classes * 5)
    net.bbox_reshape = L.Reshape(net.bbox_pred, shape=dict(dim=[batch_size, 5, num_classes, -1]))
    
    # Loss layers
    net.loss = L.YoloLoss(net.bbox_reshape, net.label,
                         detection_param=dict(
                             num_classes=num_classes,
                             conf_threshold=0.5,
                             nms_threshold=0.45
                         ))
    
    return net.to_proto()

def yolov8_train_caffe(dataset, job_params, model_name, result_dir, fit_params=None):
    """
    Train YOLOv8 model using Caffe framework
    """
    input_size = job_params["input_size"]
    num_epochs = job_params["num_epochs"]
    batch_size = job_params["batch_size"]
    
    training_data_path = "/workspace/" + dataset["training_data_path"]
    
    # Create network architecture
    net_proto = create_yolo_prototxt(input_size, len(CaffeDataLayer.classes), batch_size)
    
    # Save prototxt
    model_proto_path = os.path.join(result_dir, "yolov8.prototxt")
    with open(model_proto_path, 'w') as f:
        f.write(str(net_proto))
    
    try:
        # Initialize solver
        solver_param = {
            'base_lr': 0.001,
            'momentum': 0.9,
            'weight_decay': 0.0005,
            'lr_policy': 'step',
            'stepsize': 5000,
            'gamma': 0.1,
            'display': 20,
            'max_iter': num_epochs * 1000,
            'snapshot': 1000,
            'snapshot_prefix': os.path.join(result_dir, "yolov8"),
            'solver_mode': caffe.SOLVER_MODE_GPU,
            'net': model_proto_path
        }
        
        solver = caffe.SGDSolver(solver_param)
        
        # Training loop
        print("Starting training...")
        for iter in tqdm(range(solver_param['max_iter'])):
            solver.step(1)
            
        # Save final model
        model_path = os.path.join(result_dir, model_name + ".caffemodel")
        solver.net.save(model_path)
        print(f'Training complete, model saved to {model_path}')
        
    except Exception as e:
        print(f"Error during training: {e}")
        return str(e), -1
    
    return None, 0

def model_upload(result_dir,model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".pickle"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".pickle")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "pytorch",
          "file_name": model_name+".pickle",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "YOLOv8",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Caffe YOLOv8 Train.')
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='YOLOv8 Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='YOLOv8 DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')
    
    print("Start YOLOv8 training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("YOLOv8 job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("YOLOv8 dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("YOLOv8 result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("YOLOv8 factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("YOLOv8 fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("YOLOv8 sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    
    print("Step 1 YOLOv8 Caffe training:")
    result, ret_code = yolov8_train_caffe(dataset, job_params, model["model_name"], result_dir, fit_params)
    if ret_code != 0:
        print(f"YOLOv8 Caffe train error: {result}")
        sys.exit(-1)
    
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()

