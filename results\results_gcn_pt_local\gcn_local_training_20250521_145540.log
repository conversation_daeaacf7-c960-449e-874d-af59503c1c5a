2025-05-21 14:55:40,806 - GCN_Local - INFO - Logging to console and file: results\results_gcn_pt_local\gcn_local_training_20250521_145540.log
2025-05-21 14:55:40,807 - GCN_Local - INFO - GCN PyTorch Local Mode Training Script Initialized.
2025-05-21 14:55:40,807 - GCN_Local - INFO - PyTorch Version: 2.4.1+cpu
2025-05-21 14:55:40,807 - GCN_Local - INFO - Arguments: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'result_dir': 'results\\results_gcn_pt_local', 'model_name': 'GCN_PT_Local_Model', 'label_column': 'Label', 'data_format': 'pkl', 'hidden_size': 128, 'num_layers': 2, 'dropout_rate': 0.5, 'num_epochs': 3, 'learning_rate': 0.01, 'edge_strategy': 'fully_connected', 'k_neighbors': 5, 'radius': 1.0, 'test_split_size': 0.2, 'random_state': 42, 'normalize': True, 'scaler_type': 'standard', 'handle_imbalance': False, 'stratify_split': True, 'numeric_impute_strategy': 'mean', 'categorical_impute_strategy': 'most_frequent', 'use_cv': False, 'cv_folds': 5, 'log_level': 'INFO', 'force_cpu': False}
2025-05-21 14:55:40,809 - GCN_Local - INFO - Using device: cpu
2025-05-21 14:55:40,814 - GCN_Local - INFO - Loading data from E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl with format pkl
2025-05-21 14:55:43,937 - GCN_Local - INFO - Data loaded as DataFrame with shape (692703, 85)
2025-05-21 14:55:44,070 - GCN_Local - INFO - Features shape: (692703, 84), Labels shape: (692703,)
2025-05-21 14:55:44,101 - GCN_Local - INFO - Label distribution: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-21 14:55:44,131 - GCN_Local - INFO - Standard train/test split mode selected.
2025-05-21 14:55:44,654 - GCN_Local - INFO - Replaced infinite values in features with NaN.
2025-05-21 14:55:44,752 - GCN_Local - INFO - Handling missing values in features.
2025-05-21 14:55:46,843 - GCN_Local - INFO - Imputed numeric missing values using 'mean'.
2025-05-21 14:55:47,357 - GCN_Local - INFO - Imputed categorical missing values using 'most_frequent'.
2025-05-21 14:55:47,468 - GCN_Local - INFO - Encoded labels. 6 classes: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-21 14:55:47,569 - GCN_Local - INFO - Encoding categorical features: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-21 14:55:51,539 - GCN_Local - INFO - Normalizing features using standard scaler.
2025-05-21 14:55:53,960 - GCN_Local - INFO - Split data: X_train (554162, 84), y_train (554162,), X_test (138541, 84), y_test (138541,)
2025-05-21 14:55:54,009 - GCN_Local - INFO - Preprocessing info for train/test split saved to: results\results_gcn_pt_local\GCN_PT_Local_Model_preprocessing_info.json
2025-05-21 14:55:54,010 - GCN_Local - INFO - Data after preprocessing (train/test split): Input size=84, Num classes=6
2025-05-21 14:55:54,010 - GCN_Local - INFO - Preparing graph datasets with edge strategy: fully_connected
2025-05-21 14:55:54,062 - GCN_Local - INFO - Creating fully connected graph for 554162 nodes.
2025-05-21 14:55:54,080 - GCN_Local - CRITICAL - An unexpected critical error occurred: [enforce fail at alloc_cpu.cpp:114] data. DefaultCPUAllocator: not enough memory: you tried to allocate 307095522244 bytes.
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_pt_local_mode.py", line 1072, in gcn_train_local_mode
    train_graph, test_graph = prepare_graph_datasets_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_pt_local_mode.py", line 430, in prepare_graph_datasets_local
    train_graph = create_graph_data_local(X_train_np, y_train_np, edge_strategy, k_neighbors, radius)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_pt_local_mode.py", line 379, in create_graph_data_local
    adj = torch.ones(num_nodes, num_nodes, dtype=torch.bool)
RuntimeError: [enforce fail at alloc_cpu.cpp:114] data. DefaultCPUAllocator: not enough memory: you tried to allocate 307095522244 bytes.
2025-05-21 14:55:54,082 - GCN_Local - CRITICAL - A critical error occurred in __main__: [enforce fail at alloc_cpu.cpp:114] data. DefaultCPUAllocator: not enough memory: you tried to allocate 307095522244 bytes.
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_pt_local_mode.py", line 1256, in <module>
    gcn_train_local_mode(parsed_args)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_pt_local_mode.py", line 1072, in gcn_train_local_mode
    train_graph, test_graph = prepare_graph_datasets_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_pt_local_mode.py", line 430, in prepare_graph_datasets_local
    train_graph = create_graph_data_local(X_train_np, y_train_np, edge_strategy, k_neighbors, radius)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_pt_local_mode.py", line 379, in create_graph_data_local
    adj = torch.ones(num_nodes, num_nodes, dtype=torch.bool)
RuntimeError: [enforce fail at alloc_cpu.cpp:114] data. DefaultCPUAllocator: not enough memory: you tried to allocate 307095522244 bytes.
