import json
import os
import uuid
import numpy as np
import tensorflow as tf
import gym
from collections import deque
import random
import requests
from minio import Minio
import pickle
import sys

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"


"""
1、训练模型：
python script.py --mode train --job_params '{"env_name":"Pendulum-v1","num_episodes":1000}'

2、测试模型：
python script.py --mode test --model_path "/path/to/your/model.pth"

3、在代码中使用保存的模型：
# 加载并使用模型
model_path = "path/to/your/model.pth"
env_name = "Pendulum-v1"
load_and_use_model(model_path, env_name, num_episodes=5, render=True)

"""


class Actor(tf.keras.Model):
    """Actor Network for DDPG"""

    def __init__(self, state_size, action_size, hidden_size=256):
        super(Actor, self).__init__()
        self.fc1 = tf.keras.layers.Dense(hidden_size, activation='relu')
        self.fc2 = tf.keras.layers.Dense(hidden_size, activation='relu')
        self.fc3 = tf.keras.layers.Dense(action_size, activation='tanh')  # Output in range [-1, 1]

    def call(self, x):
        if len(x.shape) == 1:
            x = tf.expand_dims(x, 0)
        x = self.fc1(x)
        x = self.fc2(x)
        x = self.fc3(x)
        return x


class Critic(tf.keras.Model):
    """Critic Network for DDPG"""

    def __init__(self, state_size, action_size, hidden_size=256):
        super(Critic, self).__init__()
        self.fc1 = tf.keras.layers.Dense(hidden_size, activation='relu')
        self.fc2 = tf.keras.layers.Dense(hidden_size + action_size, activation='relu')
        self.fc3 = tf.keras.layers.Dense(1)

    def call(self, state, action):
        if len(state.shape) == 1:
            state = tf.expand_dims(state, 0)
        if len(action.shape) == 1:
            action = tf.expand_dims(action, 0)
        xs = self.fc1(state)
        x = tf.concat([xs, action], axis=1)
        x = self.fc2(x)
        x = self.fc3(x)
        return x


class OUNoise:
    """Ornstein-Uhlenbeck process for exploration"""

    def __init__(self, size, mu=0., theta=0.15, sigma=0.2):
        self.mu = mu * np.ones(size)
        self.theta = theta
        self.sigma = sigma
        self.reset()

    def reset(self):
        self.state = np.copy(self.mu)

    def sample(self):
        x = self.state
        dx = self.theta * (self.mu - x) + self.sigma * np.random.randn(len(x))
        self.state = x + dx
        return self.state


class ReplayBuffer:
    """Experience replay buffer"""

    def __init__(self, buffer_size):
        self.buffer = deque(maxlen=buffer_size)

    def add(self, state, action, reward, next_state, done):
        self.buffer.append((state, action, reward, next_state, done))

    def sample(self, batch_size):
        batch = random.sample(self.buffer, batch_size)
        states, actions, rewards, next_states, dones = zip(*batch)
        return (np.array(states), np.array(actions), np.array(rewards),
                np.array(next_states), np.array(dones))

    def __len__(self):
        return len(self.buffer)


class DDPGAgent:
    """DDPG Agent"""

    def __init__(self, state_size, action_size, hidden_size=256,
                 actor_lr=1e-4, critic_lr=1e-3, gamma=0.99,
                 tau=1e-3, buffer_size=100000, batch_size=64,
                 device_name="/cpu:0"):
        self.state_size = state_size
        self.action_size = action_size
        self.gamma = gamma
        self.tau = tau
        self.batch_size = batch_size
        self.device_name = device_name

        # Actor Networks
        with tf.device(self.device_name):
            self.actor = Actor(state_size, action_size, hidden_size)
            self.actor_target = Actor(state_size, action_size, hidden_size)
            self.actor_optimizer = tf.keras.optimizers.Adam(learning_rate=actor_lr)

        # Critic Networks
        with tf.device(self.device_name):
            self.critic = Critic(state_size, action_size, hidden_size)
            self.critic_target = Critic(state_size, action_size, hidden_size)
            self.critic_optimizer = tf.keras.optimizers.Adam(learning_rate=critic_lr)

        # Copy weights
        self.soft_update(1.0)

        # Noise process
        self.noise = OUNoise(action_size)

        # Replay buffer
        self.memory = ReplayBuffer(buffer_size)

    @tf.function
    def act(self, state, add_noise=True):
        """Select an action"""
        with tf.device(self.device_name):
            state = tf.convert_to_tensor(state, dtype=tf.float32)
            action = self.actor(state)
            if add_noise:
                action += self.noise.sample()
            return tf.clip_by_value(action, -1, 1).numpy()

    def step(self, state, action, reward, next_state, done):
        """Save experience and learn"""
        self.memory.add(state, action, reward, next_state, done)

        if len(self.memory) > self.batch_size:
            experiences = self.memory.sample(self.batch_size)
            self.learn(experiences)

    def learn(self, experiences):
        """Update policy and value parameters"""
        states, actions, rewards, next_states, dones = experiences

        # Convert to tensors
        with tf.device(self.device_name):
            states = tf.convert_to_tensor(states, dtype=tf.float32)
            actions = tf.convert_to_tensor(actions, dtype=tf.float32)
            rewards = tf.convert_to_tensor(rewards, dtype=tf.float32)
            next_states = tf.convert_to_tensor(next_states, dtype=tf.float32)
            dones = tf.convert_to_tensor(dones, dtype=tf.float32)

        # Update Critic
        next_actions = self.actor_target(next_states)
        target_Q = self.critic_target(next_states, next_actions)
        target_value = rewards + (1 - dones) * self.gamma * target_Q
        current_Q = self.critic(states, actions)

        critic_loss = tf.keras.losses.MeanSquaredError()(current_Q, target_value)
        self.critic_optimizer.minimize(critic_loss, var_list=self.critic.trainable_variables)

        # Update Actor
        with tf.device(self.device_name):
            with tf.GradientTape() as tape:
                actor_loss = -tf.reduce_mean(self.critic(states, self.actor(states)))
            grads = tape.gradient(actor_loss, self.actor.trainable_variables)
            self.actor_optimizer.apply_gradients(zip(grads, self.actor.trainable_variables))

        # Soft update target networks
        self.soft_update(self.tau)

        return actor_loss.numpy(), critic_loss.numpy()

    def soft_update(self, tau):
        """Soft update target network parameters"""
        with tf.device(self.device_name):
            for target_param, param in zip(self.actor_target.trainable_variables, self.actor.trainable_variables):
                target_param.assign(tau * param + (1.0 - tau) * target_param)
            for target_param, param in zip(self.critic_target.trainable_variables, self.critic.trainable_variables):
                target_param.assign(tau * param + (1.0 - tau) * target_param)

    def save(self, filepath):
        """
        保存模型和训练状态
        """
        save_dict = {
            'actor_weights': self.actor.get_weights(),
            'actor_target_weights': self.actor_target.get_weights(),
            'critic_weights': self.critic.get_weights(),
            'critic_target_weights': self.critic_target.get_weights(),
            'actor_optimizer_weights': self.actor_optimizer.get_weights(),
            'critic_optimizer_weights': self.critic_optimizer.get_weights(),
            'hyperparameters': {
                'state_size': self.state_size,
                'action_size': self.action_size,
                'gamma': self.gamma,
                'tau': self.tau,
                'batch_size': self.batch_size
            }
        }
        with open(filepath, 'wb') as f:
            pickle.dump(save_dict, f)

    def load(self, filepath, eval_mode=True):
        """
        加载模型和训练状态
        eval_mode: 如果为True，则将模型设置为评估模式
        """
        with open(filepath, 'rb') as f:
            checkpoint = pickle.load(f)

        # 加载模型参数
        self.actor.set_weights(checkpoint['actor_weights'])
        self.actor_target.set_weights(checkpoint['actor_target_weights'])
        self.critic.set_weights(checkpoint['critic_weights'])
        self.critic_target.set_weights(checkpoint['critic_target_weights'])

        # 加载优化器状态
        self.actor_optimizer.set_weights(checkpoint['actor_optimizer_weights'])
        self.critic_optimizer.set_weights(checkpoint['critic_optimizer_weights'])

        if eval_mode:
            self.actor.trainable = False
            self.critic.trainable = False
        else:
            self.actor.trainable = True
            self.critic.trainable = True

        # 验证超参数
        loaded_params = checkpoint['hyperparameters']
        assert self.state_size == loaded_params['state_size'], "State size mismatch"
        assert self.action_size == loaded_params['action_size'], "Action size mismatch"


def prepare_env(env_name):
    """Prepare OpenAI Gym environment"""
    env = gym.make(env_name)
    return env


def train_model(env, agent, num_episodes, max_steps):
    """Train DDPG model"""
    scores = []

    for episode in range(num_episodes):
        state = env.reset()
        agent.noise.reset()
        score = 0

        for step in range(max_steps):
            action = agent.act(state)
            next_state, reward, done, _ = env.step(action)
            agent.step(state, action, reward, next_state, done)

            state = next_state
            score += reward

            if done:
                break

        scores.append(score)

        if (episode + 1) % 100 == 0:
            avg_score = np.mean(scores[-100:])
            print(f'Episode {episode + 1}, Average Score: {avg_score:.2f}')

    return scores


def ddpg_train(dataset, job_params, model_name, result_dir, fit_params=None, device="/cpu:0"):
    """
    Train DDPG model main function
    Parameters:
    - env_name: OpenAI Gym environment name
    - num_episodes: Number of training episodes
    - max_steps: Maximum steps per episode
    - actor_lr: Actor learning rate
    - critic_lr: Critic learning rate
    - gamma: Discount factor
    """
    # Get training parameters
    env_name = job_params.get("env_name", "Pendulum-v1")
    num_episodes = job_params.get("num_episodes", 1000)
    max_steps = job_params.get("max_steps", 500)
    actor_lr = job_params.get("actor_lr", 1e-4)
    critic_lr = job_params.get("critic_lr", 1e-3)
    gamma = job_params.get("gamma", 0.99)

    # Prepare environment
    env = prepare_env(env_name)
    state_size = env.observation_space.shape[0]
    action_size = env.action_space.shape[0]

    # Create agent
    agent = DDPGAgent(
        state_size=state_size,
        action_size=action_size,
        actor_lr=actor_lr,
        critic_lr=critic_lr,
        gamma=gamma,
        device=device
    )

    try:
        # 训练模型
        scores = train_model(env, agent, num_episodes, max_steps)

        # 保存完整模型状态
        model_path = os.path.join(result_dir, f"{model_name}.pkl")
        agent.save(model_path)

        # 保存训练信息
        info_path = os.path.join(result_dir, f"{model_name}_info.json")
        training_info = {
            'env_name': env_name,
            'num_episodes': num_episodes,
            'max_steps': max_steps,
            'final_average_score': np.mean(scores[-100:]),
            'scores': scores,
            'hyperparameters': {
                'actor_lr': actor_lr,
                'critic_lr': critic_lr,
                'gamma': gamma,
                'state_size': state_size,
                'action_size': action_size
            }
        }
        with open(info_path, 'w') as f:
            json.dump(training_info, f, indent=4)

        print(f'训练完成，模型保存到 {model_path}')
        print(f'训练信息保存到 {info_path}')

    except Exception as e:
        print(f"训练过程中发生错误: {e}")
        return str(e), -1

    return None, 0


def load_and_use_model(model_path, env_name, num_episodes=5, render=True):
    """
    加载并使用训练好的模型
    """
    # 加载训练信息以获取环境参数
    info_path = model_path.replace('.pkl', '_info.json')
    with open(info_path, 'r') as f:
        training_info = json.load(f)

    # 创建环境
    env = gym.make(env_name)
    state_size = env.observation_space.shape[0]
    action_size = env.action_space.shape[0]

    # 创建agent并加载模型
    agent = DDPGAgent(state_size=state_size,
                      action_size=action_size)
    agent.load(model_path, eval_mode=True)

    # 运行模型
    for episode in range(num_episodes):
        state = env.reset()
        episode_reward = 0
        done = False

        while not done:
            if render:
                env.render()

            # 使用模型选择动作（不添加噪声）
            action = agent.act(state, add_noise=False)
            next_state, reward, done, _ = env.step(action)
            episode_reward += reward
            state = next_state

        print(f"Episode {episode + 1}: Reward = {episode_reward:.2f}")

    env.close()


def model_upload(result_dir, model_name):
    minioClient = Minio(MINIO_URL,
                        access_key='AKIAIOSFODNN7EXAMPLE',
                        secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                        secure=False)
    try:
        obj_name = str(uuid.uuid1())
        upload_path = obj_name + "/" + model_name + ".pkl"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir + "/" + model_name + ".pkl")
        result = {"source": source}
        return result, 0
    except Exception as err:
        print(err)
        return None, -1


def model_register(model_name, source, group_id, headers):
    params = {
        "model_name": model_name,
        "model_type": "TENSORFLOW",
        "file_name": model_name + ".pkl",
        "s3_path": source,
        "group_id": int(float(group_id)),
        "training_id": model_name,
        "training_flag": 1,
    }

    r = requests.post(MODEL_FACTORY_URL + MODEL_ADD_URL, data=json.dumps(params), headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1


def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0

    params = {
        "factory_name": factory_name,
        "model_type": "DDPG",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL + MODEL_PUSH_URL + "/" + str(model_version_id), data=json.dumps(params),
                      headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1


def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: user_id
    }
    return headers


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='tensorflow DDPG Train.')
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                        help='DDPG Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                        help='DDPG DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                        help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                        help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                        help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                        help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str, default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                        help='sparkconf params')
    parser.add_argument('--nodehost', nargs='?', const=None, dest='nodehost', type=str, default="**************",
                        help='nodehost params')

    print("Start DDPG training job, params :\n" + str(sys.argv) + "\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("DDPG job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("DDPG dataSet:" + str(dataset) + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("DDPG result dir:" + result_dir + "\n")
    factory_name = args.factory_name
    print("DDPG factory name:" + factory_name + "\n")
    fit_params = args.fit_params
    print("DDPG fit params:" + str(fit_params) + "\n")
    sparkconf = json.loads(args.sparkconf)
    print("DDPG sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params is None:
        fit_params = {}

    device = '/GPU:0' if tf.config.list_physical_devices('GPU') else '/CPU:0'
    print("Step 1 DDPG training:\n")
    result, ret_code = ddpg_train(dataset, job_params, model["model_name"], result_dir, fit_params, device)
    if ret_code!= 0:
        print("DDPG train err, stop job....\n")
        print("Error Msg:" + result + "\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")

    print("Step 2 Model Upload to MinIO: \n")
    result, ret_code = model_upload(result_dir, model["model_name"])
    if ret_code!= 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")

    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result, ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code!= 0:
        print("model register, stop job....,err msg: " + result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result, ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code!= 0:
        print("model push error, stop job....err msg: " + result + "\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()