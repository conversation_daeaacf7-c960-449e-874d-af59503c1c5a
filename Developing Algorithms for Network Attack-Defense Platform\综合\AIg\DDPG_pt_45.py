"""DDPG"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim

class Actor(nn.Module):
    def __init__(self, state_dim, action_dim, max_action):
        super(Actor, self).__init__()
        self.l1 = nn.Linear(state_dim, 400)
        self.l2 = nn.Linear(400, 300)
        self.l3 = nn.Linear(300, action_dim)
        self.max_action = max_action

    def forward(self, x):
        x = torch.relu(self.l1(x))
        x = torch.relu(self.l2(x))
        x = torch.tanh(self.l3(x)) * self.max_action
        return x

class Critic(nn.Module):
    def __init__(self, state_dim, action_dim):
        super(Critic, self).__init__()
        self.l1 = nn.Linear(state_dim + action_dim, 400)
        self.l2 = nn.Linear(400, 300)
        self.l3 = nn.Linear(300, 1)

    def forward(self, x, u):
        x = torch.relu(self.l1(torch.cat([x, u], 1)))
        x = torch.relu(self.l2(x))
        x = self.l3(x)
        return x

class DDPGAgent:
    def __init__(self, state_dim, action_dim, max_action, discount=0.99, tau=0.005):
        self.actor = Actor(state_dim, action_dim, max_action).to(device)
        self.actor_target = Actor(state_dim, action_dim, max_action).to(device)
        self.actor_target.load_state_dict(self.actor.state_dict())
        self.actor_optimizer = optim.Adam(self.actor.parameters(), lr=3e-4)

        self.critic = Critic(state_dim, action_dim).to(device)
        self.critic_target = Critic(state_dim, action_dim).to(device)
        self.critic_target.load_state_dict(self.critic.state_dict())
        self.critic_optimizer = optim.Adam(self.critic.parameters(), lr=3e-4)

        self.max_action = max_action
        self.discount = discount
        self.tau = tau

    def select_action(self, state):
        state = torch.FloatTensor(state.reshape(1, -1)).to(device)
        return self.actor(state).cpu().data.numpy().flatten()

    def train(self, replay_buffer, batch_size=100):
        state, action, next_state, reward, not_done = replay_buffer.sample(batch_size)

        with torch.no_grad():
            next_action = self.actor_target(next_state)
            target_q = self.critic_target(next_state, next_action)
            target_q = reward + not_done * self.discount * target_q

        current_q = self.critic(state, action)
        critic_loss = nn.functional.mse_loss(current_q, target_q)

        self.critic_optimizer.zero_grad()
        critic_loss.backward()
        self.critic_optimizer.step()

        actor_loss = -self.critic(state, self.actor(state)).mean()

        self.actor_optimizer.zero_grad()
        actor_loss.backward()
        self.actor_optimizer.step()

        for param, target_param in zip(self.critic.parameters(), self.critic_target.parameters()):
            target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

        for param, target_param in zip(self.actor.parameters(), self.actor_target.parameters()):
            target_param.data.copy_(self.tau * param.data + (1 - self.tau) * target_param.data)

if __name__ == "__main__":
    state_dim = 3
    action_dim = 1
    max_action = 1
    agent = DDPGAgent(state_dim, action_dim, max_action)

    class ReplayBuffer:
        def __init__(self, max_size):
            self.storage = []
            self.max_size = max_size
            self.ptr = 0

        def add(self, transition):
            if len(self.storage) == self.max_size:
                self.storage[int(self.ptr)] = transition
                self.ptr = (self.ptr + 1) % self.max_size
            else:
                self.storage.append(transition)

        def sample(self, batch_size):
            ind = np.random.randint(0, len(self.storage), size=batch_size)
            state, action, next_state, reward, not_done = [], [], [], [], []

            for i in ind:
                s, a, n_s, r, d = self.storage[i]
                state.append(np.array(s, copy=False))
                action.append(np.array(a, copy=False))
                next_state.append(np.array(n_s, copy=False))
                reward.append(np.array(r, copy=False))
                not_done.append(np.array(1 - d, copy=False))

            return (
                torch.FloatTensor(np.array(state)).to(device),
                torch.FloatTensor(np.array(action)).to(device),
                torch.FloatTensor(np.array(next_state)).to(device),
                torch.FloatTensor(np.array(reward)).to(device),
                torch.FloatTensor(np.array(not_done)).to(device),
            )

    replay_buffer = ReplayBuffer(max_size=1000000)

    for episode in range(1000):
        state = np.random.randn(state_dim)
        done = False
        while not done:
            action = agent.select_action(state)
            next_state = state + action + np.random.randn(state_dim) * 0.1
            reward = -np.sum(np.square(action))
            done = np.random.rand() > 0.95
            replay_buffer.add((state, action, next_state, reward, done))
            state = next_state

        agent.train(replay_buffer)
        print(f"Episode {episode+1} completed")
