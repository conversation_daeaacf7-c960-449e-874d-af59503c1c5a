# BiLSTM推理服务核心依赖
# 基于BiLSTMPredictor.py的最小依赖集

# PyTorch核心
torch>=1.13.0,<2.2.0
torchvision>=0.14.0,<0.17.0
torchaudio>=0.13.0,<2.2.0

# 数据处理
numpy>=1.21.0,<1.25.0
pandas>=1.4.0,<1.6.0

# 机器学习
scikit-learn>=1.1.0,<1.4.0

# Web服务
flask>=2.2.0
fastapi>=0.95.0
uvicorn[standard]>=0.20.0

# Seldon Core
seldon-core>=1.13.1

# 工具和配置
pyyaml>=6.0
click>=8.1.0
tqdm>=4.65.0
requests>=2.28.0
joblib>=1.2.0
psutil>=5.9.0

# 日志和调试
rich>=13.3.0 