2025-06-13 14:52:02,346 - YOLOv8_local - INFO - Logging to file: ./results/results_yolov8_pt_local\YOLOv8_local_training_20250613_145202.log
2025-06-13 14:52:02,346 - YOLOv8_local - INFO - YOLOv8 PyTorch Local Mode. Args: {'data_path': './demo_data', 'dataset_type': 'folder', 'annotations_file': None, 'input_size': 640, 'model_name': 'YOLOv8_local', 'model_type': 'nano', 'pretrained_model_path': None, 'mode': 'all', 'num_epochs': 2, 'batch_size': 16, 'conf_thres': 0.25, 'iou_thres': 0.45, 'early_stopping_patience': 30, 'use_cv': False, 'cv_folds': 5, 'trained_model_path': None, 'test_data_path': None, 'annotations_file_test': None, 'result_dir': './results/results_yolov8_pt_local', 'random_seed': 42, 'force_cpu': False, 'log_level': 'INFO'}
2025-06-13 14:52:02,736 - YOLOv8_local - INFO - Using device: cpu
2025-06-13 14:52:02,742 - YOLOv8_local - INFO - Random seed set to: 42
2025-06-13 14:52:02,742 - YOLOv8_local - INFO - --- Training Mode ---
2025-06-13 14:52:02,742 - YOLOv8_local - INFO - Starting Standard Training...
2025-06-13 14:52:35,169 - YOLOv8_local - INFO - Training completed. Model saved: ./results/results_yolov8_pt_local\YOLOv8_local.pt
2025-06-13 14:52:35,709 - YOLOv8_local - INFO - --- Testing Mode ---
2025-06-13 14:52:35,757 - YOLOv8_local - INFO - Testing completed. Test metrics: {'error': 'Unsupported dataset_type for testing: folder'}
2025-06-13 14:52:35,758 - YOLOv8_local - INFO - Full run summary saved to: ./results/results_yolov8_pt_local\YOLOv8_local_full_summary.json
