"""高斯混合聚类"""

import sys
import pandas as pd
from sklearn.mixture import GaussianMixture

def gaussian_mixture_clustering(input_file, n_components=1):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - n_components: 高斯成分数量，默认为1
    """
    data = pd.read_csv(input_file)
    
    model = GaussianMixture(n_components=n_components)
    model.fit(data)
    
    clusters = model.predict(data)
    
    output_file = 'gaussian_mixture_clusters.csv'
    pd.DataFrame({'Cluster': clusters}).to_csv(output_file, index=False)
    print(f"Gaussian mixture clustering completed. Output saved to {output_file}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python gaussian_mixture_clustering.py <input_file> <n_components>")
        sys.exit(1)
    input_file, n_components = sys.argv[1], sys.argv[2]
    gaussian_mixture_clustering(input_file, int(n_components))
