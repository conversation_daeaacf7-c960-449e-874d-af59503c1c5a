"""K-Means 聚类 - 增强版

此脚本使用K-Means聚类算法对数据集进行聚类分析，
支持多种数据格式、灵活的预处理选项和结果可视化。

功能:
- 灵活数据加载(支持pkl、csv、json格式)
- 高级聚类分析与评估
- 结果可视化(散点图、聚类分布)
- 模型保存与管理(MinIO存储)
- 完整的日志记录
- 错误处理机制

使用方法:
python kmeans_local.py --data your_data_path.pkl --n_clusters 5 --output ./results
"""

import pickle
import requests
import joblib
import sys
import argparse
import json
from minio import Minio
import uuid
import os
from sklearn.cluster import KMeans
import logging
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 非交互式后端，适用于无显示环境
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import traceback
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score, calinski_harabasz_score
from sklearn.model_selection import KFold, GridSearchCV

MINIO_URL = "minio-prophecis.prophecis:9000"

MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"


MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

# 使用常量定义Minio凭证，便于维护
MINIO_ACCESS_KEY = 'AKIAIOSFODNN7EXAMPLE'
MINIO_SECRET_KEY = 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY'

def setup_logging(log_dir, log_level=logging.INFO):
    """设置日志配置"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"kmeans_{timestamp}.log")
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger("KMeans")

def flexible_data_load(file_path, feature_cols=None, data_format='pkl'):
    """
    灵活加载不同格式的数据集
    
    参数:
    file_path: 数据文件路径
    feature_cols: 特征列名列表，None表示使用所有列
    data_format: 数据格式，支持'pkl'、'csv'、'json'等
    
    返回:
    X: 特征数据
    """
    logger = logging.getLogger("KMeans")
    logger.info(f"加载数据，文件路径: {file_path}，格式: {data_format}")
    
    # 加载不同格式的数据
    if data_format.lower() == 'pkl':
        with open(file_path, 'rb') as file:
            data = pickle.load(file, encoding='bytes')
    elif data_format.lower() == 'csv':
        data = pd.read_csv(file_path)
    elif data_format.lower() == 'json':
        data = pd.read_json(file_path)
    else:
        raise ValueError(f"不支持的数据格式: {data_format}")
    
    # 处理不同类型的数据结构
    if isinstance(data, pd.DataFrame):
        logger.info(f"数据加载为DataFrame，形状: {data.shape}")
        
        # 清理列名，去除可能的空格
        data.columns = data.columns.str.strip()
        
        # 根据特征列选择特征
        if feature_cols is not None:
            missing_cols = [col for col in feature_cols if col not in data.columns]
            if missing_cols:
                logger.warning(f"部分特征列未在数据中找到: {missing_cols}")
            X = data[feature_cols]
        else:
            X = data
        
        logger.info(f"特征形状: {X.shape}")
    
    elif isinstance(data, tuple) and len(data) >= 2:
        # 处理已经分割好的数据集，用于聚类只需要X部分
        logger.info("数据加载为元组（已分割）")
        # 假设前两个元素是训练数据和训练标签
        X = data[0]
        
        # 检查数据类型，确保可以处理
        logger.info(f"特征类型: {type(X)}, 形状: {X.shape if hasattr(X, 'shape') else '未知'}")
    
    elif isinstance(data, dict):
        logger.info("数据加载为字典")
        if 'features' in data:
            X = data['features']
        elif 'data' in data:
            X = data['data']
        else:
            # 检查是否包含训练、测试数据
            if 'train' in data and 'test' in data:
                # 只返回训练数据
                if isinstance(data['train'], tuple) and len(data['train']) >= 1:
                    X = data['train'][0]
                else:
                    raise ValueError("无法识别的训练数据格式")
            else:
                raise ValueError("字典数据应包含'features'或'data'键")
    else:
        # 尝试处理tuple类型的特殊情况：(train_data, validation_data, test_data)
        if isinstance(data, tuple) and len(data) == 3:
            # 处理原始代码中的特殊格式
            training_data = data[0]
            if isinstance(training_data, tuple) and len(training_data) >= 1:
                X = training_data[0]
                logger.info(f"从元组格式加载数据，形状: X={X.shape if hasattr(X, 'shape') else '未知'}")
                return X
            else:
                raise TypeError(f"训练数据格式不符合预期: {type(training_data)}")
        else:
            raise TypeError(f"不支持的数据类型: {type(data)}")
    
    # 确保X是二维的
    if hasattr(X, 'ndim') and X.ndim == 1:
        X = X.reshape(-1, 1)
        logger.info(f"将一维数据转换为二维，新形状: {X.shape}")
    
    logger.info(f"处理后的特征形状: {X.shape if hasattr(X, 'shape') else '未知'}")
    return X

def kmeans_clustering(dataset, job_params, model_name, result_dir, fit_params=None):
    """
    参数说明：
    - n_clusters: 聚类数量，默认为8
    """
    training_data_path = "/workspace/" + dataset["training_data_path"]
    f = open(training_data_path, 'rb')
    training_data, validation_data, test_data = pickle.load(f, encoding='bytes')
    train_x, train_y = training_data

    # 初始化K均值聚类模型
    n_clusters = job_params["n_clusters"]
    kmeans = KMeans(n_clusters=n_clusters)

    # 拟合模型并进行聚类
    kmeans.fit(train_x)
    labels = kmeans.predict(train_x)

    model_file = open(result_dir + "/" + model_name + ".pickle", "wb")
    pickle.dump(kmeans, model_file)
    model_file.close()
    print(f'K均值聚类完成，模型保存到 {result_dir}/{model_name}.pickle')

    return labels, 0

def find_optimal_clusters(X, max_clusters=10, random_state=42):
    """
    使用肘部法则和轮廓分析寻找最佳聚类数
    
    参数:
    X: 特征数据
    max_clusters: 最大聚类数
    random_state: 随机种子
    
    返回:
    best_k: 最佳聚类数
    elbow_data: 肘部法则数据，用于绘图
    """
    logger = logging.getLogger("KMeans")
    logger.info(f"开始寻找最佳聚类数，最大聚类数: {max_clusters}")
    
    # 记录开始时间
    start_time = datetime.now()
    
    # 保存每个k值对应的惯性和轮廓系数
    inertia_values = []
    silhouette_values = []
    k_range = range(2, max_clusters + 1)
    
    for k in k_range:
        logger.info(f"评估聚类数 k={k}")
        
        # 训练K均值模型
        kmeans = KMeans(n_clusters=k, random_state=random_state)
        kmeans.fit(X)
        
        # 计算惯性
        inertia = kmeans.inertia_
        inertia_values.append(inertia)
        
        # 计算轮廓系数
        labels = kmeans.labels_
        try:
            silhouette = silhouette_score(X, labels)
            silhouette_values.append(silhouette)
            logger.info(f"k={k}, 惯性={inertia:.4f}, 轮廓系数={silhouette:.4f}")
        except Exception as e:
            silhouette_values.append(0)
            logger.warning(f"k={k}, 计算轮廓系数失败: {str(e)}")
    
    # 计算总耗时
    total_time = (datetime.now() - start_time).total_seconds()
    logger.info(f"评估完成，总耗时: {total_time:.2f}秒")
    
    # 肘部法则：计算惯性变化率
    inertia_diffs = np.diff(inertia_values)
    inertia_diffs_rate = inertia_diffs / inertia_values[:-1]
    
    # 找到拐点
    # 当惯性变化率变小，说明增加聚类数的收益减少
    diffs_of_diffs = np.diff(inertia_diffs_rate)
    potential_elbows = np.where(diffs_of_diffs > 0)[0] + 2  # +2是因为计算了两次差分，并且k从2开始
    
    # 选择轮廓系数最大的k作为最佳聚类数
    silhouette_arr = np.array(silhouette_values)
    best_silhouette_k = k_range[np.argmax(silhouette_values)]
    
    # 综合考虑肘部法则和轮廓系数
    # 如果有明显的拐点，且拐点处的轮廓系数还不错，则选择拐点
    # 否则选择轮廓系数最大的k
    best_k = best_silhouette_k
    
    if len(potential_elbows) > 0:
        elbow_k = potential_elbows[0]
        # 如果拐点的轮廓系数至少达到最大轮廓系数的80%，则选择拐点
        if silhouette_values[elbow_k-2] >= 0.8 * silhouette_values[best_silhouette_k-2]:
            best_k = elbow_k
            logger.info(f"基于肘部法则选择聚类数 k={best_k}")
        else:
            logger.info(f"基于轮廓系数选择聚类数 k={best_k}")
    else:
        logger.info(f"未找到明显的拐点，基于轮廓系数选择聚类数 k={best_k}")
    
    # 准备肘部法则数据用于绘图
    elbow_data = {
        'k_range': list(k_range),
        'inertia': inertia_values,
        'silhouette': silhouette_values
    }
    
    return best_k, elbow_data

def convert_to_serializable(obj):
    """
    将对象转换为可序列化的格式
    
    参数:
    obj: 需要转换的对象
    
    返回:
    转换后的对象
    """
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, dict):
        return {k: convert_to_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_serializable(item) for item in obj]
    else:
        return obj

def model_upload(result_dir, model_name):    
    """
    将模型上传到 MinIO 存储
    
    参数:
    result_dir: 结果保存目录
    model_name: 模型名称
    
    返回:
    result: 上传结果
    ret_code: 返回代码
    """
    minioClient = Minio(MINIO_URL,
                  access_key=MINIO_ACCESS_KEY,
                  secret_key=MINIO_SECRET_KEY,
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".pickle"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".pickle")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        logger = logging.getLogger("KMeans")
        logger.error(f"模型上传错误: {str(err)}")
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    """
    注册模型到模型工厂
    
    参数:
    model_name: 模型名称
    source: 模型源路径
    group_id: 模型组ID
    headers: 请求头
    
    返回:
    result: 注册结果
    ret_code: 返回代码
    """
    params = {
          "model_name": model_name,
          "model_type": "MLPipeline",
          "file_name": model_name+".pickle",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name, headers):
    """
    推送模型到工厂
    
    参数:
    model_id: 模型ID
    model_version_id: 模型版本ID
    factory_name: 工厂名称
    headers: 请求头
    
    返回:
    result: 推送结果
    ret_code: 返回代码
    """
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "KMeans",
        "model_usage": "Clustering"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    """
    生成请求头
    
    参数:
    user_id: 用户ID
    
    返回:
    headers: 请求头
    """
    if user_id is None:
        user_id = "admin"  # 默认用户ID
        
    headers = {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: user_id
        }
    return headers

    
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='MLPipeline  kmeans Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help=' kmeans Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help=' kmeans DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start  kmeans training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print(" kmeans job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print(" kmeans dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print(" kmeans result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print(" kmeans factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print(" kmeans fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("kmeans sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
     
    print("Step 1  kmeans training:\n")
    result,ret_code =  kmeans_clustering(dataset,job_params, model["model_name"],result_dir,fit_params)
    if ret_code != 0:
        print(" kmeans train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name, headers)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()
   
  
