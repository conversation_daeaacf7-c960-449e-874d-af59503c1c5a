2025-05-21 14:36:47,803 - GAT_TF_Local - INFO - Logging to console and file: ./results/results_gat_tf_local\gat_tf_local_training_20250521_143647.log
2025-05-21 14:36:47,804 - GAT_TF_Local - INFO - GAT TensorFlow Local Mode Training Script Started.
2025-05-21 14:36:47,804 - GAT_TF_Local - INFO - TensorFlow Version: 2.13.0
2025-05-21 14:36:47,804 - GAT_TF_Local - INFO - Provided arguments: Namespace(adj_type='identity', categorical_impute_strategy='most_frequent', cv_folds=5, data_format='pkl', dropout=0.5, final_dropout=0.5, handle_imbalance=False, hidden_size=64, input_file='E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', k_neighbors=5, l2_reg=0.0001, label_column='Label', learning_rate=0.005, log_level='INFO', model_name='GAT_TF_Local_Model', normalize=True, num_epochs=3, num_heads=4, numeric_impute_strategy='mean', random_state=42, result_dir='./results/results_gat_tf_local', scaler_type='standard', stratify_split=True, test_split_size=0.2, use_cv=False)
2025-05-21 14:36:47,806 - GAT_TF_Local - INFO - Loading data from: E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl (Format: pkl, Label: 'Label')
2025-05-21 14:36:51,070 - GAT_TF_Local - INFO - Data loaded: X shape (692703, 84), y shape (692703,). Label dist: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-21 14:36:51,100 - GAT_TF_Local - INFO - Standard train/test split mode selected for GAT (TF).
2025-05-21 14:36:51,598 - GAT_TF_Local - INFO - Replaced infinite values with NaN.
2025-05-21 14:36:51,692 - GAT_TF_Local - INFO - Handling missing values.
2025-05-21 14:36:53,818 - GAT_TF_Local - INFO - Imputed numeric missing values using 'mean'.
2025-05-21 14:36:54,343 - GAT_TF_Local - INFO - Imputed categorical missing values using 'most_frequent'.
2025-05-21 14:36:54,450 - GAT_TF_Local - INFO - Encoded labels. Classes: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-21 14:36:54,532 - GAT_TF_Local - INFO - Encoding categorical features: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-21 14:36:58,606 - GAT_TF_Local - INFO - Normalizing features using standard scaler.
2025-05-21 14:37:00,394 - GAT_TF_Local - INFO - Split data: X_train (554162, 84), y_train (554162,), X_test (138541, 84), y_test (138541,)
2025-05-21 14:37:00,548 - GAT_TF_Local - INFO - Preprocessing info saved to ./results/results_gat_tf_local\GAT_TF_Local_Model_preprocessing_info.json
2025-05-21 14:37:00,549 - GAT_TF_Local - INFO - Data after preprocessing: Input size=84, Num classes=6
2025-05-21 14:37:00,614 - GAT_TF_Local - INFO - Combined train/test for graph: Total nodes 692703, Train 554162, Val/Test 138541
2025-05-21 14:37:00,615 - GAT_TF_Local - INFO - Using special 'IDENTITY_ADJACENCY' marker for 692703 nodes (self-loops only).
2025-05-21 14:37:00,859 - GAT_TF_Local - INFO - Starting GAT (TF) model training (standard split).
2025-05-21 14:37:00,859 - GAT_TF_Local - INFO - Starting GAT (TF) model training for 3 epochs.
2025-05-21 14:37:18,943 - GAT_TF_Local - INFO - Epoch [1/3] Train Loss: 2.6390, Train Acc: 18.45%, Val Loss: 0.8594, Val Acc: 73.44%
2025-05-21 14:37:47,943 - GAT_TF_Local - INFO - Epoch [3/3] Train Loss: 0.8038, Train Acc: 76.19%, Val Loss: 0.4145, Val Acc: 88.63%
2025-05-21 14:37:47,970 - GAT_TF_Local - INFO - Model config saved to ./results/results_gat_tf_local\models\GAT_TF_Local_Model_final_config.json, weights to ./results/results_gat_tf_local\models\GAT_TF_Local_Model_final_weights.h5
2025-05-21 14:37:47,971 - GAT_TF_Local - INFO - Saved final GAT (TF) model components to prefix: ./results/results_gat_tf_local\models\GAT_TF_Local_Model_final
2025-05-21 14:37:47,980 - GAT_TF_Local - INFO - Evaluating GAT (TF) model on test set.
2025-05-21 14:37:48,069 - GAT_TF_Local - INFO - Model built successfully with dummy input before loading weights.
2025-05-21 14:37:48,112 - GAT_TF_Local - INFO - Model loaded from config ./results/results_gat_tf_local\models\GAT_TF_Local_Model_final_config.json and weights ./results/results_gat_tf_local\models\GAT_TF_Local_Model_final_weights.h5
2025-05-21 14:37:51,660 - GAT_TF_Local - INFO - Test Accuracy (TF GAT): 88.63%
2025-05-21 14:37:51,661 - GAT_TF_Local - INFO - Classification Report (TF GAT):                  precision    recall  f1-score   support

          BENIGN       0.93      0.91      0.92     88006
   DoS GoldenEye       0.41      0.28      0.34      2059
        DoS Hulk       0.91      0.88      0.89     46215
DoS Slowhttptest       0.33      0.77      0.46      1100
   DoS slowloris       0.26      0.48      0.34      1159
      Heartbleed       0.00      1.00      0.00         2

        accuracy                           0.89    138541
       macro avg       0.47      0.72      0.49    138541
    weighted avg       0.91      0.89      0.89    138541

2025-05-21 14:37:52,755 - GAT_TF_Local - INFO - Confusion matrix plot saved to ./results/results_gat_tf_local\plots\GAT_TF_Local_Model_confusion_matrix.png
2025-05-21 14:37:52,777 - GAT_TF_Local - INFO - Test report saved to ./results/results_gat_tf_local\GAT_TF_Local_Model_classification_report.txt
2025-05-21 14:38:03,626 - GAT_TF_Local - INFO - Overall GAT (TF) training summary saved to ./results/results_gat_tf_local\GAT_TF_Local_Model_training_summary.json
2025-05-21 14:38:03,626 - GAT_TF_Local - INFO - GAT (TF) Local Mode Training Script Finished Successfully.
