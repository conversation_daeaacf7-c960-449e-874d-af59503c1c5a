import os
import pickle
from PIL import Image
import numpy as np

def load_flowers_dataset(dataset_path):
    data = []
    labels = []
    for class_folder in os.listdir(dataset_path):
        class_path = os.path.join(dataset_path, class_folder)
        if os.path.isdir(class_path):
            for image_file in os.listdir(class_path):
                image_path = os.path.join(class_path, image_file)
                image = Image.open(image_path)
                image_array = np.array(image)
                data.append(image_array)
                labels.append(class_folder)
    
    return np.array(data), np.array(labels)

# 假设数据集路径为 'path/to/flowers_dataset'
dataset_path = 'E:/data/Flowers Recognition/flowers'
data, labels = load_flowers_dataset(dataset_path)

# 保存为pkl文件
with open('flowers_dataset.pkl', 'wb') as f:
    pickle.dump((data, labels), f)

print("数据集已保存为 flowers_dataset.pkl")