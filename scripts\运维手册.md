# PVE+Hadoop+K8S集群运维手册

## 1. 环境概览

### 基础架构
- **PVE物理机**: Proxmox Virtual Environment
- **Hadoop集群**: 6个节点（6个虚拟机）
- **Kubernetes集群**: 5个节点（5个虚拟机）
- **总计**: 11个虚拟机

## 2. 服务分类清单


### 2.2 Hadoop集群服务
```
├── NameNode (主节点1)
│   ├── hadoop-hdfs-namenode        # HDFS名称节点
│   ├── hadoop-yarn-resourcemanager # YARN资源管理器
│   ├── hadoop-mapreduce-historyserver # MapReduce历史服务器
│   └── zookeeper                   # ZooKeeper协调服务
├
├── DataNodes (从节点 × 4)
│   ├── hadoop-hdfs-datanode        # HDFS数据节点
│   ├── hadoop-yarn-nodemanager     # YARN节点管理器
│   └── zookeeper                   # ZooKeeper协调服务（仅在3个节点上）
└── 相关服务
    ├── hadoop-hdfs-journalnode     # Journal节点（高可用配置）
    ├── hive-metastore             # Hive元数据存储
    ├── hive-server2               # Hive服务器
    ├── spark-history-server       # Spark历史服务器
    
```

### 2.3 Kubernetes集群服务
```
├── Master Node (控制平面)
│   ├
│   ├── kube-controller-manager    # 控制器管理器
│   ├── kube-scheduler            # 调度器
│   ├── etcd                      # 分布式键值存储
│   └── kube-proxy                # 网络代理
├── Worker Nodes × 4
│   ├── kubelet                   # 节点代理
│   ├── kube-proxy               # 网络代理
│   ├── container-runtime        # 容器运行时 (Docker/containerd)
│   └── cni-plugins              # 容器网络接口插件
└── 附加服务
    ├── coredns                   # DNS服务
    ├── calico/flannel           # 网络插件
    ├── metrics-server           # 资源监控
    ├── dashboard                # Web UI
    ├── ingress-controller       # 入口控制器
    └── storage-provisioner      # 存储供应商
```

## 3. 运维脚本集合

### 3.1 系统状态检查脚本
```bash
# 检查所有集群状态
./scripts/check_cluster_status.sh

# 使用方法：
chmod +x scripts/check_cluster_status.sh
./scripts/check_cluster_status.sh
```

### 3.2 集群重启脚本
```bash
# 重启所有服务
./scripts/restart_clusters.sh

# 仅重启特定服务
./scripts/restart_clusters.sh -p    # 仅PVE
./scripts/restart_clusters.sh -H    # 仅Hadoop
./scripts/restart_clusters.sh -k    # 仅K8S
./scripts/restart_clusters.sh -v 100  # 重启VM ID 100

# 使用方法：
chmod +x scripts/restart_clusters.sh
./scripts/restart_clusters.sh --help
```

### 3.3 备份脚本
```bash
# 完整备份
./scripts/backup_clusters.sh

# 选择性备份
./scripts/backup_clusters.sh -p     # 仅备份PVE
./scripts/backup_clusters.sh -H -k  # 备份Hadoop和K8S
./scripts/backup_clusters.sh -d /custom/backup  # 自定义备份目录

# 使用方法：
chmod +x scripts/backup_clusters.sh
./scripts/backup_clusters.sh --help
```

### 3.4 监控脚本
```bash
# 实时监控
./scripts/monitor_clusters.sh

# 一次性检查
./scripts/monitor_clusters.sh -o

# 生成报告
./scripts/monitor_clusters.sh -r

# 自定义监控间隔
./scripts/monitor_clusters.sh -i 60  # 60秒间隔

# 使用方法：
chmod +x scripts/monitor_clusters.sh
./scripts/monitor_clusters.sh --help
```

## 4. 日常运维指南

### 4.1 每日检查清单
- [ ] PVE物理机资源使用率
- [ ] 虚拟机状态检查
- [ ] Hadoop集群健康状态
- [ ] K8S集群状态检查
- [ ] 存储空间监控
- [ ] 网络连通性测试
- [ ] 日志检查

### 4.2 每周维护任务
- [ ] 系统更新检查
- [ ] 数据备份验证
- [ ] 性能报告生成
- [ ] 磁盘清理
- [ ] 安全扫描

### 4.3 每月维护任务
- [ ] 系统补丁更新
- [ ] 容量规划评估
- [ ] 性能优化调整
- [ ] 备份策略验证
- [ ] 文档更新

## 5. 故障排除指南

### 5.1 PVE相关问题

#### 问题1: PVE Web界面无法访问
**症状**: 无法打开PVE Web管理界面
**诊断步骤**:
```bash
# 检查pveproxy服务状态
systemctl status pveproxy

# 检查端口是否监听
ss -tlnp | grep :8006

# 检查防火墙设置
iptables -L | grep 8006
```
**解决方案**:
```bash
# 重启pveproxy服务
systemctl restart pveproxy

# 如果端口被占用，查找占用进程
lsof -i :8006

# 重启所有PVE服务
systemctl restart pve-cluster pveproxy pvedaemon
```

#### 问题2: 虚拟机无法启动
**症状**: VM启动失败，显示错误信息
**诊断步骤**:
```bash
# 查看VM配置
cat /etc/pve/qemu-server/VM_ID.conf

# 检查存储状态
pvesh get /storage

# 查看系统日志
journalctl -u qemu-server@VM_ID.service
```
**解决方案**:
```bash
# 解锁VM
qm unlock VM_ID

# 重置VM配置
qm set VM_ID --lock ""

# 检查磁盘文件
qemu-img check /path/to/vm/disk.qcow2
```

#### 问题3: 集群节点离线
**症状**: 集群中某个节点显示离线状态
**诊断步骤**:
```bash
# 检查集群状态
pvecm status

# 检查网络连通性
ping other_node_ip

# 检查corosync日志
journalctl -u corosync
```
**解决方案**:
```bash
# 重新加入集群
pvecm add existing_cluster_ip

# 修复corosync配置
systemctl restart corosync pve-cluster
```

### 5.2 Hadoop集群问题

#### 问题1: NameNode启动失败
**症状**: HDFS NameNode无法启动
**诊断步骤**:
```bash
# 检查NameNode日志
tail -f /opt/hadoop/logs/hadoop-*-namenode-*.log

# 检查磁盘空间
df -h /opt/hadoop/dfs/name

# 检查文件权限
ls -la /opt/hadoop/dfs/name
```
**解决方案**:
```bash
# 进入安全模式并修复
hadoop namenode -safemode enter
hdfs fsck / -files -blocks -locations

# 如果元数据损坏，从备份恢复
hadoop namenode -recover

# 重新格式化(谨慎操作)
hdfs namenode -format
```

#### 问题2: DataNode连接不上NameNode
**症状**: DataNode日志显示无法连接到NameNode
**诊断步骤**:
```bash
# 检查DataNode日志
tail -f /opt/hadoop/logs/hadoop-*-datanode-*.log

# 测试网络连通性
telnet namenode_ip 9000

# 检查防火墙设置
iptables -L | grep 9000
```
**解决方案**:
```bash
# 重启DataNode服务
systemctl restart hadoop-hdfs-datanode

# 清理DataNode数据目录(如果必要)
rm -rf /opt/hadoop/dfs/data/*
systemctl restart hadoop-hdfs-datanode

# 检查hostname解析
echo "namenode_ip namenode_hostname" >> /etc/hosts
```

#### 问题3: YARN作业卡住
**症状**: YARN作业长时间处于PENDING状态
**诊断步骤**:
```bash
# 检查YARN资源使用情况
yarn node -list
yarn application -list

# 检查队列状态
mapred queue -list

# 查看ResourceManager日志
tail -f /opt/hadoop/logs/yarn-*-resourcemanager-*.log
```
**解决方案**:
```bash
# 增加内存和CPU配置
# 编辑 $HADOOP_HOME/etc/hadoop/yarn-site.xml
<property>
    <name>yarn.nodemanager.resource.memory-mb</name>
    <value>8192</value>
</property>

# 重启YARN服务
systemctl restart hadoop-yarn-resourcemanager
systemctl restart hadoop-yarn-nodemanager

# 杀死卡住的作业
yarn application -kill application_id
```

### 5.3 K8S集群问题

#### 问题1: API Server无法访问
**症状**: kubectl命令返回连接拒绝错误
**诊断步骤**:
```bash
# 检查API Server状态
systemctl status kube-apiserver

# 检查端口监听
ss -tlnp | grep :6443

# 检查证书是否过期
openssl x509 -in /etc/kubernetes/pki/apiserver.crt -text -noout | grep "Not After"
```
**解决方案**:
```bash
# 重启API Server
systemctl restart kube-apiserver

# 如果证书过期，更新证书
kubeadm alpha certs renew all

# 重新生成kubeconfig
kubeadm init phase kubeconfig admin
```

#### 问题2: Pod无法调度
**症状**: Pod一直处于Pending状态
**诊断步骤**:
```bash
# 查看Pod事件
kubectl describe pod pod_name

# 检查节点状态
kubectl get nodes
kubectl describe node node_name

# 查看调度器日志
kubectl logs -n kube-system kube-scheduler-master
```
**解决方案**:
```bash
# 如果是资源不足
kubectl top nodes
kubectl top pods

# 如果是污点问题
kubectl taint nodes node_name key:effect-

# 如果是亲和性问题
kubectl edit pod pod_name  # 修改nodeSelector或affinity
```

#### 问题3: 网络插件故障
**症状**: Pod间网络不通
**诊断步骤**:
```bash
# 检查网络插件Pod状态
kubectl get pods -n kube-system | grep -E "(calico|flannel|weave)"

# 检查节点网络配置
ip route show
ip addr show

# 测试Pod间连通性
kubectl exec -it pod1 -- ping pod2_ip
```
**解决方案**:
```bash
# 重启网络插件
kubectl delete pods -n kube-system -l k8s-app=calico-node

# 重新应用网络插件配置
kubectl apply -f calico.yaml

# 清理网络配置
ip route flush table main
systemctl restart kubelet
```

### 5.4 网络问题

#### 问题1: 集群间网络不通
**诊断步骤**:
```bash
# 检查网络配置
ip addr show
ip route show

# 测试连通性
ping target_ip
traceroute target_ip
mtr target_ip

# 检查防火墙
iptables -L -n
ufw status
```
**解决方案**:
```bash
# 添加路由规则
ip route add network/mask via gateway_ip

# 调整防火墙规则
iptables -A INPUT -s cluster_network/24 -j ACCEPT

# 检查交换机配置
# 确保VLAN和端口配置正确
```

#### 问题2: DNS解析问题
**诊断步骤**:
```bash
# 检查DNS配置
cat /etc/resolv.conf

# 测试DNS解析
nslookup hostname
dig hostname

# 检查本地hosts文件
cat /etc/hosts
```
**解决方案**:
```bash
# 更新DNS配置
echo "nameserver *******" >> /etc/resolv.conf

# 添加hosts记录
echo "ip_address hostname" >> /etc/hosts

# 重启网络服务
systemctl restart systemd-resolved
```

### 5.5 存储问题

#### 问题1: 磁盘空间不足
**诊断步骤**:
```bash
# 检查磁盘使用情况
df -h
du -sh /var /opt /home

# 查找大文件
find / -size +1G -type f 2>/dev/null
```
**解决方案**:
```bash
# 清理日志文件
journalctl --vacuum-time=7d
find /var/log -name "*.log" -mtime +7 -delete

# 清理临时文件
rm -rf /tmp/*
rm -rf /var/tmp/*

# 清理包缓存
apt clean
yum clean all
```

#### 问题2: 存储性能问题
**诊断步骤**:
```bash
# 检查磁盘I/O
iostat -x 1
iotop

# 检查磁盘健康状态
smartctl -a /dev/sdX

# 测试磁盘性能
dd if=/dev/zero of=/tmp/testfile bs=1G count=1 oflag=direct
```
**解决方案**:
```bash
# 优化文件系统
tune2fs -o journal_data_writeback /dev/sdX
mount -o remount,noatime /mount/point

# 调整I/O调度器
echo deadline > /sys/block/sdX/queue/scheduler

# 增加内存缓存
echo 3 > /proc/sys/vm/drop_caches
sysctl vm.dirty_ratio=5
```

## 6. 性能优化建议

### 6.1 硬件优化

#### CPU优化
```bash
# 查看CPU信息
lscpu
cat /proc/cpuinfo

# 设置CPU性能模式
echo performance > /sys/devices/system/cpu/cpu*/cpufreq/scaling_governor

# 禁用CPU节能模式
echo 1 > /sys/devices/system/cpu/intel_pstate/no_turbo

# 绑定中断到特定CPU核心
echo 2 > /proc/irq/24/smp_affinity
```

#### 内存优化
```bash
# 调整内存参数
sysctl vm.swappiness=1
sysctl vm.dirty_ratio=15
sysctl vm.dirty_background_ratio=5

# 启用大页内存
echo always > /sys/kernel/mm/transparent_hugepage/enabled
echo 1024 > /proc/sys/vm/nr_hugepages

# 内存预分配优化
echo 0 > /proc/sys/vm/zone_reclaim_mode
```

#### NUMA优化
```bash
# 查看NUMA拓扑
numactl --hardware
lstopo

# 绑定进程到NUMA节点
numactl --cpunodebind=0 --membind=0 your_process

# 禁用NUMA平衡
echo 0 > /proc/sys/kernel/numa_balancing
```

### 6.2 网络优化

#### 网络参数调优
```bash
# TCP参数优化
sysctl net.core.rmem_max=67108864
sysctl net.core.wmem_max=67108864
sysctl net.ipv4.tcp_rmem="4096 65536 67108864"
sysctl net.ipv4.tcp_wmem="4096 65536 67108864"

# 网络缓冲区优化
sysctl net.core.netdev_max_backlog=5000
sysctl net.ipv4.tcp_congestion_control=bbr

# 减少网络延迟
sysctl net.ipv4.tcp_low_latency=1
sysctl net.ipv4.tcp_timestamps=0
```

#### 网卡优化
```bash
# 调整网卡队列
ethtool -L eth0 combined 4

# 启用网卡offload功能
ethtool -K eth0 gso on
ethtool -K eth0 tso on
ethtool -K eth0 gro on

# 调整中断合并
ethtool -C eth0 rx-usecs 100
```

#### 防火墙优化
```bash
# 优化iptables规则
iptables -t raw -I PREROUTING -p tcp --dport 80 -j NOTRACK
iptables -t raw -I OUTPUT -p tcp --sport 80 -j NOTRACK

# 使用ipset优化大量IP规则
ipset create blocked_ips hash:ip
iptables -I INPUT -m set --match-set blocked_ips src -j DROP
```

### 6.3 存储优化

#### 磁盘I/O优化
```bash
# 选择合适的I/O调度器
echo deadline > /sys/block/sda/queue/scheduler  # 机械硬盘
echo noop > /sys/block/nvme0n1/queue/scheduler   # SSD

# 调整队列深度
echo 128 > /sys/block/sda/queue/nr_requests

# 优化读写策略
echo 0 > /sys/block/sda/queue/add_random
echo 2 > /sys/block/sda/queue/rq_affinity
```

#### 文件系统优化
```bash
# ext4优化挂载选项
mount -o noatime,nodiratime,barrier=0,data=writeback /dev/sda1 /mount/point

# XFS优化
mount -o noatime,nodiratime,inode64,largeio /dev/sda1 /mount/point

# 调整文件系统参数
tune2fs -o journal_data_writeback /dev/sda1
```

#### RAID优化
```bash
# 查看RAID状态
cat /proc/mdstat
mdadm --detail /dev/md0

# 调整RAID stripe cache
echo 16384 > /sys/block/md0/md/stripe_cache_size

# 优化重建速度
echo 50000 > /proc/sys/dev/raid/speed_limit_min
echo 200000 > /proc/sys/dev/raid/speed_limit_max
```

### 6.4 应用优化

#### PVE虚拟化优化
```bash
# 虚拟机CPU配置
cpu: host,+aes
numa: 1

# 虚拟机内存配置
balloon: 0  # 禁用内存balloon
hugepages: 1gb  # 使用大页内存

# 网络virtio优化
model: virtio,queues=4

# 磁盘virtio优化
cache: writethrough
aio: native
```

#### Hadoop集群优化

**HDFS优化**:
```xml
<!-- hdfs-site.xml -->
<property>
    <name>dfs.datanode.max.transfer.threads</name>
    <value>8192</value>
</property>

<property>
    <name>dfs.datanode.handler.count</name>
    <value>64</value>
</property>

<property>
    <name>dfs.namenode.handler.count</name>
    <value>100</value>
</property>
```

**YARN优化**:
```xml
<!-- yarn-site.xml -->
<property>
    <name>yarn.nodemanager.resource.memory-mb</name>
    <value>14336</value>  <!-- 物理内存的70-80% -->
</property>

<property>
    <name>yarn.scheduler.maximum-allocation-mb</name>
    <value>14336</value>
</property>

<property>
    <name>yarn.nodemanager.vmem-check-enabled</name>
    <value>false</value>
</property>
```

**MapReduce优化**:
```xml
<!-- mapred-site.xml -->
<property>
    <name>mapreduce.task.io.sort.mb</name>
    <value>512</value>
</property>

<property>
    <name>mapreduce.map.memory.mb</name>
    <value>2048</value>
</property>

<property>
    <name>mapreduce.reduce.memory.mb</name>
    <value>4096</value>
</property>
```

#### Kubernetes集群优化

**kubelet优化**:
```yaml
# /var/lib/kubelet/config.yaml
maxPods: 250
registryPullQPS: 10
registryBurst: 20
eventRecordQPS: 10
eventBurst: 20
kubeAPIQPS: 50
kubeAPIBurst: 100
```

**kube-proxy优化**:
```yaml
# kube-proxy配置
conntrack:
  maxPerCore: 131072
  min: 131072
  tcpCloseWaitTimeout: 1h0m0s
  tcpEstablishedTimeout: 24h0m0s
```

**etcd优化**:
```bash
# etcd启动参数
--max-request-bytes=33554432
--quota-backend-bytes=8589934592
--auto-compaction-mode=periodic
--auto-compaction-retention=300
```

**容器运行时优化**:
```json
// /etc/docker/daemon.json
{
  "exec-opts": ["native.cgroupdriver=systemd"],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  },
  "storage-driver": "overlay2",
  "storage-opts": [
    "overlay2.override_kernel_check=true"
  ]
}
```

#### JVM优化（适用于Java应用）
```bash
# Hadoop/Spark JVM参数
export JAVA_OPTS="-Xms4g -Xmx8g"
export JAVA_OPTS="$JAVA_OPTS -XX:+UseG1GC"
export JAVA_OPTS="$JAVA_OPTS -XX:MaxGCPauseMillis=200"
export JAVA_OPTS="$JAVA_OPTS -XX:+UnlockExperimentalVMOptions"
export JAVA_OPTS="$JAVA_OPTS -XX:+UseZGC"  # Java 11+
export JAVA_OPTS="$JAVA_OPTS -XX:+UseLargePages"
```

## 7. 安全配置

### 7.1 访问控制

#### SSH安全配置
```bash
# /etc/ssh/sshd_config 优化
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
Port 2222  # 更改默认端口
MaxAuthTries 3
ClientAliveInterval 300
ClientAliveCountMax 2

# 重启SSH服务
systemctl restart sshd
```

#### 用户权限管理
```bash
# 创建管理员用户
useradd -m -s /bin/bash admin
usermod -aG sudo admin

# 设置sudo免密（谨慎使用）
echo "admin ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers.d/admin

# 配置密钥认证
mkdir -p /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh
# 将公钥添加到 /home/<USER>/.ssh/authorized_keys
chmod 600 /home/<USER>/.ssh/authorized_keys
chown -R admin:admin /home/<USER>/.ssh
```

#### PVE访问控制
```bash
# 创建PVE用户组和用户
pveum group add monitoring -comment "监控用户组"
pveum user add monitor@pve -comment "监控用户"
pveum usermod monitor@pve -group monitoring

# 分配权限
pveum acl modify / -user monitor@pve -role PVEAuditor

# 启用双因素认证
pveum user modify admin@pam -keys "v3/BHhi=..."
```

### 7.2 网络安全

#### 防火墙配置
```bash
# UFW防火墙配置
ufw enable
ufw default deny incoming
ufw default allow outgoing

# 允许SSH（使用自定义端口）
ufw allow 2222/tcp

# 允许PVE Web界面
ufw allow 8006/tcp

# 允许Hadoop端口
ufw allow 9000,9870,8088,19888/tcp

# 允许Kubernetes端口
ufw allow 6443,2379,2380,10250,10251,10252/tcp

# 内网访问规则
ufw allow from ***********/24
```

#### iptables高级规则
```bash
# 防止DDoS攻击
iptables -A INPUT -p tcp --dport 80 -m limit --limit 25/minute --limit-burst 100 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -m limit --limit 25/minute --limit-burst 100 -j ACCEPT

# 防止端口扫描
iptables -A INPUT -p tcp --tcp-flags ALL NONE -j DROP
iptables -A INPUT -p tcp --tcp-flags ALL ALL -j DROP

# 地理位置封锁
iptables -A INPUT -m geoip --src-cc CN,US -j ACCEPT
iptables -A INPUT -m geoip ! --src-cc CN,US -j DROP
```

#### SSL/TLS配置
```bash
# 生成自签名证书
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/ssl/private/cluster.key \
    -out /etc/ssl/certs/cluster.crt

# 配置强加密套件
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
ssl_prefer_server_ciphers off;
```

### 7.3 数据安全

#### 数据加密
```bash
# HDFS透明加密
hadoop key create mykey -size 256
hdfs crypto -createZone -keyName mykey -path /encrypted

# etcd数据加密
--encryption-provider-config=/etc/kubernetes/encryption-config.yaml

# 数据库加密
echo "encrypt=1" >> /etc/mysql/my.cnf
```

#### 备份加密
```bash
# 加密备份脚本
encrypt_backup() {
    local backup_file="$1"
    local encrypted_file="${backup_file}.gpg"
    
    gpg --cipher-algo AES256 --compress-algo 1 --symmetric \
        --output "$encrypted_file" "$backup_file"
    
    # 删除原文件
    rm "$backup_file"
    
    echo "备份已加密: $encrypted_file"
}

# 解密备份
decrypt_backup() {
    local encrypted_file="$1"
    local decrypted_file="${encrypted_file%.gpg}"
    
    gpg --decrypt --output "$decrypted_file" "$encrypted_file"
}
```

#### 密钥管理
```bash
# 生成随机密码
openssl rand -base64 32

# Vault集成（推荐）
vault kv put secret/hadoop/db password="$(openssl rand -base64 32)"
vault kv get secret/hadoop/db

# Kubernetes Secrets
kubectl create secret generic db-secret \
    --from-literal=username=admin \
    --from-literal=password=secretpassword
```

### 7.4 审计日志

#### 系统审计配置
```bash
# 安装auditd
apt install auditd audispd-plugins

# 配置审计规则 /etc/audit/rules.d/audit.rules
-w /etc/passwd -p wa -k identity
-w /etc/shadow -p wa -k identity
-w /etc/sudoers -p wa -k privilege_escalation
-w /var/log/auth.log -p wa -k auth_log

# 重启审计服务
systemctl restart auditd

# 查看审计日志
ausearch -k identity
```

#### 应用审计
```bash
# Hadoop审计日志
# 在hdfs-site.xml中启用
<property>
    <name>dfs.namenode.audit.log.async</name>
    <value>true</value>
</property>

# Kubernetes审计
apiVersion: audit.k8s.io/v1
kind: Policy
rules:
- level: RequestResponse
  namespaces: ["kube-system"]
  verbs: ["create", "delete", "patch"]
```

#### 日志聚合
```bash
# rsyslog配置 /etc/rsyslog.d/50-cluster.conf
$ModLoad imfile
$InputFileName /var/log/hadoop/hdfs-audit.log
$InputFileTag hadoop-audit:
$InputFileStateFile stat-hadoop-audit
$InputRunFileMonitor

# 发送到中央日志服务器
*.info @192.168.1.100:514
```

## 8. 联系信息和升级路径

### 8.1 紧急联系方式

#### 运维团队联系信息
```
主管理员: 
- 姓名: [管理员姓名]
- 电话: [紧急联系电话]
- 邮箱: <EMAIL>
- 微信: [微信号]

备用联系人:
- 姓名: [备用管理员]
- 电话: [备用电话]
- 邮箱: <EMAIL>

技术支持群组:
- 钉钉群: [群号]
- 企业微信: [群ID]
- Slack: #ops-emergency
```

#### 故障上报流程
```
严重故障(P0): 立即电话通知 + 群组消息
重要故障(P1): 30分钟内群组通知
一般故障(P2): 2小时内邮件通知
轻微故障(P3): 日报告知
```

### 8.2 供应商支持

#### 硬件支持
```
服务器厂商: [厂商名称]
- 支持热线: [电话]
- 在线支持: [网址]
- 合同号: [合同编号]
- SLA等级: 4小时响应

网络设备: [厂商名称]
- 技术支持: [电话]
- 远程支持: [VPN地址]
```

#### 软件支持
```
Proxmox VE:
- 社区版: 免费社区支持
- 商业版: [支持合同号]
- 文档: https://pve.proxmox.com/wiki/

Hadoop生态:
- Cloudera支持: [如有商业版]
- Apache社区: hadoop.apache.org
- 国内社区: hadoop.org.cn

Kubernetes:
- CNCF支持: kubernetes.io
- 商业支持: [如使用RedHat OpenShift等]
```

### 8.3 社区资源

#### 技术论坛和文档
```
PVE相关:
- 官方论坛: forum.proxmox.com
- 中文社区: pve.org.cn
- GitHub: github.com/proxmox

Hadoop相关:
- Apache官网: hadoop.apache.org
- Stack Overflow: [hadoop标签]
- 知乎专栏: [Hadoop大数据]

Kubernetes相关:
- 官方文档: kubernetes.io/docs/
- CNCF Slack: cloud-native.slack.com
- 中文社区: k8s.org.cn
```

#### 学习资源
```
在线课程:
- Linux Academy
- Cloud Native Computing Foundation
- edX Kubernetes课程

书籍推荐:
- 《Hadoop权威指南》
- 《Kubernetes权威指南》
- 《深入理解Linux内核》

培训认证:
- CKA (Certified Kubernetes Administrator)
- RHCE (Red Hat Certified Engineer)
- Cloudera认证数据工程师
```

### 8.4 升级计划

#### 版本管理策略
```
操作系统:
- 当前版本: Ubuntu 20.04 LTS
- 目标版本: Ubuntu 22.04 LTS
- 升级窗口: 2024年Q2

Proxmox VE:
- 当前版本: 7.x
- 目标版本: 8.x
- 升级计划: 跟随LTS版本

Hadoop生态:
- 当前版本: 3.3.x
- 升级策略: 小版本跟进，大版本谨慎
- 测试环境: 先在测试集群验证
```

#### 升级检查清单
```
升级前检查:
□ 完整备份所有数据
□ 文档化当前配置
□ 通知所有用户
□ 准备回滚方案
□ 在测试环境验证

升级中监控:
□ 实时监控系统状态
□ 保持通信畅通
□ 记录所有操作
□ 准备应急响应

升级后验证:
□ 功能完整性测试
□ 性能基准测试
□ 用户接受度测试
□ 文档更新
□ 经验总结
```

#### 技术债务管理
```
定期评估:
- 每季度技术债务评估
- 安全漏洞扫描和修复
- 性能瓶颈分析
- 容量规划更新

改进计划:
- 自动化运维流程
- 监控告警优化
- 文档持续更新
- 团队技能提升
```

---

## 附录

### A. 常用命令速查表
### B. 端口清单
### C. 配置文件模板
### D. 故障排除流程图

**文档版本**: v1.0  
**最后更新**: $(date)  
**维护者**: 运维团队  
**审核者**: 技术主管 