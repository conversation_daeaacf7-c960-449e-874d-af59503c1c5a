"""LSTM"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler

class LSTMModel(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers, output_size):
        super(LSTMModel, self).__init__()
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        self.fc = nn.Linear(hidden_size, output_size)

    def forward(self, x):
        h0 = torch.zeros(num_layers, x.size(0), hidden_size).to(x.device)
        c0 = torch.zeros(num_layers, x.size(0), hidden_size).to(x.device)
        out, _ = self.lstm(x, (h0, c0))
        out = self.fc(out[:, -1, :])
        return out

def lstm_analysis(input_file, input_column, target_column, input_size, hidden_size, num_layers, output_size, epochs, batch_size):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - input_column: 输入特征列名列表
    - target_column: 目标列名
    - input_size: 输入特征维度
    - hidden_size: 隐藏层大小
    - num_layers: LSTM层数
    - output_size: 输出大小
    - epochs: 训练轮数
    - batch_size: 批次大小
    """
    data = pd.read_csv(input_file)
    X = data[input_column].values
    y = data[target_column].values

    scaler = StandardScaler()
    X = scaler.fit_transform(X)

    X = np.reshape(X, (X.shape[0], 1, X.shape[1]))

    X = torch.tensor(X, dtype=torch.float32)
    y = torch.tensor(y, dtype=torch.float32)

    dataset = TensorDataset(X, y)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)

    model = LSTMModel(input_size, hidden_size, num_layers, output_size)
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    for epoch in range(epochs):
        for inputs, targets in dataloader:
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
        print(f"Epoch {epoch+1}/{epochs}, Loss: {loss.item()}")

    print("LSTM training completed")

if __name__ == "__main__":
    input_file = 'data.csv'
    input_column = ['feature1', 'feature2']
    target_column = 'target'
    input_size = len(input_column)
    hidden_size = 128
    num_layers = 2
    output_size = 1
    epochs = 20
    batch_size = 32

    lstm_analysis(input_file, input_column, target_column, input_size, hidden_size, num_layers, output_size, epochs, batch_size)
