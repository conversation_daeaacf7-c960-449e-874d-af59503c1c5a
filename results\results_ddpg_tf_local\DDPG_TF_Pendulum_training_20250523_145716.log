2025-05-23 14:57:16,059 - DDPG_TF_Local - INFO - Logging to: ./results/ddpg_tf_local_run\DDPG_TF_Pendulum_training_20250523_145716.log
2025-05-23 14:57:16,059 - DDPG_TF_Local - INFO - DDPG TF Local Training (DDPG_TF_Pendulum) starting.
2025-05-23 14:57:16,059 - DDPG_TF_Local - INFO - Params:
{
  "env_name": "Pendulum-v1",
  "result_dir": "./results/ddpg_tf_local_run",
  "model_name": "DDPG_TF_Pendulum",
  "hidden_size": 256,
  "actor_lr": 0.0001,
  "critic_lr": 0.001,
  "gamma": 0.99,
  "tau": 0.001,
  "buffer_size": 50000,
  "batch_size": 64,
  "noise_sigma": 0.1,
  "num_episodes": 3,
  "max_steps_per_episode": 200,
  "random_seed": null,
  "save_freq": 20,
  "plot_freq": 10,
  "tf_log_level": "ERROR",
  "logger": "<Logger DDPG_TF_Local (INFO)>"
}
2025-05-23 14:57:16,083 - DDPG_TF_Local.Env - INFO - Gymnasium API for Pendulum-v1
2025-05-23 14:57:16,083 - DDPG_TF_Local - INFO - Env: Pendulum-v1, State=3, Action=1, Bound=2.0
2025-05-23 14:57:22,970 - DDPG_TF_Local - INFO - Ep 1/3 | Score: -1612.46 | AvgScore(100): -1612.46 | Steps: 200 | Time: 6.63s
2025-05-23 14:57:25,941 - DDPG_TF_Local - INFO - Ep 2/3 | Score: -1299.94 | AvgScore(100): -1456.20 | Steps: 200 | Time: 2.97s
2025-05-23 14:57:28,849 - DDPG_TF_Local - INFO - Ep 3/3 | Score: -1251.07 | AvgScore(100): -1387.82 | Steps: 200 | Time: 2.91s
2025-05-23 14:57:30,083 - DDPG_TF_Local.Plot - INFO - Plots saved in ./results/ddpg_tf_local_run\plots
2025-05-23 14:57:30,155 - DDPG_TF_Local - ERROR - Critical error: Object of type float32 is not JSON serializable
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\DDPG_tf_local_mode.py", line 482, in <module>
    ddpg_train_tf_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\DDPG_tf_local_mode.py", line 390, in ddpg_train_tf_local
    agent.save_weights_local(final_model_path_prefix)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\DDPG_tf_local_mode.py", line 233, in save_weights_local
    json.dump(hyperparams, f, indent=4)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\json\__init__.py", line 179, in dump
    for chunk in iterable:
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\json\encoder.py", line 431, in _iterencode
    yield from _iterencode_dict(o, _current_indent_level)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\json\encoder.py", line 405, in _iterencode_dict
    yield from chunks
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\json\encoder.py", line 438, in _iterencode
    o = _default(o)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\json\encoder.py", line 179, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
TypeError: Object of type float32 is not JSON serializable
