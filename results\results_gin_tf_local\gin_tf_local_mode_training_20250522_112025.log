2025-05-22 11:20:25,766 - GIN_TF_Local_Mode - INFO - 日志将记录到控制台和文件: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_gin_tf_local\gin_tf_local_mode_training_20250522_112025.log
2025-05-22 11:20:25,766 - GIN_TF_Local_Mode - INFO - GIN TensorFlow Local Mode training script initialized.
2025-05-22 11:20:25,767 - GIN_TF_Local_Mode - INFO - TensorFlow version: 2.13.0
2025-05-22 11:20:25,767 - GIN_TF_Local_Mode - INFO - CLI Arguments: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'label_column': 'Label', 'data_format': 'pkl', 'input_size': 84, 'hidden_size': 64, 'output_size': 6, 'num_layers': 3, 'dropout_rate': 0.5, 'train_eps': True, 'l2_reg': 0.0001, 'edge_strategy': 'fully_connected', 'k_neighbors': 5, 'num_epochs': 10, 'learning_rate': 0.001, 'batch_size': 32, 'early_stopping_patience': None, 'test_split_size': 0.2, 'random_state': 42, 'normalize': True, 'scaler_type': 'standard', 'handle_imbalance': False, 'stratify_split': True, 'numeric_impute_strategy': 'mean', 'categorical_impute_strategy': 'most_frequent', 'use_cv': False, 'cv_folds': 5, 'result_dir': 'E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\results\\results_gin_tf_local', 'model_name': 'GIN_TF_Local_Model', 'log_level': 'INFO'}
2025-05-22 11:20:25,769 - GIN_TF_Local_Mode - INFO - No GPU found by TensorFlow, using CPU.
2025-05-22 11:20:25,770 - GIN_TF_Local_Mode.DataLoad - INFO - Loading data from E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl, format: pkl
2025-05-22 11:20:26,152 - GIN_TF_Local_Mode - ERROR - Value error during GIN TF training: Label column 'Label' not found in DataFrame.
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GIN_tf_local_mode.py", line 941, in gin_train_tf_local_mode
    X_original_df, y_original_series = flexible_data_load_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GIN_tf_local_mode.py", line 256, in flexible_data_load_local
    raise ValueError(f"Label column '{label_col}' not found in DataFrame.")
ValueError: Label column 'Label' not found in DataFrame.
