2025-05-21 15:13:49,787 - GCN_TF_Local - INFO - 日志将记录到控制台和文件: results\results_gcn_tf_local\gcn_tf_local_training_20250521_151349.log
2025-05-21 15:13:49,788 - GCN_TF_Local - INFO - GCN TensorFlow 本地模式训练脚本已初始化。
2025-05-21 15:13:49,788 - GCN_TF_Local - INFO - TensorFlow 版本: 2.13.0
2025-05-21 15:13:49,789 - GCN_TF_Local - INFO - 参数: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'result_dir': 'results\\results_gcn_tf_local', 'model_name': 'GCN_TF_Local_Model', 'label_column': 'Label', 'data_format': 'pkl', 'hidden_size': 64, 'num_layers': 2, 'dropout_rate': 0.5, 'num_epochs': 3, 'learning_rate': 0.005, 'optimizer': 'adam', 'loss_function': 'sparsecategoricalcrossentropy', 'edge_strategy': 'fully_connected', 'test_split_size': 0.2, 'random_state': 42, 'normalize': True, 'scaler_type': 'standard', 'handle_imbalance': False, 'stratify_split': True, 'numeric_impute_strategy': 'mean', 'categorical_impute_strategy': 'most_frequent', 'use_cv': False, 'cv_folds': 5, 'log_level': 'INFO', 'force_cpu': False}
2025-05-21 15:13:49,790 - GCN_TF_Local - INFO - 未找到GPU或强制使用CPU。
2025-05-21 15:13:49,790 - GCN_TF_Local - INFO - 从 E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl (格式: pkl) 加载数据
2025-05-21 15:13:50,165 - GCN_TF_Local - INFO - 数据加载为 DataFrame，形状: (692703, 85)
2025-05-21 15:13:50,289 - GCN_TF_Local - INFO - 特征形状: (692703, 84), 标签形状: (692703,)
2025-05-21 15:13:50,320 - GCN_TF_Local - INFO - 标签分布: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-21 15:13:50,348 - GCN_TF_Local - INFO - 选择标准训练/测试分割模式。
2025-05-21 15:13:50,858 - GCN_TF_Local - INFO - 已将特征中的无限值替换为NaN。
2025-05-21 15:13:50,949 - GCN_TF_Local - INFO - 处理特征中的缺失值。
2025-05-21 15:13:53,000 - GCN_TF_Local - INFO - 使用 'mean' 填充了数值型缺失值。
2025-05-21 15:13:53,479 - GCN_TF_Local - INFO - 使用 'most_frequent' 填充了类别型缺失值。
2025-05-21 15:13:53,583 - GCN_TF_Local - INFO - 标签已编码。6 个类别: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-21 15:13:53,672 - GCN_TF_Local - INFO - 编码类别特征: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-21 15:13:57,617 - GCN_TF_Local - INFO - 使用 standard 缩放器标准化特征。
2025-05-21 15:13:59,898 - GCN_TF_Local - INFO - 数据分割: X_train (554162, 84), y_train (554162,), X_test (138541, 84), y_test (138541,)
2025-05-21 15:13:59,949 - GCN_TF_Local - INFO - 训练/测试分割的预处理信息已保存: results\results_gcn_tf_local\GCN_TF_Local_Model_preprocessing_info_tf.json
2025-05-21 15:13:59,950 - GCN_TF_Local - INFO - 预处理后数据 (训练/测试分割): 输入大小=84, 类别数=6
2025-05-21 15:13:59,950 - GCN_TF_Local - WARNING - 节点数 (554162) 超过阈值 (20000)，全连接策略将使用等效单位矩阵进行归一化 (仅自环)。
2025-05-21 15:14:00,028 - GCN_TF_Local - INFO - 请求为 554162 个节点创建基于标识的归一化邻接矩阵。
2025-05-21 15:14:00,028 - GCN_TF_Local - INFO - 为 554162 个节点生成单位归一化邻接矩阵。
2025-05-21 15:14:00,045 - GCN_TF_Local - CRITICAL - 发生意外严重错误: {{function_node __wrapped__MatrixDiagV3_device_/job:localhost/replica:0/task:0/device:CPU:0}} OOM when allocating tensor with shape[554162,554162] and type float on /job:localhost/replica:0/task:0/device:CPU:0 by allocator cpu [Op:MatrixDiagV3] name: diag
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 1032, in gcn_train_tf_local_mode
    train_graph_dict, test_graph_dict = prepare_tf_datasets_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 529, in prepare_tf_datasets_local
    train_graph_tf_dict = create_graph_data_tf_local(train_x_np, train_y_np, adj_matrix_train_np)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 472, in create_graph_data_tf_local
    norm_adj_tf = normalize_adjacency_matrix_tf_local("IDENTITY_ADJACENCY_REQUESTED_TF", num_nodes_for_identity=current_num_nodes)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 439, in normalize_adjacency_matrix_tf_local
    return tf.eye(num_nodes_for_identity, dtype=tf.float32)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\tensorflow\python\util\traceback_utils.py", line 153, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\tensorflow\python\framework\ops.py", line 6656, in raise_from_not_ok_status
    raise core._status_to_exception(e) from None  # pylint: disable=protected-access
tensorflow.python.framework.errors_impl.ResourceExhaustedError: {{function_node __wrapped__MatrixDiagV3_device_/job:localhost/replica:0/task:0/device:CPU:0}} OOM when allocating tensor with shape[554162,554162] and type float on /job:localhost/replica:0/task:0/device:CPU:0 by allocator cpu [Op:MatrixDiagV3] name: diag
2025-05-21 15:14:00,054 - GCN_TF_Local - CRITICAL - 主程序中发生严重错误: {{function_node __wrapped__MatrixDiagV3_device_/job:localhost/replica:0/task:0/device:CPU:0}} OOM when allocating tensor with shape[554162,554162] and type float on /job:localhost/replica:0/task:0/device:CPU:0 by allocator cpu [Op:MatrixDiagV3] name: diag
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 1164, in <module>
    gcn_train_tf_local_mode(parsed_args)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 1032, in gcn_train_tf_local_mode
    train_graph_dict, test_graph_dict = prepare_tf_datasets_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 529, in prepare_tf_datasets_local
    train_graph_tf_dict = create_graph_data_tf_local(train_x_np, train_y_np, adj_matrix_train_np)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 472, in create_graph_data_tf_local
    norm_adj_tf = normalize_adjacency_matrix_tf_local("IDENTITY_ADJACENCY_REQUESTED_TF", num_nodes_for_identity=current_num_nodes)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 439, in normalize_adjacency_matrix_tf_local
    return tf.eye(num_nodes_for_identity, dtype=tf.float32)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\tensorflow\python\util\traceback_utils.py", line 153, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\tensorflow\python\framework\ops.py", line 6656, in raise_from_not_ok_status
    raise core._status_to_exception(e) from None  # pylint: disable=protected-access
tensorflow.python.framework.errors_impl.ResourceExhaustedError: {{function_node __wrapped__MatrixDiagV3_device_/job:localhost/replica:0/task:0/device:CPU:0}} OOM when allocating tensor with shape[554162,554162] and type float on /job:localhost/replica:0/task:0/device:CPU:0 by allocator cpu [Op:MatrixDiagV3] name: diag
