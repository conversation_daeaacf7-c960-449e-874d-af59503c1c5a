{"args": {"data_path": "E:/data/Flowers Recognition/flowers", "dataset_type": "folder", "annotations_file": null, "image_size": 224, "image_channels": 3, "model_name": "GAN_PT_Local", "latent_dim": 100, "num_epochs": 2, "batch_size": 64, "g_lr": 0.0002, "d_lr": 0.0002, "beta1": 0.5, "sample_interval": 400, "fixed_noise_samples": 16, "save_epoch_interval": 5, "result_dir": "./results_gan_pt_local", "num_workers": 0, "pin_memory": false, "random_seed": null, "force_cpu": false, "log_level": "INFO"}, "error": "mat1 and mat2 shapes cannot be multiplied (64x2048 and 25088x1)", "traceback": "Traceback (most recent call last):\n  File \"E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\卷积神经网络\\GAN_pt_local_mode.py\", line 603, in <module>\n    gan_main_local_pt(args)\n  File \"E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\卷积神经网络\\GAN_pt_local_mode.py\", line 534, in gan_main_local_pt\n    training_history = train_gan_local(\n  File \"E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\卷积神经网络\\GAN_pt_local_mode.py\", line 394, in train_gan_local\n    fake_validity_d = discriminator(fake_imgs.detach()) # Detach to avoid G gradients\n  File \"c:\\ProgramData\\Anaconda3\\envs\\bypt\\lib\\site-packages\\torch\\nn\\modules\\module.py\", line 1553, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n  File \"c:\\ProgramData\\Anaconda3\\envs\\bypt\\lib\\site-packages\\torch\\nn\\modules\\module.py\", line 1562, in _call_impl\n    return forward_call(*args, **kwargs)\n  File \"E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\卷积神经网络\\GAN_pt_local_mode.py\", line 327, in forward\n    validity = self.adv_layer(out)\n  File \"c:\\ProgramData\\Anaconda3\\envs\\bypt\\lib\\site-packages\\torch\\nn\\modules\\module.py\", line 1553, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n  File \"c:\\ProgramData\\Anaconda3\\envs\\bypt\\lib\\site-packages\\torch\\nn\\modules\\module.py\", line 1562, in _call_impl\n    return forward_call(*args, **kwargs)\n  File \"c:\\ProgramData\\Anaconda3\\envs\\bypt\\lib\\site-packages\\torch\\nn\\modules\\container.py\", line 219, in forward\n    input = module(input)\n  File \"c:\\ProgramData\\Anaconda3\\envs\\bypt\\lib\\site-packages\\torch\\nn\\modules\\module.py\", line 1553, in _wrapped_call_impl\n    return self._call_impl(*args, **kwargs)\n  File \"c:\\ProgramData\\Anaconda3\\envs\\bypt\\lib\\site-packages\\torch\\nn\\modules\\module.py\", line 1562, in _call_impl\n    return forward_call(*args, **kwargs)\n  File \"c:\\ProgramData\\Anaconda3\\envs\\bypt\\lib\\site-packages\\torch\\nn\\modules\\linear.py\", line 117, in forward\n    return F.linear(input, self.weight, self.bias)\nRuntimeError: mat1 and mat2 shapes cannot be multiplied (64x2048 and 25088x1)\n", "timestamp": "2025-05-23T16:44:23.934556"}