2025-05-23 15:11:34,639 - QLearningTF_CartPole_main - INFO - Logging to file: ./results/qlearning_tf_local_run\QLearningTF_CartPole_main_training_20250523_151134.log
2025-05-23 15:11:34,639 - QLearningTF_CartPole_main - INFO - Executing Q-Learning TF Local Mode. Args: {'env_name': 'CartPole-v1', 'result_dir': './results/qlearning_tf_local_run', 'model_name': 'QLearningTF_CartPole', 'hidden_size': 64, 'learning_rate': 0.001, 'gamma': 0.99, 'epsilon_start': 1.0, 'epsilon_end': 0.01, 'epsilon_decay': 0.995, 'buffer_size': 10000, 'batch_size': 64, 'num_episodes': 3, 'max_steps_per_episode': 200, 'random_seed': None, 'save_freq': 50, 'plot_freq': 20, 'target_update_freq': 10, 'tf_log_level': 'ERROR', 'log_level': 'INFO', 'mode': 'train', 'test_qnetwork_weights_path': None, 'test_hyperparams_path': None, 'test_episodes': 5, 'render_test': False}
2025-05-23 15:11:37,091 - QLearningTF_CartPole_main - ERROR - Critical error: 'NoneType' object has no attribute 'get'
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\Q-Learning_tf_local_mode.py", line 422, in <module>
    q_learning_train_tf_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\Q-Learning_tf_local_mode.py", line 326, in q_learning_train_tf_local
    'hyperparameters_used': agent.save_agent_local(f"{final_agent_prefix}_temp_for_summary").get('hyperparameters', {}), # Call to get dict structure
AttributeError: 'NoneType' object has no attribute 'get'
