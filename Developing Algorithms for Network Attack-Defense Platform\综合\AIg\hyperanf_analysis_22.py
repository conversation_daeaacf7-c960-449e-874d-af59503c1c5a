"""HyperANF"""

import sys
import pandas as pd
import networkx as nx
from networkx.algorithms import approx

def hyperanf_analysis(input_file, source_column, target_column):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - source_column: 源节点列名
    - target_column: 目标节点列名
    """
    data = pd.read_csv(input_file)
    
    G = nx.from_pandas_edgelist(data, source=source_column, target=target_column)
    
    centrality = approx.betweenness_centrality_subset(G, G.nodes())
    
    output_file = 'hyperanf_centrality.csv'
    pd.DataFrame(list(centrality.items()), columns=['Node', 'Centrality']).to_csv(output_file, index=False)
    print(f"HyperANF analysis completed. Output saved to {output_file}")

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python hyperanf_analysis.py <input_file> <source_column> <target_column>")
        sys.exit(1)
    input_file, source_column, target_column = sys.argv[1], sys.argv[2], sys.argv[3]
    hyperanf_analysis(input_file, source_column, target_column)
