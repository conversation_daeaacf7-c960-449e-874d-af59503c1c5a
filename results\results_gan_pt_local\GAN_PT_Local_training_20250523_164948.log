2025-05-23 16:49:48,966 - GAN_PT_Local - INFO - Logging to file: ./results_gan_pt_local\GAN_PT_Local_training_20250523_164948.log
2025-05-23 16:49:48,967 - GAN_PT_Local - INFO - GAN PyTorch Local Mode started. Args: {'data_path': 'E:/data/Flowers Recognition/flowers', 'dataset_type': 'folder', 'annotations_file': None, 'image_size': 224, 'image_channels': 3, 'model_name': 'GAN_PT_Local', 'latent_dim': 100, 'num_epochs': 2, 'batch_size': 32, 'g_lr': 0.0002, 'd_lr': 0.0002, 'beta1': 0.5, 'sample_interval': 400, 'fixed_noise_samples': 16, 'save_epoch_interval': 5, 'result_dir': './results_gan_pt_local', 'num_workers': 0, 'pin_memory': False, 'random_seed': None, 'force_cpu': False, 'log_level': 'INFO'}
2025-05-23 16:49:48,968 - GAN_PT_Local - INFO - Using device: cpu
2025-05-23 16:49:48,968 - GAN_PT_Local - INFO - Preparing dataset from: E:/data/Flowers Recognition/flowers, type: folder
2025-05-23 16:49:48,969 - GAN_PT_Local - INFO - Loading images for dataset type 'folder' from source: 'E:/data/Flowers Recognition/flowers'
2025-05-23 16:49:49,004 - GAN_PT_Local - INFO - Found 4317 images.
2025-05-23 16:49:49,005 - GAN_PT_Local - WARNING - Target image_size 224 is not a direct power-of-2 multiple of init_size 4. Generator will produce 256x256 images, which will be resized to 224x224 in the training loop before Discriminator.
2025-05-23 16:49:49,012 - GAN_PT_Local - INFO - Generator configured. Latent dim: 100, Init size: 4x4, Num upsamples: 6. Effective output spatial size: 256x256. Output channels: 3. (Original image_size arg for dataset/discriminator: 224x224)
2025-05-23 16:49:49,023 - GAN_PT_Local - INFO - Discriminator configured. Input: 224x224x3. Conv backbone downsamples to: 14x14 with 4 stages. Final conv output channels: 512.
2025-05-23 16:49:49,025 - GAN_PT_Local - INFO - --- Training GAN ---
