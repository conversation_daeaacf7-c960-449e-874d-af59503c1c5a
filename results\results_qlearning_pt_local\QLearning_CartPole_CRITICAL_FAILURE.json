{"args": {"env_name": "CartPole-v1", "result_dir": "./results/results_qlearning_pt_local", "model_name": "QLearning_CartPole", "hidden_size": 64, "learning_rate": 0.001, "gamma": 0.99, "epsilon_start": 1.0, "epsilon_end": 0.01, "epsilon_decay": 0.995, "buffer_size": 10000, "batch_size": 32, "num_episodes": 3, "max_steps_per_episode": 200, "random_seed": null, "device": "auto", "save_freq": 100, "plot_freq": 50, "log_level": "INFO", "mode": "train", "test_model_path": null, "test_episodes": 10, "render_test": false}, "error": "[enforce fail at inline_container.cc:642] . invalid file name: ", "traceback": "Traceback (most recent call last):\n  File \"E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\强化学习\\Q-Learning_pt_local_mode.py\", line 445, in <module>\n    q_learning_train_local(\n  File \"E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\强化学习\\Q-Learning_pt_local_mode.py\", line 355, in q_learning_train_local\n    'hyperparameters': agent.training_info.get('hyperparameters', agent.save_checkpoint_local('').get('hyperparameters', {})), # Get from checkpoint structure\n  File \"E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\强化学习\\Q-Learning_pt_local_mode.py\", line 201, in save_checkpoint_local\n    torch.save(checkpoint, filepath)\n  File \"c:\\ProgramData\\Anaconda3\\envs\\bypt\\lib\\site-packages\\torch\\serialization.py\", line 651, in save\n    with _open_zipfile_writer(f) as opened_zipfile:\n  File \"c:\\ProgramData\\Anaconda3\\envs\\bypt\\lib\\site-packages\\torch\\serialization.py\", line 525, in _open_zipfile_writer\n    return container(name_or_buffer)\n  File \"c:\\ProgramData\\Anaconda3\\envs\\bypt\\lib\\site-packages\\torch\\serialization.py\", line 496, in __init__\n    super().__init__(torch._C.PyTorchFileWriter(self.name))\nRuntimeError: [enforce fail at inline_container.cc:642] . invalid file name: \n", "timestamp": "2025-05-23T15:06:55.382355"}