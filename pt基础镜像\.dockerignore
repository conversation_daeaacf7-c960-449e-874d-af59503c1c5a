# Docker构建忽略文件
# 算法代码和模型文件不包含在基础镜像中

# 算法代码目录
*.py
*.ipynb
*.pkl
*.pth
*.pt
*.model
*.h5
*.onnx

# 数据文件
*.csv
*.json
*.txt
*.log
*.npy
*.npz

# 模型和权重文件
model/
models/
weights/
checkpoints/
data/
logs/
output/

# 开发和测试文件
test_*.py
*_test.py
tests/
__pycache__/
*.pyc
*.pyo
*.pyd
.pytest_cache/

# 版本控制
.git/
.gitignore

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 系统文件
.DS_Store
Thumbs.db

# 文档
*.md
docs/

# 临时文件
tmp/
temp/
*.tmp

# 只保留必要的配置文件
!requirements*.txt
!Dockerfile*
!.dockerignore

# .dockerignore for BiLSTM Inference Service
# 基于优化后的BiLSTMPredictor.py

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyTorch相关
*.pth
*.pt
*.bin
*.safetensors
checkpoints/
experiments/
results/

# 日志和临时文件
*.log
*.tmp
*.temp
.DS_Store
Thumbs.db

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# Git
.git/
.gitignore

# 环境和配置
.env
.venv
env/
venv/
ENV/

# 测试和覆盖率
.coverage
.pytest_cache/
.tox/
.nox/
htmlcov/

# Jupyter
.ipynb_checkpoints

# 文档
docs/_build/
*.md

# 大型文件 - 已删除的文件类型
*.tar
*.tar.gz
*.zip
*.7z 