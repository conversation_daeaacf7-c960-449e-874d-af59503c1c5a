<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络攻防博弈平台资源监控</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@400;500;600;700&family=Share+Tech+Mono&display=swap');

        :root {
            /* 现代蓝色主题配色 - 基于RGB(50,117,237) */
            --bg-primary: #0f1524;
            --bg-secondary: #151a2e;
            --bg-tertiary: #1a2037;
            --bg-dark: #0a0f1a;
            --bg-card: rgba(22, 35, 68, 0.85);
            --bg-card-alt: rgba(35, 55, 95, 0.75);
            --bg-light: rgba(50, 70, 120, 0.6);
            
            /* 蓝色调色彩系统 */
            --text-primary: #e8f0ff;
            --text-secondary: #a8c5ff;
            --accent-blue: #3275ed;
            --accent-light-blue: #5b93ff;
            --accent-dark-blue: #1e5bb8;
            --accent-purple: #7c5eff;
            --accent-pink: #ed5ca0;
            --accent-yellow: #ffb347;
            --accent-green: #47d170;
            --accent-cyan: #47c9d1;
            --accent-orange: #ff7a47;
            
            /* 图表专用色彩 */
            --graph-blue: #3275ed;
            --graph-light-blue: #5b93ff;
            --graph-green: #47d170;
            --graph-red: #ed5ca0;
            --graph-yellow: #ffb347;
            --graph-purple: #7c5eff;
            --graph-cyan: #47c9d1;
            
            /* 光效和边框 */
            --card-glow: 0 8px 32px rgba(50, 117, 237, 0.3);
            --card-border: 1px solid rgba(50, 117, 237, 0.4);
            --border-light: rgba(50, 117, 237, 0.2);
            --shadow-color: rgba(50, 117, 237, 0.15);
            
            /* 网格线和装饰 */
            --grid-color: rgba(50, 117, 237, 0.08);
            --grid-glow: rgba(91, 147, 255, 0.12);
        }

        body {
            font-family: system-ui, -apple-system, 'Segoe UI', Roboto, Ubuntu, Cantarell, 'Noto Sans', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-primary) 50%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            overflow-x: hidden;
            overflow-y: auto;
            font-size: 16px; /* 从14px增加到16px */
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: "";
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            background-image: 
                linear-gradient(var(--grid-glow) 2px, transparent 2px),
                linear-gradient(90deg, var(--grid-glow) 2px, transparent 2px),
                radial-gradient(circle at 20% 80%, rgba(50, 117, 237, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(124, 94, 255, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(237, 92, 160, 0.1) 0%, transparent 50%);
            background-size: 50px 50px, 50px 50px, 800px 800px, 600px 600px, 400px 400px;
            z-index: -1;
            opacity: 0.6;
        }

        body::after {
            content: "";
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            background: 
                linear-gradient(45deg, transparent 49%, rgba(50, 117, 237, 0.03) 50%, transparent 51%),
                linear-gradient(-45deg, transparent 49%, rgba(124, 94, 255, 0.03) 50%, transparent 51%);
            background-size: 100px 100px;
            z-index: -1;
            animation: gridShift 20s linear infinite;
        }

        @keyframes gridShift {
            0% { transform: translate(0, 0); }
            100% { transform: translate(50px, 50px); }
        }

        .dashboard-container {
            display: grid;
            width: 100%;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: 50px minmax(550px, auto) repeat(2, minmax(400px, auto)) minmax(450px, auto);
            gap: 20px;
            grid-template-areas:
                "header header header header"
                "system-interaction system-interaction system-interaction system-interaction"
                "nvidia-gpu nvidia-gpu huawei-gpu huawei-gpu"
                "cpu-servers cpu-servers cpu-servers storage";
            padding: 20px;
            animation: fadeIn 0.5s ease-in;
            margin-bottom: 20px;
            background: transparent;
            position: relative;
        }

        .dashboard-container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(ellipse at center, rgba(50, 117, 237, 0.05) 0%, transparent 70%);
            border-radius: 20px;
            z-index: -1;
            pointer-events: none;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulseGlow {
            0% { box-shadow: var(--card-glow); }
            50% { box-shadow: 0 8px 32px rgba(0, 255, 255, 0.4); }
            100% { box-shadow: var(--card-glow); }
        }

        .header {
            grid-area: header;
            display: flex;
            align-items: center;
            padding: 0 20px;
            background: linear-gradient(90deg, var(--bg-card), var(--bg-card-alt));
            box-shadow: var(--card-glow);
            border: var(--card-border);
            border-radius: 12px;
            backdrop-filter: blur(20px);
            position: relative;
            overflow: hidden;
            min-height: 50px; /* 确保匹配网格行高 */
        }

        .header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), var(--accent-pink));
            opacity: 0.9;
        }

        .header h1 {
            margin: 0;
            font-size: 28px; /* 从24px增加到28px */
            font-weight: 700;
            letter-spacing: 2px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 20px rgba(50, 117, 237, 0.5);
        }

        .header .subtitle {
            color: var(--text-secondary);
            margin-left: 15px;
            font-size: 18px; /* 从16px增加到18px */
            font-weight: 500;
        }

        .card {
            border-radius: 15px;
            background: var(--bg-card);
            box-shadow: var(--card-glow);
            border: var(--card-border);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
            min-height: 300px;
            backdrop-filter: blur(20px);
        }

        .card::after {
            content: "";
            position: absolute;
            top: 0;
            right: 0;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, transparent 50%, rgba(50, 117, 237, 0.1) 50%);
            border-bottom-left-radius: 15px;
            opacity: 0.7;
            pointer-events: none;
        }

        .card::before {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 40%;
            height: 4px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), transparent);
            opacity: 0.8;
            z-index: 2;
            border-radius: 2px;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(50, 117, 237, 0.4);
            border-color: var(--accent-blue);
        }

        .card-header {
            padding: 20px 25px;
            font-weight: 600;
            font-size: 22px; /* 从18px增加到22px */
            background: linear-gradient(135deg, var(--bg-card-alt), var(--bg-card));
            border-bottom: 2px solid var(--border-light);
            z-index: 10;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            justify-content: center; /* 标题居中 */
            letter-spacing: 1px;
            position: relative;
            backdrop-filter: blur(15px);
            text-align: center;
        }

        .card-header i {
            margin-right: 12px;
            color: var(--accent-blue);
            position: relative;
            z-index: 1;
            font-size: 24px; /* 从20px增加到24px */
            text-shadow: 0 0 10px currentColor;
        }

        .card-header::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            width: 50px;
            height: 100%;
            background: linear-gradient(90deg, rgba(50, 117, 237, 0.15), transparent);
            z-index: 0;
        }

        .card-header::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple), transparent);
            opacity: 0.6;
        }

        .card-content {
            flex: 1;
            position: relative;
            overflow: auto;
            scrollbar-width: thin;
            scrollbar-color: var(--accent-blue) var(--bg-card-alt);
        }

        .card-content::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        .card-content::-webkit-scrollbar-track {
            background: var(--bg-card-alt);
        }

        .card-content::-webkit-scrollbar-thumb {
            background: var(--accent-blue);
            border-radius: 3px;
        }

        #nvidia-gpu-container {
            grid-area: nvidia-gpu;
        }

        #huawei-gpu-container {
            grid-area: huawei-gpu;
        }

        #cpu-servers-container {
            grid-area: cpu-servers;
            min-height: 450px; /* 确保足够高度 */
        }

        #storage-container {
            grid-area: storage;
            min-height: 450px;
        }

        .nvidia-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            padding: 10px;
            height: 100%;
        }

        .nvidia-item {
            background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt));
            border-radius: 12px;
            border: var(--card-border);
            padding: 10px;
            backdrop-filter: blur(15px);
            transition: all 0.3s ease;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .nvidia-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--card-glow);
            border-color: var(--accent-blue);
        }

        .nvidia-item h5 {
            font-size: 16px; /* 从14px增加到16px */
            margin: 0 0 8px 0;
            text-align: center;
            color: var(--text-primary);
            font-weight: 600;
            letter-spacing: 1px;
            text-shadow: 0 0 8px rgba(50, 117, 237, 0.3);
        }

        .chart-wrapper {
            flex: 1;
            position: relative;
            min-height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .small-chart,
        .chart-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        #deepseek-container .card-content {
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        #deepseek-info-container {
            max-height: 50%;
            padding: 0;
            overflow: auto;
            flex-shrink: 0;
            margin-top: 10px;
            border-top: 1px solid rgba(0, 212, 255, 0.15);
        }

        #deepseek-info-container .table {
            margin-bottom: 0;
            background: var(--bg-card);
            color: var(--text-primary);
        }

        #deepseek-info-container .table th,
        #task-table-container th,
        .table-light {
            background: linear-gradient(90deg, rgba(15, 25, 40, 0.9), rgba(25, 35, 55, 0.8)) !important;
            color: var(--accent-cyan) !important;
            border-color: rgba(0, 212, 255, 0.3) !important;
            font-weight: 600;
            letter-spacing: 0.5px;
            text-shadow: 0 0 5px rgba(0, 212, 255, 0.5);
        }

        #deepseek-info-container .table td {
            padding: 6px 4px;
            font-size: 12px;
            border-color: rgba(0, 212, 255, 0.1);
            background: transparent;
            color: var(--text-primary);
            vertical-align: middle;
        }

        .deepseek-info {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .deepseek-info .row,
        .deepseek-info .col-md-12 {
            height: 100%;
            flex: 1;
            margin: 0;
            padding: 0;
        }

        .deepseek-info .table thead th {
            padding: 4px;
            font-size: 11px;
            background: rgba(0, 0, 0, 0.3);
            color: var(--accent-blue);
            border-color: rgba(0, 212, 255, 0.2);
        }

        .deepseek-info .table td {
            padding: 6px 4px;
            font-size: 11.5px;
            vertical-align: middle;
            border-color: rgba(0, 212, 255, 0.1);
        }

        .deepseek-info .table {
            margin-bottom: 0;
            background: transparent;
            color: var(--text-primary);
        }

        #task-container .chart-wrapper {
            height: 180px;
            min-height: 180px;
            position: relative;
            margin-bottom: 10px;
        }

        #task-container .card-content {
            display: flex;
            flex-direction: column;
        }

        #task-table-container {
            padding: 5px;
            overflow: auto;
            flex: 1;
            display: block !important;
            min-height: 120px;
            border-top: 1px solid rgba(0, 212, 255, 0.15);
            margin-top: 5px;
        }

        #task-table-container table {
            font-size: 12px;
            margin-bottom: 0;
            color: var(--text-primary);
        }

        #task-table-container th {
            padding: 8px 15px;
            border-color: rgba(204, 221, 238, 0.12);
            background-color: rgba(0, 212, 255, 0.2) !important;
            color: #ffffff !important;
            font-weight: bold;
            text-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
        }

        #task-table-container td {
            padding: 8px 15px;
            border-color: rgba(204, 221, 238, 0.12);
            color: #ffffff;
            font-size: 15px;
        }

        #task-table-container tr:nth-child(2) td {
            color: #00d4ff;
            font-weight: bold;
            text-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
        }

        #task-table-container .badge {
            font-size: 11px;
            padding: 3px 5px;
            background: linear-gradient(135deg, var(--accent-green), var(--accent-blue));
            font-weight: 500;
            color: white;
            box-shadow: 0 1px 3px rgba(95, 169, 255, 0.2);
            letter-spacing: 0.5px;
            border-radius: 12px;
        }

        #cpu-servers-container .card-content {
            overflow: hidden;
            padding: 20px; /* 增加内边距 */
        }

        #cpu-servers-wrapper {
            width: 100%;
            height: 100%;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: 1fr 1fr;
            gap: 8px;
            padding: 8px;
        }

        .cpu-system-item {
            background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt));
            border-radius: 12px;
            border: var(--card-border);
            overflow: hidden;
            position: relative;
            width: 100%;
            height: auto;
            min-height: 180px; /* 从120px增加到180px */
            padding: 20px; /* 从15px增加到20px */
            backdrop-filter: blur(15px);
            transition: all 0.3s ease;
            margin-bottom: 20px; /* 从15px增加到20px */
        }

        .cpu-system-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--card-glow);
            border-color: var(--accent-cyan);
        }

        #cpu-system4 {
            grid-column: 3 / span 1;
            grid-row: 2;
            margin-right: 37%;
            width: 95%;
        }

        #cpu-system5 {
            grid-column: 1 / span 1;
            grid-row: 2;
            margin-left: 37%;
            width: 95%;
        }

        .empty-cell {
            display: none;
        }

        .echarts-tooltip {
            font-size: 11px;
            backdrop-filter: blur(8px) !important;
            background: rgba(255, 255, 255, 0.97) !important;
            border: 1px solid rgba(95, 169, 255, 0.3) !important;
            border-radius: 6px !important;
            box-shadow: 0 3px 12px rgba(95, 169, 255, 0.2) !important;
        }

        .table {
            width: 100%;
            max-width: 100%;
            table-layout: fixed;
            border-color: rgba(95, 169, 255, 0.2);
        }
        
        .table td, .table th {
            padding: 5px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .table-striped > tbody > tr:nth-of-type(odd) {
            background: linear-gradient(90deg, rgba(247,250,255,0.6), rgba(255,255,255,0.4));
        }

        #storage-wrapper {
            width: 100%;
            height: 100%;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            padding: 8px;
        }

        .storage-item {
            background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt));
            border-radius: 12px;
            border: var(--card-border);
            overflow: hidden;
            position: relative;
            padding: 15px;
            backdrop-filter: blur(15px);
            transition: all 0.3s ease;
            margin-bottom: 15px;
            min-height: 120px;
        }

        .storage-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--card-glow);
            border-color: var(--accent-cyan);
        }

        .storage-item .table {
            font-size: 16px;
        }

        .storage-item .table td,
        .storage-item .table th {
            padding: 15px 12px;
            font-size: 16px;
        }

        .storage-item h5 {
            font-size: 18px;
            margin: 0 0 15px 0;
            text-align: center;
            color: var(--text-primary);
            font-weight: 700;
            letter-spacing: 1px;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.4);
            background: linear-gradient(90deg, var(--accent-cyan), var(--accent-purple));
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .badge {
            font-weight: normal;
        }

        #deepseek-container .chart-wrapper {
            height: 45% !important;
            min-height: 170px;
            margin-bottom: 10px;
        }

        /* Neon text effects */
        .text-danger {
            color: var(--accent-pink) !important;
            font-weight: 600;
        }

        .text-success {
            color: var(--accent-green) !important;
            font-weight: 600;
        }

        .text-primary {
            color: var(--accent-blue) !important;
            font-weight: 600;
        }

        /* Tech grid pattern with more visible light blue lines */
        body::before {
            background-image: 
                linear-gradient(rgba(95, 169, 255, 0.12) 1px, transparent 1px),
                linear-gradient(90deg, rgba(95, 169, 255, 0.12) 1px, transparent 1px);
            background-size: 30px 30px;
        }

        /* Digital counter effect */
        .digital-counter {
            font-family: 'Share Tech Mono', monospace;
            letter-spacing: 1px;
            color: var(--accent-blue);
        }

        /* Pulse animation for badges and important elements */
        .pulse {
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.8; }
            100% { opacity: 1; }
        }

        .table-bordered {
            border-color: rgba(95, 169, 255, 0.2);
        }

        .table-bordered > :not(caption) > * > * {
            border-color: rgba(95, 169, 255, 0.1);
        }

        .table-light {
            background: #f0f7ff; /* Very light blue table headers */
            color: var(--text-primary);
        }

        /* 模型可视化增强样式 */
        .model-detail-card {
            background: var(--bg-card);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: var(--card-border);
            transition: all 0.3s ease;
        }

        .active-model {
            box-shadow: var(--card-glow);
            border-color: var(--accent-cyan);
        }

        .model-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-light);
        }

        .model-icon {
            margin-right: 15px;
            font-size: 24px;
            color: var(--accent-cyan);
            text-shadow: 0 0 10px currentColor;
        }

        .model-info h3 {
            margin: 0 0 5px 0;
            color: var(--text-primary);
            font-size: 18px;
            font-weight: 600;
        }

        .model-version {
            margin: 0 0 5px 0;
            color: var(--text-secondary);
            font-size: 12px;
        }

        .model-desc {
            margin: 0;
            color: var(--text-secondary);
            font-size: 13px;
        }

        .model-stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            background: var(--bg-card-alt);
            padding: 12px;
            border-radius: 8px;
            border: 1px solid var(--border-light);
        }

        .stat-label {
            display: block;
            color: var(--text-secondary);
            font-size: 11px;
            margin-bottom: 5px;
        }

        .stat-value {
            display: block;
            color: var(--accent-cyan);
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .stat-bar {
            height: 4px;
            background: var(--bg-dark);
            border-radius: 2px;
            overflow: hidden;
        }

        .stat-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-cyan), var(--accent-purple));
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .model-features {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 20px;
        }

        .feature-tag {
            background: var(--bg-card-alt);
            color: var(--accent-cyan);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            border: 1px solid var(--accent-cyan);
        }

        .model-performance h4 {
            color: var(--text-primary);
            font-size: 14px;
            margin: 0 0 10px 0;
        }

        .performance-chart {
            height: 150px;
            background: var(--bg-card-alt);
            border-radius: 8px;
            border: 1px solid var(--border-light);
        }

        /* Restored and improved Huawei GPU chart size fix */
        #huawei-gpu-container .chart-wrapper {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        
        #huawei-gpu-usage text {
            fill: var(--text-primary) !important;
            font-weight: 500 !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
        }

        /* Improved label contrast for DeepSeek chart */
        #deepseek-resource-usage text {
            fill: var(--text-primary) !important;
            font-weight: 500 !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
        }

        /* Add tech decorative elements to cards */
        .nvidia-grid, #cpu-servers-wrapper, #storage-wrapper {
            position: relative;
        }
        
        .nvidia-grid::before, #cpu-servers-wrapper::before, #storage-wrapper::before {
            content: "";
            position: absolute;
            top: 5px;
            left: 5px;
            width: 40px;
            height: 40px;
            border-top: 2px solid rgba(95, 169, 255, 0.2);
            border-left: 2px solid rgba(95, 169, 255, 0.2);
            border-radius: 6px 0 0 0;
            pointer-events: none;
            z-index: 2;
        }

        /* Cyber attack/defense visualization section */
        #cyber-visual-container {
            grid-area: cyber-visual;
            display: none; /* 隐藏网络攻防博弈态势部分 */
        }
        
        .cyber-visual-header {
            padding: 15px 20px;
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 18px;
            color: var(--accent-blue);
            position: relative;
            background: linear-gradient(90deg, rgba(0, 0, 10, 0.6), rgba(10, 10, 40, 0.6));
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
            z-index: 10;
        }
        
        .cyber-visual-header i {
            margin-right: 8px;
        }
        
        .cyber-visual-content {
            flex: 1;
            padding: 15px;
            position: relative;
            overflow: hidden;
        }
        
        #attack-defense-visual {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            background: linear-gradient(135deg, rgba(10, 10, 20, 0.9), rgba(20, 20, 40, 0.9));
            border-radius: 5px;
            overflow: hidden;
            position: relative;
        }

        /* 顶部状态栏 */
        .game-status-bar {
            width: 100%;
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 0 20px;
        }

        .status-indicator {
            background: rgba(0, 0, 0, 0.3);
            padding: 8px 15px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(0, 212, 255, 0.3);
        }

        .status-indicator i {
            color: var(--accent-blue);
        }

        #battle-timer {
            font-family: 'Share Tech Mono', monospace;
        }

        #equilibrium-status {
            background: rgba(247, 147, 30, 0.2);
            border-color: rgba(247, 147, 30, 0.4);
        }

        #equilibrium-status i {
            color: var(--accent-yellow);
        }

        /* 主战场区域 */
        .main-battlefield {
            flex: 1;
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
            background: 
                radial-gradient(circle at 20% 80%, rgba(255, 8, 68, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                linear-gradient(45deg, transparent 49%, rgba(255, 255, 255, 0.05) 50%, transparent 51%);
            background-size: 100px 100px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .attacker-zone {
            position: absolute;
            left: 40px;
            top: 50%;
            transform: translateY(-50%);
            width: 120px;
            height: 120px;
            background: radial-gradient(circle, var(--accent-pink) 0%, rgba(255, 8, 68, 0.4) 70%, transparent 100%);
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            animation: pulse-red 2s infinite;
        }

        .defender-zone {
            position: absolute;
            right: 40px;
            top: 50%;
            transform: translateY(-50%);
            width: 120px;
            height: 120px;
            background: radial-gradient(circle, var(--accent-blue) 0%, rgba(0, 212, 255, 0.4) 70%, transparent 100%);
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            animation: pulse-blue 2s infinite alternate;
        }

        @keyframes pulse-red {
            0%, 100% { transform: translateY(-50%) scale(1); }
            50% { transform: translateY(-50%) scale(1.1); }
        }

        @keyframes pulse-blue {
            0% { transform: translateY(-50%) scale(1); }
            100% { transform: translateY(-50%) scale(1.05); }
        }

        .zone-label {
            font-size: 16px;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(0, 0, 0, 0.7);
            margin-bottom: 5px;
        }

        .attack-model-level, .defense-model-level {
            font-size: 24px;
            font-weight: bold;
            font-family: 'Share Tech Mono', monospace;
        }

        .attack-model-level {
            color: var(--accent-pink);
            text-shadow: 0 0 10px rgba(255, 8, 68, 0.7);
        }

        .defense-model-level {
            color: var(--accent-blue);
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.7);
        }

        .attack-beam {
            position: absolute;
            top: 50%;
            left: 160px;
            right: 160px;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-pink), rgba(255, 8, 68, 0.3));
            opacity: 0;
            animation: attack-wave 3s infinite;
        }

        @keyframes attack-wave {
            0% { opacity: 0; transform: scaleX(0); transform-origin: left center; }
            20% { opacity: 1; transform: scaleX(1); transform-origin: left center; }
            80% { opacity: 1; transform: scaleX(1); transform-origin: left center; }
            100% { opacity: 0; transform: scaleX(0); transform-origin: right center; }
        }

        .defense-shield {
            position: absolute;
            right: 160px;
            top: 50%;
            transform: translateY(-50%);
            width: 80px;
            height: 80px;
            border: 3px solid var(--accent-blue);
            border-radius: 50%;
            opacity: 0;
            animation: shield-activate 3s infinite 1.5s;
        }

        @keyframes shield-activate {
            0% { opacity: 0; transform: translateY(-50%) scale(0.8); }
            30% { opacity: 1; transform: translateY(-50%) scale(1.2); }
            70% { opacity: 1; transform: translateY(-50%) scale(1); }
            100% { opacity: 0; transform: translateY(-50%) scale(0.8); }
        }

        .equilibrium-point {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            border: 2px solid var(--accent-yellow);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(247, 147, 30, 0.1);
            animation: equilibrium-glow 2s infinite alternate;
            font-size: 12px;
            text-align: center;
            font-weight: bold;
            color: var(--accent-yellow);
        }

        @keyframes equilibrium-glow {
            0% { box-shadow: 0 0 15px rgba(247, 147, 30, 0.3); }
            100% { box-shadow: 0 0 30px rgba(247, 147, 30, 0.6); }
        }

        .floating-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 3px;
            height: 3px;
            border-radius: 50%;
            animation: float 8s infinite linear;
        }

        .particle.attack {
            background: var(--accent-pink);
            box-shadow: 0 0 5px var(--accent-pink);
        }

        .particle.defense {
            background: var(--accent-blue);
            box-shadow: 0 0 5px var(--accent-blue);
        }

        @keyframes float {
            0% { transform: translateY(100%) translateX(0); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-20%) translateX(100px); opacity: 0; }
        }

        /* 控制面板 */
        .battle-controls {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 10px;
            height: 160px;
        }

        .strategy-panel {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .panel-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--accent-yellow);
            border-bottom: 1px solid rgba(247, 147, 30, 0.3);
            padding-bottom: 5px;
        }

        .strategy-matrix {
            display: grid;
            grid-template-rows: auto auto auto;
            gap: 5px;
        }

        .matrix-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 5px;
        }

        .matrix-cell {
            background: rgba(0, 0, 0, 0.3);
            padding: 6px;
            text-align: center;
            border-radius: 4px;
            font-size: 11px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .header-row .matrix-cell {
            background: rgba(247, 147, 30, 0.2);
            font-weight: bold;
            color: var(--accent-yellow);
        }

        .battle-stats {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.05);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .stats-row {
            display: flex;
            justify-content: space-between;
            gap: 10px;
        }

        .stat-item {
            flex: 1;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 6px;
            padding: 8px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .stat-value {
            font-size: 18px;
            font-weight: bold;
            font-family: 'Share Tech Mono', monospace;
            color: var(--accent-blue);
            margin-bottom: 5px;
        }

        .attack-value {
            color: var(--accent-pink);
        }

        .defense-value {
            color: var(--accent-blue);
        }

        .stat-label {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .battle-log-panel {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.05);
            display: flex;
            flex-direction: column;
        }

        .battle-logs {
            flex: 1;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 6px;
            padding: 8px;
            font-size: 11px;
            scrollbar-width: thin;
            scrollbar-color: var(--accent-blue) var(--bg-card-alt);
        }

        .battle-logs::-webkit-scrollbar {
            width: 5px;
        }

        .battle-logs::-webkit-scrollbar-track {
            background: var(--bg-card-alt);
        }

        .battle-logs::-webkit-scrollbar-thumb {
            background: var(--accent-blue);
            border-radius: 3px;
        }

        .log-message {
            padding: 3px 0;
            margin-bottom: 2px;
            border-bottom: 1px dotted rgba(255, 255, 255, 0.1);
            animation: fadeIn 0.3s ease;
        }

        .log-message.attack {
            color: var(--accent-pink);
        }

        .log-message.defense {
            color: var(--accent-blue);
        }

        .log-message.system {
            color: var(--accent-green);
        }

        .log-message .timestamp {
            font-family: 'Share Tech Mono', monospace;
            margin-right: 5px;
            opacity: 0.7;
        }

        /* 统一图表文本颜色 */
        .echarts-for-huawei-gpu text {
            fill: var(--text-primary) !important;
            font-weight: 500 !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
        }
        
        #huawei-gpu-container .card-content {
            position: relative;
            overflow: hidden;
            height: 100%;
        }
        
        #huawei-gpu-container .chart-wrapper {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        #huawei-gpu-container .chart-container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        /* 华为GPU表格 */
        #huawei-gpu-info-container {
            margin-top: 10px;
            border-top: 1px solid rgba(0, 212, 255, 0.15);
            max-height: 40%;
            overflow: auto;
            padding: 5px;
        }
        
        #huawei-gpu-container .card-content {
            position: relative;
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        
        #huawei-gpu-container .chart-wrapper {
            height: 60%;
            min-height: 200px;
            flex-shrink: 0;
        }

        #system-interaction-container {
            grid-area: system-interaction;
            min-height: 550px;
            height: auto;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            position: relative;
            border-radius: 15px;
            background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt));
            backdrop-filter: blur(20px);
        }
        
        #system-interaction-container:hover {
            box-shadow: var(--card-glow);
        }
        
        #system-interaction-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                135deg, 
                rgba(15, 21, 36, 0.6) 0%, 
                rgba(35, 55, 95, 0.4) 50%, 
                rgba(15, 21, 36, 0.6) 100%
            );
            border-radius: 15px;
            z-index: 1;
            backdrop-filter: blur(1px);
            pointer-events: none;
        }
        
        #system-interaction-container .card-header,
        #system-interaction-container .card-content {
            position: relative;
            z-index: 2;
        }
        
        #system-interaction-container .card-header {
            flex-shrink: 0;
            height: 60px; /* 增加标题高度 */
            position: relative;
            z-index: 10;
        }
        
        #system-interaction-container .card-content {
            display: flex;
            position: relative;
            min-height: 500px; /* 保持足够的内容区域高度 */
            overflow: hidden;
            padding: 10px; /* 减少内边距 */
            background: rgba(15, 21, 36, 0.1);
            border-radius: 8px;
        }
        
        #subsystem-interaction-visual {
            width: 100%;
            height: 500px;
            min-height: 500px;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, rgba(15, 21, 36, 0.9), rgba(35, 55, 95, 0.8));
            border-radius: 12px;
            border: 1px solid var(--border-light);
        }

        /* 系统节点样式 */
        .system-node {
            position: absolute;
            width: 120px;
            height: 80px;
            background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt));
            border: 2px solid var(--accent-blue);
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(50, 117, 237, 0.3);
        }

        .system-node:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(50, 117, 237, 0.5);
            border-color: var(--accent-cyan);
        }

        .system-node .icon {
            font-size: 24px;
            color: var(--accent-blue);
            margin-bottom: 5px;
            text-shadow: 0 0 10px currentColor;
        }

        .system-node .label {
            font-size: 12px;
            color: var(--text-primary);
            text-align: center;
            font-weight: 600;
            line-height: 1.2;
        }

        /* 连接线样式 */
        .connection-line {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
            opacity: 0.7;
            animation: dataFlow 3s infinite;
        }

        @keyframes dataFlow {
            0% { opacity: 0.3; }
            50% { opacity: 1; }
            100% { opacity: 0.3; }
        }

        /* 数据流动粒子 */
        .data-particle {
            position: absolute;
            width: 6px;
            height: 6px;
            background: var(--accent-cyan);
            border-radius: 50%;
            box-shadow: 0 0 10px currentColor;
            animation: particleFlow 4s infinite linear;
        }

        @keyframes particleFlow {
            0% { transform: translateX(0); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateX(300px); opacity: 0; }
        }

        /* 系统状态指示器 */
        .status-indicator {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--accent-green);
            box-shadow: 0 0 8px currentColor;
            animation: statusPulse 2s infinite;
        }

        @keyframes statusPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 攻击任务对话框样式 */
        .attack-task-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .attack-task-dialog {
            background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt));
            border-radius: 15px;
            border: 2px solid var(--accent-blue);
            width: 600px;
            max-height: 500px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(50, 117, 237, 0.4);
            backdrop-filter: blur(20px);
        }

        .attack-task-header {
            padding: 20px;
            background: linear-gradient(90deg, var(--accent-blue), var(--accent-purple));
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .attack-task-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .attack-task-content {
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .task-item {
            background: var(--bg-card-alt);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid var(--border-light);
            transition: all 0.3s ease;
        }

        .task-item:hover {
            border-color: var(--accent-cyan);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2);
        }

        .task-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .task-description {
            color: var(--text-secondary);
            font-size: 14px;
            margin-bottom: 10px;
        }

        .task-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .task-status.active {
            background: linear-gradient(90deg, var(--accent-green), var(--accent-cyan));
            color: white;
        }

        .task-status.pending {
            background: linear-gradient(90deg, var(--accent-yellow), var(--accent-orange));
            color: white;
        }

        .task-status.completed {
            background: linear-gradient(90deg, var(--accent-purple), var(--accent-pink));
            color: white;
        }

        /* 攻击任务对话框动画 */
        .attack-task-modal {
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .attack-task-modal.show {
            opacity: 1;
        }

        .attack-task-dialog {
            transform: scale(0.9);
            transition: transform 0.3s ease;
        }

        .attack-task-modal.show .attack-task-dialog {
            transform: scale(1);
        }

        /* 增强卡片样式 */
        .card {
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
            z-index: 1;
        }

        .card:hover::before {
            left: 100%;
        }

        /* 系统节点悬停效果增强 */
        .system-node {
            position: relative;
            overflow: hidden;
        }

        .system-node::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(50, 117, 237, 0.3), transparent);
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
            border-radius: 50%;
        }

        .system-node:hover::after {
            width: 200px;
            height: 200px;
        }

        /* 进程状态指示器动画 */
        .status-badge {
            position: relative;
            overflow: hidden;
        }

        .status-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* 响应式设计优化 */
        @media (max-width: 1200px) {
            .dashboard-container {
                grid-template-columns: repeat(2, 1fr);
                grid-template-areas:
                    "header header"
                    "system-interaction system-interaction"
                    "nvidia-gpu nvidia-gpu"
                    "huawei-gpu huawei-gpu"
                    "cpu-servers storage";
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                grid-template-columns: 1fr;
                grid-template-areas:
                    "header"
                    "system-interaction"
                    "nvidia-gpu"
                    "huawei-gpu"
                    "cpu-servers"
                    "storage";
            }

            .system-node {
                width: 100px;
                height: 70px;
            }

            .system-node .label {
                font-size: 10px;
            }
        }

        /* 添加系统交互元素样式 */
        .node-pulse {
            animation: nodePulse 2s infinite;
        }
        
        @keyframes nodePulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(1.15); }
            100% { opacity: 1; transform: scale(1); }
        }
        
        .link-flow {
            animation: linkFlow 3s infinite;
        }
        
        @keyframes linkFlow {
            0% { stroke-dashoffset: 1000; }
            100% { stroke-dashoffset: 0; }
        }
        
        .particle-flow {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #fff;
            pointer-events: none;
            z-index: 100;
            box-shadow: 0 0 10px #fff, 0 0 20px #fff;
        }

        /* 算法模型可视化样式 */
        #models-visualization-container {
            grid-area: models-visualization;
            min-height: 450px;
            display: flex;
            flex-direction: column;
        }

        #models-visualization {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            min-height: 500px;
        }

        .model-selector {
            margin-bottom: 15px;
        }

        .model-tabs {
            display: flex;
            flex-wrap: wrap;
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
            padding: 0 5px;
        }

        .model-tab {
            padding: 15px 25px; /* 从12px 20px增加 */
            cursor: pointer;
            color: var(--text-secondary);
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
            font-weight: 600;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt));
            margin-right: 5px;
            border-radius: 12px 12px 0 0;
            border: 1px solid var(--border-light);
            backdrop-filter: blur(10px);
            font-size: 16px; /* 增大选项卡字体 */
        }

        .model-tab::before {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-cyan), var(--accent-purple));
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .model-tab:hover {
            color: var(--text-primary);
            background: linear-gradient(135deg, var(--bg-card-alt), var(--bg-card));
            border-color: var(--accent-cyan);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2);
        }

        .model-tab:hover::before {
            transform: translateX(0);
        }

        .model-tab.active {
            color: var(--text-primary);
            background: linear-gradient(135deg, var(--bg-card-alt), var(--bg-card));
            border-color: var(--accent-cyan);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
        }

        .model-tab.active::before {
            transform: translateX(0);
        }

        .model-visualization-area {
            flex: 1;
            position: relative;
            overflow: auto;
            max-height: 520px;
        }

        /* 模型容器优化 */
        .model-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.5s ease, visibility 0.5s ease;
            padding: 0 20px;
            background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt));
            border-radius: 15px;
        }

        .model-container.active {
            opacity: 1;
            visibility: visible;
        }

        .model-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
            padding: 15px;
            background: linear-gradient(135deg, var(--bg-card-alt), var(--bg-card));
            border-radius: 12px;
            border: 1px solid var(--border-light);
        }

        .model-performance {
            padding: 20px;
            background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt));
            border-radius: 12px;
            border: 1px solid var(--border-light);
            box-shadow: var(--card-glow);
            backdrop-filter: blur(10px);
        }

        .performance-title {
            margin-bottom: 15px;
            font-weight: 600;
            color: var(--accent-cyan);
            font-size: 20px; /* 从18px增加到20px */
            text-shadow: 0 0 10px currentColor;
        }

        .model-header {
            display: flex;
            flex-direction: column;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
        }

        .model-header h3 {
            margin: 0 0 10px 0;
        }

        .model-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 10px;
        }

        .stat-item {
            padding: 10px 15px;
            background-color: var(--bg-card);
            border-radius: 8px;
            border: 1px solid var(--border-light);
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: transform 0.2s, box-shadow 0.2s;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .stat-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
            border-color: var(--accent-blue);
        }

        .stat-label {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 5px;
        }

        .stat-value {
            font-size: 16px;
            font-weight: 600;
            color: var(--accent-blue);
        }

        .model-structure {
            flex: 1;
            display: flex;
            position: relative;
            overflow: hidden;
            margin-bottom: 15px;
            min-height: 200px;
            border: 1px solid rgba(0, 212, 255, 0.1);
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
        }

        .model-layers {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            min-height: 150px;
            position: relative;
        }

        .layer {
            background: rgba(26, 26, 46, 0.7);
            border-radius: 8px;
            padding: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-width: 80px;
            min-height: 80px;
            position: relative;
            border: 1px solid rgba(0, 212, 255, 0.3);
            transition: all 0.3s ease;
            z-index: 5;
        }

        .layer:hover {
            transform: scale(1.05);
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
            z-index: 10;
        }

        .layer-icon {
            font-size: 20px;
            margin-bottom: 8px;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .layer-name {
            font-size: 12px;
            font-weight: 600;
        }

        .input-layer {
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.8), rgba(26, 26, 46, 0.6));
        }

        .input-layer .layer-icon {
            background: rgba(0, 212, 255, 0.2);
            color: var(--accent-blue);
        }

        .conv-layer {
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.8), rgba(26, 26, 46, 0.6));
        }

        .conv-layer .layer-icon {
            background: rgba(255, 8, 68, 0.2);
            color: var(--accent-pink);
        }

        .pool-layer {
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.8), rgba(26, 26, 46, 0.6));
        }

        .pool-layer .layer-icon {
            background: rgba(247, 147, 30, 0.2);
            color: var(--accent-yellow);
        }

        .fc-layer {
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.8), rgba(26, 26, 46, 0.6));
        }

        .fc-layer .layer-icon {
            background: rgba(46, 204, 113, 0.2);
            color: var(--accent-green);
        }

        .output-layer {
            background: linear-gradient(135deg, rgba(26, 26, 46, 0.8), rgba(26, 26, 46, 0.6));
        }

        .output-layer .layer-icon {
            background: rgba(134, 120, 255, 0.2);
            color: var(--accent-purple);
        }

        .gcn-layer .layer-icon {
            background: rgba(0, 212, 255, 0.2);
            color: var(--accent-blue);
        }

        .model-data-flow {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .model-details {
            padding: 15px;
            border-radius: 8px;
            background-color: var(--bg-card-alt);
            border: 1px solid var(--card-border);
            box-shadow: var(--card-glow);
        }
        
        .detail-title {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            color: var(--accent-blue);
            font-weight: 600;
            font-size: 16px;
        }
        
        .detail-title i {
            margin-right: 10px;
            color: var(--accent-blue);
        }
        
        .detail-content p {
            margin-bottom: 15px;
            line-height: 1.6;
            color: var(--text-primary);
            font-size: 14px;
        }
        
        .model-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .metric-item {
            padding: 15px;
            background-color: var(--bg-card);
            border-radius: 8px;
            text-align: center;
            border: 1px solid var(--border-light);
            transition: transform 0.2s;
        }
        
        .metric-item:hover {
            transform: translateY(-2px);
            border-color: var(--accent-blue);
        }
        
        .metric-label {
            font-size: 11px;
            color: var(--text-secondary);
            margin-bottom: 3px;
        }
        
        .metric-value {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* 数据流动画 */
        .flow-particle {
            position: absolute;
            width: 5px;
            height: 5px;
            border-radius: 50%;
            background: var(--accent-blue);
            box-shadow: 0 0 8px var(--accent-blue);
            z-index: 2;
            opacity: 0;
        }

        /* GAN模型特有样式 */
        .gan-structure {
            display: grid;
            grid-template-columns: 1fr 60px 1fr;
            gap: 10px;
            padding: 15px;
        }

        .gan-generator, .gan-discriminator {
            display: flex;
            flex-direction: column;
            border-radius: 8px;
            border: 1px solid rgba(0, 212, 255, 0.2);
            padding: 10px;
            background: rgba(0, 0, 0, 0.2);
        }

        .gan-title {
            text-align: center;
            font-weight: 600;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
            color: var(--text-primary);
        }

        .gan-layers {
            display: flex;
            flex-direction: column;
            gap: 15px;
            flex: 1;
            justify-content: space-around;
        }

        .gan-flow {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .flow-arrows {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .flow-arrows::before {
            content: "";
            position: absolute;
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, var(--accent-pink), var(--accent-blue));
            transform: translateY(-50%);
        }

        .flow-arrows::after {
            content: "";
            position: absolute;
            top: 50%;
            right: 5px;
            width: 0;
            height: 0;
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
            border-left: 8px solid var(--accent-blue);
            transform: translateY(-50%);
        }

        /* GCN模型特有样式 */
        .gcn-structure {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .gcn-graph {
            border-radius: 8px;
            border: 1px solid rgba(0, 212, 255, 0.2);
            background: rgba(0, 0, 0, 0.2);
            position: relative;
            min-height: 200px;
            overflow: hidden;
        }

        .gcn-nodes, .gcn-edges {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .gcn-layers {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            height: 100%;
        }

        /* DQN模型特有样式 */
        .dqn-structure {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .dqn-components {
            display: grid;
            grid-template-columns: 1fr 1.5fr 1fr;
            gap: 20px;
        }

        .dqn-environment, .dqn-agent, .dqn-memory {
            border-radius: 8px;
            border: 1px solid rgba(0, 212, 255, 0.2);
            background: rgba(0, 0, 0, 0.2);
            padding: 10px;
        }

        .dqn-title {
            text-align: center;
            font-weight: 600;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
            color: var(--text-primary);
        }

        .dqn-env-visual, .dqn-memory-visual {
            min-height: 100px;
            position: relative;
        }

        .dqn-network {
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: center;
            justify-content: space-around;
            min-height: 150px;
        }

        .dqn-flow {
            height: 30px;
            position: relative;
        }

        /* BERT模型特有样式 */
        .bert-structure {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .bert-layers {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 100%;
            gap: 20px;
        }

        .bert-encoder {
            border-radius: 8px;
            border: 1px solid rgba(0, 212, 255, 0.2);
            background: rgba(0, 0, 0, 0.2);
            padding: 10px;
        }

        .bert-title {
            text-align: center;
            font-weight: 600;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
            color: var(--text-primary);
        }

        .bert-blocks {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .bert-block {
            background: rgba(26, 26, 46, 0.6);
            border-radius: 5px;
            padding: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid rgba(0, 212, 255, 0.1);
        }

        .bert-attention {
            border-radius: 8px;
            border: 1px solid rgba(0, 212, 255, 0.2);
            background: rgba(0, 0, 0, 0.2);
            padding: 10px;
            display: flex;
            flex-direction: column;
        }

        .attention-map {
            flex: 1;
            min-height: 150px;
            position: relative;
            margin-top: 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            overflow: hidden;
        }

        /* 工具提示 */
        [data-tooltip] {
            position: relative;
        }

        [data-tooltip]:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            white-space: nowrap;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            margin-bottom: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(0, 212, 255, 0.3);
        }

        /* 响应式调整 */
        @media (max-width: 1200px) {
            .model-structure {
                flex-direction: column;
                padding: 10px;
            }
            
            .gan-structure {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto;
            }
            
            .gcn-structure, .bert-structure {
                grid-template-columns: 1fr;
            }
            
            .dqn-components {
                grid-template-columns: 1fr;
            }
            
            .model-details {
                flex-direction: column;
            }
        }

        .models-visualization {
            grid-area: models-visualization;
            background-color: var(--bg-card);
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            padding: 0;
            border: 1px solid var(--border-light);
            box-shadow: 0 4px 15px var(--shadow-color);
            overflow: hidden;
            margin-bottom: 20px;
        }

        .model-container {
            display: none;
            padding: 20px;
            animation: fadeIn 0.5s ease;
            background-color: #0086d4;
        }

        .model-container.active {
            display: flex;
            flex-direction: column;
        }

        .model-tabs {
            display: flex;
            flex-wrap: wrap;
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
            padding: 0 5px;
        }

        .model-tab {
            padding: 15px 25px; /* 从12px 20px增加 */
            cursor: pointer;
            font-weight: 600;
            color: var(--text-secondary);
            transition: all 0.3s ease;
            position: relative;
        }

        .model-tab:hover {
            color: var(--accent-blue);
            background-color: rgba(0, 120, 212, 0.05);
        }

        .model-tab.active {
            color: var(--accent-blue);
            background-color: var(--bg-card);
        }

        .model-tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: var(--accent-blue);
        }

        .model-showcase {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .model-visual-effect,
        .model-application {
            padding: 20px;
            border-radius: 10px;
            background-color: var(--bg-light);
            border: 1px solid var(--border-light);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .effect-title,
        .application-title {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            color: var(--accent-blue);
            font-weight: 600;
            font-size: 16px;
        }
        
        .effect-title i,
        .application-title i {
            margin-right: 10px;
            color: var(--accent-blue);
        }
        
        .application-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
        }
        
        .application-item {
            display: flex;
            padding: 15px;
            background-color: var(--bg-card);
            border-radius: 8px;
            border: 1px solid var(--border-light);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .application-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
        }
        
        .application-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            background-color: rgba(0, 120, 212, 0.1);
            border-radius: 50%;
            margin-right: 15px;
            color: var(--accent-blue);
        }
        
        /* 图表容器样式调整 */
        .echarts-container {
            background-color: var(--bg-card) !important;
            border-radius: 10px !important;
            border: 1px solid var(--border-light) !important;
            overflow: hidden !important;
        }
        
        /* 强制修改图表文字颜色 */
        .echarts-for-react div,
        .echarts-container div,
        .performance-chart div {
            color: var(--text-primary) !important;
        }
        
        /* 强制所有svg文本使用深色 */
        svg text {
            fill: var(--text-primary) !important;
        }
        
        /* 强制图表背景为白色 */
        .performance-chart canvas {
            background-color: var(--bg-card) !important;
        }

        .performance-chart {
            width: 100%;
            height: 200px;
            margin-bottom: 10px;
            background-color: rgba(0, 134, 212, 0.3);
            border-radius: 8px;
            padding: 8px;
            box-shadow: var(--card-glow);
            border: var(--card-border);
        }

        /* 模型内容样式 */
        .model-content {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding: 10px;
            background: rgba(0, 134, 212, 0.3);
            border-radius: 8px;
        }
        
        .model-use-cases {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 10px;
        }
        
        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 8px;
            margin-top: 10px;
        }
        
        .metric {
            padding: 6px;
            text-align: center;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            border: var(--card-border);
        }
        
        .model-description {
            padding: 15px;
            background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt));
            border-radius: 8px;
            border: var(--card-border);
            box-shadow: var(--card-glow);
        }
        
        .model-description p {
            margin: 0;
            color: var(--text-primary);
            line-height: 1.5;
        }

        .model-performance {
            padding: 12px;
            background: rgba(0, 134, 212, 0.2);
            border-radius: 10px;
            border: var(--card-border);
            box-shadow: var(--card-glow);
        }

        .performance-title {
            margin-bottom: 10px;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 15px;
        }

        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 8px;
            margin-top: 10px;
        }

        .metric {
            padding: 6px;
            text-align: center;
            background-color: rgba(0, 134, 212, 0.3);
            border-radius: 6px;
            border: var(--card-border);
        }

        /* 科技感文字效果 */
        .text-danger {
            color: var(--accent-pink) !important;
            font-weight: 600;
            text-shadow: 0 0 8px currentColor;
        }

        .text-success {
            color: var(--accent-green) !important;
            font-weight: 600;
            text-shadow: 0 0 8px currentColor;
        }

        .text-primary {
            color: var(--accent-blue) !important;
            font-weight: 600;
            text-shadow: 0 0 8px currentColor;
        }

        /* 模型选项卡优化 */
        .model-tab {
            padding: 15px 25px; /* 从12px 20px增加 */
            cursor: pointer;
            color: var(--text-secondary);
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
            font-weight: 600;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt));
            margin-right: 5px;
            border-radius: 12px 12px 0 0;
            border: 1px solid var(--border-light);
            backdrop-filter: blur(10px);
            font-size: 16px; /* 增大选项卡字体 */
        }

        .model-tab::before {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, var(--accent-cyan), var(--accent-purple));
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .model-tab:hover {
            color: var(--text-primary);
            background: linear-gradient(135deg, var(--bg-card-alt), var(--bg-card));
            border-color: var(--accent-cyan);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.2);
        }

        .model-tab:hover::before {
            transform: translateX(0);
        }

        .model-tab.active {
            color: var(--text-primary);
            background: linear-gradient(135deg, var(--bg-card-alt), var(--bg-card));
            border-color: var(--accent-cyan);
            box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
        }

        .model-tab.active::before {
            transform: translateX(0);
        }

        /* 表格样式优化 - 增大字体和间距 */
        .table {
            width: 100%;
            max-width: 100%;
            table-layout: fixed;
            border-color: var(--border-light);
            background: var(--bg-card);
            color: var(--text-primary);
            font-size: 15px; /* 增大表格字体 */
        }

        .table td, .table th {
            padding: 12px 10px; /* 从8px增加到12px */
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            border-color: var(--border-light);
            background: transparent;
            font-size: 15px; /* 增大字体 */
        }

        .table th {
            background: linear-gradient(135deg, var(--bg-card-alt), var(--bg-card));
            color: var(--text-primary);
            font-weight: 600;
            text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
            font-size: 16px; /* 表头字体更大 */
        }

        .table-striped > tbody > tr:nth-of-type(odd) {
            background: linear-gradient(90deg, rgba(50, 117, 237, 0.08), rgba(124, 94, 255, 0.05));
        }

        .table-light {
            background: linear-gradient(135deg, var(--bg-card-alt), var(--bg-card));
            color: var(--text-primary);
        }

        .table-bordered {
            border-color: var(--border-light);
        }

        .table-bordered > :not(caption) > * > * {
            border-color: var(--border-light);
        }

        /* 徽章样式 */
        .badge {
            font-weight: 600;
            padding: 6px 12px; /* 从4px 8px增加 */
            background: linear-gradient(135deg, var(--accent-green), var(--accent-cyan));
            color: var(--text-primary);
            box-shadow: 0 2px 8px rgba(0, 255, 136, 0.3);
            border-radius: 6px;
            text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
            font-size: 14px; /* 增大徽章字体 */
        }

        /* 数字计数器效果 */
        .digital-counter {
            font-family: 'Share Tech Mono', monospace;
            letter-spacing: 2px;
            color: var(--accent-cyan);
            text-shadow: 0 0 10px currentColor;
        }

        /* 脉冲动画 */
        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(1.05); }
            100% { opacity: 1; transform: scale(1); }
        }

        /* 模型性能图片展示优化 */
        .model-performance-images {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: space-between;
            margin: 15px 0;
        }

        .performance-image {
            flex: 1;
            min-width: 300px;
            background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt));
            border-radius: 12px;
            padding: 15px;
            border: 1px solid var(--border-light);
            box-shadow: var(--card-glow);
            transition: all 0.3s ease;
        }

        .performance-image:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(0, 212, 255, 0.4);
        }

        .performance-image h4 {
            margin-bottom: 15px;
            color: var(--accent-cyan);
            font-weight: 600;
            text-align: center;
            text-shadow: 0 0 10px currentColor;
            font-size: 18px; /* 增大图片标题字体 */
        }

        .performance-image img {
            width: 100%;
            height: auto;
            border-radius: 8px;
            border: 1px solid var(--border-light);
        }

        /* 性能指标优化 */
        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .metric, .metric-item {
            padding: 12px;
            text-align: center;
            background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt));
            border-radius: 10px;
            border: 1px solid var(--border-light);
            color: var(--text-primary);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .metric:hover, .metric-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--card-glow);
            border-color: var(--accent-cyan);
        }

        .metric-value {
            font-size: 24px; /* 从20px增加到24px */
            font-weight: 700;
            color: var(--accent-green);
            margin-bottom: 5px;
            text-shadow: 0 0 10px currentColor;
        }

        .metric-label {
            font-size: 14px; /* 从12px增加到14px */
            color: var(--text-secondary);
            font-weight: 600;
        }

        /* CPU系统表格特别优化 */
        .cpu-system-item .table {
            font-size: 16px; /* CPU表格字体更大 */
            margin-bottom: 0;
        }

        .cpu-system-item .table td,
        .cpu-system-item .table th {
            padding: 15px 12px; /* 增大CPU表格单元格内边距 */
            font-size: 16px;
            line-height: 1.4;
        }

        .cpu-system-item .table th {
            font-size: 17px;
            font-weight: 700;
        }

        /* CPU系统标题优化 */
        .cpu-system-item h5 {
            font-size: 18px; /* 增大CPU项目标题 */
            margin: 0 0 15px 0;
            text-align: center;
            color: var(--text-primary);
            font-weight: 700;
            letter-spacing: 1px;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.4);
            background: linear-gradient(90deg, var(--accent-cyan), var(--accent-purple));
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        /* 存储容器内容优化 */
        #storage-container .card-content {
            padding: 20px;
        }

        /* 模型网格样式 */
        .models-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
            padding: 10px;
        }

        .algorithm-card {
            background: linear-gradient(145deg, rgba(15, 20, 25, 0.8), rgba(20, 30, 40, 0.8));
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 212, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .algorithm-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #00d4ff, #b967ff, #ff3d9a, #00d4ff);
            background-size: 300% 100%;
            animation: gradientFlow 3s ease-in-out infinite;
        }

        .algorithm-card:hover {
            transform: translateY(-8px);
            border-color: rgba(0, 212, 255, 0.6);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4), 0 0 30px rgba(0, 212, 255, 0.3);
        }

        .algorithm-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(145deg, rgba(50, 117, 237, 0.2), rgba(124, 94, 255, 0.2));
            border-radius: 50%;
            border: 2px solid rgba(50, 117, 237, 0.5);
        }

        .algorithm-icon i {
            font-size: 24px;
            color: #00d4ff;
            text-shadow: 0 0 10px currentColor;
        }

        .algorithm-title {
            font-size: 18px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 8px;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        .algorithm-desc {
            font-size: 14px;
            color: #a0a8b0;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .algorithm-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 15px;
            gap: 10px;
        }

        .algorithm-stats .stat {
            flex: 1;
            text-align: center;
        }

        .stat-label {
            display: block;
            font-size: 12px;
            color: #8090a0;
            margin-bottom: 4px;
        }

        .stat-value {
            display: block;
            font-size: 16px;
            font-weight: bold;
            color: #00d4ff;
            text-shadow: 0 0 8px currentColor;
        }

        .algorithm-features {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }

        .feature-tag {
            background: linear-gradient(45deg, rgba(185, 103, 255, 0.3), rgba(255, 61, 154, 0.3));
            color: #ffffff;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            border: 1px solid rgba(185, 103, 255, 0.5);
            backdrop-filter: blur(5px);
        }

        @keyframes gradientFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .cpu-system-item h5 {
            font-size: 18px;
            margin: 0 0 15px 0;
            text-align: center;
            color: #ffffff;
            font-weight: 700;
            letter-spacing: 1px;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.6);
            background: linear-gradient(90deg, var(--accent-cyan), var(--accent-purple));
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .cpu-system-item .table {
            font-size: 16px;
        }

        .cpu-system-item .table td,
        .cpu-system-item .table th {
            padding: 12px 8px;
            font-size: 16px;
            color: #ffffff;
            line-height: 1.5;
        }

        .cpu-system-item .badge {
            font-size: 13px;
            padding: 5px 8px;
            font-weight: bold;
            color: #ffffff;
        }

        /* 模型选择器样式 */
        .model-selector {
            margin-bottom: 20px;
            padding: 15px;
            background: linear-gradient(135deg, rgba(15, 20, 25, 0.8), rgba(20, 30, 40, 0.8));
            border-radius: 10px;
            border: 1px solid rgba(0, 212, 255, 0.3);
        }

        .model-selector label {
            color: #ffffff;
            font-size: 16px;
            font-weight: bold;
            margin-right: 15px;
            text-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
        }

        .model-selector select {
            background: linear-gradient(135deg, rgba(10, 15, 20, 0.9), rgba(15, 25, 35, 0.9));
            color: #ffffff;
            border: 2px solid rgba(0, 212, 255, 0.5);
            border-radius: 8px;
            padding: 10px 15px;
            font-size: 14px;
            min-width: 300px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .model-selector select:hover {
            border-color: rgba(0, 212, 255, 0.8);
            box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
        }

        .model-selector select:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }

        .model-selector option {
            background: rgba(10, 15, 20, 0.95);
            color: #ffffff;
            padding: 8px;
        }

        /* 模型详情卡片样式 */
        .model-detail-card {
            background: linear-gradient(145deg, rgba(15, 20, 25, 0.8), rgba(20, 30, 40, 0.8));
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 212, 255, 0.1);
            display: none;
        }

        .model-detail-card.active {
            display: block;
            animation: fadeInUp 0.5s ease;
        }

        .model-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
        }

        .model-icon {
            font-size: 40px;
            color: #00d4ff;
            margin-right: 20px;
            text-shadow: 0 0 15px rgba(0, 212, 255, 0.6);
        }

        .model-info h3 {
            color: #ffffff;
            font-size: 24px;
            margin: 0 0 8px 0;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.4);
        }

        .model-version {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            margin: 0 0 8px 0;
        }

        .model-desc {
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            margin: 0;
        }

        .model-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(0, 212, 255, 0.2);
        }

        .stat-item .stat-label {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 5px;
            display: block;
        }

        .stat-item .stat-value {
            color: #00d4ff;
            font-size: 20px;
            font-weight: bold;
            text-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
            display: block;
            margin-bottom: 8px;
        }

        .stat-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            overflow: hidden;
        }

        .stat-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4ff, #b967ff);
            border-radius: 3px;
            transition: width 0.8s ease;
        }

        .model-features {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
        }

        .feature-tag {
            background: linear-gradient(135deg, rgba(50, 117, 237, 0.2), rgba(124, 94, 255, 0.2));
            color: #ffffff;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            border: 1px solid rgba(0, 212, 255, 0.3);
            text-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        #deepseek-info-container th {
            padding: 8px 15px;
            border-color: rgba(204, 221, 238, 0.12);
            background-color: rgba(185, 103, 255, 0.2) !important;
            color: #ffffff !important;
            font-weight: bold;
            text-shadow: 0 0 8px rgba(185, 103, 255, 0.5);
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> 网络攻防博弈平台资源监控</h1>
            <span class="subtitle digital-counter">实时数据监控面板</span>
        </div>
        
        <!-- 网络攻防博弈可视化 -->
        <div id="cyber-visual-container" class="card">
            <div class="cyber-visual-header">
                <i class="fas fa-shield-alt"></i> 网络攻防博弈态势
            </div>
            <div class="cyber-visual-content">
                <div id="attack-defense-visual">
                    <!-- 顶部状态栏 -->
                    <div class="game-status-bar">
                        <div class="status-indicator" id="game-phase">
                            <i class="fas fa-play-circle"></i>
                            <span>博弈进行中</span>
                        </div>
                        <div class="status-indicator" id="battle-timer">
                            <i class="fas fa-stopwatch"></i>
                            <span id="battle-time">00:00:00</span>
                        </div>
                        <div class="status-indicator" id="equilibrium-status">
                            <i class="fas fa-balance-scale"></i>
                            <span>寻找平衡点</span>
                        </div>
                    </div>
                    
                    <!-- 主战场区域 -->
                    <div class="main-battlefield">
                        <div class="attacker-zone">
                            <div class="zone-label">攻击方</div>
                            <div class="attack-model-level">L<span id="attack-level">1</span></div>
                        </div>
                        
                        <div class="defender-zone">
                            <div class="zone-label">防御方</div>
                            <div class="defense-model-level">L<span id="defense-level">1</span></div>
                        </div>
                        
                        <div class="equilibrium-point">
                            Nash<br>均衡
                        </div>
                        
                        <div class="attack-beam"></div>
                        <div class="defense-shield"></div>
                        
                        <div class="floating-particles" id="particles"></div>
                    </div>
                    
                    <!-- 下方控制面板 -->
                    <div class="battle-controls">
                        <div class="strategy-panel">
                            <div class="panel-title">策略矩阵</div>
                            <div class="strategy-matrix">
                                <div class="matrix-row header-row">
                                    <div class="matrix-cell">策略</div>
                                    <div class="matrix-cell">防御强</div>
                                    <div class="matrix-cell">防御弱</div>
                                </div>
                                <div class="matrix-row">
                                    <div class="matrix-cell">攻击强</div>
                                    <div class="matrix-cell">(-2, 3)</div>
                                    <div class="matrix-cell">(4, -1)</div>
                                </div>
                                <div class="matrix-row">
                                    <div class="matrix-cell">攻击弱</div>
                                    <div class="matrix-cell">(1, 1)</div>
                                    <div class="matrix-cell">(-1, 2)</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="battle-stats">
                            <div class="stats-row">
                                <div class="stat-item">
                                    <div class="stat-value attack-value" id="attack-count">0</div>
                                    <div class="stat-label">攻击总数</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value defense-value" id="defense-count">0</div>
                                    <div class="stat-label">防御总数</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value attack-value" id="attack-success-rate">0%</div>
                                    <div class="stat-label">攻击成功率</div>
                                </div>
                            </div>
                            <div class="stats-row">
                                <div class="stat-item">
                                    <div class="stat-value" id="nash-distance">0.35</div>
                                    <div class="stat-label">Nash距离</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="system-status">正常</div>
                                    <div class="stat-label">系统状态</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value" id="game-rounds">1</div>
                                    <div class="stat-label">博弈轮数</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="battle-log-panel">
                            <div class="panel-title">
                                <i class="fas fa-history"></i> 博弈日志
                            </div>
                            <div class="battle-logs" id="battle-logs">
                                <!-- 日志将由JS动态添加 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 系统交互关系可视化 -->
        <div class="card" id="system-interaction-container">
            <div class="card-header">
                <i class="fas fa-project-diagram"></i> 网络攻防博弈架构
            </div>
            <div class="card-content">
                <div id="subsystem-interaction-visual">
                    <!-- 网络攻击链生成分系统 -->
                    <div class="system-node" id="attack-chain-system" style="top: 50%; left: 10%;" onclick="showAttackTasks()">
                        <div class="icon"><i class="fas fa-link"></i></div>
                        <div class="label">网络攻击链<br>生成分系统</div>
                        <div class="status-indicator"></div>
                    </div>

                    <!-- 网络威胁样本生成分系统 -->
                    <div class="system-node" id="threat-sample-system" style="top: 20%; left: 35%;" onclick="window.open('http://threat-sample-system.local', '_blank')">
                        <div class="icon"><i class="fas fa-virus"></i></div>
                        <div class="label">网络威胁样本<br>生成分系统</div>
                        <div class="status-indicator"></div>
                    </div>

                    <!-- 未知威胁监测分系统 -->
                    <div class="system-node" id="threat-detection-system" style="top: 20%; left: 65%;" onclick="window.open('http://threat-detection-system.local', '_blank')">
                        <div class="icon"><i class="fas fa-search"></i></div>
                        <div class="label">未知威胁<br>监测分系统</div>
                        <div class="status-indicator"></div>
                    </div>

                    <!-- 智能化训练运用分系统 -->
                    <div class="system-node" id="training-system" style="top: 80%; left: 50%;" onclick="window.open('http://training-system.local', '_blank')">
                        <div class="icon"><i class="fas fa-graduation-cap"></i></div>
                        <div class="label">智能化训练<br>运用分系统</div>
                        <div class="status-indicator"></div>
                    </div>

                    <!-- 网络攻防博弈分系统 -->
                    <div class="system-node" id="game-system" style="top: 80%; left: 10%;" onclick="window.open('http://game-system.local', '_blank')">
                        <div class="icon"><i class="fas fa-chess"></i></div>
                        <div class="label">网络攻防<br>博弈分系统</div>
                        <div class="status-indicator"></div>
                    </div>

                    <!-- 智能对抗博弈可视化分系统 -->
                    <div class="system-node" id="visualization-system" style="top: 5%; left: 50%;" onclick="window.open('http://visualization-system.local', '_blank')">
                        <div class="icon"><i class="fas fa-eye"></i></div>
                        <div class="label">智能对抗博弈<br>可视化分系统</div>
                        <div class="status-indicator"></div>
                    </div>

                    <!-- 连接线和数据流 -->
                    <div class="connection-line" style="top: 50%; left: 22%; width: 13%; transform: rotate(-20deg);"></div>
                    <div class="connection-line" style="top: 50%; left: 22%; width: 13%; transform: rotate(20deg);"></div>
                    <div class="connection-line" style="top: 30%; left: 47%; width: 18%;"></div>
                    <div class="connection-line" style="top: 70%; left: 35%; width: 15%; transform: rotate(45deg);"></div>
                    <div class="connection-line" style="top: 70%; left: 50%; width: 15%; transform: rotate(-45deg);"></div>
                    <div class="connection-line" style="top: 15%; left: 50%; width: 1%; height: 65%; transform: rotate(90deg);"></div>

                    <!-- 数据流动粒子 -->
                    <div class="data-particle" style="top: 50%; left: 22%; animation-delay: 0s;"></div>
                    <div class="data-particle" style="top: 30%; left: 47%; animation-delay: 1s;"></div>
                    <div class="data-particle" style="top: 70%; left: 35%; animation-delay: 2s;"></div>
                    <div class="data-particle" style="top: 15%; left: 50%; animation-delay: 3s;"></div>
                </div>
            </div>
        </div>
        
        <!-- 攻击任务对话框 -->
        <div class="attack-task-modal" id="attack-task-modal">
            <div class="attack-task-dialog">
                <div class="attack-task-header">
                    <h3><i class="fas fa-tasks"></i> 攻击任务列表</h3>
                    <button class="close-btn" onclick="closeAttackTasks()">&times;</button>
                </div>
                <div class="attack-task-content">
                    <div class="task-item">
                        <div class="task-title">SQL注入攻击链构建</div>
                        <div class="task-description">基于Web应用漏洞，构建多层次SQL注入攻击链，包含权限提升和数据窃取环节</div>
                        <div class="task-status active">进行中</div>
                    </div>
                    <div class="task-item">
                        <div class="task-title">APT攻击路径生成</div>
                        <div class="task-description">模拟高级持续性威胁，生成包含初始入侵、横向移动、权限维持的完整攻击路径</div>
                        <div class="task-status pending">等待中</div>
                    </div>
                    <div class="task-item">
                        <div class="task-title">DDoS攻击向量优化</div>
                        <div class="task-description">基于网络拓扑分析，优化分布式拒绝服务攻击的向量选择和流量分配</div>
                        <div class="task-status completed">已完成</div>
                    </div>
                    <div class="task-item">
                        <div class="task-title">恶意软件传播链分析</div>
                        <div class="task-description">构建恶意软件在网络中的传播路径，包含感染机制和扩散策略</div>
                        <div class="task-status active">进行中</div>
                    </div>
                    <div class="task-item">
                        <div class="task-title">社会工程学攻击序列</div>
                        <div class="task-description">设计多阶段社会工程学攻击，结合钓鱼邮件、电话诈骗和物理接触</div>
                        <div class="task-status pending">等待中</div>
                    </div>
                </div>
            </div>
        </div>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">召回率</span>
                                    <span class="stat-value">95.8%</span>
                                    <div class="stat-bar">
                                        <div class="stat-fill" style="width: 95.8%"></div>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">F1分数</span>
                                    <span class="stat-value">96.6%</span>
                                    <div class="stat-bar">
                                        <div class="stat-fill" style="width: 96.6%"></div>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">推理时间</span>
                                    <span class="stat-value">8.2ms</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">模型大小</span>
                                    <span class="stat-value">245MB</span>
                                </div>
                            </div>
                            <div class="model-features">
                                <span class="feature-tag">威胁识别</span>
                                <span class="feature-tag">视觉分析</span>
                                <span class="feature-tag">实时检测</span>
                                <span class="feature-tag">恶意代码检测</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- NVIDIA GPU服务器资源监控与任务占用 -->
        <div class="card" id="nvidia-gpu-container">
            <div class="card-header">
                <i class="fas fa-microchip"></i> NVIDIA GPU服务器资源监控与任务占用
            </div>
            <div class="card-content">
                <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; height: 100%;">
                    <!-- 左侧：GPU资源监控网格 -->
                    <div class="nvidia-grid" style="grid-template-columns: repeat(2, 1fr); grid-template-rows: repeat(2, 1fr);">
                        <div class="nvidia-item">
                            <h5>GPU资源池</h5>
                            <div class="chart-wrapper">
                                <div class="small-chart" id="nvidia-gpu-usage"></div>
                            </div>
                        </div>
                        <div class="nvidia-item">
                            <h5>CPU使用率</h5>
                            <div class="chart-wrapper">
                                <div class="small-chart" id="nvidia-cpu-usage"></div>
                            </div>
                        </div>
                        <div class="nvidia-item">
                            <h5>内存使用率</h5>
                            <div class="chart-wrapper">
                                <div class="small-chart" id="nvidia-memory-usage"></div>
                            </div>
                        </div>
                        <div class="nvidia-item">
                            <h5>存储使用率</h5>
                            <div class="chart-wrapper">
                                <div class="small-chart" id="nvidia-storage-usage"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：任务资源占用 -->
                    <div style="display: flex; flex-direction: column;">
                        <div style="background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt)); border-radius: 12px; border: 1px solid var(--border-light); padding: 15px; margin-bottom: 15px;">
                            <h5 style="text-align: center; color: var(--text-primary); margin-bottom: 15px; font-size: 16px;">任务资源占用</h5>
                            <div class="chart-wrapper" style="height: 150px;">
                                <div class="chart-container" id="task-resource-usage"></div>
                            </div>
                        </div>
                        <div id="task-table-container" style="flex: 1; overflow: auto; background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt)); border-radius: 12px; border: 1px solid var(--border-light); padding: 10px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 华为GPU服务器资源监控与DeepSeek大模型占用 -->
        <div class="card" id="huawei-gpu-container">
            <div class="card-header">
                <i class="fas fa-server"></i> 华为GPU服务器资源监控与DeepSeek大模型占用
            </div>
            <div class="card-content">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; height: 100%;">
                    <!-- 左侧：华为GPU资源监控 -->
                    <div style="display: flex; flex-direction: column;">
                        <div style="background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt)); border-radius: 12px; border: 1px solid var(--border-light); padding: 15px; flex: 1;">
                            <h5 style="text-align: center; color: var(--text-primary); margin-bottom: 15px; font-size: 16px;">华为GPU资源使用</h5>
                            <div class="chart-wrapper" style="height: 250px;">
                                <div class="chart-container" id="huawei-gpu-usage"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧：DeepSeek大模型资源占用 -->
                    <div style="display: flex; flex-direction: column;">
                        <div style="background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt)); border-radius: 12px; border: 1px solid var(--border-light); padding: 15px; margin-bottom: 15px;">
                            <h5 style="text-align: center; color: var(--text-primary); margin-bottom: 15px; font-size: 16px;">DeepSeek模型资源占用</h5>
                            <div class="chart-wrapper" style="height: 150px;">
                                <div class="chart-container" id="deepseek-resource-usage"></div>
                            </div>
                        </div>
                        <div id="deepseek-info-container" style="flex: 1; overflow: auto; background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt)); border-radius: 12px; border: 1px solid var(--border-light); padding: 10px;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 通用CPU服务器资源监控 -->
        <div class="card" id="cpu-servers-container">
            <div class="card-header">
                <i class="fas fa-network-wired"></i> 通用CPU服务器资源监控与分系统进程状态
            </div>
            <div class="card-content">
                <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px; height: 100%;">
                    <!-- 左侧：CPU服务器资源监控 -->
                    <div id="cpu-servers-wrapper" style="display: grid; grid-template-columns: repeat(2, 1fr); grid-template-rows: repeat(3, 1fr); gap: 15px;">
                        <div class="cpu-system-item" id="cpu-system1"></div>
                        <div class="cpu-system-item" id="cpu-system2"></div>
                        <div class="cpu-system-item" id="cpu-system3"></div>
                        <div class="cpu-system-item" id="cpu-system4"></div>
                        <div class="cpu-system-item" id="cpu-system5"></div>
                        <div class="cpu-system-item" style="background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt)); border-radius: 12px; border: 1px solid var(--border-light); padding: 15px; display: flex; align-items: center; justify-content: center; color: var(--text-secondary);">
                            <i class="fas fa-plus" style="font-size: 24px; opacity: 0.5;"></i>
                        </div>
                    </div>

                    <!-- 右侧：分系统进程监控 -->
                    <div style="display: flex; flex-direction: column;">
                        <div style="background: linear-gradient(135deg, var(--bg-card), var(--bg-card-alt)); border-radius: 12px; border: 1px solid var(--border-light); padding: 15px; flex: 1; overflow: hidden;">
                            <h5 style="text-align: center; color: var(--text-primary); margin-bottom: 15px; font-size: 16px;">分系统进程状态</h5>
                            <div id="subsystem-processes" style="height: 100%; overflow-y: auto;">
                                <div class="process-item" style="background: var(--bg-card-alt); border-radius: 8px; padding: 10px; margin-bottom: 8px; border: 1px solid var(--border-light);">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                        <span style="font-weight: 600; color: var(--text-primary); font-size: 14px;">攻击链生成系统</span>
                                        <span class="status-badge" style="background: var(--accent-green); color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px;">运行中</span>
                                    </div>
                                    <div style="font-size: 12px; color: var(--text-secondary);">
                                        <div>进程: attack_chain_service</div>
                                        <div>CPU: 15.2% | 内存: 2.1GB</div>
                                    </div>
                                </div>
                                <div class="process-item" style="background: var(--bg-card-alt); border-radius: 8px; padding: 10px; margin-bottom: 8px; border: 1px solid var(--border-light);">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                        <span style="font-weight: 600; color: var(--text-primary); font-size: 14px;">威胁样本生成</span>
                                        <span class="status-badge" style="background: var(--accent-green); color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px;">运行中</span>
                                    </div>
                                    <div style="font-size: 12px; color: var(--text-secondary);">
                                        <div>进程: threat_generator</div>
                                        <div>CPU: 28.7% | 内存: 4.3GB</div>
                                    </div>
                                </div>
                                <div class="process-item" style="background: var(--bg-card-alt); border-radius: 8px; padding: 10px; margin-bottom: 8px; border: 1px solid var(--border-light);">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                        <span style="font-weight: 600; color: var(--text-primary); font-size: 14px;">威胁监测系统</span>
                                        <span class="status-badge" style="background: var(--accent-green); color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px;">运行中</span>
                                    </div>
                                    <div style="font-size: 12px; color: var(--text-secondary);">
                                        <div>进程: threat_detector</div>
                                        <div>CPU: 22.1% | 内存: 3.8GB</div>
                                    </div>
                                </div>
                                <div class="process-item" style="background: var(--bg-card-alt); border-radius: 8px; padding: 10px; margin-bottom: 8px; border: 1px solid var(--border-light);">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                        <span style="font-weight: 600; color: var(--text-primary); font-size: 14px;">智能化训练系统</span>
                                        <span class="status-badge" style="background: var(--accent-yellow); color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px;">高负载</span>
                                    </div>
                                    <div style="font-size: 12px; color: var(--text-secondary);">
                                        <div>进程: training_service</div>
                                        <div>CPU: 78.5% | 内存: 12.1GB</div>
                                    </div>
                                </div>
                                <div class="process-item" style="background: var(--bg-card-alt); border-radius: 8px; padding: 10px; margin-bottom: 8px; border: 1px solid var(--border-light);">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                        <span style="font-weight: 600; color: var(--text-primary); font-size: 14px;">博弈分析系统</span>
                                        <span class="status-badge" style="background: var(--accent-green); color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px;">运行中</span>
                                    </div>
                                    <div style="font-size: 12px; color: var(--text-secondary);">
                                        <div>进程: game_analyzer</div>
                                        <div>CPU: 18.9% | 内存: 2.7GB</div>
                                    </div>
                                </div>
                                <div class="process-item" style="background: var(--bg-card-alt); border-radius: 8px; padding: 10px; margin-bottom: 8px; border: 1px solid var(--border-light);">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                        <span style="font-weight: 600; color: var(--text-primary); font-size: 14px;">可视化系统</span>
                                        <span class="status-badge" style="background: var(--accent-green); color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px;">运行中</span>
                                    </div>
                                    <div style="font-size: 12px; color: var(--text-secondary);">
                                        <div>进程: visualization_web</div>
                                        <div>CPU: 8.3% | 内存: 1.2GB</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据存储情况 -->
        <div class="card" id="storage-container">
            <div class="card-header">
                <i class="fas fa-database"></i> 数据存储使用情况
            </div>
            <div class="card-content">
                <div id="storage-wrapper">
                    <div class="storage-item" id="cephfs-storage"></div>
                    <div class="storage-item" id="mysql-storage"></div>
                    <div class="storage-item" id="graph-storage"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 我们的自定义脚本 -->
    <script>
        // 攻击任务对话框功能
        function showAttackTasks() {
            const modal = document.getElementById('attack-task-modal');
            modal.style.display = 'flex';
            // 添加淡入动画
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
        }

        function closeAttackTasks() {
            const modal = document.getElementById('attack-task-modal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // 点击模态框背景关闭
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('attack-task-modal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeAttackTasks();
                }
            });
        });


        document.addEventListener('DOMContentLoaded', function() {
            // 修改charts.js文件中的初始化方式以直接使用我们的容器
            
            // 初始化网络攻防博弈可视化
            function initCyberVisualization() {
                // 获取DOM元素
                const battleTimeEl = document.getElementById('battle-time');
                const attackLevelEl = document.getElementById('attack-level');
                const defenseLevelEl = document.getElementById('defense-level');
                const attackCountEl = document.getElementById('attack-count');
                const defenseCountEl = document.getElementById('defense-count');
                const attackSuccessRateEl = document.getElementById('attack-success-rate');
                const nashDistanceEl = document.getElementById('nash-distance');
                const systemStatusEl = document.getElementById('system-status');
                const gameRoundsEl = document.getElementById('game-rounds');
                const battleLogsEl = document.getElementById('battle-logs');
                const equilibriumStatusEl = document.getElementById('equilibrium-status').querySelector('span');
                
                // 初始化博弈数据
                const stats = {
                    attackCount: 0,
                    defenseCount: 0,
                    attackSuccess: 0,
                    attackLevel: 1,
                    defenseLevel: 1,
                    gameRounds: 1,
                    nashAttack: 0.6, // 理论Nash均衡点
                    nashDefense: 0.4,
                    attackStrategy: 0.65,
                    defenseStrategy: 0.45
                };
                
                // 初始化计时器
                let battleSeconds = 0;
                const phases = ["侦查阶段", "突破阶段", "控制阶段", "对抗阶段", "演化阶段"];
                let currentPhase = 0;
                
                // 更新博弈时间
                function updateBattleTime() {
                    const hours = Math.floor(battleSeconds / 3600).toString().padStart(2, '0');
                    const minutes = Math.floor((battleSeconds % 3600) / 60).toString().padStart(2, '0');
                    const seconds = (battleSeconds % 60).toString().padStart(2, '0');
                    battleTimeEl.textContent = `${hours}:${minutes}:${seconds}`;
                    battleSeconds++;
                    
                    // 每60秒更新阶段
                    if (battleSeconds % 60 === 0) {
                        currentPhase = (currentPhase + 1) % phases.length;
                        addBattleLog('system', `进入${phases[currentPhase]}`);
                        
                        // 随机升级攻击或防御模型
                        if (Math.random() > 0.5 && stats.attackLevel < 4) {
                            stats.attackLevel++;
                            attackLevelEl.textContent = stats.attackLevel;
                            addBattleLog('attack', `攻击方模型升级到L${stats.attackLevel}`);
                        } else if (stats.defenseLevel < 4) {
                            stats.defenseLevel++;
                            defenseLevelEl.textContent = stats.defenseLevel;
                            addBattleLog('defense', `防御方模型升级到L${stats.defenseLevel}`);
                        }
                    }
                }
                
                // 每秒更新一次时间
                setInterval(updateBattleTime, 1000);
                
                // 添加战况日志
                function addBattleLog(type, message) {
                    const logDiv = document.createElement('div');
                    logDiv.className = `log-message ${type}`;
                    
                    const now = new Date();
                    const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
                    
                    logDiv.innerHTML = `<span class="timestamp">${timeStr}</span> ${message}`;
                    
                    battleLogsEl.appendChild(logDiv);
                    battleLogsEl.scrollTop = battleLogsEl.scrollHeight;
                    
                    // 限制日志数量
                    if (battleLogsEl.children.length > 10) {
                        battleLogsEl.removeChild(battleLogsEl.firstChild);
                    }
                }
                
                // 初始化粒子效果
                function initParticles() {
                    const container = document.getElementById('particles');
                    
                    // 清空现有粒子
                    container.innerHTML = '';
                    
                    // 创建新粒子
                    for (let i = 0; i < 20; i++) {
                        const particle = document.createElement('div');
                        particle.className = `particle ${Math.random() > 0.5 ? 'attack' : 'defense'}`;
                        particle.style.left = Math.random() * 100 + '%';
                        particle.style.animationDelay = Math.random() * 8 + 's';
                        particle.style.animationDuration = (Math.random() * 3 + 5) + 's';
                        container.appendChild(particle);
                    }
                }
                
                // 计算Nash均衡距离
                function calculateNashDistance() {
                    const distance = Math.sqrt(
                        Math.pow(stats.attackStrategy - stats.nashAttack, 2) + 
                        Math.pow(stats.defenseStrategy - stats.nashDefense, 2)
                    );
                    return Math.min(distance, 1).toFixed(2);
                }
                
                // 更新均衡状态
                function updateEquilibriumStatus() {
                    const nashDistance = parseFloat(calculateNashDistance());
                    nashDistanceEl.textContent = nashDistance;
                    
                    if (nashDistance < 0.1) {
                        equilibriumStatusEl.textContent = '已达到平衡';
                    } else if (nashDistance < 0.3) {
                        equilibriumStatusEl.textContent = '接近平衡';
                    } else {
                        equilibriumStatusEl.textContent = '寻找平衡点';
                    }
                }
                
                // 博弈逻辑更新
                function updateGameLogic() {
                    // 更新策略
                    const learningRate = 0.1;
                    const randomness = 0.2;
                    
                    // 计算收益矩阵
                    let attackPayoff, defensePayoff;
                    
                    if (Math.random() < stats.attackStrategy && Math.random() < stats.defenseStrategy) {
                        // 攻击强 vs 防御强
                        attackPayoff = -2;
                        defensePayoff = 3;
                        addBattleLog('defense', '防御方成功拦截高强度攻击');
                    } else if (Math.random() < stats.attackStrategy) {
                        // 攻击强 vs 防御弱
                        attackPayoff = 4;
                        defensePayoff = -1;
                        addBattleLog('attack', '高强度攻击突破弱防御');
                        stats.attackSuccess++;
                    } else if (Math.random() < stats.defenseStrategy) {
                        // 攻击弱 vs 防御强
                        attackPayoff = 1;
                        defensePayoff = 1;
                        addBattleLog('defense', '防御方轻松拦截低强度攻击');
                    } else {
                        // 攻击弱 vs 防御弱
                        attackPayoff = -1;
                        defensePayoff = 2;
                        addBattleLog('defense', '低强度攻击被轻量防御拦截');
                    }
                    
                    stats.attackCount++;
                    stats.defenseCount++;
                    
                    // 策略学习更新
                    if (attackPayoff > 0) {
                        stats.attackStrategy = Math.min(1, stats.attackStrategy + learningRate * 0.1);
                    } else {
                        stats.attackStrategy = Math.max(0, stats.attackStrategy - learningRate * 0.05);
                    }
                    
                    if (defensePayoff > 0) {
                        stats.defenseStrategy = Math.min(1, stats.defenseStrategy + learningRate * 0.1);
                    } else {
                        stats.defenseStrategy = Math.max(0, stats.defenseStrategy - learningRate * 0.05);
                    }
                    
                    // 添加随机扰动
                    stats.attackStrategy += (Math.random() - 0.5) * randomness * 0.1;
                    stats.defenseStrategy += (Math.random() - 0.5) * randomness * 0.1;
                    
                    stats.attackStrategy = Math.max(0, Math.min(1, stats.attackStrategy));
                    stats.defenseStrategy = Math.max(0, Math.min(1, stats.defenseStrategy));
                    
                    // 更新统计数据
                    updateStats();
                    
                    // 更新轮数
                    stats.gameRounds++;
                    gameRoundsEl.textContent = stats.gameRounds;
                }
                
                // 更新统计数据
                function updateStats() {
                    attackCountEl.textContent = stats.attackCount;
                    defenseCountEl.textContent = stats.defenseCount;
                    
                    // 计算攻击成功率
                    const attackSuccessRate = stats.attackCount > 0 
                        ? Math.round((stats.attackSuccess / stats.attackCount) * 100) 
                        : 0;
                    attackSuccessRateEl.textContent = attackSuccessRate + '%';
                    
                    // 更新Nash距离
                    updateEquilibriumStatus();
                    
                    // 系统状态
                    if (attackSuccessRate > 60) {
                        systemStatusEl.textContent = '告警';
                        systemStatusEl.style.color = '#ff0844'; // var(--accent-pink)
                    } else if (attackSuccessRate > 30) {
                        systemStatusEl.textContent = '警戒';
                        systemStatusEl.style.color = '#f7931e'; // var(--accent-yellow)
                    } else {
                        systemStatusEl.textContent = '正常';
                        systemStatusEl.style.color = '#2ecc71'; // var(--accent-green)
                    }
                }
                
                // 初始化
                initParticles();
                addBattleLog('system', '博弈开始，进入侦查阶段');
                
                // 定期更新博弈逻辑
                setInterval(updateGameLogic, 3000);
            }
            
            // 立即初始化华为GPU图表，确保颜色正确
            const huaweiGpuChart = initHuaweiGpuChart();
            
            // 重写CPU服务器初始化方法
            const originalCpuSystemsInit = function() {
                // 为每个分系统初始化图表
                const cpuSystems = [
                    {
                        id: 'cpu-system1',
                        name: '网络威胁样本生成分系统',
                        cpu: 65,
                        memory: 55,
                        storage: 40,
                        total: {
                            cpu: '24核',
                            memory: '128GB',
                            storage: '2TB'
                        }
                    },
                    {
                        id: 'cpu-system2',
                        name: '网络攻击链生成分系统',
                        cpu: 70,
                        memory: 60,
                        storage: 45,
                        total: {
                            cpu: '32核',
                            memory: '256GB',
                            storage: '4TB'
                        }
                    },
                    {
                        id: 'cpu-system3',
                        name: '未知威胁监测分系统',
                        cpu: 75,
                        memory: 65,
                        storage: 50,
                        total: {
                            cpu: '48核',
                            memory: '384GB',
                            storage: '6TB'
                        }
                    },
                    {
                        id: 'cpu-system5',
                        name: '智能对抗博弈可视化分系统',
                        cpu: 80,
                        memory: 70,
                        storage: 55,
                        total: {
                            cpu: '24核',
                            memory: '128GB',
                            storage: '2TB'
                        }
                    },
                    {
                        id: 'cpu-system4',
                        name: '智能化训练运用分系统',
                        cpu: 60,
                        memory: 50,
                        storage: 35,
                        total: {
                            cpu: '36核',
                            memory: '192GB',
                            storage: '3TB'
                        }
                    }
                ];
                
                cpuSystems.forEach(system => {
                    const systemChart = echarts.init(document.getElementById(system.id));
                    const systemOption = {
                        title: {
                            text: system.name,
                            subtext: `总资源: ${system.total.cpu} | ${system.total.memory} | ${system.total.storage}`,
                            left: 'center',
                            textStyle: {
                                fontSize: 14,
                                fontWeight: 'bold'
                            },
                            subtextStyle: {
                                fontSize: 11
                            }
                        },
                        tooltip: {
                            trigger: 'axis',
                            formatter: '{b}: {c}%'
                        },
                        grid: {
                            left: '5%',
                            right: '5%',
                            bottom: '10%',
                            top: '33%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: ['CPU', '内存', '存储'],
                            axisLabel: {
                                fontSize: 12
                            }
                        },
                        yAxis: {
                            type: 'value',
                            max: 100,
                            axisLabel: {
                                formatter: '{value}%',
                                fontSize: 12
                            }
                        },
                        series: [
                            {
                                name: '使用率',
                                type: 'bar',
                                data: [
                                    {
                                        value: system.cpu,
                                        itemStyle: { color: '#3498db' }
                                    },
                                    {
                                        value: system.memory,
                                        itemStyle: { color: '#2ecc71' }
                                    },
                                    {
                                        value: system.storage,
                                        itemStyle: { color: '#e74c3c' }
                                    }
                                ],
                                barWidth: '40%',
                                label: {
                                    show: true,
                                    position: 'top',
                                    formatter: '{c}%',
                                    fontSize: 12
                                }
                            }
                        ]
                    };
                    systemChart.setOption(systemOption);
                });
            };
            
            // 重写存储初始化方法
            const originalStorageInit = function() {
                // 创建三个存储图表
                const storageTypes = [
                    {
                        id: 'cephfs-storage',
                        name: 'CephFS分布式文件存储',
                        used: 150,
                        available: 100,
                        total: '250TB',
                        color: ['#3498db', '#85c1e9']
                    },
                    {
                        id: 'mysql-storage',
                        name: 'MySQL数据库存储',
                        used: 50,
                        available: 30,
                        total: '80TB',
                        color: ['#2ecc71', '#82e0aa']
                    },
                    {
                        id: 'graph-storage',
                        name: '图数据库存储',
                        used: 30,
                        available: 20,
                        total: '50TB',
                        color: ['#e74c3c', '#f5b7b1']
                    }
                ];
                
                storageTypes.forEach(storage => {
                    const storageChart = echarts.init(document.getElementById(storage.id));
                    const percentage = Math.round((storage.used / (storage.used + storage.available)) * 100);
                    
                    const storageOption = {
                        title: {
                            text: storage.name,
                            subtext: `总容量: ${storage.total} | 使用率: ${percentage}%`,
                            left: 'center',
                            textStyle: {
                                fontSize: 14,
                                fontWeight: 'bold',
                                color: '#ffffff'
                            },
                            subtextStyle: {
                                fontSize: 11,
                                color: 'rgba(255, 255, 255, 0.8)'
                            }
                        },
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c}TB ({d}%)'
                        },
                        series: [
                            {
                                name: '存储空间',
                                type: 'pie',
                                radius: ['40%', '70%'],
                                center: ['50%', '55%'],
                                avoidLabelOverlap: true,
                                itemStyle: {
                                    borderRadius: 6,
                                    borderColor: '#fff',
                                    borderWidth: 1
                                },
                                label: {
                                    show: true,
                                    position: 'outside',
                                    formatter: function(params) {
                                        return `${params.name}\n${params.value}TB\n(${params.percent}%)`;
                                    },
                                    fontSize: 11,
                                    color: '#ffffff',
                                    fontWeight: 'bold'
                                },
                                emphasis: {
                                    label: {
                                        show: true,
                                        fontSize: 13,
                                        fontWeight: 'bold'
                                    }
                                },
                                data: [
                                    {
                                        value: storage.used,
                                        name: '已用空间',
                                        itemStyle: { color: storage.color[0] }
                                    },
                                    {
                                        value: storage.available,
                                        name: '可用空间',
                                        itemStyle: { color: storage.color[1] }
                                    }
                                ]
                            }
                        ]
                    };
                    storageChart.setOption(storageOption);
                });
            };
            
            // 调整ECharts字体大小
            const updateChartFontSizes = function() {
                // 获取所有图表实例
                const charts = [];
                document.querySelectorAll('[id$="-usage"]').forEach(element => {
                    const chart = echarts.getInstanceByDom(element);
                    if (chart) {
                        charts.push(chart);
                    }
                });
                
                // 修改字体大小
                charts.forEach(chart => {
                    const option = chart.getOption();
                    
                    // 标题字体
                    if (option.title) {
                        option.title.forEach(title => {
                            if (title.textStyle) {
                                title.textStyle.fontSize = 13;
                                title.textStyle.color = '#ffffff';
                            }
                            if (title.subtextStyle) {
                                title.subtextStyle.fontSize = 11;
                                title.subtextStyle.color = 'rgba(255, 255, 255, 0.7)';
                            }
                        });
                    }
                    
                    // 图例字体
                    if (option.legend) {
                        option.legend.forEach(legend => {
                            if (legend.textStyle) {
                                legend.textStyle.fontSize = 12;
                                legend.textStyle.color = '#ffffff';
                            }
                        });
                    }
                    
                    // 坐标轴字体
                    if (option.xAxis) {
                        option.xAxis.forEach(axis => {
                            if (axis.axisLabel) {
                                axis.axisLabel.fontSize = 12;
                                axis.axisLabel.color = '#ffffff';
                            }
                            if (axis.axisLine && axis.axisLine.lineStyle) {
                                axis.axisLine.lineStyle.color = 'rgba(255, 255, 255, 0.3)';
                            }
                        });
                    }
                    
                    if (option.yAxis) {
                        option.yAxis.forEach(axis => {
                            if (axis.axisLabel) {
                                axis.axisLabel.fontSize = 12;
                                axis.axisLabel.color = '#ffffff';
                            }
                            if (axis.splitLine && axis.splitLine.lineStyle) {
                                axis.splitLine.lineStyle.color = 'rgba(255, 255, 255, 0.1)';
                            }
                            if (axis.nameTextStyle) {
                                axis.nameTextStyle.color = '#ffffff';
                            }
                        });
                    }
                    
                    // 数据标签字体
                    if (option.series) {
                        option.series.forEach(series => {
                            if (series.label) {
                                series.label.fontSize = 12;
                                series.label.color = '#ffffff';
                            }
                            // 处理饼图或仪表盘的细节
                            if (series.detail) {
                                series.detail.fontSize = 14;
                                series.detail.color = '#ffffff';
                            }
                            if (series.data) {
                                series.data.forEach(item => {
                                    if (item.label) {
                                        item.label.fontSize = 12;
                                        item.label.color = '#ffffff';
                                    }
                                });
                            }
                        });
                    }
                    
                    chart.setOption(option);
                    chart.resize();
                });
            };
            
            // 将DeepSeek和Task表格移动到新位置并确保显示
            const moveExtras = function() {
                // 处理任务表格
                const taskTable = document.querySelector('.task-table');
                if (taskTable) {
                    document.getElementById('task-table-container').appendChild(taskTable);
                    taskTable.style.marginTop = '0';
                    // 确保表格可见
                    taskTable.style.display = 'table';
                }
                
                // 处理DeepSeek信息表格
                // 先清空容器，避免重复
                const deepseekInfoContainer = document.getElementById('deepseek-info-container');
                if (deepseekInfoContainer) {
                    deepseekInfoContainer.innerHTML = '';
                    const deepseekInfo = document.querySelector('.deepseek-info');
                    if (deepseekInfo) {
                        deepseekInfoContainer.appendChild(deepseekInfo);
                        deepseekInfo.style.marginTop = '0';
                        deepseekInfo.style.padding = '2px';
                    }
                }
            };
            
            // 添加任务资源初始化方法
            const initTaskResourceChart = function() {
                // 初始化NVIDIA GPU任务资源占用图表
                const taskResourceChart = echarts.init(document.getElementById('task-resource-usage'));
                
                const taskProcesses = [
                    { 
                        name: '图神经网络训练-1', 
                        gpu: 90, 
                        cpu: 65, 
                        memory: 75, 
                        storage: 40,
                        status: '运行中',
                        gpuValue: '14.4/16GB',
                        cpuValue: '31/48核',
                        memoryValue: '384/512GB',
                        storageValue: '4/10TB'
                    },
                    { 
                        name: '深度强化学习-2', 
                        gpu: 85, 
                        cpu: 60, 
                        memory: 70, 
                        storage: 35,
                        status: '运行中',
                        gpuValue: '13.6/16GB',
                        cpuValue: '29/48核',
                        memoryValue: '358/512GB',
                        storageValue: '3.5/10TB'
                    },
                    { 
                        name: '对抗样本生成-3', 
                        gpu: 75, 
                        cpu: 55, 
                        memory: 65, 
                        storage: 30,
                        status: '运行中',
                        gpuValue: '12/16GB',
                        cpuValue: '26/48核',
                        memoryValue: '333/512GB',
                        storageValue: '3/10TB'
                    },
                    { 
                        name: '模型推理服务-1', 
                        gpu: 60, 
                        cpu: 40, 
                        memory: 50, 
                        storage: 25,
                        status: '运行中',
                        gpuValue: '9.6/16GB',
                        cpuValue: '19/48核',
                        memoryValue: '256/512GB',
                        storageValue: '2.5/10TB'
                    },
                    { 
                        name: '特征提取-1', 
                        gpu: 50, 
                        cpu: 35, 
                        memory: 45, 
                        storage: 20,
                        status: '运行中',
                        gpuValue: '8/16GB',
                        cpuValue: '17/48核',
                        memoryValue: '230/512GB',
                        storageValue: '2/10TB'
                    }
                ];
                
                const taskResourceOption = {
                    title: {
                        text: 'GPU任务进程资源占用详情',
                        left: 'center',
                        textStyle: {
                            fontSize: 14
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        formatter: function(params) {
                            const process = taskProcesses[params[0].dataIndex];
                            return `${process.name}<br/>
                                    状态: <span style="color:#2ecc71">${process.status}</span><br/>
                                    GPU: ${process.gpu}% (${process.gpuValue})<br/>
                                    CPU: ${process.cpu}% (${process.cpuValue})<br/>
                                    内存: ${process.memory}% (${process.memoryValue})<br/>
                                    存储: ${process.storage}% (${process.storageValue})`;
                        }
                    },
                    grid: {
                        left: '15%',
                        right: '15%',
                        bottom: '3%',
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'value',
                        max: 100,
                        axisLabel: {
                            formatter: '{value}%',
                            fontSize: 12
                        }
                    },
                    yAxis: {
                        type: 'category',
                        data: taskProcesses.map(p => p.name),
                        axisLabel: {
                            width: 120,
                            overflow: 'truncate',
                            interval: 0,
                            fontSize: 12
                        }
                    },
                    series: [
                        {
                            name: 'GPU占用',
                            type: 'bar',
                            stack: 'total',
                            barMaxWidth: 30,
                            itemStyle: {
                                color: '#c0392b'
                            },
                            label: {
                                show: true,
                                position: 'right',
                                formatter: function(params) {
                                    const process = taskProcesses[params.dataIndex];
                                    return `${process.gpu}% (${process.gpuValue})`;
                                },
                                fontSize: 12
                            },
                            data: taskProcesses.map(p => p.gpu)
                        }
                    ]
                };
                taskResourceChart.setOption(taskResourceOption);
                
                // 添加任务进程详情表格
                const taskTableDiv = document.createElement('div');
                taskTableDiv.className = 'task-table';
                taskTableDiv.style.marginTop = '0';
                taskTableDiv.style.width = '100%';
                taskTableDiv.innerHTML = `
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>任务名称</th>
                                <th>GPU使用率</th>
                                <th>CPU使用率</th>
                                <th>内存使用率</th>
                                <th>存储使用率</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${taskProcesses.map(p => `
                                <tr>
                                    <td>${p.name}</td>
                                    <td>${p.gpu}% (${p.gpuValue})</td>
                                    <td>${p.cpu}% (${p.cpuValue})</td>
                                    <td>${p.memory}% (${p.memoryValue})</td>
                                    <td>${p.storage}% (${p.storageValue})</td>
                                    <td><span class="badge bg-success">${p.status}</span></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
                document.getElementById('task-table-container').appendChild(taskTableDiv);
            };
            
            // 修改DeepSeek图表初始化函数，避免表格重复
            function initDeepseekChart() {
                const deepseekChart = echarts.init(document.getElementById('deepseek-resource-usage'));
                
                const deepseekServers = [
                    {
                        name: '服务器1', 
                        gpu: 90, 
                        cpu: 70, 
                        memory: 65,
                        gpuValue: '14.4/16GB',
                        cpuValue: '17/24核',
                        memoryValue: '163/250GB',
                    },
                    {
                        name: '服务器2', 
                        gpu: 88, 
                        cpu: 75, 
                        memory: 70,
                        gpuValue: '14.1/16GB',
                        cpuValue: '18/24核',
                        memoryValue: '175/250GB',
                    },
                    {
                        name: '服务器3', 
                        gpu: 92, 
                        cpu: 80, 
                        memory: 75,
                        gpuValue: '14.7/16GB',
                        cpuValue: '19/24核',
                        memoryValue: '188/250GB',
                    },
                    {
                        name: '服务器4', 
                        gpu: 85, 
                        cpu: 85, 
                        memory: 80,
                        gpuValue: '13.6/16GB',
                        cpuValue: '20/24核',
                        memoryValue: '200/250GB',
                    }
                ];
                
                const deepseekOption = {
                    backgroundColor: 'transparent',
                    title: {
                        text: 'DeepSeek大模型分布式部署资源占用',
                        subtext: '部署在华为GPU服务器上',
                        left: 'center',
                        top: 10,
                        textStyle: {
                            fontSize: 14,
                            color: '#ffffff'
                        },
                        subtextStyle: {
                            color: 'rgba(255, 255, 255, 0.7)',
                            fontSize: 12
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        formatter: function(params) {
                            const seriesName = params[0].seriesName;
                            const serverIndex = params[0].dataIndex;
                            const server = deepseekServers[serverIndex];
                            
                            let value = '';
                            if (seriesName === 'GPU显存') {
                                value = server.gpuValue;
                            } else if (seriesName === 'CPU') {
                                value = server.cpuValue;
                            } else if (seriesName === '内存') {
                                value = server.memoryValue;
                            }
                            
                            return `${server.name}<br/>${seriesName}: ${value}<br/>使用率: ${params[0].value}%`;
                        },
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        borderColor: 'rgba(0, 212, 255, 0.2)',
                        textStyle: {
                            color: '#ffffff'
                        }
                    },
                    legend: {
                        data: ['GPU显存', 'CPU', '内存'],
                        right: 10, 
                        top: 10,
                        textStyle: {
                            color: '#ffffff',
                            fontSize: 12
                        }
                    },
                    grid: {
                        left: '5%',
                        right: '8%',
                        bottom: '15%',
                        top: '35%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: ['服务器1', '服务器2', '服务器3', '服务器4'],
                        axisLine: {
                            lineStyle: {
                                color: 'rgba(255, 255, 255, 0.3)'
                            }
                        },
                        axisLabel: {
                            color: '#ffffff',
                            interval: 0
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '使用率 (%)',
                        min: 0,
                        max: 100,
                        interval: 20,
                        axisLabel: {
                            formatter: '{value}%',
                            color: '#ffffff'
                        },
                        splitLine: {
                            lineStyle: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        nameTextStyle: {
                            color: '#ffffff'
                        }
                    },
                    series: [
                        {
                            name: 'GPU显存',
                            type: 'bar',
                            data: deepseekServers.map(server => ({
                                value: server.gpu,
                            itemStyle: {
                                    color: '#ff6b6b'
                                },
                                label: {
                                    show: true,
                                    position: 'top',
                                    formatter: function(params) {
                                        return deepseekServers[params.dataIndex].gpuValue;
                                    },
                                    color: '#ffffff',
                                    fontSize: 11
                                }
                            })),
                            barWidth: '20%',
                            barGap: '10%'
                        },
                        {
                            name: 'CPU',
                            type: 'bar',
                            data: deepseekServers.map(server => ({
                                value: server.cpu,
                            itemStyle: {
                                    color: '#4aa5ff'
                                },
                                label: {
                                    show: true,
                                    position: 'top',
                                    formatter: function(params) {
                                        return deepseekServers[params.dataIndex].cpuValue;
                                    },
                                    color: '#ffffff',
                                    fontSize: 11
                                }
                            })),
                            barWidth: '20%',
                            barGap: '10%'
                        },
                        {
                            name: '内存',
                            type: 'bar',
                            data: deepseekServers.map(server => ({
                                value: server.memory,
                            itemStyle: {
                                    color: '#5bddb5'
                                },
                                label: {
                                    show: true,
                                    position: 'top',
                                    formatter: function(params) {
                                        return deepseekServers[params.dataIndex].memoryValue;
                                    },
                                    color: '#ffffff',
                                    fontSize: 11
                                }
                            })),
                            barWidth: '20%',
                            barGap: '10%'
                        }
                    ]
                };
                deepseekChart.setOption(deepseekOption);
                
                // 添加DeepSeek图形指示器表格
                const deepseekInfoContainer = document.getElementById('deepseek-info-container');
                if (deepseekInfoContainer) {
                    // 检查是否已有表格，如果有则不再添加
                    if (!deepseekInfoContainer.querySelector('.deepseek-info')) {
                        const deepseekInfoDiv = document.createElement('div');
                        deepseekInfoDiv.className = 'deepseek-info';
                        deepseekInfoDiv.style.marginTop = '0';
                        deepseekInfoDiv.style.padding = '2px';
                        deepseekInfoDiv.innerHTML = `
                            <table class="table table-bordered table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th width="15%">服务器节点</th>
                                        <th width="20%">GPU显存使用率</th>
                                        <th width="20%">CPU使用率</th>
                                        <th width="20%">内存使用率</th>
                                        <th width="25%">大模型分片</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><strong>服务器1</strong></td>
                                        <td><span class="text-danger">90%</span> (14.4/16GB)</td>
                                        <td><span class="text-primary">70%</span> (17/24核)</td>
                                        <td><span class="text-success">65%</span> (163/250GB)</td>
                                        <td>推理引擎 + 模型分片1</td>
                                    </tr>
                                    <tr>
                                        <td><strong>服务器2</strong></td>
                                        <td><span class="text-danger">88%</span> (14.1/16GB)</td>
                                        <td><span class="text-primary">75%</span> (18/24核)</td>
                                        <td><span class="text-success">70%</span> (175/250GB)</td>
                                        <td>推理引擎 + 模型分片2</td>
                                    </tr>
                                    <tr>
                                        <td><strong>服务器3</strong></td>
                                        <td><span class="text-danger">92%</span> (14.7/16GB)</td>
                                        <td><span class="text-primary">80%</span> (19/24核)</td>
                                        <td><span class="text-success">75%</span> (188/250GB)</td>
                                        <td>推理引擎 + 模型分片3</td>
                                    </tr>
                                    <tr>
                                        <td><strong>服务器4</strong></td>
                                        <td><span class="text-danger">85%</span> (13.6/16GB)</td>
                                        <td><span class="text-primary">85%</span> (20/24核)</td>
                                        <td><span class="text-success">80%</span> (200/250GB)</td>
                                        <td>推理引擎 + 模型分片4</td>
                                    </tr>
                                </tbody>
                            </table>
                        `;
                        deepseekInfoContainer.appendChild(deepseekInfoDiv);
                    }
                }
            };
            
            // 修改setTimeout调用，确保任务图表初始化
            setTimeout(function() {
                // 初始化CPU系统和存储
                originalCpuSystemsInit();
                originalStorageInit();
                
                // 初始化DeepSeek图表
                initDeepseekChart();
                
                // 初始化华为GPU图表 - 确保清除旧实例并重新创建
                const existingHuaweiChart = echarts.getInstanceByDom(document.getElementById('huawei-gpu-usage'));
                if (existingHuaweiChart) {
                    existingHuaweiChart.dispose();
                }
                initHuaweiGpuChart();
                
                // 初始化NVIDIA GPU图表
                initNvidiaGPUCharts();
                
                // 初始化任务资源图表
                initTaskResourceChart();
                
                // 初始化网络攻防博弈可视化
                initCyberVisualization();
                
                // 初始化系统交互关系可视化
                initSystemInteraction();
                
                updateChartFontSizes();
                moveExtras();
                
                // 监听窗口大小变化
                window.addEventListener('resize', function() {
                    document.querySelectorAll('[id$="-usage"], [id^="cpu-system"], [id$="-storage"]').forEach(element => {
                        const chart = echarts.getInstanceByDom(element);
                        if (chart) chart.resize();
                    });
                });
                
                // 确保在页面完全加载后重新渲染华为GPU图表
                window.addEventListener('load', function() {
                    console.log("Window loaded - reinitializing Huawei GPU chart");
                    // 强制重新初始化华为GPU图表
                    const huaweiGpuElement = document.getElementById('huawei-gpu-usage');
                    if (huaweiGpuElement) {
                        const existingChart = echarts.getInstanceByDom(huaweiGpuElement);
                        if (existingChart) {
                            existingChart.dispose();
                        }
                        const newChart = initHuaweiGpuChart();
                        newChart.setOption({
                            series: [
                                {
                                    name: '已用',
                                    itemStyle: { color: '#ff3d9a' }
                                },
                                {
                                    name: '可用',
                                    itemStyle: { color: '#00d4ff' }
                                },
                                {
                                    name: '使用率',
                                    itemStyle: { color: '#b967ff' },
                                    lineStyle: { color: '#b967ff' }
                                }
                            ]
                        }, true);
                    }
                    
                    // 修复表格表头样式
                    setTimeout(function() {
                        const taskHeaders = document.querySelectorAll('#task-table-container th');
                        taskHeaders.forEach(header => {
                            header.style.backgroundColor = 'rgba(0, 212, 255, 0.2)';
                            header.style.color = '#ffffff';
                            header.style.fontWeight = 'bold';
                            header.style.textShadow = '0 0 8px rgba(0, 212, 255, 0.5)';
                        });
                        
                        const deepseekHeaders = document.querySelectorAll('#deepseek-info-container th');
                        deepseekHeaders.forEach(header => {
                            header.style.backgroundColor = 'rgba(185, 103, 255, 0.2)';
                            header.style.color = '#ffffff';
                            header.style.fontWeight = 'bold';
                            header.style.textShadow = '0 0 8px rgba(185, 103, 255, 0.5)';
                        });
                        
                        // 确保模型选择器默认显示CNN
                        const cnnCard = document.getElementById('model-cnn');
                        if (cnnCard) {
                            cnnCard.classList.add('active');
                        }
                    }, 1500);
                });
                
                // 强制让任务区域显示表格
                setTimeout(function() {
                    const taskResourceChart = echarts.getInstanceByDom(document.getElementById('task-resource-usage'));
                    if (taskResourceChart) {
                        taskResourceChart.resize();
                    }
                    // 确保任务表格正确显示
                    const taskTableContainer = document.getElementById('task-table-container');
                    if (taskTableContainer) {
                        taskTableContainer.style.display = 'block';
                        // 确保任务表格存在
                        if (!taskTableContainer.querySelector('.task-table')) {
                            initTaskResourceChart();
                        }
                    }
                    
                    // 确保DeepSeek表格正确显示
                    const deepseekInfoContainer = document.getElementById('deepseek-info-container');
                    if (deepseekInfoContainer) {
                        deepseekInfoContainer.style.display = 'block';
                        // 确保DeepSeek表格存在
                        if (!deepseekInfoContainer.querySelector('.deepseek-info')) {
                            initDeepseekChart();
                        }
                    }
                    
                    // 确保华为GPU表格正确显示
                    const huaweiGpuInfoContainer = document.getElementById('huawei-gpu-info-container');
                    if (huaweiGpuInfoContainer) {
                        huaweiGpuInfoContainer.style.display = 'block';
                        // 确保华为GPU表格存在
                        if (!huaweiGpuInfoContainer.querySelector('.huawei-gpu-info')) {
                            // 重新初始化华为GPU图表和表格
                            initHuaweiGpuChart();
                        }
                    }
                }, 300);
            }, 500);

            // 自定义华为GPU资源监控图表，替换原有的charts.js中的实现
            function initHuaweiGpuChart() {
                // 检查是否已经存在图表实例，如果存在则销毁
                const existingChart = echarts.getInstanceByDom(document.getElementById('huawei-gpu-usage'));
                if (existingChart) {
                    existingChart.dispose();
                }
                
                const huaweiGpuChart = echarts.init(document.getElementById('huawei-gpu-usage'));
                
                // 准备数据 - 四种资源类型的数据
                const resourceData = [
                    { name: 'GPU资源 (128GB)', used: 8.5, available: 1.5, usageRate: 85 },
                    { name: 'CPU资源 (96核)', used: 36, available: 12, usageRate: 75 },
                    { name: '内存资源 (1TB)', used: 72, available: 28, usageRate: 72 },
                    { name: '存储资源 (20TB)', used: 45, available: 55, usageRate: 45 }
                ];
                
                // 准备x轴类别数据
                const categories = resourceData.map(item => item.name);
                // 准备已用数据
                const usedData = resourceData.map(item => item.used);
                // 准备可用数据
                const availableData = resourceData.map(item => item.available);
                // 准备使用率数据
                const usageRateData = resourceData.map(item => item.usageRate);
                
                const huaweiOption = {
                    backgroundColor: 'transparent',
                    title: {
                        text: '华为GPU服务器资源池总览',
                        subtext: '4台服务器，8张GPU卡资源池',
                        left: 'center',
                        top: '2%',
                        textStyle: {
                            color: '#ffffff',
                            fontSize: 14,
                            fontWeight: 'bold'
                        },
                        subtextStyle: {
                            color: 'rgba(255, 255, 255, 0.7)',
                            fontSize: 12
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        formatter: function(params) {
                            const index = params[0].dataIndex;
                            const resource = resourceData[index];
                            let html = `${resource.name}<br/>`;
                            html += `<div style="display:flex;justify-content:space-between;">
                                      <span>已用</span>
                                      <span style="color:#0099FF;font-weight:bold;">${resource.used} TB</span>
                                     </div>`;
                            html += `<div style="display:flex;justify-content:space-between;">
                                      <span>可用</span>
                                      <span style="color:#66CCFF;font-weight:bold;">${resource.available} TB</span>
                                     </div>`;
                            html += `<div style="display:flex;justify-content:space-between;">
                                      <span>使用率</span>
                                      <span style="color:#0033FF;font-weight:bold;">${resource.usageRate}%</span>
                                     </div>`;
                            return html;
                        },
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        borderColor: 'rgba(0, 212, 255, 0.2)',
                        textStyle: {
                            color: '#ffffff'
                        }
                    },
                    legend: {
                        data: ['已用', '可用', '使用率'],
                        top: 65,
                        left: 'center',
                        orient: 'horizontal',
                        textStyle: {
                            color: '#ffffff',
                            fontSize: 12
                        },
                        icon: 'rect',
                        itemGap: 30,
                        itemWidth: 15,
                        itemHeight: 10,
                        padding: 5,
                        backgroundColor: 'rgba(0, 0, 0, 0.2)',
                        borderRadius: 4
                    },
                    grid: {
                        left: '3%',
                        right: '5%',
                        bottom: '8%',     // 增加底部边距，避免x轴标签被遮挡
                        top: '25%',       // 增加顶部边距，为标题和图例留出空间
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: categories,
                        axisLine: {
                            lineStyle: {
                                color: 'rgba(255, 255, 255, 0.3)'
                            }
                        },
                        axisLabel: {
                            color: '#ffffff',
                            fontSize: 10,
                            interval: 0,
                            rotate: 0,
                            margin: 10,    // 增加标签与轴的距离
                            formatter: function(value) {
                                // 限制标签长度并在适当位置换行
                                if (value.length > 10) {
                                    const parts = value.split(' ');
                                    return parts.join('\n');
                                }
                                return value;
                            }
                        }
                    },
                    yAxis: [
                        {
                            type: 'value',
                            name: '容量',
                            min: 0,
                            max: 100,
                            interval: 20,
                            axisLabel: {
                                formatter: '{value} TB',
                                color: '#ffffff'
                            },
                            splitLine: {
                                lineStyle: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            },
                            axisLine: {
                                lineStyle: {
                                    color: 'rgba(255, 255, 0.3)'
                                }
                            },
                            nameTextStyle: {
                                color: '#ffffff'
                            }
                        },
                        {
                            type: 'value',
                            name: '比率',
                            min: 0,
                            max: 100,
                            interval: 20,
                            axisLabel: {
                                formatter: '{value} %',
                                color: '#ffffff'
                            },
                            splitLine: {
                                show: false
                            },
                            axisLine: {
                                lineStyle: {
                                    color: 'rgba(255, 255, 0.3)'
                                }
                            },
                            nameTextStyle: {
                                color: '#ffffff'
                            }
                        }
                    ],
                    series: [
                        {
                            name: '已用',
                            type: 'bar',
                            emphasis: {
                                focus: 'series'
                            },
                            data: usedData,
                            itemStyle: {
                                color: '#0099FF'
                            },
                            barWidth: '30%',
                            barGap: '0%',
                            label: {
                                show: true,
                                position: 'top',
                                formatter: '{c} TB',
                                color: '#ffffff',
                                fontSize: 11,
                                fontWeight: 'bold'
                            }
                        },
                        {
                            name: '可用',
                            type: 'bar',
                            emphasis: {
                                focus: 'series'
                            },
                            data: availableData,
                            itemStyle: {
                                color: '#00d4ff'
                            },
                            barWidth: '30%',
                            barGap: '0%',
                            label: {
                                show: true,
                                position: 'top',
                                formatter: '{c} TB',
                                color: '#ffffff',
                                fontSize: 11,
                                fontWeight: 'bold'
                            }
                        },
                        {
                            name: '使用率',
                            type: 'line',
                            yAxisIndex: 1,
                            emphasis: {
                                focus: 'series'
                            },
                            data: usageRateData,
                            lineStyle: {
                                color: '#b967ff',
                                width: 3
                            },
                            itemStyle: {
                                color: '#b967ff'
                            },
                            symbol: 'circle',
                            symbolSize: 8,
                            label: {
                                show: true,
                                position: 'top',
                                formatter: '{c}%',
                                color: '#b967ff',
                                fontSize: 12,
                                fontWeight: 'bold'
                            }
                        }
                    ]
                };
                
                huaweiGpuChart.setOption(huaweiOption);
                
                // 添加华为GPU资源表格
                const huaweiGpuInfoContainer = document.getElementById('huawei-gpu-info-container');
                if (huaweiGpuInfoContainer) {
                    // 检查是否已有表格，如果有则不再添加
                    if (!huaweiGpuInfoContainer.querySelector('.huawei-gpu-info')) {
                        const huaweiGpuInfoDiv = document.createElement('div');
                        huaweiGpuInfoDiv.className = 'huawei-gpu-info';
                        huaweiGpuInfoDiv.innerHTML = `
                            <table class="table table-bordered table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th>资源类型</th>
                                        <th>总容量</th>
                                        <th>已用容量</th>
                                        <th>可用容量</th>
                                        <th>使用率</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><strong>GPU资源</strong></td>
                                        <td>10 TB</td>
                                        <td><span style="color:#0099FF;font-weight:bold;">8.5 TB</span></td>
                                        <td><span style="color:#66CCFF;">1.5 TB</span></td>
                                        <td><span style="color:#0033FF;font-weight:bold;">85%</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>CPU资源</strong></td>
                                        <td>48 TB</td>
                                        <td><span style="color:#0099FF;font-weight:bold;">36 TB</span></td>
                                        <td><span style="color:#66CCFF;">12 TB</span></td>
                                        <td><span style="color:#0033FF;font-weight:bold;">75%</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>内存资源</strong></td>
                                        <td>100 TB</td>
                                        <td><span style="color:#0099FF;font-weight:bold;">72 TB</span></td>
                                        <td><span style="color:#66CCFF;">28 TB</span></td>
                                        <td><span style="color:#0033FF;font-weight:bold;">72%</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>存储资源</strong></td>
                                        <td>100 TB</td>
                                        <td><span style="color:#0099FF;font-weight:bold;">45 TB</span></td>
                                        <td><span style="color:#66CCFF;">55 TB</span></td>
                                        <td><span style="color:#0033FF;font-weight:bold;">45%</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        `;
                        huaweiGpuInfoContainer.appendChild(huaweiGpuInfoDiv);
                    }
                }
                
                return huaweiGpuChart;
            }

            // 添加NVIDIA GPU图表初始化函数
            function initNvidiaGPUCharts() {
                // GPU资源池
                const nvidiaGpuChart = echarts.init(document.getElementById('nvidia-gpu-usage'));
                const nvidiaGpuOption = {
                    backgroundColor: 'transparent',
                    series: [{
                        type: 'gauge',
                        radius: '85%',
                        center: ['50%', '65%'], // 整体下移
                        startAngle: 180,
                        endAngle: 0,
                        min: 0,
                        max: 100,
                        splitNumber: 5,
                        axisLine: {
                            lineStyle: {
                                width: 15,
                                color: [
                                    [0.6, '#2ecc71'],
                                    [0.8, '#f7931e'],
                                    [1, '#ff0844']
                                ]
                            }
                        },
                        pointer: {
                            icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
                            length: '70%',
                            width: 6,
                            offsetCenter: [0, '-10%'],
                            itemStyle: {
                                color: 'auto'
                            }
                        },
                        axisTick: {
                            length: 12,
                            lineStyle: {
                                color: 'auto',
                                width: 1
                            }
                        },
                        splitLine: {
                            length: 18,
                            lineStyle: {
                                color: 'auto',
                                width: 2
                            }
                        },
                        axisLabel: {
                            color: '#ffffff',
                            fontSize: 12,
                            distance: -45, // 调整距离确保40%和60%显示
                            formatter: function(value) {
                                return value + '%';
                            }
                        },
                        title: {
                            offsetCenter: [0, '-25%'],
                            fontSize: 14,
                            color: '#ffffff'
                        },
                        detail: {
                            offsetCenter: [0, '35%'],
                            valueAnimation: true,
                            formatter: '{value}%',
                            color: '#ffffff',
                            fontSize: 26,
                            fontFamily: 'Share Tech Mono',
                            fontWeight: 'bold'
                        },
                        data: [{
                            value: 85,
                            name: 'GPU利用率'
                        }]
                    }]
                };
                nvidiaGpuChart.setOption(nvidiaGpuOption);
                
                // CPU使用率
                const nvidiaCpuChart = echarts.init(document.getElementById('nvidia-cpu-usage'));
                const nvidiaCpuOption = {
                    backgroundColor: 'transparent',
                    series: [{
                        type: 'gauge',
                        radius: '85%',
                        center: ['50%', '65%'], // 整体下移
                        startAngle: 180,
                        endAngle: 0,
                        min: 0,
                        max: 100,
                        splitNumber: 5,
                        axisLine: {
                            lineStyle: {
                                width: 15,
                                color: [
                                    [0.6, '#2ecc71'],
                                    [0.8, '#f7931e'],
                                    [1, '#ff0844']
                                ]
                            }
                        },
                        pointer: {
                            icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
                            length: '70%',
                            width: 6,
                            offsetCenter: [0, '-10%'],
                            itemStyle: {
                                color: 'auto'
                            }
                        },
                        axisTick: {
                            length: 12,
                            lineStyle: {
                                color: 'auto',
                                width: 1
                            }
                        },
                        splitLine: {
                            length: 18,
                            lineStyle: {
                                color: 'auto',
                                width: 2
                            }
                        },
                        axisLabel: {
                            color: '#ffffff',
                            fontSize: 12,
                            distance: -45,
                            formatter: function(value) {
                                return value + '%';
                            }
                        },
                        title: {
                            offsetCenter: [0, '-25%'],
                            fontSize: 14,
                            color: '#ffffff'
                        },
                        detail: {
                            offsetCenter: [0, '35%'],
                            valueAnimation: true,
                            formatter: '{value}%',
                            color: '#ffffff',
                            fontSize: 26,
                            fontFamily: 'Share Tech Mono',
                            fontWeight: 'bold'
                        },
                        data: [{
                            value: 65,
                            name: 'CPU利用率'
                        }]
                    }]
                };
                nvidiaCpuChart.setOption(nvidiaCpuOption);
                
                // 内存使用率
                const nvidiaMemoryChart = echarts.init(document.getElementById('nvidia-memory-usage'));
                const nvidiaMemoryOption = {
                    backgroundColor: 'transparent',
                    series: [{
                        type: 'gauge',
                        radius: '85%',
                        center: ['50%', '65%'], // 整体下移
                        startAngle: 180,
                        endAngle: 0,
                        min: 0,
                        max: 100,
                        splitNumber: 5,
                        axisLine: {
                            lineStyle: {
                                width: 15,
                                color: [
                                    [0.6, '#2ecc71'],
                                    [0.8, '#f7931e'],
                                    [1, '#ff0844']
                                ]
                            }
                        },
                        pointer: {
                            icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
                            length: '70%',
                            width: 6,
                            offsetCenter: [0, '-10%'],
                            itemStyle: {
                                color: 'auto'
                            }
                        },
                        axisTick: {
                            length: 12,
                            lineStyle: {
                                color: 'auto',
                                width: 1
                            }
                        },
                        splitLine: {
                            length: 18,
                            lineStyle: {
                                color: 'auto',
                                width: 2
                            }
                        },
                        axisLabel: {
                            color: '#ffffff',
                            fontSize: 12,
                            distance: -45,
                            formatter: function(value) {
                                return value + '%';
                            }
                        },
                        title: {
                            offsetCenter: [0, '-25%'],
                            fontSize: 14,
                            color: '#ffffff'
                        },
                        detail: {
                            offsetCenter: [0, '35%'],
                            valueAnimation: true,
                            formatter: '{value}%',
                            color: '#ffffff',
                            fontSize: 26,
                            fontFamily: 'Share Tech Mono',
                            fontWeight: 'bold'
                        },
                        data: [{
                            value: 72,
                            name: '内存利用率'
                        }]
                    }]
                };
                nvidiaMemoryChart.setOption(nvidiaMemoryOption);
                
                // 存储使用率
                const nvidiaStorageChart = echarts.init(document.getElementById('nvidia-storage-usage'));
                const nvidiaStorageOption = {
                    backgroundColor: 'transparent',
                    series: [{
                        type: 'gauge',
                        radius: '85%',
                        center: ['50%', '65%'], // 整体下移
                        startAngle: 180,
                        endAngle: 0,
                        min: 0,
                        max: 100,
                        splitNumber: 5,
                        axisLine: {
                            lineStyle: {
                                width: 15,
                                color: [
                                    [0.6, '#2ecc71'],
                                    [0.8, '#f7931e'],
                                    [1, '#ff0844']
                                ]
                            }
                        },
                        pointer: {
                            icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
                            length: '70%',
                            width: 6,
                            offsetCenter: [0, '-10%'],
                            itemStyle: {
                                color: 'auto'
                            }
                        },
                        axisTick: {
                            length: 12,
                            lineStyle: {
                                color: 'auto',
                                width: 1
                            }
                        },
                        splitLine: {
                            length: 18,
                            lineStyle: {
                                color: 'auto',
                                width: 2
                            }
                        },
                        axisLabel: {
                            color: '#ffffff',
                            fontSize: 12,
                            distance: -60,
                            formatter: function(value) {
                                return value + '%';
                            }
                        },
                        title: {
                            offsetCenter: [0, '-20%'],
                            fontSize: 14,
                            color: '#ffffff'
                        },
                        detail: {
                            offsetCenter: [0, '40%'],
                            valueAnimation: true,
                            formatter: '{value}%',
                            color: '#ffffff',
                            fontSize: 26,
                            fontFamily: 'Share Tech Mono',
                            fontWeight: 'bold'
                        },
                        data: [{
                            value: 55,
                            name: '存储使用率'
                        }]
                    }]
                };
                nvidiaStorageChart.setOption(nvidiaStorageOption);
            }

            // 初始化系统交互关系可视化
            function initSystemInteraction() {
                const container = document.getElementById('subsystem-interaction-visual');
                const cardContent = document.querySelector('#system-interaction-container .card-content');
                const cardHeader = document.querySelector('#system-interaction-container .card-header');
                
                // 获取卡片标题实际高度
                const headerHeight = cardHeader ? cardHeader.offsetHeight : 60;
                
                // 强制设置容器尺寸
                if (cardContent) {
                    cardContent.style.minHeight = '500px';
                    cardContent.style.padding = '10px'; // 减少内边距，提高空间利用率
                }
                
                // 清除可能存在的旧图表实例
                echarts.dispose(container);
                
                // 初始化前确保容器可见并且已经渲染
                if (!container.clientWidth || !container.clientHeight) {
                    container.style.width = '100%';
                    container.style.height = '480px';
                }
                
                // 获取容器的实际尺寸
                const containerWidth = container.clientWidth || window.innerWidth * 0.45; // 调整为半屏宽度
                const containerHeight = container.clientHeight || 480;
                
                console.log("Container dimensions:", containerWidth, "x", containerHeight);
                
                const chart = echarts.init(container, null, {
                    renderer: 'canvas',
                    width: containerWidth,
                    height: containerHeight
                });
                
                // 安全边距，确保足够的空间显示所有内容
                const margin = {
                    top: 50,     // 适当的顶部边距
                    right: 70,   // 减少右侧边距
                    bottom: 80,  // 增加底部边距确保博弈分系统显示
                    left: 70     // 减少左侧边距
                };
                
                // 计算可用的绘图区域
                const availableWidth = containerWidth - margin.left - margin.right;
                const availableHeight = containerHeight - margin.top - margin.bottom;
                
                // 基于可用区域计算布局
                const centerX = margin.left + availableWidth / 2;
                const centerY = margin.top + availableHeight / 2;
                
                // 计算布局半径，使用更合理的布局避免文字重叠
                const gridSpacingX = availableWidth * 0.35; // 适中的水平间距
                const gridSpacingY = availableHeight * 0.32; // 适中的垂直间距
                
                // 定义各子系统的位置和信息 - 重新优化布局
                const nodes = [
                    {
                        name: '网络威胁样本\n生成分系统',
                        x: centerX - gridSpacingX * 1.1,
                        y: centerY - gridSpacingY * 0.5,
                        category: 0,
                        symbolSize: Math.min(75, Math.max(40, availableWidth / 16)),
                        // 现代DNA双螺旋变异图标 - 代表威胁样本生成和变形
                        symbol: 'path://M512 64c-247.4 0-448 200.6-448 448s200.6 448 448 448 448-200.6 448-448-200.6-448-448-448zM288 352c17.7 0 32 14.3 32 32s-14.3 32-32 32-32-14.3-32-32 14.3-32 32-32zm448 256c0 17.7-14.3 32-32 32H512c-17.7 0-32-14.3-32-32v-64c0-17.7 14.3-32 32-32h192c17.7 0 32 14.3 32 32v64zm-192-64c17.7 0 32 14.3 32 32s-14.3 32-32 32-32-14.3-32-32 14.3-32 32-32zm192-192c0 17.7-14.3 32-32 32H320c-17.7 0-32-14.3-32-32v-64c0-17.7 14.3-32 32-32h384c17.7 0 32 14.3 32 32v64zM480 224c17.7 0 32 14.3 32 32s-14.3 32-32 32-32-14.3-32-32 14.3-32 32-32zm256 128c17.7 0 32 14.3 32 32s-14.3 32-32 32-32-14.3-32-32 14.3-32 32-32z',
                        label: { 
                            fontSize: Math.min(10, Math.max(7, availableWidth / 80)),
                            color: '#ffffff',
                            fontWeight: 'bold',
                            position: 'left',
                            distance: 8
                        },
                        value: '生成威胁样本，实施变形和隐匿',
                        itemStyle: {
                            color: {
                                type: 'radial',
                                x: 0.5,
                                y: 0.5,
                                r: 0.8,
                                colorStops: [{
                                    offset: 0, color: '#FF6B6B'
                                }, {
                                    offset: 1, color: '#D63031'
                                }]
                            },
                            shadowColor: '#FF6B6B',
                            shadowBlur: 25,
                            shadowOffsetX: 2,
                            shadowOffsetY: 2,
                            borderColor: '#FFF',
                            borderWidth: 2
                        }
                    },
                    {
                        name: '网络攻击链\n生成分系统',
                        x: centerX + gridSpacingX * 1.1,
                        y: centerY - gridSpacingY * 0.5,
                        category: 1,
                        symbolSize: Math.min(75, Math.max(40, availableWidth / 16)),
                        // 现代锁链网络图标 - 代表攻击链的构建和连接
                        symbol: 'path://M170 256c-17.7 0-32 14.3-32 32v96h64v-96c0-17.7-14.3-32-32-32zm384 0c-17.7 0-32 14.3-32 32v96h64v-96c0-17.7-14.3-32-32-32zM106 416c-17.7 0-32 14.3-32 32v320c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V448c0-17.7-14.3-32-32-32H106zm522 0c-17.7 0-32 14.3-32 32v320c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V448c0-17.7-14.3-32-32-32H628zM298 576c17.7 0 32 14.3 32 32s-14.3 32-32 32-32-14.3-32-32 14.3-32 32-32zm458 0c17.7 0 32 14.3 32 32s-14.3 32-32 32-32-14.3-32-32 14.3-32 32-32zM426 608h172v64H426v-64z',
                        label: { 
                            fontSize: Math.min(10, Math.max(7, availableWidth / 80)),
                            color: '#ffffff',
                            fontWeight: 'bold',
                            position: 'right',
                            distance: 8
                        },
                        value: '构建攻击链，实施攻击序列',
                        itemStyle: {
                            color: {
                                type: 'radial',
                                x: 0.5,
                                y: 0.5,
                                r: 0.8,
                                colorStops: [{
                                    offset: 0, color: '#FF9E40'
                                }, {
                                    offset: 1, color: '#E17055'
                                }]
                            },
                            shadowColor: '#FF9E40',
                            shadowBlur: 25,
                            shadowOffsetX: 2,
                            shadowOffsetY: 2,
                            borderColor: '#FFF',
                            borderWidth: 2
                        }
                    },
                    {
                        name: '未知威胁\n监测分系统',
                        x: centerX - gridSpacingX * 1.1,
                        y: centerY + gridSpacingY * 0.5,
                        category: 2,
                        symbolSize: Math.min(75, Math.max(40, availableWidth / 16)),
                        // 现代雷达扫描图标 - 代表威胁监测和扫描
                        symbol: 'path://M512 64C264.58 64 64 264.58 64 512s200.58 448 448 448 448-200.58 448-448S759.42 64 512 64zm0 128c35.35 0 64 28.65 64 64s-28.65 64-64 64-64-28.65-64-64 28.65-64 64-64zm256 320c0 17.67-14.33 32-32 32H288c-17.67 0-32-14.33-32-32s14.33-32 32-32h448c17.67 0 32 14.33 32 32zm-64-128c0 17.67-14.33 32-32 32H352c-17.67 0-32-14.33-32-32s14.33-32 32-32h320c17.67 0 32 14.33 32 32zm-32-128c0 17.67-14.33 32-32 32H384c-17.67 0-32-14.33-32-32s14.33-32 32-32h256c17.67 0 32 14.33 32 32z',
                        label: { 
                            fontSize: Math.min(10, Math.max(7, availableWidth / 80)),
                            color: '#ffffff',
                            fontWeight: 'bold',
                            position: 'left',
                            distance: 8
                        },
                        value: '未知威胁检测，异常行为分析',
                        itemStyle: {
                            color: {
                                type: 'radial',
                                x: 0.5,
                                y: 0.5,
                                r: 0.8,
                                colorStops: [{
                                    offset: 0, color: '#B967FF'
                                }, {
                                    offset: 1, color: '#8E44AD'
                                }]
                            },
                            shadowColor: '#B967FF',
                            shadowBlur: 25,
                            shadowOffsetX: 2,
                            shadowOffsetY: 2,
                            borderColor: '#FFF',
                            borderWidth: 2
                        }
                    },
                    {
                        name: '智能化训练\n运用分系统',
                        x: centerX + gridSpacingX * 1.1,
                        y: centerY + gridSpacingY * 0.5,
                        category: 3,
                        symbolSize: Math.min(75, Math.max(40, availableWidth / 16)),
                        // 现代AI大脑神经网络图标 - 代表智能训练和学习
                        symbol: 'path://M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zM368 288c26.5 0 48 21.5 48 48s-21.5 48-48 48-48-21.5-48-48 21.5-48 48-48zm288 0c26.5 0 48 21.5 48 48s-21.5 48-48 48-48-21.5-48-48 21.5-48 48-48zM288 416c26.5 0 48 21.5 48 48s-21.5 48-48 48-48-21.5-48-48 21.5-48 48-48zm224 0c26.5 0 48 21.5 48 48s-21.5 48-48 48-48-21.5-48-48 21.5-48 48-48zm224 0c26.5 0 48 21.5 48 48s-21.5 48-48 48-48-21.5-48-48 21.5-48 48-48zM368 544c26.5 0 48 21.5 48 48s-21.5 48-48 48-48-21.5-48-48 21.5-48 48-48zm288 0c26.5 0 48 21.5 48 48s-21.5 48-48 48-48-21.5-48-48 21.5-48 48-48z',
                        label: { 
                            fontSize: Math.min(10, Math.max(7, availableWidth / 80)),
                            color: '#ffffff',
                            fontWeight: 'bold',
                            position: 'right',
                            distance: 8
                        },
                        value: '智能化训练，模型部署',
                        itemStyle: {
                            color: {
                                type: 'radial',
                                x: 0.5,
                                y: 0.5,
                                r: 0.8,
                                colorStops: [{
                                    offset: 0, color: '#FFA726'
                                }, {
                                    offset: 1, color: '#FF8F00'
                                }]
                            },
                            shadowColor: '#FFA726',
                            shadowBlur: 25,
                            shadowOffsetX: 2,
                            shadowOffsetY: 2,
                            borderColor: '#FFF',
                            borderWidth: 2
                        }
                    },
                    {
                        name: '智能博弈对抗\n可视化分系统',
                        x: centerX,
                        y: centerY - gridSpacingY * 1.2,
                        category: 4,
                        symbolSize: Math.min(70, Math.max(35, availableWidth / 18)),
                        // 现代全息数据可视化图标 - 代表数据分析和可视化
                        symbol: 'path://M64 224h896v64H64v-64zm0 128h640v64H64v-64zm0 128h768v64H64v-64zm0 128h512v64H64v-64zM832 96c35.3 0 64 28.7 64 64v64c0 35.3-28.7 64-64 64s-64-28.7-64-64v-64c0-35.3 28.7-64 64-64zm0 320c35.3 0 64 28.7 64 64v64c0 35.3-28.7 64-64 64s-64-28.7-64-64v-64c0-35.3 28.7-64 64-64zm0 320c35.3 0 64 28.7 64 64v64c0 35.3-28.7 64-64 64s-64-28.7-64-64v-64c0-35.3 28.7-64 64-64z',
                        label: { 
                            fontSize: Math.min(10, Math.max(7, availableWidth / 80)),
                            color: '#ffffff',
                            fontWeight: 'bold',
                            position: 'top',
                            distance: 8
                        },
                        value: '博弈态势可视化',
                        itemStyle: {
                            color: {
                                type: 'radial',
                                x: 0.5,
                                y: 0.5,
                                r: 0.8,
                                colorStops: [{
                                    offset: 0, color: '#FF9BD0'
                                }, {
                                    offset: 1, color: '#E91E63'
                                }]
                            },
                            shadowColor: '#FF9BD0',
                            shadowBlur: 25,
                            shadowOffsetX: 2,
                            shadowOffsetY: 2,
                            borderColor: '#FFF',
                            borderWidth: 2
                        }
                    },
                    {
                        name: '网络攻防\n博弈分系统',
                        x: centerX,
                        y: centerY + gridSpacingY * 1.3,
                        category: 5,
                        symbolSize: Math.min(100, Math.max(55, availableWidth / 12)),
                        // 现代六边形蜂窝网络核心图标 - 代表分布式网络核心
                        symbol: 'path://M512 64l192 112v224L512 512 320 400V176L512 64zm0 128l-128 74v148l128 74 128-74V266L512 192zm-64 160c0-35.3 28.7-64 64-64s64 28.7 64 64-28.7 64-64 64-64-28.7-64-64zm256-128l128 74v148l-128 74v-74l64-37v-74l-64-37v-74zm-384 0v74l-64 37v74l64 37v74l-128-74V298l128-74zm512 320l128 74v148l-128 74-128-74v-148l128-74zm-512 0l128 74v148l-128 74-128-74V586l128-74zm256 0l128 74v148l-128 74-128-74V650l128-74z',
                        label: { 
                            fontSize: Math.min(11, Math.max(8, availableWidth / 70)),
                            color: '#ffffff',
                            fontWeight: 'bold',
                            position: 'bottom',
                            distance: 15
                        },
                        value: '博弈平台核心，算力支撑',
                        itemStyle: {
                            color: {
                                type: 'radial',
                                x: 0.5,
                                y: 0.5,
                                r: 0.8,
                                colorStops: [{
                                    offset: 0, color: '#3275ED'
                                }, {
                                    offset: 1, color: '#1E5BB8'
                                }]
                            },
                            shadowColor: '#3275ED',
                            shadowBlur: 30,
                            shadowOffsetX: 3,
                            shadowOffsetY: 3,
                            borderColor: '#FFF',
                            borderWidth: 3
                        }
                    }
                ];
                
                // 定义系统间的交互关系（边），使用直线连接
                const links = [
                    // === 与网络攻防博弈分系统相关的连线（全部保留） ===
                    // 网络攻防博弈分系统为各分系统提供算力支持和基础设施
                    {
                        source: '网络攻防\n博弈分系统',
                        target: '网络威胁样本\n生成分系统',
                        value: '算力支持',
                        lineStyle: { color: '#47c9d1', width: 3, curveness: 0 },
                        effect: { symbolSize: 6, period: 3.5, constantSpeed: 75 }
                    },
                    {
                        source: '网络攻防\n博弈分系统',
                        target: '未知威胁\n监测分系统',
                        value: '算力支持',
                        lineStyle: { color: '#47c9d1', width: 3, curveness: 0 },
                        effect: { symbolSize: 6, period: 3.5, constantSpeed: 75 }
                    },
                    {
                        source: '网络攻防\n博弈分系统',
                        target: '网络攻击链\n生成分系统',
                        value: '基础设施',
                        lineStyle: { color: '#47c9d1', width: 3, curveness: 0 },
                        effect: { symbolSize: 6, period: 3.5, constantSpeed: 70 }
                    },
                    {
                        source: '网络攻防\n博弈分系统',
                        target: '智能化训练\n运用分系统',
                        value: '基础设施',
                        lineStyle: { color: '#47c9d1', width: 3, curveness: 0 },
                        effect: { symbolSize: 6, period: 3.5, constantSpeed: 70 }
                    },
                    // 智能对抗博弈可视化分系统为网络攻防博弈分系统提供可视化
                    {
                        source: '智能博弈对抗\n可视化分系统',
                        target: '网络攻防\n博弈分系统',
                        value: '博弈态势可视化',
                        lineStyle: { color: '#ed5ca0', width: 3, curveness: 0 },
                        effect: { symbolSize: 6, period: 4, constantSpeed: 65 }
                    },
                    
                    // === 其他分系统间的核心交互连线（精简版） ===
                    // 攻击链生成与威胁样本生成的双向交互
                    {
                        source: '网络攻击链\n生成分系统',
                        target: '网络威胁样本\n生成分系统',
                        value: '样本构造需求',
                        lineStyle: { color: '#ff7a47', width: 3, curveness: 0 },
                        effect: { symbolSize: 6, period: 3.5, constantSpeed: 75 }
                    },
                    {
                        source: '网络威胁样本\n生成分系统',
                        target: '网络攻击链\n生成分系统',
                        value: '威胁样本',
                        lineStyle: { color: '#ed5ca0', width: 3, curveness: 0 },
                        effect: { symbolSize: 6, period: 3.5, constantSpeed: 75 }
                    },
                    
                    // 攻击链生成对训练环境的攻击
                    {
                        source: '网络攻击链\n生成分系统',
                        target: '智能化训练\n运用分系统',
                        value: '攻击流量',
                        lineStyle: { color: '#ff7a47', width: 3, curveness: 0 },
                        effect: { symbolSize: 6, period: 3.5, constantSpeed: 70 }
                    },
                    
                    // 威胁监测的判别反馈
                    {
                        source: '未知威胁\n监测分系统',
                        target: '网络威胁样本\n生成分系统',
                        value: '判别结果',
                        lineStyle: { color: '#7c5eff', width: 3, curveness: 0 },
                        effect: { symbolSize: 6, period: 4, constantSpeed: 60 }
                    }
                ];
                
                // 定义系统类别和颜色
                const categories = [
                    { name: '样本生成', itemStyle: { color: '#ED5CA0' } },
                    { name: '攻击链生成', itemStyle: { color: '#FF7A47' } },
                    { name: '威胁监测', itemStyle: { color: '#7C5EFF' } },
                    { name: '智能训练', itemStyle: { color: '#FFB347' } },
                    { name: '对抗可视化', itemStyle: { color: '#ED5CA0' } },
                    { name: '博弈核心', itemStyle: { color: '#3275ED' } }
                ];
                
                const option = {
                    backgroundColor: 'transparent',
                    animationDurationUpdate: 1500,
                    animationEasingUpdate: 'quinticInOut',
                    series: [{
                        type: 'graph',
                        layout: 'none',
                        coordinateSystem: null,
                        symbolSize: 60,
                        roam: false,
                        label: {
                            show: true,
                            position: 'bottom',
                            distance: 20,
                            formatter: '{b}',
                            fontSize: 11,
                            color: '#ffffff',
                            fontWeight: 'bold'
                        },
                        edgeSymbol: ['none', 'arrow'],
                        edgeSymbolSize: [0, 10],
                        edgeLabel: {
                            show: true,
                            fontSize: 9,
                            color: '#ffffff',
                            backgroundColor: 'rgba(0, 0, 0, 0.9)',
                            padding: [1, 3, 1, 3],
                            borderRadius: 3,
                            fontWeight: 'bold',
                            formatter: '{c}',
                            distance: 15,
                            position: 'middle'
                        },
                        data: nodes,
                        links: links,
                        categories: categories,
                        emphasis: {
                            focus: 'adjacency',
                            lineStyle: {
                                width: 5
                            }
                        },
                        lineStyle: {
                            color: 'source',
                            curveness: 0.1,
                            width: 2
                        }
                    }]
                };
                
                chart.setOption(option);
                
                // 监听窗口大小变化
                window.addEventListener('resize', function() {
                    chart.resize();
                });
                
                return chart;
            }

            // 页面加载完成后初始化模型显示
            // 默认显示CNN模型
            const cnnCard = document.getElementById('model-cnn');
            if (cnnCard) {
                cnnCard.classList.add('active');
            }

            // 图例位置修复脚本 - 将图例放置在右上角
            setTimeout(function() {
                // 获取系统交互图表实例
                const systemChart = window.systemInteractionChart;
                if (systemChart) {
                    // 修改图例位置到右上角
                    systemChart.setOption({
                        legend: {
                            orient: 'vertical',
                            top: 60, // 下移图例，避免与标题重叠
                            right: 10,
                            left: 'auto',
                            textStyle: {
                                color: '#fff',
                                fontSize: 11,
                                fontWeight: 'bold'
                            },
                            padding: [5, 8, 5, 8],
                            backgroundColor: 'rgba(0, 0, 0, 0.6)',
                            itemGap: 6,
                            itemWidth: 14,
                            itemHeight: 8,
                            icon: 'rect',
                            zlevel: 12,
                            formatter: function(name) {
                                // 保持文本简短
                                const shortNames = {
                                    '威胁样本生成': '样本生成',
                                    '攻击链生成': '攻击链',
                                    '威胁监测': '威胁监测',
                                    '攻防博弈': '攻防博弈',
                                    '训练运用': '训练运用',
                                    '可视化系统': '可视化系统'
                                };
                                return shortNames[name] || name;
                            }
                        }
                    });
                    console.log("图例位置已调整到右上角");
                }
            }, 2000); // 延迟2秒执行，确保主脚本已完成加载

            // 在窗口大小变化时也重新应用图例设置
            window.addEventListener('resize', function() {
                setTimeout(function() {
                    const systemChart = window.systemInteractionChart;
                    if (systemChart) {
                        systemChart.setOption({
                            legend: {
                                orient: 'vertical',
                                top: 60,
                                right: 10,
                                left: 'auto',
                                textStyle: {
                                    color: '#fff',
                                    fontSize: 11,
                                    fontWeight: 'bold'
                                },
                                padding: [5, 8, 5, 8],
                                backgroundColor: 'rgba(0, 0, 0, 0.6)',
                                itemGap: 6,
                                itemWidth: 14,
                                itemHeight: 8,
                                icon: 'rect',
                                zlevel: 12,
                                formatter: function(name) {
                                    // 保持文本简短
                                    const shortNames = {
                                        '威胁样本生成': '样本生成',
                                        '攻击链生成': '攻击链',
                                        '威胁监测': '威胁监测',
                                        '攻防博弈': '攻防博弈',
                                        '训练运用': '训练运用',
                                        '可视化系统': '可视化系统'
                                    };
                                    return shortNames[name] || name;
                                }
                            }
                        });
                    }
                }, 500); // 窗口调整大小后延迟500ms执行
            });
        });
    </script>
</body>
</html> 
