"""PeepholeLSTM"""

import pickle
import requests
import joblib
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import pandas as pd
from sklearn.preprocessing import StandardScaler, LabelEncoder,OrdinalEncoder
from sklearn.model_selection import train_test_split
import numpy as np

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class PeepholeLSTMCell(nn.Module):
    """Peephole LSTM cell implementation"""
    def __init__(self, input_size, hidden_size):
        super(PeepholeLSTMCell, self).__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        
        # Input-to-hidden weights
        self.weight_ih = nn.Linear(input_size, 4 * hidden_size)
        # Hidden-to-hidden weights
        self.weight_hh = nn.Linear(hidden_size, 4 * hidden_size, bias=False)
        # Peephole weights
        self.w_ci = nn.Parameter(torch.zeros(hidden_size))
        self.w_cf = nn.Parameter(torch.zeros(hidden_size))
        self.w_co = nn.Parameter(torch.zeros(hidden_size))
        
        self.bias = nn.Parameter(torch.zeros(4 * hidden_size))
        self.init_weights()

    def init_weights(self):
        """Initialize weights using Xavier initialization"""
        for weight in [self.weight_ih.weight, self.weight_hh.weight]:
            nn.init.xavier_uniform_(weight)

    def forward(self, input, state):
        hx, cx = state
        gates = self.weight_ih(input) + self.weight_hh(hx) + self.bias
        
        # Get all gates
        ingate, forgetgate, cellgate, outgate = gates.chunk(4, 1)
        
        # Add peephole connections
        ingate = ingate + self.w_ci * cx
        forgetgate = forgetgate + self.w_cf * cx
        
        # Apply activations
        ingate = torch.sigmoid(ingate)
        forgetgate = torch.sigmoid(forgetgate)
        cellgate = torch.tanh(cellgate)
        
        # Compute new cell state
        cy = (forgetgate * cx) + (ingate * cellgate)
        
        # Add peephole connection to output gate
        outgate = outgate + self.w_co * cy
        outgate = torch.sigmoid(outgate)
        
        # Compute new hidden state
        hy = outgate * torch.tanh(cy)
        
        return hy, cy

class PeepholeLSTM(nn.Module):
    """Peephole LSTM network"""
    def __init__(self, input_size, hidden_size, num_layers, num_classes):
        super(PeepholeLSTM, self).__init__()
        
        # Convert parameters to integers and validate
        self.input_size = int(input_size)
        self.hidden_size = int(hidden_size)
        self.num_layers = int(num_layers)
        self.num_classes = int(num_classes)
        
        # Validate parameters
        if self.input_size <= 0 or self.hidden_size <= 0 or self.num_layers <= 0 or self.num_classes <= 0:
            raise ValueError("All parameters must be positive integers")
        
        # Create LSTM layers
        self.lstm_cells = nn.ModuleList([
            PeepholeLSTMCell(
                input_size if layer == 0 else hidden_size,
                hidden_size
            ) for layer in range(num_layers)
        ])
        
        self.fc = nn.Linear(hidden_size, num_classes)
    
    def forward(self, x):
        # Check input dimensions and adjust
        if x.dim() == 2:
            x = x.unsqueeze(1)
        elif x.dim() != 3:
            raise ValueError(f"Expected 2D or 3D input, but got {x.dim()}D input")
        
        batch_size, seq_len, _ = x.size()
        
        # Initialize hidden states and cell states
        h_states = [torch.zeros(batch_size, self.hidden_size).to(x.device) 
                   for _ in range(self.num_layers)]
        c_states = [torch.zeros(batch_size, self.hidden_size).to(x.device) 
                   for _ in range(self.num_layers)]
        
        # Process each time step
        for t in range(seq_len):
            current_input = x[:, t, :]
            
            for layer in range(self.num_layers):
                if layer == 0:
                    layer_input = current_input
                else:
                    layer_input = h_states[layer-1]
                
                h_states[layer], c_states[layer] = self.lstm_cells[layer](
                    layer_input, 
                    (h_states[layer], c_states[layer])
                )
        
        # Use the final hidden state for classification
        out = self.fc(h_states[-1])
        return out


def validate_params(job_params):
    required_params = ["input_size", "hidden_size", "output_size", "num_layers", 
                      "num_epochs", "learning_rate", "batch_size"]
    
    # Check if all required parameters exist
    for param in required_params:
        if param not in job_params:
            raise ValueError(f"Missing required parameter: {param}")
    
    # Convert and validate numeric parameters
    try:
        job_params["input_size"] = int(job_params["input_size"])
        job_params["hidden_size"] = int(job_params["hidden_size"])
        job_params["output_size"] = int(job_params["output_size"])
        job_params["num_layers"] = int(job_params["num_layers"])
        job_params["num_epochs"] = int(job_params["num_epochs"])
        job_params["batch_size"] = int(job_params["batch_size"])
        job_params["learning_rate"] = float(job_params["learning_rate"])
    except (ValueError, TypeError) as e:
        raise ValueError(f"Invalid parameter value: {str(e)}")
    
    # Validate parameter values
    if job_params["input_size"] <= 0:
        raise ValueError("input_size must be positive")
    if job_params["hidden_size"] <= 0:
        raise ValueError("hidden_size must be positive")
    if job_params["output_size"] <= 0:
        raise ValueError("output_size must be positive")
    if job_params["num_layers"] <= 0:
        raise ValueError("num_layers must be positive")
    if job_params["num_epochs"] <= 0:
        raise ValueError("num_epochs must be positive")
    if job_params["batch_size"] <= 0:
        raise ValueError("batch_size must be positive")
    if job_params["learning_rate"] <= 0:
        raise ValueError("learning_rate must be positive")
    
    return job_params

# 加载数据集
def load_data(file_path):
    # 从pkl文件中加载数据
    with open(file_path, 'rb') as file:
        data = pickle.load(file)
    
    # 假设数据是一个DataFrame格式，或者是特征和标签分别存储的形式
    if isinstance(data, pd.DataFrame):
        X = data.drop(['Label'], axis=1)  # 假设 'Label' 是标签列
        y = data['Label']
    elif isinstance(data, dict):  # 如果是字典形式
        X = data['features']  # 假设特征存储在 'features' 键下
        y = data['labels']    # 假设标签存储在 'labels' 键下
    
    return X, y

def load_data_preprocess(pkl_file):
    # 从pkl文件中加载数据
    with open(pkl_file, 'rb') as file:
        data = pickle.load(file)
    
    # 清理列名，去除可能的空格
    data.columns = data.columns.str.strip()

    print(type(data))  # 检查数据类型
    print(data.head())  # 查看前几行数据
    print(data.info())  # 查看数据信息

    # 假设数据是一个DataFrame格式，或者是特征和标签分别存储的形式
    if isinstance(data, pd.DataFrame):
        print(data.columns)
        X = data.drop(['Label'], axis=1)  # 假设 'Label' 是标签列
        y = data['Label']
    elif isinstance(data, dict):  # 如果是字典形式
        X = data['features']  # 假设特征存储在 'features' 键下
        y = data['labels']    # 假设标签存储在 'labels' 键下
    
    return X, y

def preprocess_data(X, y):
    # 1. 处理缺失值（删除缺失值）
    if X.isnull().any().any() or y.isnull().any():
        # 删除包含缺失值的行
        X, y = X.align(y, join='inner', axis=0)
        X = X.dropna()
        y = y[X.index]  # 保证 y 和 X 同步

    # 2. 标签编码
    le = LabelEncoder()
    y = le.fit_transform(y)

    # 3. 分类特征的编码（如果存在分类特征）
    categorical_cols = X.select_dtypes(include=['object']).columns
    encoder = OrdinalEncoder()
    if not categorical_cols.empty:
        X[categorical_cols] = encoder.fit_transform(X[categorical_cols])
    
    # 4. 检查并处理无限值
    X.replace([np.inf, -np.inf], np.nan, inplace=True)  # 替换为 NaN
    X.fillna(X.mean(), inplace=True)  # 用均值填充 NaN
    
    # 5. 数据标准化
    scaler = StandardScaler()
    X = scaler.fit_transform(X)

    # 6. 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    
    return X_train, X_test, y_train, y_test

# 数据准备
def prepare_dataset(train_x, train_y, test_x, test_y):
    train_x = torch.tensor(train_x, dtype=torch.float32)
    train_y = torch.tensor(train_y, dtype=torch.long)
    test_x = torch.tensor(test_x, dtype=torch.float32)
    test_y = torch.tensor(test_y, dtype=torch.long)
    
    train_dataset = TensorDataset(train_x, train_y)
    test_dataset = TensorDataset(test_x, test_y)
    
    return train_dataset, test_dataset

# 训练函数
def train_model(train_loader, model, criterion, optimizer, num_epochs, device):
    model.to(device)

    for epoch in range(num_epochs):
        model.train()
        running_loss = 0.0

        for inputs, labels in train_loader:
            inputs, labels = inputs.to(device), labels.to(device)

            optimizer.zero_grad()
            outputs = model(inputs)
            
            assert outputs.shape[1] == 6, f"Expected output dimension 6, but got {outputs.shape[1]}"
            assert labels.dim() == 1, f"Expected labels to be 1D, but got {labels.dim()}D"
            assert outputs.shape[0] == labels.shape[0], f"Batch size mismatch: outputs {outputs.shape[0]}, labels {labels.shape[0]}"

            loss = criterion(outputs, labels.long())
            loss.backward()
            optimizer.step()

            running_loss += loss.item()

        print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {running_loss/len(train_loader):.4f}')

# 测试函数
def test_model(test_loader, model, device):
    model.to(device)
    model.eval()
    correct, total = 0, 0

    with torch.no_grad():
        for inputs, labels in test_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            outputs = model(inputs)
            _, predicted = torch.max(outputs, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()

    print(f'Accuracy: {100 * correct / total:.2f}%')

def peephole_lstm_train(dataset, job_params, model_name, result_dir, fit_params=None, device="cpu"):
    try:
        # Validate and convert parameters
        job_params = validate_params(job_params)
        
        print("Model parameters:")
        print(f"Input size: {job_params['input_size']}")
        print(f"Hidden size: {job_params['hidden_size']}")
        print(f"Output size: {job_params['output_size']}")
        print(f"Number of layers: {job_params['num_layers']}")
        
        # Prepare dataset
        training_data_path = "/workspace/" + dataset["training_data_path"]
        X, y = load_data_preprocess(training_data_path)
        
        # Verify input size matches data
        if X.shape[1] != job_params["input_size"]:
            raise ValueError(f"Input size mismatch. Expected {job_params['input_size']}, got {X.shape[1]}")
        
        # Data preprocessing
        train_x, test_x, train_y, test_y = preprocess_data(X, y)
        train_dataset, test_dataset = prepare_dataset(train_x, train_y, test_x, test_y)
        train_loader = DataLoader(train_dataset, batch_size=job_params["batch_size"], shuffle=True)
        test_loader = DataLoader(test_dataset, batch_size=job_params["batch_size"], shuffle=False)

        # Initialize PeepholeLSTM model
        model = PeepholeLSTM(
            input_size=job_params["input_size"],
            hidden_size=job_params["hidden_size"],
            num_layers=job_params["num_layers"],
            num_classes=job_params["output_size"]
        )
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(model.parameters(), lr=job_params["learning_rate"])

        # Train model
        train_model(train_loader, model, criterion, optimizer, job_params["num_epochs"], device)
        # Test model
        test_model(test_loader, model, device)

        # Save model
        model_file = open(result_dir + "/" + model_name + ".pth", "wb")
        torch.save(model.state_dict(), model_file)
        model_file.close()
        print(f'PeepholeLSTM training completed, model saved to {result_dir}/{model_name}.pth')

        return None, 0
        
    except Exception as e:
        print(f"Error in PeepholeLSTM training: {str(e)}")
        return str(e), -1



def model_upload(result_dir, model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".pth"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".pth")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "pytorch",
          "file_name": model_name+".pth",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "RNN",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers
    
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='pytorch PeepholeLSTM Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='PeepholeLSTM Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='PeepholeLSTM DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start PeepholeLSTM training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("PeepholeLSTM job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("PeepholeLSTM dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("PeepholeLSTM result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("PeepholeLSTM factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("PeepholeLSTM fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("PeepholeLSTM sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    print("Step 1 PeepholeLSTM training:\n")
    result,ret_code = peephole_lstm_train(dataset,job_params, model["model_name"],result_dir,fit_params,device)
    if ret_code != 0:
        print("PeepholeLSTM train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()
   
  
