import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
from torchvision import datasets, transforms
import torchvision.transforms.functional as TF
from torchvision import models
import torchvision
from PIL import Image
import os
import numpy as np
from tqdm.auto import tqdm
import xml.etree.ElementTree as ET

class VOC2007Dataset(Dataset):
    def __init__(self, voc_root, image_size=64, transform=None):
        """
        VOC2007数据集加载器
        Args:
            voc_root: VOC2007数据集根目录
            image_size: 目标图像大小
            transform: 图像变换
        """
        self.voc_root = voc_root
        self.image_size = image_size
        self.images_dir = os.path.join(voc_root, "JPEGImages")
        
        if transform is None:
            self.transform = transforms.Compose([
                transforms.Resize((image_size, image_size)),
                transforms.RandomHorizontalFlip(),
                transforms.ToTensor(),
                transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])
            ])
        else:
            self.transform = transform
            
        # 获取所有图像文件路径
        self.image_paths = []
        for img_name in os.listdir(self.images_dir):
            if img_name.endswith(('.jpg', '.jpeg', '.png')):
                self.image_paths.append(os.path.join(self.images_dir, img_name))
                
        print(f"找到 {len(self.image_paths)} 张图像")

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        image = Image.open(img_path).convert('RGB')
        
        if self.transform:
            image = self.transform(image)
            
        return image

class Generator(nn.Module):
    def __init__(self, latent_dim, image_channels=3):
        super(Generator, self).__init__()
        self.latent_dim = latent_dim
        
        # 初始特征图大小为 4x4
        self.init_size = 4
        self.l1 = nn.Sequential(
            nn.Linear(latent_dim, 128 * self.init_size ** 2)
        )

        self.conv_blocks = nn.Sequential(
            # 4x4 -> 8x8
            nn.BatchNorm2d(128),
            nn.Upsample(scale_factor=2),
            nn.Conv2d(128, 128, 3, stride=1, padding=1),
            nn.BatchNorm2d(128),
            nn.LeakyReLU(0.2, inplace=True),
            
            # 8x8 -> 16x16
            nn.Upsample(scale_factor=2),
            nn.Conv2d(128, 64, 3, stride=1, padding=1),
            nn.BatchNorm2d(64),
            nn.LeakyReLU(0.2, inplace=True),
            
            # 16x16 -> 32x32
            nn.Upsample(scale_factor=2),
            nn.Conv2d(64, 32, 3, stride=1, padding=1),
            nn.BatchNorm2d(32),
            nn.LeakyReLU(0.2, inplace=True),
            
            # 32x32 -> 64x64
            nn.Upsample(scale_factor=2),
            nn.Conv2d(32, image_channels, 3, stride=1, padding=1),
            nn.Tanh()
        )

    def forward(self, z):
        out = self.l1(z)
        out = out.view(out.shape[0], 128, self.init_size, self.init_size)
        img = self.conv_blocks(out)
        return img

class Discriminator(nn.Module):
    def __init__(self, image_channels=3):
        super(Discriminator, self).__init__()

        def discriminator_block(in_filters, out_filters, bn=True):
            block = [
                nn.Conv2d(in_filters, out_filters, 3, 2, 1),
                nn.LeakyReLU(0.2, inplace=True),
                nn.Dropout2d(0.25)
            ]
            if bn:
                block.append(nn.BatchNorm2d(out_filters))
            return block

        self.model = nn.Sequential(
            *discriminator_block(image_channels, 16, bn=False),  # 64x64 -> 32x32
            *discriminator_block(16, 32),                        # 32x32 -> 16x16
            *discriminator_block(32, 64),                        # 16x16 -> 8x8
            *discriminator_block(64, 128),                       # 8x8 -> 4x4
        )

        # The height and width of downsampled image
        ds_size = 4
        self.adv_layer = nn.Sequential(
            nn.Linear(128 * ds_size ** 2, 1),
            nn.Sigmoid()
        )

    def forward(self, img):
        out = self.model(img)
        out = out.view(out.shape[0], -1)
        validity = self.adv_layer(out)
        return validity

def prepare_voc_data(voc_root, batch_size, image_size):
    """准备VOC2007数据集"""
    dataset = VOC2007Dataset(voc_root, image_size=image_size)
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=2,
        pin_memory=True
    )
    return dataloader

class GANLoss:
    def __init__(self, device):
        self.adversarial_loss = nn.BCELoss()
        self.device = device
        
    def discriminator_loss(self, real_validity, fake_validity):
        real_label = torch.ones(real_validity.size()).to(self.device)
        fake_label = torch.zeros(fake_validity.size()).to(self.device)
        
        real_loss = self.adversarial_loss(real_validity, real_label)
        fake_loss = self.adversarial_loss(fake_validity, fake_label)
        
        d_loss = (real_loss + fake_loss) / 2
        return d_loss
    
    def generator_loss(self, fake_validity):
        real_label = torch.ones(fake_validity.size()).to(self.device)
        return self.adversarial_loss(fake_validity, real_label)

def train_gan(dataloader, generator, discriminator, criterion, g_optimizer, d_optimizer, 
              num_epochs, latent_dim, device, sample_interval=100, result_dir='results'):
    os.makedirs(result_dir, exist_ok=True)
    
    # 创建固定噪声用于可视化训练进程
    fixed_noise = torch.randn(16, latent_dim).to(device)
    
    for epoch in range(num_epochs):
        pbar = tqdm(enumerate(dataloader), desc=f"Epoch {epoch+1}/{num_epochs}")
        
        for i, real_imgs in pbar:
            batch_size = real_imgs.size(0)
            
            # 配置真实图像和噪声向量
            real_imgs = real_imgs.to(device)
            z = torch.randn(batch_size, latent_dim).to(device)
            
            # -----------------
            #  训练判别器
            # -----------------
            d_optimizer.zero_grad()
            
            # 生成一批假图像
            fake_imgs = generator(z)
            
            # 判别器对真实和生成图像的判断
            real_validity = discriminator(real_imgs)
            fake_validity = discriminator(fake_imgs.detach())
            
            # 计算判别器损失
            d_loss = criterion.discriminator_loss(real_validity, fake_validity)
            
            # 反向传播和优化
            d_loss.backward()
            d_optimizer.step()
            
            # -----------------
            #  训练生成器
            # -----------------
            g_optimizer.zero_grad()
            
            # 生成器试图欺骗判别器
            fake_validity = discriminator(fake_imgs)
            
            # 计算生成器损失
            g_loss = criterion.generator_loss(fake_validity)
            
            # 反向传播和优化
            g_loss.backward()
            g_optimizer.step()
            
            # 更新进度条
            pbar.set_postfix({
                'D_loss': f'{d_loss.item():.4f}',
                'G_loss': f'{g_loss.item():.4f}'
            })
            
            # 保存采样结果
            batches_done = epoch * len(dataloader) + i
            if batches_done % sample_interval == 0:
                # 使用固定噪声生成样本，便于比较训练进展
                with torch.no_grad():
                    fake_imgs = generator(fixed_noise)
                    save_sample_images(fake_imgs, epoch, batches_done, result_dir)

def save_sample_images(gen_imgs, epoch, batches_done, result_dir, n_row=4):
    """保存生成的样本图像"""
    # 将图像转换为可视化格式
    gen_imgs = 0.5 * (gen_imgs + 1.0)  # 反归一化
    gen_imgs = gen_imgs.clamp(0, 1)
    
    # 保存图像
    save_path = os.path.join(result_dir, f'samples_epoch_{epoch}_batch_{batches_done}.png')
    torchvision.utils.save_image(gen_imgs, save_path, nrow=n_row, normalize=False)

def gan_train(voc_root, image_size, num_epochs, learning_rate, batch_size, result_dir, model_name, device):
    # 超参数
    latent_dim = 100
    
    # 准备数据
    dataloader = prepare_voc_data(voc_root, batch_size, image_size)
    
    # 初始化模型
    generator = Generator(latent_dim).to(device)
    discriminator = Discriminator().to(device)
    
    # 损失函数
    criterion = GANLoss(device)
    
    # 优化器
    g_optimizer = optim.Adam(generator.parameters(), lr=learning_rate, betas=(0.5, 0.999))
    d_optimizer = optim.Adam(discriminator.parameters(), lr=learning_rate, betas=(0.5, 0.999))
    
    try:
        # 创建结果目录
        os.makedirs(result_dir, exist_ok=True)
        
        # 训练模型
        train_gan(dataloader, generator, discriminator, criterion, 
                 g_optimizer, d_optimizer, num_epochs, latent_dim, 
                 device, sample_interval=100, result_dir=result_dir)
        
        # 保存最终模型
        torch.save(generator.state_dict(), os.path.join(result_dir, f"{model_name}_generator.pth"))
        torch.save(discriminator.state_dict(), os.path.join(result_dir, f"{model_name}_discriminator.pth"))
        print(f'训练完成，模型保存到 {result_dir}')
        
    except Exception as e:
        print(f"训练过程中发生错误: {e}")
        raise e

if __name__ == "__main__":
    # 设置参数
    voc_root = 'E:/data/VOCdevkit/VOC2007'  # VOC2007数据集根目录
    image_size = 64  # 生成图像的大小
    epochs = 1    # 训练轮数
    learning_rate = 0.0002
    batch_size = 64
    result_dir = 'E:/data/VOCdevkit/gan_results'
    model_name = 'voc_gan'
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 设置随机种子以确保可重复性
    torch.manual_seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(42)
    
    # 开始训练
    gan_train(voc_root, image_size, epochs, learning_rate, batch_size, 
             result_dir, model_name, device)