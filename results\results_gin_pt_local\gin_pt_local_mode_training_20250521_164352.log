2025-05-21 16:43:52,703 - GIN_PT_Local_Mode - INFO - 日志将记录到控制台和文件: results\results_gin_pt_local\gin_pt_local_mode_training_20250521_164352.log
2025-05-21 16:43:52,704 - GIN_PT_Local_Mode - INFO - GIN PyTorch 本地模式训练脚本已初始化。
2025-05-21 16:43:52,704 - GIN_PT_Local_Mode - INFO - PyTorch 版本: 2.4.1+cpu
2025-05-21 16:43:52,705 - GIN_PT_Local_Mode - INFO - 参数: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'result_dir': 'results\\results_gin_pt_local', 'model_name': 'GIN_PT_Local_Model', 'input_size': 84, 'hidden_size': 64, 'output_size': 6, 'num_layers': 2, 'dropout_rate': 0.5, 'num_epochs': 3, 'learning_rate': 0.001, 'batch_size': 32, 'edge_strategy': 'fully_connected', 'k_neighbors': 5, 'radius': 1.0, 'label_column': 'Label', 'data_format': 'pkl', 'test_split_size': 0.2, 'random_state': 42, 'normalize': True, 'scaler_type': 'standard', 'handle_imbalance': False, 'stratify_split': True, 'numeric_impute_strategy': 'mean', 'categorical_impute_strategy': 'most_frequent', 'use_cv': False, 'cv_folds': 5, 'log_level': 'INFO', 'force_cpu': False}
2025-05-21 16:43:52,706 - GIN_PT_Local_Mode - INFO - 使用设备: cpu
2025-05-21 16:43:52,710 - GIN_PT_Local_Mode - INFO - 从 E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl (格式: pkl) 加载数据
2025-05-21 16:43:53,122 - GIN_PT_Local_Mode - INFO - 数据加载为 DataFrame，形状: (692703, 85)
2025-05-21 16:43:53,444 - GIN_PT_Local_Mode - INFO - 特征形状: (692703, 84), 标签形状: (692703,)
2025-05-21 16:43:53,476 - GIN_PT_Local_Mode - INFO - 标签分布: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-21 16:43:53,627 - GIN_PT_Local_Mode - INFO - 模型将使用 input_size=84, output_size(num_classes)=6
2025-05-21 16:43:53,628 - GIN_PT_Local_Mode - INFO - 选择标准训练/测试分割模式。
2025-05-21 16:43:54,192 - GIN_PT_Local_Mode - INFO - 已将特征中的无限值替换为NaN。
2025-05-21 16:43:54,287 - GIN_PT_Local_Mode - INFO - 处理特征中的缺失值。
2025-05-21 16:43:54,548 - GIN_PT_Local_Mode - INFO - 标签已编码。6个类别: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-21 16:43:54,590 - GIN_PT_Local_Mode - INFO - 编码类别特征: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-21 16:43:59,755 - GIN_PT_Local_Mode - INFO - 使用 standard 缩放器标准化/缩放特征。
2025-05-21 16:44:00,987 - GIN_PT_Local_Mode - INFO - 数据分割: X_train (554162, 84), y_train (554162,), X_test (138541, 84), y_test (138541,)
2025-05-21 16:44:01,064 - GIN_PT_Local_Mode - INFO - 训练/测试分割的预处理信息已保存: results\results_gin_pt_local\GIN_PT_Local_Model_preprocessing_info_pt.json
2025-05-21 16:44:01,065 - GIN_PT_Local_Mode - INFO - 预处理后数据 (训练/测试分割): input_size=84, num_classes=6
2025-05-21 16:44:01,066 - GIN_PT_Local_Mode - CRITICAL - 发生意外严重错误: create_graph_data_list_local() got an unexpected keyword argument 'strategy'
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GIN_pt_local_mode.py", line 952, in gin_train_local_mode
    train_graphs = create_graph_data_list_local(X_train_np, y_train_np, **edge_options_main)
TypeError: create_graph_data_list_local() got an unexpected keyword argument 'strategy'
