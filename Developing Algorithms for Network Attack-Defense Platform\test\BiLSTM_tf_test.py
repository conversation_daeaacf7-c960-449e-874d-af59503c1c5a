import numpy as np
import pandas as pd
import pickle
import requests
from sklearn.preprocessing import StandardScaler, LabelEncoder, OrdinalEncoder
import json

def prepare_test_data(pkl_file, num_samples=1):
    """
    准备测试数据，执行与训练时相同的预处理步骤
    """
    # 加载数据
    with open(pkl_file, 'rb') as file:
        data = pickle.load(file)
    
    # 清理列名
    data.columns = data.columns.str.strip()
    
    # 获取特征和标签
    X = data.drop(['Label'], axis=1)
    y = data['Label']
    
    # 处理缺失值
    if X.isnull().any().any():
        X = X.dropna()
    
    # 编码分类特征
    categorical_cols = X.select_dtypes(include=['object']).columns
    if not categorical_cols.empty:
        encoder = OrdinalEncoder()
        X[categorical_cols] = encoder.fit_transform(X[categorical_cols])
    
    # 处理无限值
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.mean(), inplace=True)
    
    # 标准化数据
    scaler = StandardScaler()
    X = scaler.fit_transform(X)
    
    # 只取指定数量的样本
    X = X[:num_samples]
    y = y[:num_samples]
    
    # 重塑数据以匹配模型输入要求 (samples, timesteps, features)
    X = np.expand_dims(X, axis=1)
    
    return X, y

def test_model(url, test_data):
    """
    测试部署的模型服务
    """
    # 准备请求数据
    instances = test_data.tolist()
    request_data = {
        "instances": instances
    }
    
    # 发送预测请求
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, json=request_data, headers=headers)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error making prediction request: {e}")
        return None


def prepare_postman_data(pkl_file, num_samples=1):
    """
    准备用于 Postman 测试的数据，确保维度正确
    """
    # 加载数据
    print("Loading data from:", pkl_file)
    with open(pkl_file, 'rb') as file:
        data = pickle.load(file)
    
    print("Original data shape:", data.shape)
    
    # 清理列名
    data.columns = data.columns.str.strip()
    
    # 获取特征和标签
    X = data.drop(['Label'], axis=1)
    y = data['Label']
    
    print("\nUnique labels:", y.unique())
    print("Number of features:", X.shape[1])
    
    # 处理缺失值
    if X.isnull().any().any():
        X = X.dropna()
    
    # 编码分类特征
    categorical_cols = X.select_dtypes(include=['object']).columns
    if not categorical_cols.empty:
        encoder = OrdinalEncoder()
        X[categorical_cols] = encoder.fit_transform(X[categorical_cols])
    
    # 处理无限值
    X.replace([np.inf, -np.inf], np.nan, inplace=True)
    X.fillna(X.mean(), inplace=True)
    
    # 标准化数据
    scaler = StandardScaler()
    X = scaler.fit_transform(X)
    
    # 只取指定数量的样本
    X = X[:num_samples]
    y = y[:num_samples]
    
    # 重塑数据以匹配模型输入要求 (samples, features, 1)
    # 这里我们按照原始训练代码中的input_shape=(X_train.shape[1], 1)设置
    X = X.reshape(X.shape[0], X.shape[1], 1)
    
    # 准备 Postman 请求数据格式
    request_data = {
        "instances": X.tolist()
    }
    
    # 保存为 JSON 文件
    output_file = 'Tuesday-WorkingHours.pcap_ISCX.json'
    with open(output_file, 'w') as f:
        json.dump(request_data, f)
    
    print(f"\nPostman test data saved to {output_file}")
    print(f"Number of samples: {num_samples}")
    print(f"Data shape after reshape: {X.shape}")
    print("Shape explanation:")
    print(f"- {X.shape[0]} samples")
    print(f"- {X.shape[1]} features per sample")
    print(f"- {X.shape[2]} channel")
    print("\nOriginal labels for these samples:")
    print(y.values)
    
    # 打印生成的JSON数据的一小部分用于验证
    print("\nFirst few elements of the first sample (preview):")
    preview_data = np.array(request_data["instances"][0])[:5].tolist()
    print(preview_data)
    
    return output_file

if __name__ == "__main__":
    # 配置参数
    pkl_file = 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Tuesday-WorkingHours.pcap_ISCX.pkl'  # 替换为测试数据文件路径
    output_file = prepare_postman_data(pkl_file, num_samples=2)  # 生成2个样本用于测试 Postman
        # 打印生成的 JSON 数据的前一部分，用于验证格式
    with open(output_file, 'r') as f:
        data = json.load(f)
        print("\nGenerated JSON data (first sample):")
        print(json.dumps({"instances": [data["instances"][0]]}, indent=2))



    model_url = "http://172.16.50.51:31228/v1/models/bilstmtf:predict"
    num_test_samples = 5  # 测试样本数量
    
    # 准备测试数据
    print("准备测试数据...")
    X_test, y_test = prepare_test_data(pkl_file, num_test_samples)
    print(f"测试数据形状: {X_test.shape}")
    
    # 测试模型
    print("\n发送预测请求...")
    predictions = test_model(model_url, X_test)
    
    if predictions:
        print("\n预测结果:")
        print(predictions)
        
        if 'predictions' in predictions:
            # 获取预测的类别
            predicted_classes = np.argmax(predictions['predictions'], axis=1)
            print("\n预测的类别:")
            print(predicted_classes)
            
            print("\n实际的类别:")
            print(y_test[:num_test_samples])
            
            # 计算准确率
            accuracy = np.mean(predicted_classes == y_test[:num_test_samples])
            print(f"\n准确率: {accuracy:.2%}")