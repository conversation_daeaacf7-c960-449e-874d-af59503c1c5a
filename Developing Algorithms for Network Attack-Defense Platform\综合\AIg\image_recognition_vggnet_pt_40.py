"""图像识别 (VGGNet)"""

import torch
import torch.nn as nn
import torch.optim as optim
from torchvision import datasets, transforms
from torchvision.models import vgg16

def image_recognition_vggnet(data_dir, epochs, batch_size):
    """
    参数说明：
    - data_dir: 数据目录
    - epochs: 训练轮数
    - batch_size: 批次大小
    """
    transform = transforms.Compose([
        transforms.Resize(224),
        transforms.ToTensor(),
    ])
    train_dataset = datasets.ImageFolder(data_dir, transform=transform)
    train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, shuffle=True)

    model = vgg16(pretrained=True)
    model.classifier[6] = nn.Linear(4096, len(train_dataset.classes))
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    for epoch in range(epochs):
        for inputs, labels in train_loader:
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
        print(f"Epoch {epoch+1}/{epochs}, Loss: {loss.item()}")

    print("VGGNet training completed")

if __name__ == "__main__":
    data_dir = 'data/images'
    epochs = 10
    batch_size = 32

    image_recognition_vggnet(data_dir, epochs, batch_size)
