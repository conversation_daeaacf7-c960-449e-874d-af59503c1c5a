2025-05-23 14:54:45,030 - DDPG_TF_Local - INFO - Logging to: ./results/ddpg_tf_local_run\DDPG_TF_Pendulum_training_20250523_145445.log
2025-05-23 14:54:45,030 - DDPG_TF_Local - INFO - DDPG TF Local Training (DDPG_TF_Pendulum) starting.
2025-05-23 14:54:45,045 - DDPG_TF_Local - INFO - Params:
{
  "env_name": "Pendulum-v1",
  "result_dir": "./results/ddpg_tf_local_run",
  "model_name": "DDPG_TF_Pendulum",
  "hidden_size": 256,
  "actor_lr": 0.0001,
  "critic_lr": 0.001,
  "gamma": 0.99,
  "tau": 0.001,
  "buffer_size": 50000,
  "batch_size": 64,
  "noise_sigma": 0.1,
  "num_episodes": 100,
  "max_steps_per_episode": 200,
  "random_seed": null,
  "save_freq": 20,
  "plot_freq": 10,
  "tf_log_level": "ERROR",
  "logger": "<Logger DDPG_TF_Local (INFO)>"
}
2025-05-23 14:54:45,056 - DDPG_TF_Local.Env - INFO - Gymnasium API for Pendulum-v1
2025-05-23 14:54:45,057 - DDPG_TF_Local - INFO - Env: Pendulum-v1, State=3, Action=1, Bound=2.0
2025-05-23 14:54:49,779 - DDPG_TF_Local - ERROR - Critical error: in user code:

    File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\DDPG_tf_local_mode.py", line 211, in _soft_update_target_networks_tf  *
        actor_weights_updated = [tau * local_w + (1.0 - tau) * target_w
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 2944, in get_weights  **
        return super().get_weights()
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\base_layer.py", line 1888, in get_weights
        return backend.batch_get_value(output_weights)
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\backend.py", line 4252, in batch_get_value
        raise RuntimeError("Cannot get value inside Tensorflow graph function.")

    RuntimeError: Cannot get value inside Tensorflow graph function.
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\DDPG_tf_local_mode.py", line 482, in <module>
    ddpg_train_tf_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\DDPG_tf_local_mode.py", line 351, in ddpg_train_tf_local
    agent.step(current_s, action_val, reward_val, next_s, done_val)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\DDPG_tf_local_mode.py", line 172, in step
    self.learn(experiences)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\DDPG_tf_local_mode.py", line 206, in learn
    self._soft_update_target_networks_tf(self.tau)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\tensorflow\python\util\traceback_utils.py", line 153, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "C:\Users\<USER>\AppData\Local\Temp\__autograph_generated_filemp1l9s9b.py", line 8, in tf___soft_update_target_networks_tf
    actor_weights_updated = [((ag__.ld(tau) * ag__.ld(local_w)) + ((1.0 - ag__.ld(tau)) * ag__.ld(target_w))) for (local_w, target_w) in ag__.converted_call(ag__.ld(zip), (ag__.converted_call(ag__.ld(self).actor.get_weights, (), None, fscope), ag__.converted_call(ag__.ld(self).actor_target.get_weights, (), None, fscope)), None, fscope)]
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 2944, in get_weights
    return super().get_weights()
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\base_layer.py", line 1888, in get_weights
    return backend.batch_get_value(output_weights)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\backend.py", line 4252, in batch_get_value
    raise RuntimeError("Cannot get value inside Tensorflow graph function.")
RuntimeError: in user code:

    File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\强化学习\DDPG_tf_local_mode.py", line 211, in _soft_update_target_networks_tf  *
        actor_weights_updated = [tau * local_w + (1.0 - tau) * target_w
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\training.py", line 2944, in get_weights  **
        return super().get_weights()
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\engine\base_layer.py", line 1888, in get_weights
        return backend.batch_get_value(output_weights)
    File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\backend.py", line 4252, in batch_get_value
        raise RuntimeError("Cannot get value inside Tensorflow graph function.")

    RuntimeError: Cannot get value inside Tensorflow graph function.

