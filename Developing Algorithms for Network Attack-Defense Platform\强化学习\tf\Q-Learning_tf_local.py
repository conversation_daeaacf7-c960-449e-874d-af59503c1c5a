import pickle
import requests
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import numpy as np
import tensorflow as tf
from tensorflow import keras
import gym
from collections import deque
import random

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"

class QNetwork(keras.Model):
    """Q-Network for deep Q-learning"""
    def __init__(self, state_size, action_size):
        super(QNetwork, self).__init__()
        self.state_size = state_size
        self.action_size = action_size
        self.fc1 = keras.layers.Dense(64, activation='relu')
        self.fc2 = keras.layers.Dense(64, activation='relu')
        self.fc3 = keras.layers.Dense(action_size)
        
    def call(self, x):
        x = self.fc1(x)
        x = self.fc2(x)
        return self.fc3(x)

    def save(self, path):
        """保存模型的权重和架构信息"""
        model_info = {
            'weights': self.get_weights(),
            'state_size': self.state_size,
            'action_size': self.action_size
        }
        with open(path, 'wb') as f:
            pickle.dump(model_info, f)
    
    @classmethod
    def load(cls, path):
        """加载模型的权重和架构信息"""
        with open(path, 'rb') as f:
            model_info = pickle.load(f)
        
        model = cls(model_info['state_size'], model_info['action_size'])
        # Build model
        dummy_state = tf.zeros((1, model_info['state_size']))
        model(dummy_state)
        model.set_weights(model_info['weights'])
        return model
    
class ReplayBuffer:
    """Experience replay buffer"""
    def __init__(self, buffer_size):
        self.buffer = deque(maxlen=buffer_size)
    
    def add(self, state, action, reward, next_state, done):
        self.buffer.append((state, action, reward, next_state, done))
    
    def sample(self, batch_size):
        batch = random.sample(self.buffer, batch_size)
        states, actions, rewards, next_states, dones = zip(*batch)
        return (np.array(states), np.array(actions), np.array(rewards), 
                np.array(next_states), np.array(dones))
    
    def __len__(self):
        return len(self.buffer)

class QLearningAgent:
    """Q-Learning agent with experience replay"""
    def __init__(self, state_size, action_size, learning_rate=0.001, gamma=0.99,
                 epsilon_start=1.0, epsilon_end=0.01, epsilon_decay=0.995,
                 buffer_size=10000, batch_size=64):
        self.state_size = state_size
        self.action_size = action_size
        self.gamma = gamma
        self.epsilon = epsilon_start
        self.epsilon_end = epsilon_end
        self.epsilon_decay = epsilon_decay
        self.batch_size = batch_size
        self.training = True
        
        # Q-Network
        self.qnetwork = QNetwork(state_size, action_size)
        self.optimizer = keras.optimizers.Adam(learning_rate=learning_rate)
        
        # Replay buffer
        self.memory = ReplayBuffer(buffer_size)
        
        # 训练记录
        self.training_info = {
            'episodes': 0,
            'total_steps': 0,
            'epsilon_history': [],
            'reward_history': [],
            'loss_history': []
        }
    
    def save(self, path):
        """保存完整的agent状态"""
        save_dict = {
            'network_weights': self.qnetwork.get_weights(),
            'optimizer_weights': self.optimizer.get_weights(),
            'training_info': self.training_info,
            'hyperparameters': {
                'state_size': self.state_size,
                'action_size': self.action_size,
                'gamma': self.gamma,
                'epsilon': self.epsilon,
                'epsilon_end': self.epsilon_end,
                'epsilon_decay': self.epsilon_decay,
                'batch_size': self.batch_size
            }
        }
        with open(path, 'wb') as f:
            pickle.dump(save_dict, f)
        
    @classmethod
    def load(cls, path):
        """加载完整的agent状态"""
        with open(path, 'rb') as f:
            save_dict = pickle.load(f)
        
        # 创建新的agent实例
        agent = cls(
            state_size=save_dict['hyperparameters']['state_size'],
            action_size=save_dict['hyperparameters']['action_size'],
            gamma=save_dict['hyperparameters']['gamma'],
            epsilon_start=save_dict['hyperparameters']['epsilon'],
            epsilon_end=save_dict['hyperparameters']['epsilon_end'],
            epsilon_decay=save_dict['hyperparameters']['epsilon_decay'],
            batch_size=save_dict['hyperparameters']['batch_size']
        )
        
        # Build model
        dummy_state = tf.zeros((1, save_dict['hyperparameters']['state_size']))
        agent.qnetwork(dummy_state)
        
        # 加载网络和优化器状态
        agent.qnetwork.set_weights(save_dict['network_weights'])
        agent.optimizer.set_weights(save_dict['optimizer_weights'])
        agent.training_info = save_dict['training_info']
        
        return agent
    
    def act(self, state):
        """选择动作"""
        if random.random() < self.epsilon:
            return random.randrange(self.action_size)
        
        state = np.array(state)[np.newaxis, :]
        action_values = self.qnetwork(state)
        return np.argmax(action_values.numpy())
    
    def step(self, state, action, reward, next_state, done):
        """更新agent的经验和学习"""
        # 保存经验到replay buffer
        self.memory.add(state, action, reward, next_state, done)
        
        # 如果buffer中有足够的样本，进行学习
        if len(self.memory) >= self.batch_size:
            self._learn()
            
        # 更新epsilon
        self.epsilon = max(self.epsilon_end, self.epsilon * self.epsilon_decay)
    
    @tf.function
    def _compute_loss(self, states, actions, rewards, next_states, dones):
        """计算损失函数"""
        future_rewards = self.qnetwork(next_states)
        max_future_rewards = tf.reduce_max(future_rewards, axis=1)
        target_q_values = rewards + (1 - dones) * self.gamma * max_future_rewards
        
        mask = tf.one_hot(actions, self.action_size)
        q_values = self.qnetwork(states)
        current_q_values = tf.reduce_sum(q_values * mask, axis=1)
        
        return tf.reduce_mean(tf.square(target_q_values - current_q_values))
    
    def _learn(self):
        """从经验中学习"""
        states, actions, rewards, next_states, dones = self.memory.sample(self.batch_size)
        
        with tf.GradientTape() as tape:
            loss = self._compute_loss(
                tf.convert_to_tensor(states, dtype=tf.float32),
                tf.convert_to_tensor(actions, dtype=tf.int32),
                tf.convert_to_tensor(rewards, dtype=tf.float32),
                tf.convert_to_tensor(next_states, dtype=tf.float32),
                tf.convert_to_tensor(dones, dtype=tf.float32)
            )
        
        gradients = tape.gradient(loss, self.qnetwork.trainable_variables)
        self.optimizer.apply_gradients(zip(gradients, self.qnetwork.trainable_variables))
        
        self.training_info['loss_history'].append(float(loss))

def train_model(env, agent, num_episodes, max_steps):
    """训练Q-Learning模型"""
    scores = []
    
    for episode in range(num_episodes):
        state = env.reset()
        score = 0
        
        for step in range(max_steps):
            action = agent.act(state)
            next_state, reward, done, _ = env.step(action)
            agent.step(state, action, reward, next_state, done)
            
            state = next_state
            score += reward
            
            if done:
                break
        
        scores.append(score)
        agent.training_info['reward_history'].append(score)
        agent.training_info['epsilon_history'].append(agent.epsilon)
        
        if (episode + 1) % 100 == 0:
            avg_score = np.mean(scores[-100:])
            print(f'Episode {episode+1}, Average Score: {avg_score:.2f}, Epsilon: {agent.epsilon:.2f}')
    
    return scores

def qlearning_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """训练Q-Learning模型的主函数"""
    # 获取训练参数
    env_name = job_params.get("env_name", "CartPole-v1")
    num_episodes = job_params.get("num_episodes", 1000)
    max_steps = job_params.get("max_steps", 500)
    learning_rate = job_params.get("learning_rate", 0.001)
    gamma = job_params.get("gamma", 0.99)
    
    # 准备环境
    env = gym.make(env_name)
    state_size = env.observation_space.shape[0]
    action_size = env.action_space.n
    
    # 创建agent
    agent = QLearningAgent(
        state_size=state_size,
        action_size=action_size,
        learning_rate=learning_rate,
        gamma=gamma
    )
    
    try:
        # 训练模型
        scores = train_model(env, agent, num_episodes, max_steps)
        
        # 保存完整模型（包括训练状态）
        full_model_path = os.path.join(result_dir, f"{model_name}_full.pkl")
        agent.save(full_model_path)
        
        # 保存用于推理的精简模型
        inference_model_path = os.path.join(result_dir, f"{model_name}_inference.pkl")
        agent.qnetwork.save(inference_model_path)
        
        print(f'训练完成，模型保存到：\n'
              f'完整模型: {full_model_path}\n'
              f'推理模型: {inference_model_path}')
        
        # 保存训练配置和性能指标
        metadata = {
            'env_name': env_name,
            'state_size': state_size,
            'action_size': action_size,
            'hyperparameters': {
                'num_episodes': num_episodes,
                'max_steps': max_steps,
                'learning_rate': learning_rate,
                'gamma': gamma
            },
            'performance': {
                'final_epsilon': agent.epsilon,
                'mean_score_last_100': np.mean(scores[-100:]),
                'max_score': max(scores),
                'training_episodes': len(scores)
            }
        }
        
        metadata_path = os.path.join(result_dir, f"{model_name}_metadata.json")
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=4)
        
    except Exception as e:
        print(f"训练过程中发生错误: {e}")
        return str(e), -1
    
    return None, 0

def load_and_evaluate_model(model_path, env_name, num_episodes=10):
    """加载和评估保存的模型"""
    # 加载模型
    model = QNetwork.load(model_path)
    env = gym.make(env_name)
    
    scores = []
    for episode in range(num_episodes):
        state = env.reset()
        score = 0
        done = False
        
        while not done:
            # 使用模型进行预测
            state_tensor = tf.convert_to_tensor(state[np.newaxis, :], dtype=tf.float32)
            action_values = model(state_tensor)
            action = np.argmax(action_values.numpy()[0])
            
            # 执行动作
            state, reward, done, _ = env.step(action)
            score += reward
        
        scores.append(score)
        print(f"Episode {episode+1}: Score = {score}")
    
    print(f"\nAverage Score over {num_episodes} episodes: {np.mean(scores):.2f}")
    return scores

def model_upload(result_dir,model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".pkl"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".pkl")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "TENSORFLOW",
          "file_name": model_name+".pkl",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "QLearning",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

    
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='tensorflow QLearning Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='QLearning Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='QLearning DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')     
    print("Start QLearning training job, params :\n"+  str(sys.argv) +"\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("QLearning job parmas:" + str(job_params) + "\n")
    dataset = args.dataset
    print("QLearning dataSet:" + str(dataset)   + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("QLearning result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("QLearning factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("QLearning fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("QLearning sparkconf params:" + str(sparkconf) + "\n")
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    if fit_params==None:
        fit_params={}
    
    device = '/GPU:0' if tf.config.list_physical_devices('GPU') else '/CPU:0'
    print("Step 1 QLearning training:\n")
    result,ret_code = qlearning_train(dataset,job_params, model["model_name"],result_dir,fit_params,device)
    if ret_code != 0:
        print("QLearning train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()