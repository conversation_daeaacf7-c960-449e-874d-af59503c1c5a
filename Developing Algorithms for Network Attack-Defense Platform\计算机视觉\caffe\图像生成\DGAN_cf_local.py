import pickle
import requests
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
from torchvision import datasets, transforms
import torchvision.utils
from PIL import Image
import pandas as pd
from tqdm.auto import tqdm
import numpy as np

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"


class VOC2007Dataset(Dataset):
    def __init__(self, voc_root, image_size=64, transform=None):
        """
        VOC2007数据集加载器
        Args:
            voc_root: VOC2007数据集根目录
            image_size: 目标图像大小
            transform: 图像变换
        """
        self.voc_root = voc_root
        self.image_size = image_size
        self.images_dir = os.path.join(voc_root, "JPEGImages")
        
        if transform is None:
            self.transform = transforms.Compose([
                transforms.Resize((image_size, image_size)),
                transforms.RandomHorizontalFlip(),
                transforms.ToTensor(),
                transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])
            ])
        else:
            self.transform = transform
            
        # 获取所有图像文件路径
        self.image_paths = []
        for img_name in os.listdir(self.images_dir):
            if img_name.endswith(('.jpg', '.jpeg', '.png')):
                self.image_paths.append(os.path.join(self.images_dir, img_name))
                
        print(f"找到 {len(self.image_paths)} 张图像")

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        image = Image.open(img_path).convert('RGB')
        
        if self.transform:
            image = self.transform(image)
            
        return image

class DGANGenerator(nn.Module):
    def __init__(self, latent_dim, image_channels=3):
        super(DGANGenerator, self).__init__()
        self.latent_dim = latent_dim
        
        # 初始特征图大小为 4x4
        self.init_size = 4
        self.l1 = nn.Sequential(
            nn.Linear(latent_dim, 256 * self.init_size ** 2)
        )

        # 使用残差块和更深的架构
        self.conv_blocks = nn.Sequential(
            # 4x4 -> 8x8
            nn.BatchNorm2d(256),
            nn.Upsample(scale_factor=2),
            nn.Conv2d(256, 256, 3, stride=1, padding=1),
            nn.BatchNorm2d(256),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Conv2d(256, 256, 3, stride=1, padding=1),
            nn.BatchNorm2d(256),
            nn.LeakyReLU(0.2, inplace=True),
            
            # 8x8 -> 16x16
            nn.Upsample(scale_factor=2),
            nn.Conv2d(256, 128, 3, stride=1, padding=1),
            nn.BatchNorm2d(128),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Conv2d(128, 128, 3, stride=1, padding=1),
            nn.BatchNorm2d(128),
            nn.LeakyReLU(0.2, inplace=True),
            
            # 16x16 -> 32x32
            nn.Upsample(scale_factor=2),
            nn.Conv2d(128, 64, 3, stride=1, padding=1),
            nn.BatchNorm2d(64),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Conv2d(64, 64, 3, stride=1, padding=1),
            nn.BatchNorm2d(64),
            nn.LeakyReLU(0.2, inplace=True),
            
            # 32x32 -> 64x64
            nn.Upsample(scale_factor=2),
            nn.Conv2d(64, 32, 3, stride=1, padding=1),
            nn.BatchNorm2d(32),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Conv2d(32, image_channels, 3, stride=1, padding=1),
            nn.Tanh()
        )

    def forward(self, z):
        out = self.l1(z)
        out = out.view(out.shape[0], 256, self.init_size, self.init_size)
        img = self.conv_blocks(out)
        return img

class DGANDiscriminator(nn.Module):
    def __init__(self, image_channels=3):
        super(DGANDiscriminator, self).__init__()

        def discriminator_block(in_filters, out_filters, bn=True, kernel_size=4):
            block = [
                nn.Conv2d(in_filters, out_filters, kernel_size, 2, 1),
                nn.LeakyReLU(0.2, inplace=True),
                nn.Dropout2d(0.25)
            ]
            if bn:
                block.append(nn.BatchNorm2d(out_filters))
            return block

        self.model = nn.Sequential(
            *discriminator_block(image_channels, 32, bn=False),  # 64x64 -> 32x32
            *discriminator_block(32, 64),                        # 32x32 -> 16x16
            *discriminator_block(64, 128),                       # 16x16 -> 8x8
            *discriminator_block(128, 256),                      # 8x8 -> 4x4
        )

        # 特征匹配层
        self.features = nn.Sequential(
            nn.Linear(256 * 4 * 4, 1024),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Dropout(0.3)
        )
        
        # 判别器输出层
        self.adv_layer = nn.Sequential(
            nn.Linear(1024, 1),
            nn.Sigmoid()
        )

    def forward(self, img):
        features = self.model(img)
        features = features.view(features.shape[0], -1)
        features = self.features(features)
        validity = self.adv_layer(features)
        return validity, features
    
def prepare_voc_data(voc_root, batch_size, image_size):
    """准备VOC2007数据集"""
    dataset = VOC2007Dataset(voc_root, image_size=image_size)
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=2,
        pin_memory=True
    )
    return dataloader

class DGANLoss:
    def __init__(self, device, lambda_fm=10.0):
        self.adversarial_loss = nn.BCELoss()
        self.feature_matching_loss = nn.MSELoss()
        self.device = device
        self.lambda_fm = lambda_fm
        
    def add_noise(self, tensor, noise_factor=0.1):
        noise = torch.randn_like(tensor) * noise_factor
        return tensor + noise
        
    def discriminator_loss(self, real_validity, fake_validity, real_features, fake_features):
        real_label = torch.ones(real_validity.size()).to(self.device)
        fake_label = torch.zeros(fake_validity.size()).to(self.device)
        
        # 添加标签平滑
        real_label = real_label * 0.9
        
        real_loss = self.adversarial_loss(real_validity, real_label)
        fake_loss = self.adversarial_loss(fake_validity, fake_label)
        
        # 特征匹配损失
        fm_loss = self.feature_matching_loss(real_features, fake_features)
        
        d_loss = (real_loss + fake_loss) / 2 + self.lambda_fm * fm_loss
        return d_loss
    
    def generator_loss(self, fake_validity, real_features, fake_features):
        real_label = torch.ones(fake_validity.size()).to(self.device)
        
        # 生成器对抗损失
        g_loss = self.adversarial_loss(fake_validity, real_label)
        
        # 特征匹配损失
        fm_loss = self.feature_matching_loss(real_features, fake_features)
        
        return g_loss + self.lambda_fm * fm_loss

class UniversalImageDataset(Dataset):
    def __init__(self, data_dir, transform=None):
        self.data_dir = data_dir
        self.transform = transform
        self.image_paths = self._get_image_paths()

    def _get_image_paths(self):
        image_paths = []
        for root, _, files in os.walk(self.data_dir):
            for file in files:
                if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                    image_paths.append(os.path.join(root, file))
        return image_paths

    def __len__(self):
        return len(self.image_paths)

    def __getitem__(self, idx):
        img_path = self.image_paths[idx]
        image = Image.open(img_path).convert('RGB')
        
        if self.transform:
            image = self.transform(image)
        
        return image

def prepare_data(data_dir, image_size, batch_size):
    transform = transforms.Compose([
        transforms.Resize((image_size, image_size)),
        transforms.ToTensor(),
        transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])
    ])

    dataset = UniversalImageDataset(data_dir, transform=transform)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True, num_workers=2)
    
    return dataloader

def train_dgan(dataloader, generator, discriminator, criterion, g_optimizer, d_optimizer, 
               num_epochs, latent_dim, device, sample_interval=100, result_dir='results'):
    os.makedirs(result_dir, exist_ok=True)
    
    # 创建固定噪声用于可视化训练进程
    fixed_noise = torch.randn(16, latent_dim).to(device)
    
    for epoch in range(num_epochs):
        pbar = tqdm(enumerate(dataloader), desc=f"Epoch {epoch+1}/{num_epochs}")
        
        for i, real_imgs in pbar:
            batch_size = real_imgs.size(0)
            
            # 配置真实图像和噪声向量
            real_imgs = real_imgs.to(device)
            
            # 添加噪声到真实图像
            noisy_real_imgs = criterion.add_noise(real_imgs)
            z = torch.randn(batch_size, latent_dim).to(device)
            
            # -----------------
            #  训练判别器
            # -----------------
            d_optimizer.zero_grad()
            
            # 生成一批假图像
            fake_imgs = generator(z)
            
            # 判别器对真实和生成图像的判断
            real_validity, real_features = discriminator(noisy_real_imgs)
            fake_validity, fake_features = discriminator(fake_imgs.detach())
            
            # 计算判别器损失
            d_loss = criterion.discriminator_loss(real_validity, fake_validity, 
                                                real_features, fake_features)
            
            # 反向传播和优化
            d_loss.backward()
            d_optimizer.step()
            
            # -----------------
            #  训练生成器
            # -----------------
            g_optimizer.zero_grad()
            
            # 重新生成假图像和获取特征（因为之前的计算图已经分离）
            fake_imgs = generator(z)
            fake_validity, fake_features = discriminator(fake_imgs)
            
            # 保存real_features的副本，避免在backward时访问已释放的张量
            with torch.no_grad():
                real_features_copy = real_features.clone()
            
            # 计算生成器损失
            g_loss = criterion.generator_loss(fake_validity, real_features_copy, fake_features)
            
            # 反向传播和优化
            g_loss.backward()
            g_optimizer.step()
            
            # 更新进度条
            pbar.set_postfix({
                'D_loss': f'{d_loss.item():.4f}',
                'G_loss': f'{g_loss.item():.4f}'
            })
            
            # 保存采样结果
            batches_done = epoch * len(dataloader) + i
            if batches_done % sample_interval == 0:
                with torch.no_grad():
                    fake_imgs = generator(fixed_noise)
                    save_sample_images(fake_imgs, epoch, batches_done, result_dir)

def save_sample_images(gen_imgs, epoch, batches_done, result_dir, n_row=4):
    """保存生成的样本图像"""
    gen_imgs = 0.5 * (gen_imgs + 1.0)  # 反归一化
    gen_imgs = gen_imgs.clamp(0, 1)
    
    save_path = os.path.join(result_dir, f'samples_epoch_{epoch}_batch_{batches_done}.png')
    torchvision.utils.save_image(gen_imgs, save_path, nrow=n_row, normalize=False)

def dgan_train(dataset, job_params, model_name, result_dir, fit_params=None, device="cpu"):
    """
    DGAN训练函数
    参数说明:
    - latent_dim: 潜在空间维度
    - image_size: 生成图像的大小
    - num_epochs: 训练轮数
    - learning_rate: 学习率
    - batch_size: 批处理大小
    """
    # 解析参数
    latent_dim = job_params.get("latent_dim", 100)
    image_size = job_params.get("image_size", 64)
    num_epochs = job_params.get("num_epochs", 100)
    learning_rate = job_params.get("learning_rate", 0.0002)
    batch_size = job_params.get("batch_size", 64)

    # 准备数据
    training_data_path = "/workspace/" + dataset["training_data_path"]
    #dataloader = prepare_data(training_data_path, image_size, batch_size)
    dataloader = prepare_voc_data(training_data_path, batch_size, image_size)
    
    # 初始化模型
    generator = DGANGenerator(latent_dim).to(device)
    discriminator = DGANDiscriminator().to(device)
    # 损失函数和优化器
    criterion = DGANLoss(device)
    
    # 优化器
    g_optimizer = optim.Adam(generator.parameters(), lr=learning_rate, betas=(0.5, 0.999))
    d_optimizer = optim.Adam(discriminator.parameters(), lr=learning_rate, betas=(0.5, 0.999))
    
    try:
        # 创建结果目录
        os.makedirs(result_dir, exist_ok=True)
        
        # 训练模型
        train_dgan(dataloader, generator, discriminator, criterion, 
                  g_optimizer, d_optimizer, num_epochs, latent_dim, 
                  device, sample_interval=100, result_dir=result_dir)
        
        # 保存最终模型
        torch.save(generator.state_dict(), os.path.join(result_dir, f"{model_name}_generator.pth"))
        torch.save(discriminator.state_dict(), os.path.join(result_dir, f"{model_name}_discriminator.pth"))
        print(f'训练完成，模型保存到 {result_dir}')
        
    except Exception as e:
        print(f"训练过程中发生错误: {e}")
        return str(e), -1

    return None, 0

def model_upload(result_dir, model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".pth"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".pth")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1

def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "DGAN",
          "file_name": model_name+".pth",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "DGAN",
        "model_usage": "Generation"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='pytorch DGAN Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='DGAN Job Params, set all params in dict')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='DGAN DataSet, set as a dict')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost',nargs='?', const=None,dest='nodehost', type=str,default="**************" ,
                    help='nodehost params')
    
    print("Start DGAN training job, params:\n" + str(sys.argv) + "\n")
    args = parser.parse_args()
    
    job_params = args.job_params
    print("DGAN job params:" + str(job_params) + "\n")
    dataset = args.dataset
    print("DGAN dataset:" + str(dataset) + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("DGAN result dir:" + result_dir + "\n")    
    factory_name = args.factory_name
    print("DGAN factory name:" + factory_name + "\n")    
    fit_params = args.fit_params
    print("DGAN fit params:" + str(fit_params) + "\n")  
    sparkconf = json.loads(args.sparkconf)
    print("DGAN sparkconf params:" + str(sparkconf) + "\n")
    
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
    
    if fit_params is None:
        fit_params = {}
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    print("Step 1 DGAN training:\n")
    result, ret_code = dgan_train(dataset, job_params, model["model_name"], result_dir, fit_params,device)
    if ret_code != 0:
        print("FCN train err, stop job....\n")
        print("Error Msg:"+result+"\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    
    print("Step 2 Model Upload to MinIO: \n")
    result,ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result,ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: "+ result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result,ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: "+ result+"\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()