2025-05-22 17:43:28,795 - GraphSAGE_PT_Local_Mode - INFO - Logging to console and file: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\graphsage_pt_local_mode_training_20250522_174328.log
2025-05-22 17:43:28,795 - GraphSAGE_PT_Local_Mode - INFO - GraphSAGE PyTorch Local Mode training script initialized.
2025-05-22 17:43:28,796 - GraphSAGE_PT_Local_Mode - INFO - PyTorch version: 2.4.1+cpu
2025-05-22 17:43:28,796 - GraphSAGE_PT_Local_Mode - INFO - CLI Arguments: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'label_column': 'Label', 'data_format': 'pkl', 'hidden_size': 64, 'output_size': 6, 'num_layers': 2, 'dropout_rate': 0.5, 'edge_strategy': 'fully_connected_per_sample', 'k_neighbors': 5, 'num_epochs': 3, 'learning_rate': 0.001, 'batch_size': 32, 'test_split_size': 0.2, 'random_state': 42, 'normalize': True, 'scaler_type': 'standard', 'handle_imbalance': False, 'stratify_split': True, 'numeric_impute_strategy': 'mean', 'categorical_impute_strategy': 'most_frequent', 'use_cv': False, 'cv_folds': 5, 'result_dir': 'E:\\work\\增值项目\\网络攻击博弈平台\\2025.3\\bypt\\desin\\results\\results_graphsage_pt_local', 'model_name': 'GraphSAGE_PT_Local_Model', 'log_level': 'INFO', 'force_cpu': False}
2025-05-22 17:43:28,797 - GraphSAGE_PT_Local_Mode - INFO - Using device: cpu
2025-05-22 17:43:28,802 - GraphSAGE_PT_Local_Mode.DataLoad - INFO - Loading data from E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl, format: pkl
2025-05-22 17:43:29,348 - GraphSAGE_PT_Local_Mode.DataLoad - INFO - Data loaded. X shape: (692703, 84), y shape: (692703,)
2025-05-22 17:43:29,380 - GraphSAGE_PT_Local_Mode.DataLoad - INFO - Label distribution:
Label
BENIGN              440031
DoS Hulk            231073
DoS GoldenEye        10293
DoS slowloris         5796
DoS Slowhttptest      5499
Heartbleed              11
Name: count, dtype: int64
2025-05-22 17:43:29,534 - GraphSAGE_PT_Local_Mode - INFO - Standard train/test split mode selected for GraphSAGE (PT).
2025-05-22 17:43:36,055 - GraphSAGE_PT_Local_Mode - INFO - Scaler for train/test split saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\GraphSAGE_PT_Local_Model_scaler.joblib
2025-05-22 17:43:36,059 - GraphSAGE_PT_Local_Mode - INFO - Label encoder for train/test split saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\GraphSAGE_PT_Local_Model_label_encoder.joblib
2025-05-22 17:43:36,236 - GraphSAGE_PT_Local_Mode - INFO - Categorical encoder for train/test split saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\GraphSAGE_PT_Local_Model_cat_encoder.joblib
2025-05-22 17:43:38,698 - GraphSAGE_PT_Local_Mode - INFO - Preprocessing info for train/test split saved to: E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\results\results_graphsage_pt_local\GraphSAGE_PT_Local_Model_preproc_info.json
2025-05-22 17:43:39,768 - GraphSAGE_PT_Local_Mode - INFO - Data split: Train (554162, 84), Test (138541, 84)
2025-05-22 18:01:24,524 - GraphSAGE_PT_Local_Mode - CRITICAL - Unexpected critical error: [enforce fail at alloc_cpu.cpp:114] data. DefaultCPUAllocator: not enough memory: you tried to allocate 111552 bytes.
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GraphSAGE_pt_local_mode.py", line 870, in graphsage_train_pt_local_mode
    train_pyg_list = create_pyg_data_list_local(X_train_np, y_train_np, graph_opts)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GraphSAGE_pt_local_mode.py", line 328, in create_pyg_data_list_local
    edge_index = torch.tensor(edge_list_coo, dtype=torch.long) if edge_list_coo else torch.empty((2,0), dtype=torch.long)
RuntimeError: [enforce fail at alloc_cpu.cpp:114] data. DefaultCPUAllocator: not enough memory: you tried to allocate 111552 bytes.
