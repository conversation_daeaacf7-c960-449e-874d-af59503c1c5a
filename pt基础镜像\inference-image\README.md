# BiLSTM推理镜像使用指南

## 📋 概述

这是一个基于两阶段构建策略的BiLSTM推理服务，包含：

1. **基础镜像** (`pytorch-inference:base-latest`) - 只包含PyTorch环境和依赖
2. **推理镜像** (`bilstm-inference:latest`) - 在基础镜像上添加模型文件和推理脚本

**✨ 特色功能：**
- 🔒 **模型内嵌**: 模型文件直接复制到镜像中，无需外部挂载
- 🔄 **动态切换**: 支持运行时在内嵌的多个模型之间切换
- 📦 **自包含**: 镜像包含所有必要文件，部署简单
- 🚀 **即开即用**: 无需额外配置，直接部署运行

## 🏗️ 构建镜像

### 方式一：使用构建脚本（推荐）

```bash
# 构建推理镜像（会自动检查并构建基础镜像）
./build.sh

# 构建选项
./build.sh -m both          # 构建基础镜像和推理镜像
./build.sh -m base          # 只构建基础镜像
./build.sh -m inference     # 只构建推理镜像
./build.sh --no-cache       # 无缓存构建
./build.sh --push           # 构建后推送到仓库
```

### 方式二：手动构建

```bash
# 1. 构建基础镜像
cd ../
docker build -t pytorch-inference:base-latest -f Dockerfile .

# 2. 构建推理镜像
cd inference-image/
docker build --build-arg BASE_IMAGE=pytorch-inference:base-latest \
             -t bilstm-inference:latest \
             -f Dockerfile .
```

## 🚀 运行镜像

### 健康检查

```bash
docker run --rm bilstm-inference:latest health
```

### 测试模式

```bash
docker run --rm bilstm-inference:latest test
```

### 开发服务器模式

```bash
docker run -p 5000:5000 bilstm-inference:latest server
```

### Seldon Core模式

```bash
docker run -p 5000:5000 \
  -e SELDON_MODE=true \
  -e MODEL_NAME=BiLSTM_Example \
  bilstm-inference:latest
```

## 🎯 Seldon Core部署

### 部署到Kubernetes

```bash
# 应用部署配置
kubectl apply -f seldon-deployment.yaml

# 检查部署状态
kubectl get seldondeployments -n seldon-system
kubectl get pods -n seldon-system -l app=bilstm-inference

# 查看日志
kubectl logs -n seldon-system -l app=bilstm-inference -f
```

### 测试推理服务

```bash
# 获取服务地址
export SELDON_URL=$(kubectl get svc bilstm-inference-service -n seldon-system -o jsonpath='{.status.loadBalancer.ingress[0].ip}'):8000

# 或者使用端口转发
kubectl port-forward svc/bilstm-inference-service -n seldon-system 8000:8000

# 测试推理
curl -X POST http://localhost:8000/api/v1.0/predictions \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "ndarray": [[0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0]]
    }
  }'
```

## 📁 目录结构

```
inference-image/
├── Dockerfile              # 推理镜像构建文件
├── build.sh                # 构建脚本
├── start.py                # 启动脚本
├── seldon-deployment.yaml  # Seldon部署配置
├── README.md               # 使用指南
├── .dockerignore           # 构建优化
└── models/                 # 模型文件目录（内嵌到镜像中）
    ├── BiLSTM_Example/
    │   ├── model.pth       # PyTorch模型文件
    │   ├── model_config.json # 模型配置
    │   └── scaler.pkl      # 预处理器
    ├── BiLSTM_NetworkTraffic/
    │   ├── model.pth
    │   ├── model_config.json
    │   └── scaler.pkl
    └── BiLSTM_SmallModel/
        ├── model.pth
        ├── model_config.json
        └── scaler.pkl
```

## 🔧 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `MODEL_NAME` | `BiLSTM_Example` | 模型名称 |
| `SELECTED_MODEL_NAME` | `MODEL_NAME` | 选择的模型名称 |
| `API_TYPE` | `REST` | API类型 |
| `SERVICE_TYPE` | `MODEL` | 服务类型 |
| `SELDON_MODE` | `true` | 是否为Seldon模式 |
| `MODEL_LIBRARY_DIR` | `/app/models` | 模型库目录 |
| `STARTUP_MODE` | `seldon` | 启动模式 |

## 🔍 启动模式

### 1. Seldon模式 (`seldon`)
- 用于Seldon Core部署
- 自动初始化模型并等待请求
- 适合生产环境

### 2. 测试模式 (`test`)
- 快速测试模型加载和推理
- 适合开发调试

### 3. 服务器模式 (`server`)
- 启动独立的Flask开发服务器
- 适合本地开发测试

### 4. 健康检查模式 (`health`)
- 执行健康检查并退出
- 适合容器健康检查

## 🛠️ 自定义模型

### 添加新模型

1. 在 `models/` 目录下创建新的模型目录：
```
models/
└── YourModel_v1/
    ├── model.pth          # PyTorch模型文件
    ├── model_config.json  # 模型配置
    └── scaler.pkl         # 数据预处理器(可选)
```

2. 更新模型配置文件：
```json
{
    "model_name": "YourModel_v1",
    "model_type": "BiLSTM",
    "model_config": {
        "input_size": 10,
        "hidden_size": 64,
        "num_layers": 2,
        "num_classes": 3
    }
}
```

3. 重新构建镜像：
```bash
./build.sh
```

### 切换模型

```bash
# 通过环境变量
docker run -e SELECTED_MODEL_NAME=YourModel_v1 bilstm-inference:latest

# 通过API（运行时切换）
curl -X POST http://localhost:5000/models/switch \
  -H "Content-Type: application/json" \
  -d '{"model_name": "YourModel_v1"}'
```

## 🐛 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件是否存在
   - 验证模型配置文件格式
   - 查看容器日志

2. **健康检查失败**
   - 确认服务端口正确暴露
   - 检查模型初始化状态
   - 验证依赖库版本

3. **Seldon部署失败**
   - 检查镜像是否正确推送
   - 验证命名空间和RBAC权限
   - 查看Pod事件和日志

### 日志查看

```bash
# Docker容器日志
docker logs <container_id>

# Kubernetes Pod日志
kubectl logs -n seldon-system <pod_name> -f

# 详细调试
kubectl describe pod -n seldon-system <pod_name>
```

## 📊 监控和指标

### 健康检查端点

- `GET /health` - 基本健康检查
- `GET /metrics` - 性能指标
- `GET /models` - 可用模型列表

### 管理端点

- `POST /models/switch` - 切换模型
- `POST /models/reload` - 重新加载模型

## 🔐 安全注意事项

1. 镜像使用非root用户运行
2. 建议在生产环境中配置资源限制
3. 使用私有镜像仓库存储敏感模型
4. 定期更新基础镜像和依赖

## 📚 相关文档

- [Seldon Core官方文档](https://docs.seldon.io/)
- [PyTorch官方文档](https://pytorch.org/docs/)
- [Docker构建最佳实践](https://docs.docker.com/develop/dev-best-practices/) 