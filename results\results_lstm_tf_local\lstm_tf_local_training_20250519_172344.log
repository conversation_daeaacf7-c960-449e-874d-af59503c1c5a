2025-05-19 17:23:44,090 - LSTM_TF_Local_Main - INFO - Starting LSTM TensorFlow Local Mode main script.
2025-05-19 17:23:44,091 - LSTM_TF_Local_Main - INFO - TensorFlow version: 2.13.0
2025-05-19 17:23:44,091 - LSTM_TF_Local_Main - INFO - Received arguments: Namespace(batch_size=32, cv_folds=5, data_format='pkl', handle_imbalance=False, hidden_size=128, input_file='E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', input_size=84, label_column='Label', learning_rate=0.001, model_name='LSTM_TF_Local_Model', normalize=True, num_epochs=1, num_layers=2, output_size=6, result_dir='./results/results_lstm_tf_local', stratify=False, test_split_size=0.3, use_cv=False)
2025-05-19 17:23:44,093 - LSTM_TF_Local_Main - INFO - Effective preprocessing options: {
  "test_size": 0.3,
  "random_state": 42,
  "normalize": true,
  "encode_labels": true,
  "handle_imbalance": false,
  "stratify": false,
  "drop_missing": false,
  "fill_method": "mean",
  "handle_outliers": true
}
2025-05-19 17:23:44,094 - LSTM_TF_Local_Main - INFO - LSTM TensorFlow training (local mode) function started.
2025-05-19 17:23:44,094 - LSTM_TF_Local_Main - INFO - No GPU found, using CPU.
2025-05-19 17:23:44,094 - LSTM_TF_Local_Main - INFO - Run parameters: Input File='E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', Model Name='LSTM_TF_Local_Model'
2025-05-19 17:23:44,095 - LSTM_TF_Local_Main - INFO - Model Hyperparameters: Input Size=84, Hidden Size=128, Output Classes=6, Layers=2
2025-05-19 17:23:44,095 - LSTM_TF_Local_Main - INFO - Training Parameters: Epochs=1, LR=0.001, Batch Size=32, CV Mode=False
2025-05-19 17:23:44,095 - LSTM_TF_Local_Main - INFO - Preprocessing Options: {
  "test_size": 0.3,
  "random_state": 42,
  "normalize": true,
  "encode_labels": true,
  "handle_imbalance": false,
  "stratify": false,
  "drop_missing": false,
  "fill_method": "mean",
  "handle_outliers": true
}
2025-05-19 17:23:44,096 - LSTM_TF_Local_Main - INFO - Loading data from E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl with format pkl
2025-05-19 17:23:44,447 - LSTM_TF_Local_Main - INFO - Data loaded as DataFrame with shape (692703, 85)
2025-05-19 17:23:44,576 - LSTM_TF_Local_Main - INFO - Features shape: (692703, 84), Labels shape: (692703,)
2025-05-19 17:23:44,606 - LSTM_TF_Local_Main - INFO - Label distribution: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-19 17:23:44,647 - LSTM_TF_Local_Main - INFO - Original classes for reporting (from data): ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-19 17:23:44,648 - LSTM_TF_Local_Main - INFO - Using Keras LSTM standard train/test split.
2025-05-19 17:23:44,851 - LSTM_TF_Local_Main - INFO - Handling missing values in features
2025-05-19 17:23:45,201 - LSTM_TF_Local_Main - INFO - Missing values: 1008 -> 0
2025-05-19 17:23:45,202 - LSTM_TF_Local_Main - INFO - Encoding labels
2025-05-19 17:23:45,307 - LSTM_TF_Local_Main - INFO - Encoded 6 classes: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']")
2025-05-19 17:23:45,334 - LSTM_TF_Local_Main - INFO - Encoding 4 categorical features: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-19 17:23:49,233 - LSTM_TF_Local_Main - INFO - Checking and handling infinite values (replacing with NaN, then re-imputing).
2025-05-19 17:23:49,486 - LSTM_TF_Local_Main - INFO - Normalizing features
2025-05-19 17:23:51,670 - LSTM_TF_Local_Main - INFO - Data split into: Train X(484892, 84), y(484892,); Test X(207811, 84), y(207811,)
2025-05-19 17:23:51,730 - LSTM_TF_Local_Main - INFO - LSTM TF Preprocessing info saved to ./results/results_lstm_tf_local\preprocessing_info_lstm_tf.json
2025-05-19 17:23:51,827 - LSTM_TF_Local_Main - INFO - Prepared TF Datasets: Train X(484892, 1, 84), Y(484892,); Test X(207811, 1, 84), Y(207811,). Batch size: 32
2025-05-19 17:23:51,977 - LSTM_TF_Local_Main - INFO - Starting Keras LSTM model training (standard split).
2025-05-19 17:23:51,990 - LSTM_TF_Local_Main - INFO - Best LSTM model weights will be saved to: ./results/results_lstm_tf_local\best_model_lstm.weights.h5 based on val_loss.
2025-05-19 17:23:51,990 - LSTM_TF_Local_Main - INFO - Starting Keras LSTM model training for 1 epochs.
2025-05-19 17:24:59,525 - LSTM_TF_Local_Main - INFO - Saved final LSTM model weights to: ./results/results_lstm_tf_local\final_model_lstm.weights.h5
2025-05-19 17:25:00,533 - LSTM_TF_Local_Main - INFO - Training history plots saved to ./results/results_lstm_tf_local\plots_lstm_tf with suffix '_lstm_tf'
2025-05-19 17:25:00,534 - LSTM_TF_Local_Main - INFO - Evaluating Keras LSTM model on test set.
2025-05-19 17:25:00,534 - LSTM_TF_Local_Main - INFO - Loading best LSTM weights from ./results/results_lstm_tf_local\best_model_lstm.weights.h5 for final testing.
2025-05-19 17:25:01,249 - absl - WARNING - Skipping variable loading for optimizer 'Adam', because it has 1 variables whereas the saved optimizer has 17 variables. 
2025-05-19 17:25:01,251 - LSTM_TF_Local_Main - INFO - Starting LSTM Keras model testing.
2025-05-19 17:25:18,433 - LSTM_TF_Local_Main - INFO - Classification Report (LSTM TF):
                  precision    recall  f1-score   support

          BENIGN       1.00      1.00      1.00    132082
   DoS GoldenEye       1.00      0.99      0.99      3047
        DoS Hulk       1.00      1.00      1.00     69364
DoS Slowhttptest       0.99      0.94      0.96      1644
   DoS slowloris       0.94      0.99      0.97      1670
      Heartbleed       1.00      1.00      1.00         4

        accuracy                           1.00    207811
       macro avg       0.99      0.99      0.99    207811
    weighted avg       1.00      1.00      1.00    207811

2025-05-19 17:25:19,135 - LSTM_TF_Local_Main - INFO - Confusion matrix plot saved to ./results/results_lstm_tf_local\plots_lstm_tf with suffix '_lstm_tf'
2025-05-19 17:25:19,135 - LSTM_TF_Local_Main - INFO - LSTM TF Classification report saved to ./results/results_lstm_tf_local\classification_report_lstm_tf.txt
2025-05-19 17:25:19,136 - LSTM_TF_Local_Main - INFO - LSTM TF Test Metrics: Acc=0.9992, Precision=0.9992, Recall=0.9992, F1=0.9992
2025-05-19 17:25:19,139 - LSTM_TF_Local_Main - INFO - LSTM TF Training results saved to ./results/results_lstm_tf_local\training_results_lstm_tf.json
2025-05-19 17:25:19,140 - LSTM_TF_Local_Main - INFO - LSTM TensorFlow training (local mode) finished successfully.
2025-05-19 17:25:19,243 - LSTM_TF_Local_Main - INFO - LSTM TF training process finished. Results logged and saved in ./results/results_lstm_tf_local
2025-05-19 17:25:19,244 - LSTM_TF_Local_Main - INFO - Keras LSTM Model weights saved at: ./results/results_lstm_tf_local\best_model_lstm.weights.h5
2025-05-19 17:25:19,244 - LSTM_TF_Local_Main - INFO - Keras LSTM Test set accuracy: 99.92%
2025-05-19 17:25:19,245 - LSTM_TF_Local_Main - INFO - Keras LSTM Test set F1 score: 0.9992
2025-05-19 17:25:19,245 - LSTM_TF_Local_Main - INFO - LSTM TF Local Mode script execution completed successfully.
