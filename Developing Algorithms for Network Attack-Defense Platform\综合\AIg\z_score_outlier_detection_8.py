"""Z-Score 异常检测"""

import sys
import pandas as pd
import numpy as np

def z_score_outlier_detection(input_file, target_column, threshold=3):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - target_column: 目标变量的列名
    - threshold: 异常值检测的 Z-score 阈值，默认为3
    """
    data = pd.read_csv(input_file)
    X = data.drop(target_column, axis=1)
    
    z_scores = np.abs((X - X.mean()) / X.std())
    outliers = (z_scores > threshold).any(axis=1)
    
    output_file = 'z_score_outliers.csv'
    data[outliers].to_csv(output_file, index=False)
    print(f"Z-score outlier detection completed. Outliers saved to {output_file}")

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python z_score_outlier_detection.py <input_file> <target_column> <threshold>")
        sys.exit(1)
    input_file, target_column, threshold = sys.argv[1], sys.argv[2], sys.argv[3]
    z_score_outlier_detection(input_file, target_column, float(threshold))
