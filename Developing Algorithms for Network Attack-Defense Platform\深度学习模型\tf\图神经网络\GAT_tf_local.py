import tensorflow as tf
import numpy as np
import pickle
import requests
import joblib
import sys
import argparse
import json
from minio import Minio
import uuid
import os

MINIO_URL = "minio-prophecis.prophecis:9000"

MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"


MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"



class GraphAttentionLayer(tf.keras.layers.Layer):
    def __init__(self, out_features, dropout, alpha, concat=True):
        super(GraphAttentionLayer, self).__init__()
        self.dropout = dropout
        self.out_features = out_features
        self.alpha = alpha
        self.concat = concat
        
        # Initialize weights with Xavier/Glorot initialization
        self.W = None  # Will be initialized in build()
        self.a = None  # Will be initialized in build()
        
    def build(self, input_shape):
        in_features = input_shape[0][-1]
        
        initializer = tf.keras.initializers.GlorotUniform()
        self.W = self.add_weight("W",
                                shape=(in_features, self.out_features),
                                initializer=initializer,
                                trainable=True)
        self.a = self.add_weight("a",
                                shape=(2 * self.out_features, 1),
                                initializer=initializer,
                                trainable=True)
        
        super(GraphAttentionLayer, self).build(input_shape)
    
    def _prepare_attentional_mechanism_input(self, Wh):
        Wh1 = tf.matmul(Wh, self.a[:self.out_features])
        Wh2 = tf.matmul(Wh, self.a[self.out_features:])
        
        # Broadcast to create attention coefficients
        batch_size = tf.shape(Wh)[0]
        N = tf.shape(Wh)[0]
        
        Wh1_repeated = tf.tile(tf.expand_dims(Wh1, 1), [1, N, 1])
        Wh2_repeated = tf.tile(tf.expand_dims(Wh2, 0), [N, 1, 1])
        
        return tf.concat([Wh1_repeated, Wh2_repeated], axis=2)
    
    def call(self, inputs, training=None):
        h, adj = inputs
        
        # Apply dropout to input in training
        if training:
            h = tf.nn.dropout(h, self.dropout)
        
        # Linear transformation
        Wh = tf.matmul(h, self.W)
        
        # Attention coefficients
        a_input = self._prepare_attentional_mechanism_input(Wh)
        e = tf.squeeze(tf.matmul(a_input, self.a))
        
        # Mask attention coefficients using adjacency matrix
        mask = -10e9 * (1.0 - adj)
        attention = tf.nn.softmax(e + mask, axis=1)
        
        if training:
            attention = tf.nn.dropout(attention, self.dropout)
            
        h_prime = tf.matmul(attention, Wh)
        
        if self.concat:
            return tf.nn.elu(h_prime)
        else:
            return h_prime

class GAT(tf.keras.Model):
    def __init__(self, nfeat, nhid, nclass, dropout, alpha, nheads):
        super(GAT, self).__init__()
        self.dropout = dropout
        
        # Multiple attention heads for first layer
        self.attentions = [
            GraphAttentionLayer(nhid, dropout=dropout, alpha=alpha, concat=True)
            for _ in range(nheads)
        ]
        
        # Output attention layer
        self.out_att = GraphAttentionLayer(nclass, dropout=dropout, alpha=alpha, concat=False)
        
    def call(self, inputs, training=None):
        x, adj = inputs
        
        # Apply dropout to input in training
        if training:
            x = tf.nn.dropout(x, self.dropout)
        
        # Concatenate attention heads
        x = tf.concat([attention([x, adj], training=training) for attention in self.attentions], axis=-1)
        
        if training:
            x = tf.nn.dropout(x, self.dropout)
        
        # Output layer
        x = self.out_att([x, adj], training=training)
        
        return tf.nn.log_softmax(x, axis=1)

def gat_train(dataset, job_params, model_name, result_dir, fit_params=None):
    """
    参数说明：
    - nfeat: 输入特征的维度。
    - nhid: 隐藏层特征的维度。
    - nclass: 输出类别的数目。
    - dropout: Dropout概率。
    - alpha: LeakyReLU的负斜率。
    - nheads: 注意力头的数量。
    - num_epochs: 训练的轮数。
    - learning_rate: 学习率。
    """
    
    training_data_path = "/workspace/" + dataset["training_data_path"]
    with open(training_data_path, 'rb') as f:
        training_data, validation_data, test_data = pickle.load(f, encoding='bytes')
    
    train_x, train_adj, train_y = training_data
    test_x, test_adj, test_y = test_data
    
    # 转换为TensorFlow张量
    train_x = tf.convert_to_tensor(train_x, dtype=tf.float32)
    train_adj = tf.convert_to_tensor(train_adj, dtype=tf.float32)
    train_y = tf.convert_to_tensor(train_y, dtype=tf.int64)
    
    # 初始化GAT模型
    model = GAT(
        nfeat=job_params["nfeat"],
        nhid=job_params["nhid"],
        nclass=job_params["nclass"],
        dropout=job_params["dropout"],
        alpha=job_params["alpha"],
        nheads=job_params["nheads"]
    )
    
    optimizer = tf.keras.optimizers.Adam(learning_rate=job_params["learning_rate"])
    loss_fn = tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True)
    
    # 训练循环
    @tf.function
    def train_step(x, adj, y):
        with tf.GradientTape() as tape:
            predictions = model([x, adj], training=True)
            loss = loss_fn(y, predictions)
        gradients = tape.gradient(loss, model.trainable_variables)
        optimizer.apply_gradients(zip(gradients, model.trainable_variables))
        return loss
    
    # 训练模型
    for epoch in range(job_params["num_epochs"]):
        loss = train_step(train_x, train_adj, train_y)
        if epoch % 10 == 0:
            print(f"Epoch {epoch}, Loss: {loss:.4f}")
    
    # 保存模型
    model_path = f"{result_dir}/{model_name}.h5"
    model.save(model_path)
    print(f'GAT训练完成，模型保存到 {model_path}')
    
    return None, 0


    
def model_upload(result_dir,model_name):    
    minioClient = Minio(MINIO_URL,
                  access_key='AKIAIOSFODNN7EXAMPLE',
                  secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                  secure=False)
    try:
        obj_name = str(uuid.uuid1()) 
        upload_path = obj_name + "/" + model_name + ".h5"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir+"/"+model_name+".h5")
        result = {"source":source}        
        return result, 0
    except Exception as err:
        print(err)
        return None, -1
    
def model_register(model_name, source, group_id, headers):    
    params = {
          "model_name": model_name,
          "model_type": "TENSORFLOW",
          "file_name": model_name+".h5",
          "s3_path": source,
          "group_id": int(float(group_id)),
          "training_id": model_name,
          "training_flag": 1,
        }
    
    r = requests.post(MODEL_FACTORY_URL+MODEL_ADD_URL,data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1

def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0        
  
    params = {
        "factory_name": factory_name,
        "model_type": "Logistic_Regression",
        "model_usage": "Classification"
    }
    r = requests.post(MODEL_FACTORY_URL+MODEL_PUSH_URL+"/"+str(model_version_id),data=json.dumps(params),headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1
    

def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY:"20210803",
        MLSS_AUTH_TYPE_KEY:MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY:MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY:MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY:user_id
        }
    return headers

    
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='tensorflow GAT Train.')  
    parser.add_argument('--job_params', dest='job_params', type=json.loads,
                    help='GAT Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads,
                    help='GAT DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads,
                    help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str,
                    help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str,
                    help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads,
                    help='fit params')
    parser.add_argument('--sparkconf', dest='sparkconf', type=str,
                    default='{"spark.driver.port":30009,"spark.blockManager.port":30010}',
                    help='sparkconf params')
    parser.add_argument('--nodehost', nargs='?', const=None, dest='nodehost', 
                    type=str, default="**************",
                    help='nodehost params')     
    
    print("Start GAT training job, params:\n" + str(sys.argv) + "\n")
    
    # Parse arguments
    args = parser.parse_args()
    job_params = args.job_params
    print("GAT job params:" + str(job_params) + "\n")
    
    dataset = args.dataset
    print("GAT dataSet:" + str(dataset) + "\n")
    
    model = args.model
    print(model)
    
    result_dir = args.result_dir
    print("GAT result dir:" + result_dir + "\n")    
    
    factory_name = args.factory_name
    print("GAT factory name:" + factory_name + "\n")    
    
    fit_params = args.fit_params
    print("GAT fit params:" + str(fit_params) + "\n")  
    
    sparkconf = json.loads(args.sparkconf)
    print("GAT sparkconf params:" + str(sparkconf) + "\n")
    
    nodehost = args.nodehost
    if nodehost is not None:
        print(f"Node host is set to {nodehost}")
    else:
        print("Node host is not provided or is empty, using default settings.")
        
    if fit_params is None:
        fit_params = {}
    
    # Set GPU memory growth to avoid taking all memory
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if gpus:
        try:
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            print("GPU memory growth enabled")
        except RuntimeError as e:
            print(e)
     
    print("Step 1 GAT training:\n")
    result, ret_code = gat_train(dataset, job_params, model["model_name"], 
                                result_dir, fit_params)
    if ret_code != 0:
        print("GAT train err, stop job....\n")
        print("Error Msg:" + result + "\n")
        sys.exit(-1)
    print("Training finish, start storage model...\n")
    
    print("Step 2 Model Upload to MinIO: \n")
    result, ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("model storage err, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Storage model finish, start model registe...\n")
    
    print("Step 3 Model Registe:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result, ret_code = model_register(model["model_name"], source, 
                                    model["group_id"], headers)
    if ret_code != 0:
        print("model register, stop job....,err msg: " + result)
        sys.exit(-1)
    print("Registe model finish, start model push...")

    print("Step 4 Model Push, start push model to FPS and send RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result, ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("model push error, stop job....err msg: " + result + "\n")
    print("Model push finish, job complete...\n")
    print("Job End..\n")
    sys.exit()
   
  
