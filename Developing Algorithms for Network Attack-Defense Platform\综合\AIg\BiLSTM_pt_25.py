"""BiLSTM - Generic implementation for network traffic classification"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset, SubsetRandomSampler
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder, OrdinalEncoder
from sklearn.model_selection import train_test_split, KFold, StratifiedKFold
import pandas as pd
import pickle
import os
import json
import logging
# 配置 matplotlib 以确保图表正确显示
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端，确保在没有显示设备的环境中也能工作
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 设置显示中文字体
matplotlib.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
matplotlib.rcParams['figure.dpi'] = 150  # 提高图像质量
import matplotlib.pyplot as plt
from datetime import datetime
from sklearn.metrics import confusion_matrix, classification_report, accuracy_score, precision_recall_fscore_support
import seaborn as sns
import argparse
from tqdm import tqdm
import ipywidgets as widgets
from IPython.display import display, clear_output
import time

# 设置日志
def setup_logging(log_dir):
    """设置日志配置"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"bilstm_training_{timestamp}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger("BiLSTM")

class BiLSTMModel(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers, num_classes):
        super(BiLSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.bilstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True, bidirectional=True)
        self.fc = nn.Linear(hidden_size * 2, num_classes)

    def forward(self, x):
        # 检查输入维度并调整
        if x.dim() == 2:
            x = x.unsqueeze(1)  # 添加时间步维度
        elif x.dim() != 3:
            raise ValueError(f"Expected 2D or 3D input, but got {x.dim()}D input")

        # x shape: (batch_size, sequence_length, input_size)
        batch_size, seq_len, _ = x.size()
        
        # 初始化隐藏状态和单元状态
        h0 = torch.zeros(self.num_layers * 2, batch_size, self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers * 2, batch_size, self.hidden_size).to(x.device)
        
        # 通过LSTM
        out, _ = self.bilstm(x, (h0, c0))
        
        # 只使用最后一个时间步的输出
        out = self.fc(out[:, -1, :])
        return out

def load_data(file_path):
    # 从pkl文件中加载数据
    with open(file_path, 'rb') as file:
        data = pickle.load(file)
    
    # 假设数据是一个DataFrame格式，或者是特征和标签分别存储的形式
    if isinstance(data, pd.DataFrame):
        X = data.drop(['Label'], axis=1)  # 假设 'Label' 是标签列
        y = data['Label']
    elif isinstance(data, dict):  # 如果是字典形式
        X = data['features']  # 假设特征存储在 'features' 键下
        y = data['labels']    # 假设标签存储在 'labels' 键下
    
    return X, y

# 灵活加载数据集，支持不同的数据格式和标签列
def flexible_data_load(file_path, label_col='Label', feature_cols=None, data_format='pkl'):
    """
    灵活加载不同格式的数据集
    
    参数:
    file_path: 数据文件路径
    label_col: 标签列名，默认为'Label'
    feature_cols: 特征列名列表，None表示使用除标签列外的所有列
    data_format: 数据格式，支持'pkl'、'csv'等
    
    返回:
    X: 特征数据
    y: 标签数据
    """
    logger = logging.getLogger("BiLSTM")
    logger.info(f"Loading data from {file_path} with format {data_format}")
    
    # 加载不同格式的数据
    if data_format.lower() == 'pkl':
        with open(file_path, 'rb') as file:
            data = pickle.load(file)
    elif data_format.lower() == 'csv':
        data = pd.read_csv(file_path)
    elif data_format.lower() == 'json':
        data = pd.read_json(file_path)
    else:
        raise ValueError(f"Unsupported data format: {data_format}")
    
    # 处理不同类型的数据结构
    if isinstance(data, pd.DataFrame):
        logger.info(f"Data loaded as DataFrame with shape {data.shape}")
        
        # 清理列名，去除可能的空格
        data.columns = data.columns.str.strip()
        
        # 检查标签列是否存在
        if label_col not in data.columns:
            available_cols = ', '.join(data.columns.tolist())
            logger.error(f"Label column '{label_col}' not found in data. Available columns: {available_cols}")
            raise ValueError(f"Label column '{label_col}' not found in data.")
        
        # 根据特征列选择特征
        if feature_cols is not None:
            missing_cols = [col for col in feature_cols if col not in data.columns]
            if missing_cols:
                logger.warning(f"Some feature columns not found in data: {missing_cols}")
            X = data[feature_cols]
        else:
            X = data.drop([label_col], axis=1)
            
        y = data[label_col]
        
        logger.info(f"Features shape: {X.shape}, Labels shape: {y.shape}")
        logger.info(f"Label distribution: {y.value_counts().to_dict()}")
    
    elif isinstance(data, dict):
        logger.info("Data loaded as dictionary")
        if 'features' in data and 'labels' in data:
            X = data['features']
            y = data['labels']
        else:
            raise ValueError("Dictionary data should contain 'features' and 'labels' keys")
    else:
        raise TypeError(f"Unsupported data type: {type(data)}")
    
    return X, y

#读取 .pkl 文件之后加入预处理逻辑，需要先从 .pkl 文件中反序列化数据，然后执行相应的预处理步骤。
def load_data_preprocess(pkl_file, label_col='Label', data_format='pkl'):
    """兼容原有的数据加载函数，使用新的灵活数据加载"""
    return flexible_data_load(pkl_file, label_col=label_col, data_format=data_format)

# 高级数据预处理函数
def advanced_preprocess(X, y, options=None):
    """
    高级数据预处理函数，支持更多的预处理选项
    
    参数:
    X: 特征数据
    y: 标签数据
    options: 预处理选项的字典
    
    返回:
    处理后的数据和预处理组件
    """
    logger = logging.getLogger("BiLSTM")
    
    if options is None:
        options = {}
    
    # 提取预处理选项，使用默认值
    test_size = options.get('test_size', 0.3)
    random_state = options.get('random_state', 42)
    handle_imbalance = options.get('handle_imbalance', False)
    normalize = options.get('normalize', True)
    
    # 复制数据避免修改原始数据
    X_copy = X.copy() if hasattr(X, 'copy') else X
    y_copy = y.copy() if hasattr(y, 'copy') else y
    
    preprocessing_info = {}
    
    # 1. 处理缺失值
    if hasattr(X_copy, 'isnull') and X_copy.isnull().any().any():
        logger.info("Handling missing values in features")
        missing_count_before = X_copy.isnull().sum().sum()
        
        # 删除或填充缺失值
        if options.get('drop_missing', False):
            # 删除包含缺失值的行
            X_copy, y_copy = X_copy.align(y_copy, join='inner', axis=0)
            X_copy = X_copy.dropna()
            if hasattr(y_copy, 'loc'):
                y_copy = y_copy.loc[X_copy.index]
            else:
                # 如果y不是pandas对象，尝试按索引同步
                indices = ~X.isnull().any(axis=1)
                X_copy = X_copy[indices]
                y_copy = y_copy[indices]
        else:
            # 改进的缺失值处理代码
            if hasattr(X_copy, 'dtypes'):  # 检查是否为DataFrame
                # 按列类型分别处理
                for col in X_copy.columns:
                    # 跳过没有缺失值的列
                    if not X_copy[col].isnull().any():
                        continue
                        
                    # 获取列的数据类型
                    dtype = X_copy[col].dtype
                    
                    # 数值型数据
                    if np.issubdtype(dtype, np.number):
                        if options.get('fill_method', 'mean') == 'mean':
                            X_copy[col] = X_copy[col].fillna(X_copy[col].mean())
                        elif options.get('fill_method', 'median') == 'median':
                            X_copy[col] = X_copy[col].fillna(X_copy[col].median())
                        else:  # 'mode'
                            X_copy[col] = X_copy[col].fillna(X_copy[col].mode().iloc[0] if not X_copy[col].mode().empty else 0)
                            
                    # 类别型或对象型数据
                    elif dtype == 'object' or dtype == 'category':
                        # 对非数值型，只能使用众数或指定值填充
                        mode_value = X_copy[col].mode().iloc[0] if not X_copy[col].mode().empty else "unknown"
                        X_copy[col] = X_copy[col].fillna(mode_value)
                        
                    # 日期时间型数据
                    elif np.issubdtype(dtype, np.datetime64):
                        # 使用前值填充或指定日期
                        X_copy[col] = X_copy[col].fillna(method='ffill')  # 前值填充
                        # 如果仍有缺失值(如第一个值缺失)，使用后值填充
                        X_copy[col] = X_copy[col].fillna(method='bfill')
                        
                    # 其他类型数据
                    else:
                        # 使用众数或指定默认值
                        X_copy[col] = X_copy[col].fillna(X_copy[col].mode().iloc[0] if not X_copy[col].mode().empty else None)
            else:
                # 如果不是DataFrame，或者无法按列处理，保留原代码
                logger.warning("Data is not a DataFrame, applying simple imputation strategy")
                if options.get('fill_method', 'mean') == 'mean' and hasattr(X_copy, 'mean'):
                    X_copy = X_copy.fillna(X_copy.mean())
                elif options.get('fill_method', 'median') == 'median' and hasattr(X_copy, 'median'):
                    X_copy = X_copy.fillna(X_copy.median())
                elif hasattr(X_copy, 'mode'):
                    X_copy = X_copy.fillna(X_copy.mode().iloc[0] if not X_copy.mode().empty else None)
        
        missing_count_after = X_copy.isnull().sum().sum() if hasattr(X_copy, 'isnull') else 0
        logger.info(f"Missing values: {missing_count_before} -> {missing_count_after}")
        preprocessing_info['missing_values'] = {
            'before': missing_count_before,
            'after': missing_count_after
        }
    
    # 2. 处理标签
    if options.get('encode_labels', True):
        logger.info("Encoding labels")
        le = LabelEncoder()
        if hasattr(y_copy, 'values'):
            # 如果是pandas系列
            y_copy = le.fit_transform(y_copy.values)
        else:
            # 如果是numpy数组或其他可迭代对象
            y_copy = le.fit_transform(y_copy)
        
        preprocessing_info['label_encoder'] = le
        preprocessing_info['classes'] = le.classes_
        logger.info(f"Encoded {len(le.classes_)} classes: {list(le.classes_)}")
        
        # 处理类别不平衡问题
        if handle_imbalance and len(np.unique(y_copy)) > 1:
            logger.info("Handling class imbalance")
            class_counts = np.bincount(y_copy)
            preprocessing_info['class_distribution_before'] = dict(zip(range(len(class_counts)), class_counts))
            
            # 根据配置选择不平衡处理方法
            imbalance_method = options.get('imbalance_method', 'class_weight')
            if imbalance_method == 'class_weight':
                # 计算类别权重
                class_weights = len(y_copy) / (len(np.unique(y_copy)) * np.bincount(y_copy))
                preprocessing_info['class_weights'] = dict(zip(range(len(class_weights)), class_weights))
                logger.info(f"Class weights: {preprocessing_info['class_weights']}")
            # 其他平衡方法可以在这里添加
    
    # 3. 处理分类特征（如果存在且X是DataFrame）
    if hasattr(X_copy, 'select_dtypes'):
        categorical_cols = X_copy.select_dtypes(include=['object', 'category']).columns
        if not categorical_cols.empty:
            logger.info(f"Found {len(categorical_cols)} categorical features")
            
            # 如果有分类特征，进行编码
            encoder = OrdinalEncoder()
            X_copy[categorical_cols] = encoder.fit_transform(X_copy[categorical_cols])
            preprocessing_info['categorical_encoder'] = encoder
            preprocessing_info['categorical_columns'] = categorical_cols.tolist()
    
    # 4. 检查并处理异常值
    if options.get('handle_outliers', True):
        logger.info("Checking for outliers")
        if hasattr(X_copy, 'replace'):
            # 处理无限值
            X_copy.replace([np.inf, -np.inf], np.nan, inplace=True)
            X_copy.fillna(X_copy.mean() if hasattr(X_copy, 'mean') else 0, inplace=True)
        
        # 可选：使用IQR移除异常值
        if options.get('remove_outliers', False):
            if hasattr(X_copy, 'quantile'):
                Q1 = X_copy.quantile(0.25)
                Q3 = X_copy.quantile(0.75)
                IQR = Q3 - Q1
                
                # 创建异常值掩码
                outlier_mask = ~((X_copy < (Q1 - 1.5 * IQR)) | (X_copy > (Q3 + 1.5 * IQR))).any(axis=1)
                X_copy = X_copy.loc[outlier_mask]
                y_copy = y_copy[outlier_mask.values]
                
                logger.info(f"Removed {(~outlier_mask).sum()} outliers")
                preprocessing_info['outliers_removed'] = (~outlier_mask).sum()
    
    # 5. 特征标准化/规范化
    if normalize:
        logger.info("Normalizing features")
        scaler_type = options.get('scaler', 'standard')
        
        if scaler_type == 'standard':
            scaler = StandardScaler()
        elif scaler_type == 'minmax':
            from sklearn.preprocessing import MinMaxScaler
            scaler = MinMaxScaler()
        elif scaler_type == 'robust':
            from sklearn.preprocessing import RobustScaler
            scaler = RobustScaler()
        else:
            scaler = StandardScaler()
        
        if hasattr(X_copy, 'values'):
            X_scaled = scaler.fit_transform(X_copy.values)
        else:
            X_scaled = scaler.fit_transform(X_copy)
        
        # 如果原始X是DataFrame，保持其结构
        if hasattr(X_copy, 'columns'):
            X_copy = pd.DataFrame(X_scaled, index=X_copy.index, columns=X_copy.columns)
        else:
            X_copy = X_scaled
        
        preprocessing_info['feature_scaler'] = scaler
    
    # 6. 划分训练集和测试集
    X_train, X_test, y_train, y_test = train_test_split(
        X_copy, y_copy, test_size=test_size, 
        random_state=random_state, 
        stratify=y_copy if options.get('stratify', True) else None
    )
    
    logger.info(f"Split data: train={X_train.shape}, test={X_test.shape}")
    
    return X_train, X_test, y_train, y_test, preprocessing_info

# 修改现有的preprocess_data函数，使用新的高级预处理功能
def preprocess_data(X, y, options=None):
    """兼容原有的预处理函数，使用新的高级预处理"""
    X_train, X_test, y_train, y_test, _ = advanced_preprocess(X, y, options)
    return X_train, X_test, y_train, y_test

# 数据准备
def prepare_dataset(train_x, train_y, test_x, test_y, batch_size=32):
    """
    准备PyTorch数据集和数据加载器
    
    参数:
    train_x: 训练特征
    train_y: 训练标签
    test_x: 测试特征
    test_y: 测试标签
    batch_size: 批次大小
    
    返回:
    train_loader, test_loader: 训练和测试数据加载器
    """
    # 确保数据为NumPy数组，以便正确转换为PyTorch张量
    if isinstance(train_x, pd.DataFrame):
        train_x = train_x.values
    if isinstance(train_y, pd.Series):
        train_y = train_y.values
    if isinstance(test_x, pd.DataFrame):
        test_x = test_x.values
    if isinstance(test_y, pd.Series):
        test_y = test_y.values
    
    train_x = torch.tensor(train_x, dtype=torch.float32)
    train_y = torch.tensor(train_y, dtype=torch.long)  # 分类任务的标签应为 long 类型
    test_x = torch.tensor(test_x, dtype=torch.float32)
    test_y = torch.tensor(test_y, dtype=torch.long)
    
    train_dataset = TensorDataset(train_x, train_y)
    test_dataset = TensorDataset(test_x, test_y)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    return train_loader, test_loader

# 训练函数
def train_model(train_loader, model, criterion, optimizer, num_epochs, device, val_loader=None, result_dir=None):
    """
    训练BiLSTM模型
    
    参数：
    train_loader: 训练数据加载器
    model: 模型
    criterion: 损失函数
    optimizer: 优化器
    num_epochs: 训练轮数
    device: 计算设备
    val_loader: 验证数据加载器（可选）
    result_dir: 结果保存目录（可选）
    
    返回：
    history: 训练历史记录
    """
    logger = logging.getLogger("BiLSTM")
    
    history = {
        'train_loss': [],
        'train_acc': []
    }
    
    if val_loader is not None:
        history['val_loss'] = []
        history['val_acc'] = []
    
    # 如果提供了结果目录，则将模型检查点保存到该目录
    if result_dir and not os.path.exists(result_dir):
        os.makedirs(result_dir)
    
    # 将模型移动到指定设备
    model = model.to(device)
    
    # 计算总批次数用于进度显示
    total_batches = len(train_loader)
    best_val_loss = float('inf')
    
    # 使用tqdm创建总体训练进度条
    with tqdm(total=num_epochs, desc="Training Progress", unit="epoch") as epoch_pbar:
        for epoch in range(num_epochs):
            # 训练模式
            model.train()
            train_loss = 0.0
            correct = 0
            total = 0
            
            # 使用tqdm创建每个epoch内的batch进度条
            with tqdm(total=total_batches, desc=f"Epoch {epoch+1}/{num_epochs}", unit="batch", leave=False) as batch_pbar:
                for i, (inputs, labels) in enumerate(train_loader):
                    # 将数据移动到指定设备
                    inputs, labels = inputs.to(device), labels.to(device)
                    
                    # 梯度清零
                    optimizer.zero_grad()
                    
                    # 前向传播
                    outputs = model(inputs)
                    loss = criterion(outputs, labels)
                    
                    # 反向传播和优化
                    loss.backward()
                    optimizer.step()
                    
                    # 统计
                    train_loss += loss.item()
                    _, predicted = torch.max(outputs.data, 1)
                    total += labels.size(0)
                    correct += (predicted == labels).sum().item()
                    
                    # 更新batch进度条
                    curr_loss = loss.item()
                    curr_acc = 100.0 * (predicted == labels).sum().item() / labels.size(0)
                    batch_pbar.set_postfix({
                        'loss': f"{curr_loss:.4f}",
                        'acc': f"{curr_acc:.2f}%"
                    })
                    batch_pbar.update(1)
            
            # 计算平均训练损失和准确率
            epoch_loss = train_loss / total_batches
            epoch_acc = 100.0 * correct / total
            
            # 记录训练历史
            history['train_loss'].append(epoch_loss)
            history['train_acc'].append(epoch_acc)
            
            # 评估验证集
            val_loss = None
            val_acc = None
            
            if val_loader is not None:
                val_loss, val_acc = evaluate_model(val_loader, model, criterion, device)
                history['val_loss'].append(val_loss)
                history['val_acc'].append(val_acc)
                
                # 保存最佳模型
                if result_dir and val_loss < best_val_loss:
                    best_val_loss = val_loss
                    best_model_path = os.path.join(result_dir, 'best_model.pth')
                    torch.save(model.state_dict(), best_model_path)
                    logger.info(f"Epoch {epoch+1}: 保存最佳模型，验证损失: {val_loss:.4f}")
            
            # 更新epoch进度条
            epoch_status = {
                'train_loss': f"{epoch_loss:.4f}",
                'train_acc': f"{epoch_acc:.2f}%"
            }
            
            if val_loader is not None:
                epoch_status.update({
                    'val_loss': f"{val_loss:.4f}",
                    'val_acc': f"{val_acc:.2f}%"
                })
            
            epoch_pbar.set_postfix(epoch_status)
            epoch_pbar.update(1)
            
            # 输出训练信息
            logger.info(f"Epoch {epoch+1}/{num_epochs} - "
                       f"Train Loss: {epoch_loss:.4f}, Train Acc: {epoch_acc:.2f}%" +
                       (f", Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%" if val_loader else ""))
    
    # 保存最终模型
    if result_dir:
        final_model_path = os.path.join(result_dir, 'final_model.pth')
        torch.save(model.state_dict(), final_model_path)
        logger.info(f"保存最终模型到: {final_model_path}")
    
    return history

# 评估函数
def evaluate_model(data_loader, model, criterion, device):
    """
    评估模型在给定数据集上的性能
    
    参数:
    data_loader: 数据加载器
    model: 模型
    criterion: 损失函数
    device: 计算设备
    
    返回:
    avg_loss, accuracy: 平均损失和准确率
    """
    model.eval()
    running_loss = 0.0
    correct = 0
    total = 0
    
    with torch.no_grad():
        for inputs, labels in data_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            outputs = model(inputs)
            
            loss = criterion(outputs, labels)
            running_loss += loss.item()
            
            _, predicted = torch.max(outputs.data, 1)
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
    
    avg_loss = running_loss / len(data_loader)
    accuracy = 100 * correct / total
    
    return avg_loss, accuracy

# 测试函数
def test_model(test_loader, model, device, result_dir=None, class_names=None):
    """
    测试模型并生成详细的性能报告
    
    参数:
    test_loader: 测试数据加载器
    model: 模型
    device: 计算设备
    result_dir: 结果保存目录
    class_names: 类别名称列表
    
    返回:
    metrics: 包含各种评估指标的字典
    """
    logger = logging.getLogger("BiLSTM")
    model.to(device)
    model.eval()
    
    # 收集所有预测和真实标签
    all_preds = []
    all_labels = []
    
    with torch.no_grad():
        for inputs, labels in test_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            outputs = model(inputs)
            _, predicted = torch.max(outputs, 1)
            
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    # 计算性能指标
    all_preds = np.array(all_preds)
    all_labels = np.array(all_labels)
    
    accuracy = accuracy_score(all_labels, all_preds)
    precision, recall, f1, _ = precision_recall_fscore_support(all_labels, all_preds, average='weighted')
    
    metrics = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1
    }
    
    # 记录详细的性能报告
    class_report = classification_report(all_labels, all_preds, target_names=class_names)
    logger.info(f"Classification Report:\n{class_report}")
    
    # 生成混淆矩阵
    if result_dir is not None:
        cm = confusion_matrix(all_labels, all_preds)
        plot_confusion_matrix(cm, class_names, result_dir)
        
        # 将性能报告保存到文件
        with open(os.path.join(result_dir, 'classification_report.txt'), 'w') as f:
            f.write(f"Model Evaluation Results\n")
            f.write(f"------------------------\n")
            f.write(f"Accuracy: {accuracy:.4f}\n")
            f.write(f"Precision: {precision:.4f}\n")
            f.write(f"Recall: {recall:.4f}\n")
            f.write(f"F1 Score: {f1:.4f}\n\n")
            f.write(f"Classification Report:\n{class_report}")
    
    logger.info(f'Final Test Metrics: Accuracy={accuracy:.4f}, Precision={precision:.4f}, Recall={recall:.4f}, F1={f1:.4f}')
    
    return metrics

# 交叉验证函数
def cross_validation_split(X, y, n_splits=5, shuffle=True, random_state=42, stratify=True):
    """
    创建交叉验证的数据分割
    
    参数:
    X: 特征数据
    y: 标签数据
    n_splits: 折数
    shuffle: 是否打乱数据
    random_state: 随机种子
    stratify: 是否使用分层抽样
    
    返回:
    splits: 包含训练和验证索引的列表
    """
    logger = logging.getLogger("BiLSTM")
    logger.info(f"Performing {n_splits}-fold cross-validation")
    
    if stratify and len(np.unique(y)) > 1:
        # 使用分层K折交叉验证
        kf = StratifiedKFold(n_splits=n_splits, shuffle=shuffle, random_state=random_state)
        logger.info("Using stratified k-fold cross-validation")
    else:
        # 使用普通K折交叉验证
        kf = KFold(n_splits=n_splits, shuffle=shuffle, random_state=random_state)
        logger.info("Using standard k-fold cross-validation")
    
    splits = list(kf.split(X, y))
    return splits

# 绘制训练历史图表
def plot_training_history(history, result_dir):
    """
    绘制训练历史图表并保存到指定目录
    
    参数:
    history: 包含训练历史数据的字典
    result_dir: 结果保存目录
    """
    logger = logging.getLogger("BiLSTM")
    
    try:
        # 创建图表目录
        plots_dir = os.path.join(result_dir, 'plots')
        if not os.path.exists(plots_dir):
            os.makedirs(plots_dir)
        logger.info(f"创建图表保存目录: {plots_dir}")
        
        # 绘制损失曲线
        plt.figure(figsize=(10, 6))
        plt.plot(history['train_loss'], label='Training Loss')
        if 'val_loss' in history and history['val_loss']:
            plt.plot(history['val_loss'], label='Validation Loss')
        plt.title('Model Loss During Training')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True)
        
        loss_plot_path = os.path.join(plots_dir, 'loss_history.png')
        plt.savefig(loss_plot_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
        plt.close()
        logger.info(f"损失曲线图表已保存到: {loss_plot_path}")
        
        # 绘制准确率曲线
        plt.figure(figsize=(10, 6))
        plt.plot(history['train_acc'], label='Training Accuracy')
        if 'val_acc' in history and history['val_acc']:
            plt.plot(history['val_acc'], label='Validation Accuracy')
        plt.title('Model Accuracy During Training')
        plt.xlabel('Epoch')
        plt.ylabel('Accuracy (%)')
        plt.legend()
        plt.grid(True)
        
        acc_plot_path = os.path.join(plots_dir, 'accuracy_history.png')
        plt.savefig(acc_plot_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
        plt.close()
        logger.info(f"准确率曲线图表已保存到: {acc_plot_path}")
        
        # 验证文件是否成功保存
        if os.path.exists(loss_plot_path) and os.path.getsize(loss_plot_path) > 0:
            logger.info(f"确认损失曲线图表已成功保存，文件大小: {os.path.getsize(loss_plot_path)} 字节")
        else:
            logger.warning(f"损失曲线图表可能未正确保存或文件为空")
            
        if os.path.exists(acc_plot_path) and os.path.getsize(acc_plot_path) > 0:
            logger.info(f"确认准确率曲线图表已成功保存，文件大小: {os.path.getsize(acc_plot_path)} 字节")
        else:
            logger.warning(f"准确率曲线图表可能未正确保存或文件为空")
            
        # 打印图表位置，方便用户查看
        print(f"训练历史图表已保存到 {plots_dir} 目录")
        print(f"- 损失曲线: {loss_plot_path}")
        print(f"- 准确率曲线: {acc_plot_path}")
        
    except Exception as e:
        logger.error(f"绘制训练历史图表时出错: {str(e)}", exc_info=True)
        print(f"警告: 绘制训练历史图表时发生错误: {str(e)}")
        # 继续执行，不因图表绘制失败而影响整体流程

# 绘制混淆矩阵
def plot_confusion_matrix(cm, class_names, result_dir):
    """
    绘制混淆矩阵并保存到指定目录
    
    参数:
    cm: 混淆矩阵
    class_names: 类别名称列表
    result_dir: 结果保存目录
    """
    logger = logging.getLogger("BiLSTM")
    
    try:
        plots_dir = os.path.join(result_dir, 'plots')
        if not os.path.exists(plots_dir):
            os.makedirs(plots_dir)
        logger.info(f"创建/检查图表保存目录: {plots_dir}")
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=class_names, yticklabels=class_names)
        plt.title('Confusion Matrix')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
        plt.tight_layout()
        
        cm_plot_path = os.path.join(plots_dir, 'confusion_matrix.png')
        plt.savefig(cm_plot_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
        plt.close()
        logger.info(f"混淆矩阵图表已保存到: {cm_plot_path}")
        
        # 验证文件是否保存成功
        if os.path.exists(cm_plot_path) and os.path.getsize(cm_plot_path) > 0:
            logger.info(f"确认混淆矩阵图表已成功保存，文件大小: {os.path.getsize(cm_plot_path)} 字节")
        else:
            logger.warning(f"混淆矩阵图表可能未正确保存或文件为空")
        
        # 打印图表位置
        print(f"混淆矩阵图表已保存到: {cm_plot_path}")
        
    except Exception as e:
        logger.error(f"绘制混淆矩阵时出错: {str(e)}", exc_info=True)
        print(f"警告: 绘制混淆矩阵时发生错误: {str(e)}")
        # 继续执行，不因图表绘制失败而影响整体流程

# 辅助函数：将对象转换为JSON可序列化的格式
def convert_to_serializable(obj):
    """
    递归地将对象转换为JSON可序列化的格式
    
    参数:
    obj: 需要转换的对象
    
    返回:
    转换后的对象
    """
    if isinstance(obj, dict):
        return {convert_to_serializable(k): convert_to_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_to_serializable(item) for item in obj]
    elif isinstance(obj, (np.integer, np.int64, np.int32)):
        return int(obj)
    elif isinstance(obj, (np.floating, np.float64, np.float32)):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    else:
        return obj

# 绘制交叉验证结果
def plot_cv_results(fold_metrics, result_dir):
    """
    绘制交叉验证结果图表
    
    参数:
    fold_metrics: 每个折的性能指标列表
    result_dir: 结果保存目录
    """
    logger = logging.getLogger("BiLSTM")
    
    try:
        plots_dir = os.path.join(result_dir, 'plots')
        if not os.path.exists(plots_dir):
            os.makedirs(plots_dir)
        logger.info(f"创建/检查图表保存目录: {plots_dir}")
        
        # 绘制准确率条形图
        plt.figure(figsize=(12, 6))
        folds = [m['fold'] for m in fold_metrics]
        accuracies = [m['accuracy'] for m in fold_metrics]
        
        mean_acc = np.mean(accuracies)
        plt.bar(folds, accuracies, color='skyblue')
        plt.axhline(y=mean_acc, color='r', linestyle='-', label=f'Mean: {mean_acc:.2f}%')
        
        plt.title('Cross-Validation Accuracy by Fold')
        plt.xlabel('Fold')
        plt.ylabel('Accuracy (%)')
        plt.xticks(folds)
        plt.legend()
        plt.grid(True, axis='y', linestyle='--', alpha=0.7)
        
        plt.tight_layout()
        cv_acc_path = os.path.join(plots_dir, 'cv_accuracy.png')
        plt.savefig(cv_acc_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
        plt.close()
        logger.info(f"交叉验证准确率图表已保存到: {cv_acc_path}")
        
        # 记录生成的图表
        generated_plots = [cv_acc_path]
        
        # 如果有验证损失，绘制损失曲线
        if all('val_loss' in m and m['val_loss'] is not None for m in fold_metrics):
            plt.figure(figsize=(12, 6))
            for m in fold_metrics:
                plt.plot(m['val_loss'], label=f"Fold {m['fold']}")
            
            plt.title('Validation Loss by Fold')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()
            plt.grid(True)
            
            plt.tight_layout()
            cv_loss_path = os.path.join(plots_dir, 'cv_loss.png')
            plt.savefig(cv_loss_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
            plt.close()
            logger.info(f"交叉验证损失图表已保存到: {cv_loss_path}")
            
            generated_plots.append(cv_loss_path)
            
            # 验证文件
            if os.path.exists(cv_loss_path) and os.path.getsize(cv_loss_path) > 0:
                logger.info(f"确认交叉验证损失图表已成功保存，文件大小: {os.path.getsize(cv_loss_path)} 字节")
            else:
                logger.warning(f"交叉验证损失图表可能未正确保存或文件为空")
        
        # 验证准确率图
        if os.path.exists(cv_acc_path) and os.path.getsize(cv_acc_path) > 0:
            logger.info(f"确认交叉验证准确率图表已成功保存，文件大小: {os.path.getsize(cv_acc_path)} 字节")
        else:
            logger.warning(f"交叉验证准确率图表可能未正确保存或文件为空")
        
        # 打印图表位置
        print(f"交叉验证结果图表已保存到 {plots_dir} 目录")
        for plot in generated_plots:
            print(f"- {os.path.basename(plot)}: {plot}")
            
    except Exception as e:
        logger.error(f"绘制交叉验证结果图表时出错: {str(e)}", exc_info=True)
        print(f"警告: 绘制交叉验证结果图表时发生错误: {str(e)}")
        # 继续执行，不因图表绘制失败而影响整体流程

# 交叉验证训练BiLSTM模型
def cross_validation_train(X, y, input_size, hidden_size, output_size, num_layers, num_epochs, learning_rate, batch_size, model_name, result_dir, n_splits=5, device="cpu"):
    """
    使用交叉验证训练BiLSTM模型
    
    参数:
    X: 特征数据
    y: 标签数据
    input_size: 输入特征数
    hidden_size: 隐藏层特征数
    output_size: 类别数量
    num_layers: 隐藏层数量
    num_epochs: 训练轮数
    learning_rate: 学习率
    batch_size: 批处理大小
    model_name: 模型名称
    result_dir: 结果保存目录
    n_splits: 交叉验证折数
    device: 计算设备
    
    返回:
    trained_model: 在全部数据上训练的最终模型
    cv_results: 交叉验证结果
    """
    logger = logging.getLogger("BiLSTM")
    logger.info(f"Starting {n_splits}-fold cross-validation training")
    
    # 获取交叉验证分割
    splits = cross_validation_split(X, y, n_splits=n_splits, stratify=True)
    
    # 用于存储每个折的性能
    fold_metrics = []
    
    # 对每个折进行训练和评估
    for fold, (train_idx, val_idx) in enumerate(splits):
        logger.info(f"Training fold {fold+1}/{n_splits}")
        
        # 分割数据
        X_train, X_val = X[train_idx], X[val_idx]
        y_train, y_val = y[train_idx], y[val_idx]
        
        # 准备数据加载器
        train_loader, val_loader = prepare_dataset(X_train, y_train, X_val, y_val, batch_size)
        
        # 创建模型、损失函数和优化器
        model = BiLSTMModel(input_size, hidden_size, num_layers, output_size)
        criterion = nn.CrossEntropyLoss()
        optimizer = optim.Adam(model.parameters(), lr=learning_rate)
        
        # 训练模型
        fold_dir = os.path.join(result_dir, f'fold_{fold+1}')
        if not os.path.exists(fold_dir):
            os.makedirs(fold_dir)
            
        history = train_model(train_loader, model, criterion, optimizer, num_epochs, device, val_loader, fold_dir)
        
        # 评估模型
        val_loss, accuracy = evaluate_model(val_loader, model, criterion, device)
        
        # 保存本折的模型
        torch.save(model.state_dict(), os.path.join(fold_dir, f"{model_name}_fold_{fold+1}.pth"))
        
        # 记录性能
        fold_metrics.append({
            'fold': fold + 1,
            'accuracy': accuracy,
            'val_loss': history['val_loss'][-1] if 'val_loss' in history and history['val_loss'] else None
        })
        
        logger.info(f"Fold {fold+1} completed with validation accuracy: {accuracy:.2f}%")
    
    # 汇总交叉验证结果
    cv_accuracy = np.mean([m['accuracy'] for m in fold_metrics])
    cv_std = np.std([m['accuracy'] for m in fold_metrics])
    
    logger.info(f"Cross-validation completed with mean accuracy: {cv_accuracy:.2f}% ± {cv_std:.2f}%")
    
    # 创建交叉验证结果可视化
    plot_cv_results(fold_metrics, result_dir)
    
    # 在全部数据上训练最终模型
    logger.info("Training final model on all data")
    train_loader, _ = prepare_dataset(X, y, X[:1], y[:1], batch_size)  # 创建一个假的测试集
    
    final_model = BiLSTMModel(input_size, hidden_size, num_layers, output_size)
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(final_model.parameters(), lr=learning_rate)
    
    train_model(train_loader, final_model, criterion, optimizer, num_epochs, device, result_dir=result_dir)
    
    # 保存最终模型
    final_model_path = os.path.join(result_dir, f"{model_name}.pth")
    torch.save(final_model.state_dict(), final_model_path)
    logger.info(f"Final model saved to {final_model_path}")
    
    # 保存交叉验证结果到JSON文件
    cv_results = {
        'folds': fold_metrics,
        'mean_accuracy': float(cv_accuracy),
        'std_accuracy': float(cv_std)
    }
    
    with open(os.path.join(result_dir, 'cv_results.json'), 'w') as f:
        # 使用辅助函数转换为可序列化格式
        serializable_results = convert_to_serializable(cv_results)
        json.dump(serializable_results, f, indent=4)
    
    return final_model, cv_results

def bilstm_train(input_file, input_size, hidden_size, output_size, num_layers, num_epochs, learning_rate, batch_size, result_dir, model_name, device, use_cross_validation=False, cv_folds=5, label_column='Label', data_format='pkl', preprocess_options=None):
    """
    训练BiLSTM模型的主函数
    
    参数说明：
    input_file: 输入文件路径
    input_size: 输入特征数
    hidden_size: 隐藏层特征数
    output_size: 类别数量
    num_layers: 隐藏层数量
    num_epochs: 训练的轮数
    learning_rate: 学习率
    batch_size: 批处理大小
    result_dir: 结果保存目录
    model_name: 模型名称
    device: 计算设备('cpu'或'cuda')
    use_cross_validation: 是否使用交叉验证
    cv_folds: 交叉验证折数
    label_column: 标签列名
    data_format: 数据格式
    preprocess_options: 预处理选项
    
    返回:
    training_results: 训练结果字典
    """
    # 创建结果目录
    if not os.path.exists(result_dir):
        os.makedirs(result_dir)
    
    # 设置日志
    logger = setup_logging(result_dir)
    logger.info("BiLSTM训练开始")
    logger.info(f"设备: {device}")
    logger.info(f"模型参数: input_size={input_size}, hidden_size={hidden_size}, output_size={output_size}, num_layers={num_layers}")
    logger.info(f"训练参数: num_epochs={num_epochs}, learning_rate={learning_rate}, batch_size={batch_size}")
    
    try:
        # 加载和预处理数据
        logger.info(f"从 {input_file} 加载数据")
        X, y = flexible_data_load(input_file, label_col=label_column, data_format=data_format)
        
        # 获取类别名称
        if hasattr(y, 'unique'):
            class_names = y.unique().tolist()
            logger.info(f"加载了{len(class_names)}个类别: {class_names}")
        else:
            class_names = [f"Class_{i}" for i in range(output_size)]
            logger.info(f"使用通用类别名称: {class_names}")
        
        # 使用交叉验证或标准训练
        if use_cross_validation:
            logger.info(f"使用{cv_folds}折交叉验证")
            
            # 确保X和y是numpy数组
            if hasattr(X, 'values'):
                X_processed = X.values
            else:
                X_processed = X
                
            if hasattr(y, 'values'):
                y_processed = y.values
            else:
                y_processed = y
                
            # 运行交叉验证训练
            final_model, cv_results = cross_validation_train(
                X_processed, y_processed, input_size, hidden_size, output_size, 
                num_layers, num_epochs, learning_rate, batch_size, 
                model_name, result_dir, n_splits=cv_folds, device=device
            )
            
            # 整理返回结果
            results = {
                "model_path": os.path.join(result_dir, f"{model_name}.pth"),
                "cv_results": cv_results
            }
            
            logger.info("交叉验证训练完成")
            return results
            
        else:
            logger.info("使用标准训练测试集分割")
            
            # 预处理数据
            X_train, X_test, y_train, y_test, preprocessing_info = advanced_preprocess(X, y, preprocess_options)
            
            # 保存预处理信息
            with open(os.path.join(result_dir, 'preprocessing_info.json'), 'w') as f:
                # 将不可序列化的对象转换为字符串
                preproc_info_serializable = {}
                for k, v in preprocessing_info.items():
                    if k == 'label_encoder' or k == 'feature_scaler' or k == 'categorical_encoder':
                        preproc_info_serializable[k] = str(v)
                    elif k == 'classes':
                        preproc_info_serializable[k] = v.tolist() if hasattr(v, 'tolist') else list(v)
                    elif isinstance(v, dict):
                        # 处理嵌套字典，例如class_weights
                        serialized_dict = {}
                        for dict_k, dict_v in v.items():
                            # 确保字典的键和值也是可序列化的
                            if isinstance(dict_k, np.integer):
                                dict_k = int(dict_k)
                            if isinstance(dict_v, (np.integer, np.floating)):
                                dict_v = float(dict_v) if isinstance(dict_v, np.floating) else int(dict_v)
                            serialized_dict[dict_k] = dict_v
                        preproc_info_serializable[k] = serialized_dict
                    elif isinstance(v, (np.integer, np.int64, np.int32)):
                        preproc_info_serializable[k] = int(v)
                    elif isinstance(v, (np.floating, np.float64, np.float32)):
                        preproc_info_serializable[k] = float(v)
                    elif isinstance(v, np.ndarray):
                        preproc_info_serializable[k] = v.tolist()
                    else:
                        preproc_info_serializable[k] = v
                json.dump(preproc_info_serializable, f, indent=4)
            
            # 准备数据加载器
            train_loader, test_loader = prepare_dataset(X_train, y_train, X_test, y_test, batch_size)
            
            # 获取类权重（如果有）
            class_weights = preprocessing_info.get('class_weights', None)
            if class_weights is not None:
                logger.info(f"使用类别权重: {class_weights}")
                weights = torch.FloatTensor([class_weights.get(i, 1.0) for i in range(output_size)]).to(device)
                criterion = nn.CrossEntropyLoss(weight=weights)
            else:
                criterion = nn.CrossEntropyLoss()
            
            # 初始化BiLSTM模型
            model = BiLSTMModel(input_size, hidden_size, num_layers, output_size)
            optimizer = optim.Adam(model.parameters(), lr=learning_rate)
            
            # 训练模型
            logger.info("开始模型训练")
            history = train_model(train_loader, model, criterion, optimizer, num_epochs, device, result_dir=result_dir)
            
            # 测试模型
            logger.info("在测试集上评估模型")
            metrics = test_model(test_loader, model, device, result_dir, class_names)
            
            # 保存模型
            model_path = os.path.join(result_dir, f"{model_name}.pth")
            torch.save(model.state_dict(), model_path)
            logger.info(f"模型已保存到 {model_path}")
            
            # 记录所有训练结果
            results = {
                "model_path": model_path,
                "metrics": metrics,
                "history": {
                    "train_loss": [float(x) for x in history['train_loss']],
                    "train_acc": [float(x) for x in history['train_acc']]
                }
            }
            
            # 将结果保存为JSON
            with open(os.path.join(result_dir, 'training_results.json'), 'w') as f:
                # 确保所有值都是JSON可序列化的
                results_serializable = {}
                for k, v in results.items():
                    if isinstance(v, (np.integer, np.int64, np.int32)):
                        results_serializable[k] = int(v)
                    elif isinstance(v, (np.floating, np.float64, np.float32)):
                        results_serializable[k] = float(v)
                    elif isinstance(v, dict):
                        # 递归处理嵌套字典
                        results_serializable[k] = convert_to_serializable(v)
                    else:
                        results_serializable[k] = v
                json.dump(results_serializable, f, indent=4)
            
            logger.info("BiLSTM训练成功完成")
            return results
            
    except Exception as e:
        logger.error(f"BiLSTM训练过程中出错: {str(e)}", exc_info=True)
        raise

if __name__ == "__main__":
    # 使用argparse处理命令行参数
    parser = argparse.ArgumentParser(description='BiLSTM网络流量分类器 - 支持多种数据格式和交叉验证')
    parser.add_argument('--input_file', type=str, default='E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl',
                        help='输入数据文件路径')
    parser.add_argument('--input_size', type=int, default=84,
                        help='输入特征维度，默认为84（适用于CIC-IDS2017数据集）')
    parser.add_argument('--hidden_size', type=int, default=128,
                        help='LSTM隐藏层大小，默认为128')
    parser.add_argument('--output_size', type=int, default=6,
                        help='输出类别数量，默认为6（适用于CIC-IDS2017数据集）')
    parser.add_argument('--num_layers', type=int, default=2,
                        help='LSTM层数，默认为2')
    parser.add_argument('--num_epochs', type=int, default=1,
                        help='训练轮数，默认为1')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                        help='学习率，默认为0.001')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='批处理大小，默认为32')
    parser.add_argument('--result_dir', type=str, default='./results',
                        help='结果保存目录，默认为./results')
    parser.add_argument('--model_name', type=str, default='BiLSTM_model',
                        help='模型名称，默认为BiLSTM_model')
    parser.add_argument('--use_cv', action='store_true',
                        help='是否使用交叉验证，默认不使用')
    parser.add_argument('--cv_folds', type=int, default=5,
                        help='交叉验证折数，默认为5')
    parser.add_argument('--label_column', type=str, default='Label',
                        help='标签列名，默认为Label')
    parser.add_argument('--data_format', type=str, default='pkl', choices=['pkl', 'csv', 'json'],
                        help='数据格式，支持pkl、csv、json，默认为pkl')
    parser.add_argument('--handle_imbalance', action='store_true',
                        help='是否处理类别不平衡，默认不处理')
    
    args = parser.parse_args()
    
    # 设置预处理选项
    preprocess_options = {
        'handle_imbalance': args.handle_imbalance,
        'test_size': 0.3,
        'random_state': 42,
        'normalize': True
    }
    
    # 设置计算设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 调用主函数执行训练
    try:
        results = bilstm_train(
            args.input_file, args.input_size, args.hidden_size, args.output_size,
            args.num_layers, args.num_epochs, args.learning_rate, args.batch_size,
            args.result_dir, args.model_name, device, args.use_cv, args.cv_folds,
            args.label_column, args.data_format, preprocess_options
        )
        
        print(f"BiLSTM训练完成，模型保存到 {results['model_path']}")
        
        if args.use_cv:
            print(f"交叉验证平均准确率: {results['cv_results']['mean_accuracy']:.2f}% ± {results['cv_results']['std_accuracy']:.2f}%")
        else:
            print(f"测试集准确率: {results['metrics']['accuracy']*100:.2f}%")
            print(f"测试集F1分数: {results['metrics']['f1']*100:.2f}%")
    
    except Exception as e:
        print(f"训练过程中出错: {str(e)}")
