"""随机森林分类"""

import sys
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score

def random_forest_classifier(input_file, target_column, n_estimators=100, test_size=0.2):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - target_column: 目标变量的列名
    - n_estimators: 树的数量，默认为100
    - test_size: 测试集比例，默认为0.2
    """
    data = pd.read_csv(input_file)
    X = data.drop(target_column, axis=1)
    y = data[target_column]
    
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size)
    
    model = RandomForestClassifier(n_estimators=n_estimators)
    model.fit(X_train, y_train)
    
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    
    print(f"Random forest classification completed. Accuracy: {accuracy:.2f}")

if __name__ == "__main__":
    if len(sys.argv) != 5:
        print("Usage: python random_forest_classifier.py <input_file> <target_column> <n_estimators> <test_size>")
        sys.exit(1)
    input_file, target_column, n_estimators, test_size = sys.argv[1], sys.argv[2], sys.argv[3], sys.argv[4]
    random_forest_classifier(input_file, target_column, int(n_estimators), float(test_size))
