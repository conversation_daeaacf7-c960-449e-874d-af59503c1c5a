import pickle
import requests
import joblib
import sys
import argparse
import json
import jieba
from gensim.models import Word2Vec
from minio import Minio
import uuid
import os

MINIO_URL = "minio-prophecis.prophecis:9000"
MODEL_FACTORY_URL = "http://controlcenter-gateway.prophecis:8081"
MODEL_ADD_URL = "/mf/v1/model"
MODEL_PUSH_URL = "/mf/v1/modelVersion/push"

MLSS_APP_TIMESTAMP_KEY = "MLSS-AppTimestamp"
MLSS_AUTH_TYPE_KEY = "MLSS-Auth-Type"
MLSS_AUTH_TYPE_VAL = "SYSTEM"
MLSS_APP_ID_KEY = "MLSS-APPID"
MLSS_APP_ID_VAL = "MLFLOW"
MLSS_APP_SIGNATURE_KEY = "MLSS-APPSignature"
MLSS_APP_SIGNATURE_VAL = "MLFLOW"
MLSS_USER_ID_KEY = "MLSS-UserID"


def chinese_word_segmentation(sentences):
    """进行中文分词"""
    return [list(jieba.cut(sentence)) for sentence in sentences]

def word2vec_train(segmented_sentences, job_params, model_name, result_dir):
    """
    参数说明：
    - sentences: 分词后的句子
    - size: 词向量维度
    - window: 窗口大小
    - min_count: 最小词频
    """
    size = job_params["size"]
    window = job_params["window"]
    min_count = job_params["min_count"]

    # 训练Word2Vec模型
    model = Word2Vec(sentences=segmented_sentences, vector_size=size, window=window, min_count=min_count)
    model_file = result_dir + "/" + model_name + ".model"
    model.save(model_file)
    print(f'Word2Vec训练完成，模型保存到 {model_file}')

    return None, 0

    
def model_upload(result_dir, model_name):
    minioClient = Minio(MINIO_URL,
                        access_key='AKIAIOSFODNN7EXAMPLE',
                        secret_key='wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
                        secure=False)
    try:
        obj_name = str(uuid.uuid1())
        upload_path = obj_name + "/" + model_name + ".model"
        source = "s3://mlss-mf/" + obj_name
        res = minioClient.fput_object('mlss-mf', upload_path, result_dir + "/" + model_name + ".model")
        result = {"source": source}
        return result, 0
    except Exception as err:
        print(err)
        return None, -1

    
def model_register(model_name, source, group_id, headers):
    params = {
        "model_name": model_name,
        "model_type": "MLPipeline",
        "file_name": model_name + ".model",
        "s3_path": source,
        "group_id": int(float(group_id)),
        "training_id": model_name,
        "training_flag": 1,
    }

    r = requests.post(MODEL_FACTORY_URL + MODEL_ADD_URL, data=json.dumps(params), headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1


def model_push(model_id, model_version_id, factory_name):
    if factory_name == "None":
        return "Factory Name is none, Skip Model Push.", 0

    params = {
        "factory_name": factory_name,
        "model_type": "Word2Vec",
        "model_usage": "Text Processing"
    }
    r = requests.post(MODEL_FACTORY_URL + MODEL_PUSH_URL + "/" + str(model_version_id), data=json.dumps(params), headers=headers)
    res_data = r.content.decode()
    status_code = r.status_code
    if status_code == 200:
        return json.loads(res_data), 0
    else:
        return res_data, -1


def header_gen(user_id):
    headers = {
        MLSS_APP_TIMESTAMP_KEY: "20210803",
        MLSS_AUTH_TYPE_KEY: MLSS_AUTH_TYPE_VAL,
        MLSS_APP_ID_KEY: MLSS_APP_ID_VAL,
        MLSS_APP_SIGNATURE_KEY: MLSS_APP_SIGNATURE_VAL,
        MLSS_USER_ID_KEY: user_id
    }
    return headers


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='MLPipeline Chinese Word Segmentation and Word2Vec Training.')
    parser.add_argument('--job_params', dest='job_params', type=json.loads, help='Word2Vec Job Params, set all params in dict, example:')
    parser.add_argument('--dataset', dest='dataset', type=json.loads, help='Text DataSet, set as a dict, example:')
    parser.add_argument('--model', dest='model', type=json.loads, help='mlflow training model msg')
    parser.add_argument('--factory_name', dest='factory_name', type=str, help='factory name')
    parser.add_argument('--result_dir', dest='result_dir', type=str, help='training model result')
    parser.add_argument('--fit_params', dest='fit_params', type=json.loads, help='fit params')
    
    print("Start Chinese Word Segmentation and Word2Vec training job, params :\n" + str(sys.argv) + "\n")
    args = parser.parse_args()
    job_params = args.job_params
    print("Word2Vec job params:" + str(job_params) + "\n")
    dataset = args.dataset
    print("Text dataSet:" + str(dataset) + "\n")
    model = args.model
    print(model)
    result_dir = args.result_dir
    print("Word2Vec result dir:" + result_dir + "\n")
    factory_name = args.factory_name
    print("Word2Vec factory name:" + factory_name + "\n")
    fit_params = args.fit_params
    print("Word2Vec fit params:" + str(fit_params) + "\n")
    
    print("Step 1: Chinese Word Segmentation\n")
    segmented_sentences = chinese_word_segmentation(dataset["sentences"])
    
    print("Step 2: Word2Vec Training\n")
    result, ret_code = word2vec_train(segmented_sentences, job_params, model["model_name"], result_dir)
    if ret_code != 0:
        print("Word2Vec training error, stop job....\n")
        sys.exit(-1)
    print("Training finished, start storing model...\n")

    print("Step 3: Model Upload to MinIO: \n")
    result, ret_code = model_upload(result_dir, model["model_name"])
    if ret_code != 0:
        print("Model storage error, stop job....error msg: " + result + "\n")
        sys.exit(-1)
    print("Model storage finished, start model registration...\n")

    print("Step 4: Model Registration:\n")
    source = result["source"]
    headers = header_gen(os.environ.get("USER_ID"))
    result, ret_code = model_register(model["model_name"], source, model["group_id"], headers)
    if ret_code != 0:
        print("Model registration error, stop job....,err msg: " + result)
        sys.exit(-1)
    print("Model registration finished, start model push...\n")

    print("Step 5: Model Push, pushing model to FPS and sending RMB msg\n")
    model_id = result["result"]["model_id"]
    model_version_id = result["result"]["model_version_id"]
    result, ret_code = model_push(model_id, model_version_id, factory_name)
    if ret_code != 0:
        print("Model push error, stop job....err msg: " + result + "\n")
    print("Model push finished, job complete...\n")
    print("Job End..\n")
    sys.exit()
