2025-05-21 14:31:40,831 - GAT_TF_Local - INFO - Logging to console and file: ./results/results_gat_tf_local\gat_tf_local_training_20250521_143140.log
2025-05-21 14:31:40,832 - GAT_TF_Local - INFO - GAT TensorFlow Local Mode Training Script Started.
2025-05-21 14:31:40,832 - GAT_TF_Local - INFO - TensorFlow Version: 2.13.0
2025-05-21 14:31:40,832 - GAT_TF_Local - INFO - Provided arguments: Namespace(adj_type='identity', categorical_impute_strategy='most_frequent', cv_folds=5, data_format='pkl', dropout=0.5, final_dropout=0.5, handle_imbalance=False, hidden_size=64, input_file='E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', k_neighbors=5, l2_reg=0.0001, label_column='Label', learning_rate=0.005, log_level='INFO', model_name='GAT_TF_Local_Model', normalize=True, num_epochs=3, num_heads=4, numeric_impute_strategy='mean', random_state=42, result_dir='./results/results_gat_tf_local', scaler_type='standard', stratify_split=True, test_split_size=0.2, use_cv=False)
2025-05-21 14:31:40,863 - GAT_TF_Local - INFO - Loading data from: E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl (Format: pkl, Label: 'Label')
2025-05-21 14:31:41,396 - GAT_TF_Local - INFO - Data loaded: X shape (692703, 84), y shape (692703,). Label dist: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-21 14:31:41,426 - GAT_TF_Local - INFO - Standard train/test split mode selected for GAT (TF).
2025-05-21 14:31:41,915 - GAT_TF_Local - INFO - Replaced infinite values with NaN.
2025-05-21 14:31:42,006 - GAT_TF_Local - INFO - Handling missing values.
2025-05-21 14:31:44,216 - GAT_TF_Local - INFO - Imputed numeric missing values using 'mean'.
2025-05-21 14:31:44,714 - GAT_TF_Local - INFO - Imputed categorical missing values using 'most_frequent'.
2025-05-21 14:31:44,818 - GAT_TF_Local - INFO - Encoded labels. Classes: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-21 14:31:44,903 - GAT_TF_Local - INFO - Encoding categorical features: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-21 14:31:48,872 - GAT_TF_Local - INFO - Normalizing features using standard scaler.
2025-05-21 14:31:50,624 - GAT_TF_Local - INFO - Split data: X_train (554162, 84), y_train (554162,), X_test (138541, 84), y_test (138541,)
2025-05-21 14:31:50,780 - GAT_TF_Local - INFO - Preprocessing info saved to ./results/results_gat_tf_local\GAT_TF_Local_Model_preprocessing_info.json
2025-05-21 14:31:50,781 - GAT_TF_Local - INFO - Data after preprocessing: Input size=84, Num classes=6
2025-05-21 14:31:50,838 - GAT_TF_Local - INFO - Combined train/test for graph: Total nodes 692703, Train 554162, Val/Test 138541
2025-05-21 14:31:50,839 - GAT_TF_Local - INFO - Using special 'IDENTITY_ADJACENCY' marker for 692703 nodes (self-loops only).
2025-05-21 14:31:51,127 - GAT_TF_Local - INFO - Starting GAT (TF) model training (standard split).
2025-05-21 14:31:51,148 - GAT_TF_Local - INFO - Starting GAT (TF) model training for 3 epochs.
2025-05-21 14:31:51,351 - GAT_TF_Local - CRITICAL - An unexpected critical error occurred: Exception encountered when calling layer 'gat_layer_1' (type GATLayer).

{{function_node __wrapped__AddV2_device_/job:localhost/replica:0/task:0/device:CPU:0}} OOM when allocating tensor with shape[1,692703,692703] and type float on /job:localhost/replica:0/task:0/device:CPU:0 by allocator cpu [Op:AddV2] name: 

Call arguments received by layer 'gat_layer_1' (type GATLayer):
  • inputs=['tf.Tensor(shape=(692703, 84), dtype=float32)', "'IDENTITY_ADJACENCY'"]
  • training=True
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GAT_tf_local_mode.py", line 1141, in gat_train_tf_local_entrypoint
    train_history = train_gat_model_keras_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GAT_tf_local_mode.py", line 622, in train_gat_model_keras_local
    predictions = model([node_features, adj_matrix], training=True) # Logits
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GAT_tf_local_mode.py", line 259, in call
    x = self.gat1([node_features, adj_matrix], training=training)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GAT_tf_local_mode.py", line 140, in call
    e = tf.keras.layers.LeakyReLU(alpha=0.2)(attn_self + tf.transpose(attn_neigh, perm=[0, 2, 1]))
tensorflow.python.framework.errors_impl.ResourceExhaustedError: Exception encountered when calling layer 'gat_layer_1' (type GATLayer).

{{function_node __wrapped__AddV2_device_/job:localhost/replica:0/task:0/device:CPU:0}} OOM when allocating tensor with shape[1,692703,692703] and type float on /job:localhost/replica:0/task:0/device:CPU:0 by allocator cpu [Op:AddV2] name: 

Call arguments received by layer 'gat_layer_1' (type GATLayer):
  • inputs=['tf.Tensor(shape=(692703, 84), dtype=float32)', "'IDENTITY_ADJACENCY'"]
  • training=True
