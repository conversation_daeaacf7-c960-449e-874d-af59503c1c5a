"""
简化版 BiLSTM 推理服务
支持网络流量分析和其他数据集的分类预测
基于bilstm_predictor.py的增强版本
"""

import numpy as np
import torch
import torch.nn as nn
import pickle
import logging
import os
import json
import traceback
from typing import Dict, List, Any, Union, Optional

# 尝试导入可选依赖
try:
    import pandas as pd
except ImportError:
    pd = None
    
try:
    from sklearn.preprocessing import StandardScaler, LabelEncoder
except ImportError:
    StandardScaler = None
    LabelEncoder = None

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 预定义数据集配置
DATASET_CONFIGS = {
    "network_traffic": {
        "input_size": 84, "hidden_size": 128, "num_layers": 2, "output_size": 6,
        "class_names": ["Benign", "DDoS", "PortScan", "Bot", "Infiltration", "WebAttack"]
    },
    "iris": {
        "input_size": 4, "hidden_size": 32, "num_layers": 1, "output_size": 3,
        "class_names": ["setosa", "versicolor", "virginica"]
    },
    "wine": {
        "input_size": 13, "hidden_size": 64, "num_layers": 2, "output_size": 3,
        "class_names": ["Class_1", "Class_2", "Class_3"]
    },
    "mnist": {
        "input_size": 784, "hidden_size": 256, "num_layers": 2, "output_size": 10,
        "class_names": [str(i) for i in range(10)]
    }
}

class BiLSTM(nn.Module):
    """BiLSTM模型定义，与训练脚本保持一致"""
    def __init__(self, input_size, hidden_size, num_layers, num_classes):
        super(BiLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.bilstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True, bidirectional=True)
        self.fc = nn.Linear(hidden_size * 2, num_classes)

    def forward(self, x):
        # 检查输入维度并调整
        if x.dim() == 2:
            x = x.unsqueeze(1)  # 添加时间步维度
        elif x.dim() != 3:
            raise ValueError(f"Expected 2D or 3D input, but got {x.dim()}D input")

        # x shape: (batch_size, sequence_length, input_size)
        batch_size, seq_len, _ = x.size()
        
        # 初始化隐藏状态和单元状态
        h0 = torch.zeros(self.num_layers * 2, batch_size, self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers * 2, batch_size, self.hidden_size).to(x.device)
        
        # 通过LSTM
        out, _ = self.bilstm(x, (h0, c0))
        
        # 只使用最后一个时间步的输出
        out = self.fc(out[:, -1, :])
        return out

class BiLSTMPredictor:
    """
    BiLSTM预测器类，兼容Seldon Core
    """
    
    def __init__(self):
        """
        初始化预测器
        """
        self.model = None
        self.scaler = None
        self.label_encoder = None
        self.model_config = None
        self.class_names = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.is_loaded = False
        
        # 从环境变量获取配置
        self.model_name = os.environ.get("MODEL_NAME", "PyTorchModel_v1")
        self.api_type = os.environ.get("API_TYPE", "REST")
        self.service_type = os.environ.get("SERVICE_TYPE", "MODEL")
        
        logger.info(f"BiLSTM Predictor initialized on device: {self.device}")
        logger.info(f"Model Name: {self.model_name}")

    def load(self, model_name: Optional[str] = None) -> bool:
        """
        加载模型和预处理器
        
        Args:
            model_name: 可选的模型名称
            
        Returns:
            bool: 加载是否成功
        """
        try:
            # 获取模型名称
            if model_name:
                self.model_name = model_name
                os.environ["MODEL_NAME"] = model_name
            elif not hasattr(self, 'model_name') or not self.model_name:
                self.model_name = os.environ.get("MODEL_NAME", "PyTorchModel_v1")
            
            # 模型目录候选路径 - 模型文件位于app文件夹
            model_library_candidates = [
                "/app",             # 用户指定的app文件夹
                os.environ.get("MODEL_LIBRARY_DIR", "/app/models"),  # 镜像内模型库
                "/app/models",      # 默认镜像内位置
                os.environ.get("MODEL_DIR", "/opt/ml/model"),  # Seldon默认
                "./models",         # 相对路径
                "./model-library",  # 本地开发模型库
                "./model",          # 本地开发单模型
                "."                 # 当前目录
            ]
            
            model_library_dir = None
            for candidate in model_library_candidates:
                if candidate and os.path.exists(candidate):
                    model_library_dir = candidate
                    break
            
            if not model_library_dir:
                raise FileNotFoundError("No valid model library directory found")
            
            # 构建特定模型的路径
            model_dir = os.path.join(model_library_dir, self.model_name)
            if not os.path.exists(model_dir):
                # 如果指定模型目录不存在，尝试在根目录查找
                model_dir = model_library_dir
                logger.warning(f"Model directory {os.path.join(model_library_dir, self.model_name)} not found, using {model_library_dir}")
            
            dataset_type = os.environ.get("DATASET_TYPE", "network_traffic")
            
            logger.info(f"Model Library: {model_library_dir}")
            logger.info(f"Loading model: {self.model_name}")
            logger.info(f"Model directory: {model_dir}")
            logger.info(f"Dataset type: {dataset_type}")
            
            # 使用预定义配置或从文件加载
            config_path = os.path.join(model_dir, "model_config.json")
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    self.model_config = json.load(f)
                logger.info(f"Loaded config from file: {config_path}")
            elif dataset_type in DATASET_CONFIGS:
                self.model_config = DATASET_CONFIGS[dataset_type].copy()
                logger.info(f"Using predefined config for {dataset_type}")
            else:
                # 默认网络流量配置
                self.model_config = DATASET_CONFIGS["network_traffic"].copy()
                logger.warning("Using default network traffic configuration")
            
            self.class_names = self.model_config["class_names"]
            
            # 加载模型文件
            model_file_candidates = [
                "model.pth",
                "bilstm_model.pth", 
                "pytorch_model.bin",
                "model.pt"
            ]
            
            model_path = None
            for filename in model_file_candidates:
                candidate_path = os.path.join(model_dir, filename)
                if os.path.exists(candidate_path):
                    model_path = candidate_path
                    break
            
            if not model_path:
                raise FileNotFoundError(f"No model file found in {model_dir}")
            
            # 创建模型实例
            self.model = BiLSTM(
                input_size=self.model_config["input_size"],
                hidden_size=self.model_config["hidden_size"],
                num_layers=self.model_config["num_layers"],
                num_classes=self.model_config["output_size"]
            )
            
            # 加载模型权重
            checkpoint = torch.load(model_path, map_location=self.device)
            if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
            else:
                self.model.load_state_dict(checkpoint)
            
            self.model.to(self.device)
            self.model.eval()
            logger.info(f"Model loaded successfully: {model_path}")
            
            # 加载预处理器（可选）
            if StandardScaler:
                scaler_path = os.path.join(model_dir, "scaler.pkl")
                if os.path.exists(scaler_path):
                    with open(scaler_path, 'rb') as f:
                        self.scaler = pickle.load(f)
                    logger.info("Scaler loaded")
            
            if LabelEncoder:
                label_encoder_path = os.path.join(model_dir, "label_encoder.pkl")
                if os.path.exists(label_encoder_path):
                    with open(label_encoder_path, 'rb') as f:
                        self.label_encoder = pickle.load(f)
                    logger.info("Label encoder loaded")
            
            self.is_loaded = True
            logger.info("Model loading completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            logger.error(traceback.format_exc())
            return False

    def preprocess(self, X: Union[np.ndarray, list]) -> torch.Tensor:
        """
        预处理输入数据
        
        Args:
            X: 输入数据
            
        Returns:
            torch.Tensor: 预处理后的数据
        """
        try:
            # 转换为numpy数组
            if isinstance(X, list):
                X = np.array(X)
            
            if X.ndim == 1:
                X = X.reshape(1, -1)
            
            # 验证输入维度
            if self.model_config is None:
                raise RuntimeError("Model config not loaded")
            expected_size = self.model_config["input_size"]
            if X.shape[1] != expected_size:
                raise ValueError(f"Expected input size {expected_size}, but got {X.shape[1]}")
            
            # 特征标准化（如果有scaler）
            if self.scaler is not None:
                X = self.scaler.transform(X)
            
            # 转换为PyTorch张量
            X_tensor = torch.tensor(X, dtype=torch.float32).to(self.device)
            
            return X_tensor
            
        except Exception as e:
            logger.error(f"Error in preprocessing: {str(e)}")
            raise

    def predict(self, X: Union[np.ndarray, list], names: Optional[List[str]] = None, meta: Optional[Dict] = None) -> Union[np.ndarray, Dict]:
        """
        进行预测
        
        Args:
            X: 输入数据
            names: 特征名称（Seldon Core标准参数）
            meta: 元数据（Seldon Core标准参数）
            
        Returns:
            预测结果字典
        """
        try:
            if not self.is_loaded:
                raise RuntimeError("Model not loaded. Call load() first.")
            
            if self.model is None:
                raise RuntimeError("Model is None")
            
            if self.class_names is None:
                raise RuntimeError("Class names not loaded")
            
            # 预处理数据
            X_processed = self.preprocess(X)
            
            # 执行预测
            with torch.no_grad():
                outputs = self.model(X_processed)
                probabilities = torch.softmax(outputs, dim=1)
                predicted_classes = torch.argmax(outputs, dim=1)
            
            # 转换为numpy数组
            probs_np = probabilities.cpu().numpy()
            preds_np = predicted_classes.cpu().numpy()
            
            # Seldon Core期望的返回格式（numpy数组）
            if meta and meta.get("seldon_format", False):
                return probs_np
            
            # 获取预测标签
            predicted_labels = [self.class_names[i] for i in preds_np]
            
            # 单样本结果
            if len(preds_np) == 1:
                return {
                    "predicted_class": predicted_labels[0],
                    "confidence": float(np.max(probs_np[0])),
                    "probabilities": {
                        self.class_names[j]: float(probs_np[0][j]) 
                        for j in range(len(self.class_names))
                    }
                }
            
            # 多样本结果
            return {
                "predictions": [
                    {
                        "predicted_class": predicted_labels[i],
                        "confidence": float(np.max(probs_np[i])),
                        "probabilities": {
                            self.class_names[j]: float(probs_np[i][j]) 
                            for j in range(len(self.class_names))
                        }
                    }
                    for i in range(len(preds_np))
                ]
            }
            
        except Exception as e:
            logger.error(f"Error during prediction: {str(e)}")
            logger.error(traceback.format_exc())
            raise

    def list_available_models(self) -> Dict[str, Any]:
        """
        列出模型库中可用的模型
        
        Returns:
            Dict: 包含可用模型列表的字典
        """
        try:
            model_library_candidates = [
                "/app",             # 用户指定的app文件夹
                os.environ.get("MODEL_LIBRARY_DIR", "/app/models"),
                "/app/models",
                "./models", 
                "./model-library",
                "./model"
            ]
            
            available_models = []
            model_library_dir = None
            
            for candidate in model_library_candidates:
                if candidate and os.path.exists(candidate):
                    model_library_dir = candidate
                    break
            
            if model_library_dir and os.path.exists(model_library_dir):
                # 查找所有包含模型文件的子目录
                for item in os.listdir(model_library_dir):
                    item_path = os.path.join(model_library_dir, item)
                    if os.path.isdir(item_path):
                        # 检查目录中是否包含模型文件
                        model_files = ["model.pth", "bilstm_model.pth", "pytorch_model.bin", "model.pt"]
                        if any(os.path.exists(os.path.join(item_path, mf)) for mf in model_files):
                            available_models.append(item)
                    elif item in ["model.pth", "bilstm_model.pth", "pytorch_model.bin", "model.pt"]:
                        # 如果在根目录发现模型文件，使用目录名作为模型名
                        available_models.append(os.path.basename(model_library_dir))
                        break
            
            return {
                "available_models": sorted(list(set(available_models))),
                "model_library_dir": model_library_dir,
                "total_models": len(available_models)
            }
        except Exception as e:
            logger.error(f"Error listing models: {str(e)}")
            return {"available_models": [], "error": str(e)}

    def switch_model(self, new_model_name: str) -> bool:
        """
        切换到新的模型
        
        Args:
            new_model_name: 新模型名称
            
        Returns:
            bool: 切换是否成功
        """
        try:
            logger.info(f"Switching to model: {new_model_name}")
            
            # 重置当前状态
            self.is_loaded = False
            self.model = None
            self.model_config = None
            
            # 加载新模型
            success = self.load(new_model_name)
            
            if success:
                logger.info(f"Successfully switched to: {new_model_name}")
            else:
                logger.error(f"Failed to switch to: {new_model_name}")
            
            return success
        except Exception as e:
            logger.error(f"Error switching model: {str(e)}")
            return False

    def health_status(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            status = {
                "status": "healthy" if self.is_loaded else "unhealthy",
                "model_loaded": self.is_loaded,
                "model_name": getattr(self, 'model_name', 'unknown'),
                "api_type": getattr(self, 'api_type', 'unknown'),
                "service_type": getattr(self, 'service_type', 'unknown'),
                "device": str(self.device),
                "device_type": "GPU" if torch.cuda.is_available() else "CPU",
                "config": self.model_config if self.is_loaded else None,
                "dependencies": {
                    "sklearn_available": StandardScaler is not None,
                    "pandas_available": pd is not None
                }
            }
            
            # 添加模型库信息
            models_info = self.list_available_models()
            status["models"] = models_info
            
            return status
        except Exception as e:
            return {"status": "error", "error": str(e)}

# 全局预测器实例
predictor = BiLSTMPredictor()

# Seldon Core要求的函数
def load():
    """Seldon Core标准模型加载函数"""
    model_name = os.environ.get("SELECTED_MODEL_NAME") or os.environ.get("MODEL_NAME")
    return predictor.load(model_name)

def predict(X, names=None, meta=None):
    """Seldon Core标准预测函数"""
    return predictor.predict(X, names, meta)

def health_status():
    """Seldon Core标准健康检查函数"""
    return predictor.health_status()

# 测试代码
if __name__ == "__main__":
    import sys
    
    # 设置环境变量进行测试
    os.environ["DATASET_TYPE"] = "network_traffic"
    os.environ["MODEL_DIR"] = "./model"
    
    test_predictor = BiLSTMPredictor()
    
    if test_predictor.load():
        print("✓ Model loaded successfully")
        print(f"✓ Config: {test_predictor.model_config}")
        
        # 创建测试数据
        input_size = test_predictor.model_config["input_size"]
        test_data = np.random.randn(input_size).tolist()
        
        try:
            result = test_predictor.predict(test_data)
            print(f"✓ Test prediction: {result}")
        except Exception as e:
            print(f"✗ Test prediction failed: {e}")
    else:
        print("✗ Failed to load model - Please ensure model files exist in ./model/") 