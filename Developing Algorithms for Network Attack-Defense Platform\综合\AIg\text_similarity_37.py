"""文本相似度计算"""

from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

def text_similarity(text1, text2):
    """
    参数说明：
    - text1: 文本1
    - text2: 文本2
    """
    texts = [text1, text2]
    vectorizer = TfidfVectorizer()
    tfidf_matrix = vectorizer.fit_transform(texts)
    similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])
    return similarity[0][0]

if __name__ == "__main__":
    text1 = "这是第一个文本"
    text2 = "这是第二个文本"
    similarity = text_similarity(text1, text2)
    print(f"文本相似度: {similarity}")
