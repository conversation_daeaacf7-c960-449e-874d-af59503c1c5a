"""GIN"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch_geometric.nn import GINConv
from torch_geometric.data import Data, DataLoader
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler

class GINModel(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim):
        super(GINModel, self).__init__()
        nn1 = nn.Sequential(nn.Linear(input_dim, hidden_dim), nn.ReLU(), nn.Linear(hidden_dim, hidden_dim))
        self.conv1 = GINConv(nn1)
        nn2 = nn.Sequential(nn.Linear(hidden_dim, hidden_dim), nn.ReLU(), nn.Linear(hidden_dim, output_dim))
        self.conv2 = GINConv(nn2)

    def forward(self, x, edge_index):
        x = torch.relu(self.conv1(x, edge_index))
        x = torch.relu(self.conv2(x, edge_index))
        return x

def gin_analysis(input_file, node_feature_column, edge_index_column, target_column, input_dim, hidden_dim, output_dim, epochs, batch_size):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - node_feature_column: 节点特征列名
    - edge_index_column: 边索引列名
    - target_column: 目标列名
    - input_dim: 输入维度
    - hidden_dim: 隐藏层维度
    - output_dim: 输出维度
    - epochs: 训练轮数
    - batch_size: 批次大小
    """
    data = pd.read_csv(input_file)
    node_features = data[node_feature_column].values
    edge_index = data[edge_index_column].values
    target = data[target_column].values

    scaler = StandardScaler()
    node_features = scaler.fit_transform(node_features)

    node_features = torch.tensor(node_features, dtype=torch.float32)
    edge_index = torch.tensor(edge_index, dtype=torch.long)
    target = torch.tensor(target, dtype=torch.long)

    data = Data(x=node_features, edge_index=edge_index.t().contiguous(), y=target)
    dataloader = DataLoader([data], batch_size=batch_size, shuffle=True)

    model = GINModel(input_dim, hidden_dim, output_dim)
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    for epoch in range(epochs):
        for data in dataloader:
            out = model(data.x, data.edge_index)
            loss = criterion(out, data.y)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
        print(f"Epoch {epoch+1}/{epochs}, Loss: {loss.item()}")

    print("GIN training completed")

if __name__ == "__main__":
    input_file = 'data.csv'
    node_feature_column = ['feature1', 'feature2']
    edge_index_column = 'edge_index'
    target_column = 'target'
    input_dim = 2
    hidden_dim = 16
    output_dim = 10
    epochs = 20
    batch_size = 32

    gin_analysis(input_file, node_feature_column, edge_index_column, target_column, input_dim, hidden_dim, output_dim, epochs, batch_size)
