"""词向量转换"""

from gensim.models import Word2Vec

def word2vec_conversion(sentences, vector_size=100, window=5, min_count=1, workers=4):
    """
    参数说明：
    - sentences: 输入句子列表
    - vector_size: 词向量维度
    - window: 窗口大小
    - min_count: 最小词频
    - workers: 工作线程数
    """
    model = Word2Vec(sentences, vector_size=vector_size, window=window, min_count=min_count, workers=workers)
    return model

if __name__ == "__main__":
    sentences = [["这是", "一个", "例子"], ["另一个", "例子"]]
    model = word2vec_conversion(sentences)
    print(model.wv['这是'])
