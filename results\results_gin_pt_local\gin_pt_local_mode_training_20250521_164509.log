2025-05-21 16:45:09,926 - GIN_PT_Local_Mode - INFO - 日志将记录到控制台和文件: results\results_gin_pt_local\gin_pt_local_mode_training_20250521_164509.log
2025-05-21 16:45:09,927 - GIN_PT_Local_Mode - INFO - GIN PyTorch 本地模式训练脚本已初始化。
2025-05-21 16:45:09,927 - GIN_PT_Local_Mode - INFO - PyTorch 版本: 2.4.1+cpu
2025-05-21 16:45:09,927 - GIN_PT_Local_Mode - INFO - 参数: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'result_dir': 'results\\results_gin_pt_local', 'model_name': 'GIN_PT_Local_Model', 'input_size': 84, 'hidden_size': 64, 'output_size': 6, 'num_layers': 2, 'dropout_rate': 0.5, 'num_epochs': 3, 'learning_rate': 0.001, 'batch_size': 32, 'edge_strategy': 'fully_connected', 'k_neighbors': 5, 'radius': 1.0, 'label_column': 'Label', 'data_format': 'pkl', 'test_split_size': 0.2, 'random_state': 42, 'normalize': True, 'scaler_type': 'standard', 'handle_imbalance': False, 'stratify_split': True, 'numeric_impute_strategy': 'mean', 'categorical_impute_strategy': 'most_frequent', 'use_cv': False, 'cv_folds': 5, 'log_level': 'INFO', 'force_cpu': False}
2025-05-21 16:45:09,928 - GIN_PT_Local_Mode - INFO - 使用设备: cpu
2025-05-21 16:45:09,931 - GIN_PT_Local_Mode - INFO - 从 E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl (格式: pkl) 加载数据
2025-05-21 16:45:10,350 - GIN_PT_Local_Mode - INFO - 数据加载为 DataFrame，形状: (692703, 85)
2025-05-21 16:45:10,650 - GIN_PT_Local_Mode - INFO - 特征形状: (692703, 84), 标签形状: (692703,)
2025-05-21 16:45:10,681 - GIN_PT_Local_Mode - INFO - 标签分布: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-21 16:45:10,828 - GIN_PT_Local_Mode - INFO - 模型将使用 input_size=84, output_size(num_classes)=6
2025-05-21 16:45:10,829 - GIN_PT_Local_Mode - INFO - 选择标准训练/测试分割模式。
2025-05-21 16:45:11,550 - GIN_PT_Local_Mode - INFO - 已将特征中的无限值替换为NaN。
2025-05-21 16:45:11,681 - GIN_PT_Local_Mode - INFO - 处理特征中的缺失值。
2025-05-21 16:45:11,958 - GIN_PT_Local_Mode - INFO - 标签已编码。6个类别: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-21 16:45:11,992 - GIN_PT_Local_Mode - INFO - 编码类别特征: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-21 16:45:17,204 - GIN_PT_Local_Mode - INFO - 使用 standard 缩放器标准化/缩放特征。
2025-05-21 16:45:18,401 - GIN_PT_Local_Mode - INFO - 数据分割: X_train (554162, 84), y_train (554162,), X_test (138541, 84), y_test (138541,)
2025-05-21 16:45:18,480 - GIN_PT_Local_Mode - INFO - 训练/测试分割的预处理信息已保存: results\results_gin_pt_local\GIN_PT_Local_Model_preprocessing_info_pt.json
2025-05-21 16:45:18,481 - GIN_PT_Local_Mode - INFO - 预处理后数据 (训练/测试分割): input_size=84, num_classes=6
2025-05-21 16:45:18,481 - GIN_PT_Local_Mode - INFO - 使用 'fully_connected' 策略将数据转换为图格式。节点数: 554162
2025-05-21 16:45:18,527 - GIN_PT_Local_Mode - WARNING - 节点数 (554162) 超过阈值 (20000)。全连接策略将生成一个没有边的图 (模型应处理或添加自环)。
2025-05-21 16:45:18,530 - GIN_PT_Local_Mode - INFO - 创建了 1 个图数据对象。第一个图的边数: 0
2025-05-21 16:45:18,532 - GIN_PT_Local_Mode - INFO - PyG DataLoader创建成功。批大小: 32, Shuffle: True。包含 1 个批次。
2025-05-21 16:45:18,533 - GIN_PT_Local_Mode - INFO - 使用 'fully_connected' 策略将数据转换为图格式。节点数: 138541
2025-05-21 16:45:18,545 - GIN_PT_Local_Mode - WARNING - 节点数 (138541) 超过阈值 (20000)。全连接策略将生成一个没有边的图 (模型应处理或添加自环)。
2025-05-21 16:45:18,546 - GIN_PT_Local_Mode - INFO - 创建了 1 个图数据对象。第一个图的边数: 0
2025-05-21 16:45:18,546 - GIN_PT_Local_Mode - INFO - PyG DataLoader创建成功。批大小: 32, Shuffle: False。包含 1 个批次。
2025-05-21 16:45:18,701 - GIN_PT_Local_Mode - INFO - 开始 GIN (PyTorch) 模型训练 (标准分割)。
2025-05-21 16:45:20,562 - GIN_PT_Local_Mode - ERROR - 值错误: Expected input batch_size (1) to match target batch_size (554162).
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GIN_pt_local_mode.py", line 982, in gin_train_local_mode
    train_history = train_gin_model_local(
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GIN_pt_local_mode.py", line 435, in train_gin_model_local
    loss = criterion(out, batch_data.y)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\nn\modules\module.py", line 1553, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\nn\modules\module.py", line 1562, in _call_impl
    return forward_call(*args, **kwargs)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\nn\modules\loss.py", line 1188, in forward
    return F.cross_entropy(input, target, weight=self.weight,
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\torch\nn\functional.py", line 3104, in cross_entropy
    return torch._C._nn.cross_entropy_loss(input, target, weight, _Reduction.get_enum(reduction), ignore_index, label_smoothing)
ValueError: Expected input batch_size (1) to match target batch_size (554162).
