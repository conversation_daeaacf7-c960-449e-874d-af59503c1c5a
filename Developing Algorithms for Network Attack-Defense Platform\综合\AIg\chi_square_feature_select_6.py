"""基于卡方检验的特征选择"""

import sys
import pandas as pd
import numpy as np
from sklearn.feature_selection import chi2, SelectKBest

def chi_square_feature_select(input_file, target_column, k='all'):
    """
    参数说明：
    - input_file: 输入CSV文件路径
    - target_column: 目标变量的列名
    - k: 选择的特征数量，可以是整数或'all'（表示选择所有特征），默认为'all'
    """
    data = pd.read_csv(input_file)
    X = data.drop(target_column, axis=1)
    y = data[target_column]
    
    selector = SelectKBest(chi2, k=k)
    X_new = selector.fit_transform(X, y)
    
    selected_features = X.columns[selector.get_support()].tolist()
    
    output_file = 'chi_square_selected_features.csv'
    pd.DataFrame(X_new, columns=selected_features).to_csv(output_file, index=False)
    print(f"Chi-square feature selection completed. Output saved to {output_file}")
    print(f"Selected features: {', '.join(selected_features)}")

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python chi_square_feature_select.py <input_file> <target_column> <k>")
        sys.exit(1)
    input_file, target_column, k = sys.argv[1], sys.argv[2], sys.argv[3]
    chi_square_feature_select(input_file, target_column, k)
