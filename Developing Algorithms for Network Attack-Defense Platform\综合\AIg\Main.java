public class Main {
    public static void main(String[] args) {
        String pythonPath = "/usr/bin/python3";  // 根据你的Python安装路径进行修改
        AlgorithmManager manager = new AlgorithmManager(pythonPath);

        // 加载算法
        manager.loadAlgorithm("pca", "/path/to/pca_script.py");
        manager.loadAlgorithm("standardization", "/path/to/standardization_script.py");
        // 加载更多算法...

        try {
            // 运行PCA算法
            String pcaResult = manager.runAlgorithm("pca", "input_data.csv", "5");
            System.out.println("PCA Result: " + pcaResult);

            // 运行标准化算法
            String standardizationResult = manager.runAlgorithm("standardization", "input_data.csv");
            System.out.println("Standardization Result: " + standardizationResult);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}