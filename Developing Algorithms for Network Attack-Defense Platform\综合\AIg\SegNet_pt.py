import pickle
import requests
import sys
import argparse
import json
from minio import Minio
import uuid
import os
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, Dataset
from torchvision import datasets, transforms
from torchvision import models
from PIL import Image
import xml.etree.ElementTree as ET
import pandas as pd
from tqdm.auto import tqdm
import numpy as np


class SegNet(nn.Module):
    def __init__(self, num_classes):
        super(SegNet, self).__init__()
        
        # 使用较小的初始卷积核和适当的padding来维持特征图大小
        self.enc1 = nn.Sequential(
            nn.Conv2d(3, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
        )

        self.enc2 = nn.Sequential(
            nn.Conv2d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
        )

        self.enc3 = nn.Sequential(
            nn.Conv2d(128, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
        )

        self.enc4 = nn.Sequential(
            nn.Conv2d(256, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
        )

        self.enc5 = nn.Sequential(
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
        )

        # 解码器使用转置卷积，确保正确的上采样
        self.dec5 = nn.Sequential(
            nn.ConvTranspose2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
        )

        self.dec4 = nn.Sequential(
            nn.ConvTranspose2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(512, 512, kernel_size=3, padding=1),
            nn.BatchNorm2d(512),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(512, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
        )

        self.dec3 = nn.Sequential(
            nn.ConvTranspose2d(256, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(256, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(256, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
        )

        self.dec2 = nn.Sequential(
            nn.ConvTranspose2d(128, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(128, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
        )

        self.dec1 = nn.Sequential(
            nn.ConvTranspose2d(64, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.ConvTranspose2d(64, num_classes, kernel_size=3, padding=1),
        )

        # 初始化权重
        self._initialize_weights()

    def _initialize_weights(self):
        # 加载预训练的VGG16权重
        vgg16 = models.vgg16(pretrained=True)
        
        # 将预训练的权重复制到编码器
        vgg_features = list(vgg16.features.children())
        
        blocks = [self.enc1, self.enc2, self.enc3, self.enc4, self.enc5]
        ranges = [(0, 4), (4, 9), (9, 16), (16, 23), (23, 30)]
        
        for idx, (block, ranges) in enumerate(zip(blocks, ranges)):
            for l1, l2 in zip(block.children(), vgg_features[ranges[0]:ranges[1]]):
                if isinstance(l1, nn.Conv2d) and isinstance(l2, nn.Conv2d):
                    l1.weight.data = l2.weight.data
                    l1.bias.data = l2.bias.data

    def forward(self, x):
        # 保存池化索引和大小用于上采样
        indices = []
        unpool_sizes = []
        
        # 编码器前向传播
        def encode_block(x, block):
            x = block(x)
            size = x.size()
            x, ind = F.max_pool2d(x, kernel_size=2, stride=2, return_indices=True)
            indices.append(ind)
            unpool_sizes.append(size)
            return x

        # 编码过程
        x = encode_block(x, self.enc1)
        x = encode_block(x, self.enc2)
        x = encode_block(x, self.enc3)
        x = encode_block(x, self.enc4)
        x = encode_block(x, self.enc5)

        # 解码器前向传播
        def decode_block(x, block, indices, size):
            x = F.max_unpool2d(x, indices, kernel_size=2, stride=2, output_size=size)
            x = block(x)
            return x

        # 解码过程
        x = decode_block(x, self.dec5, indices.pop(), unpool_sizes.pop())
        x = decode_block(x, self.dec4, indices.pop(), unpool_sizes.pop())
        x = decode_block(x, self.dec3, indices.pop(), unpool_sizes.pop())
        x = decode_block(x, self.dec2, indices.pop(), unpool_sizes.pop())
        x = decode_block(x, self.dec1, indices.pop(), unpool_sizes.pop())

        return x

class UniversalImageDataset(Dataset):
    def __init__(self, data_dir, transform=None, target_transform=None, dataset_type='folder', annotations_file=None):
        self.data_dir = data_dir
        self.transform = transform
        self.target_transform = target_transform
        self.dataset_type = dataset_type
        self.annotations_file = annotations_file

        self.classes = ['background'] + [f'class_{i}' for i in range(1, 21)]  # 21类（包括背景）
        self.class_to_idx = {cls: idx for idx, cls in enumerate(self.classes)}

        if dataset_type == 'pickle':
            self.image_data, self.mask_data = self.load_pickle_data()
        elif dataset_type == 'coco':
            self.image_paths, self.mask_data = self.load_coco_data()
        elif dataset_type == 'yolo':
            self.image_paths, self.mask_data = self.load_yolo_data()
        elif dataset_type in ['folder', 'voc']:
            self.image_paths, self.mask_paths = self.load_segmentation_data()
        else:
            raise ValueError("Unsupported dataset type for segmentation.")

    def load_segmentation_data(self):
        image_paths = []
        mask_paths = []
        
        # 假设数据集结构为：
        # data_dir/
        #   └── JPEGImages/      # 原始图像
        #   └── SegmentationClass/  # 分割掩码
        
        images_dir = os.path.join(self.data_dir, 'JPEGImages')
        masks_dir = os.path.join(self.data_dir, 'SegmentationClass')
        
        for img_name in os.listdir(images_dir):
            if img_name.endswith(('.jpg', '.jpeg', '.png')):
                img_path = os.path.join(images_dir, img_name)
                mask_name = img_name.replace('.jpg', '.png').replace('.jpeg', '.png')
                mask_path = os.path.join(masks_dir, mask_name)
                
                if os.path.exists(mask_path):
                    image_paths.append(img_path)
                    mask_paths.append(mask_path)

        return image_paths, mask_paths
    
    def load_pickle_data(self):
        """加载pickle格式的数据集"""
        with open(self.annotations_file, 'rb') as f:
            data = pickle.load(f)
            
        if isinstance(data, dict):
            # 假设pickle文件包含'images'和'masks'键
            return data.get('images', []), data.get('masks', [])
        elif isinstance(data, tuple) and len(data) == 2:
            # 假设pickle文件直接存储(images, masks)元组
            return data
        else:
            raise ValueError("Invalid pickle data format")

    def load_coco_data(self):
        """加载COCO格式的数据集"""
        from pycocotools.coco import COCO
        
        coco = COCO(self.annotations_file)
        
        # 获取所有图像ID
        image_ids = coco.getImgIds()
        image_paths = []
        mask_data = []
        
        for img_id in image_ids:
            # 获取图像信息
            img_info = coco.loadImgs(img_id)[0]
            img_path = os.path.join(self.data_dir, img_info['file_name'])
            image_paths.append(img_path)
            
            # 获取分割掩码
            ann_ids = coco.getAnnIds(imgIds=img_id)
            anns = coco.loadAnns(ann_ids)
            
            # 合并所有分割掩码
            mask = np.zeros((img_info['height'], img_info['width']), dtype=np.uint8)
            for ann in anns:
                curr_mask = coco.annToMask(ann)
                mask[curr_mask == 1] = ann['category_id']
            
            mask_data.append(mask)
            
        return image_paths, mask_data

    def load_yolo_data(self):
        """加载YOLO格式的数据集"""
        image_paths = []
        mask_data = []
        
        # 获取图像文件列表
        images_dir = os.path.join(self.data_dir, 'images')
        labels_dir = os.path.join(self.data_dir, 'labels')
        
        for img_name in os.listdir(images_dir):
            if img_name.endswith(('.jpg', '.jpeg', '.png')):
                img_path = os.path.join(images_dir, img_name)
                label_name = os.path.splitext(img_name)[0] + '.txt'
                label_path = os.path.join(labels_dir, label_name)
                
                if os.path.exists(label_path):
                    image_paths.append(img_path)
                    
                    # 读取图像获取尺寸
                    img = Image.open(img_path)
                    img_width, img_height = img.size
                    
                    # 创建空白掩码
                    mask = np.zeros((img_height, img_width), dtype=np.uint8)
                    
                    # 读取YOLO格式的标签文件
                    with open(label_path, 'r') as f:
                        for line in f:
                            class_id, x_center, y_center, width, height = map(float, line.strip().split())
                            
                            # 转换YOLO坐标为像素坐标
                            x1 = int((x_center - width/2) * img_width)
                            y1 = int((y_center - height/2) * img_height)
                            x2 = int((x_center + width/2) * img_width)
                            y2 = int((y_center + height/2) * img_height)
                            
                            # 在掩码上填充分割区域
                            mask[y1:y2, x1:x2] = int(class_id) + 1  # +1 因为背景是0
                    
                    mask_data.append(mask)
        
        return image_paths, mask_data

    def __len__(self):
        if self.dataset_type == 'pickle':
            return len(self.image_data)
        else:
            return len(self.image_paths)

    def __getitem__(self, idx):
        if self.dataset_type == 'pickle':
            # 直接从内存加载数据
            image = self.image_data[idx]
            mask = self.mask_data[idx]
            
            # 如果图像数据是numpy数组，转换为PIL Image
            if isinstance(image, np.ndarray):
                image = Image.fromarray(image)
            
        elif self.dataset_type in ['coco', 'yolo']:
            # 从文件加载图像，从内存加载掩码
            image = Image.open(self.image_paths[idx]).convert('RGB')
            mask = self.mask_data[idx]
            mask = Image.fromarray(mask)
            
        else:  # folder or voc
            # 从文件加载图像和掩码
            image = Image.open(self.image_paths[idx]).convert('RGB')
            mask = Image.open(self.mask_paths[idx])
        
        # 应用变换
        if self.transform:
            image = self.transform(image)
        if self.target_transform:
            mask = self.target_transform(mask)
        else:
            # 确保掩码是长整型张量
            if isinstance(mask, Image.Image):
                mask = np.array(mask)
            mask = torch.from_numpy(mask).long()
        
        return image, mask

def prepare_data(data_dir, dataset_type, batch_size, image_size, annotations_file=None):
    # 确保输入图像尺寸是32的倍数，这样经过5次下采样后仍然有足够的空间
    if image_size % 32 != 0:
        image_size = ((image_size // 32) + 1) * 32
        print(f"调整输入图像尺寸为: {image_size}x{image_size}")
    
    transform = transforms.Compose([
        transforms.Resize((image_size, image_size)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    target_transform = transforms.Compose([
        transforms.Resize((image_size, image_size), interpolation=Image.NEAREST),
        lambda x: torch.from_numpy(np.array(x)).long()
    ])

    dataset = UniversalImageDataset(
        data_dir, 
        transform=transform,
        target_transform=target_transform,
        dataset_type=dataset_type,
        annotations_file=annotations_file
    )

    train_size = int(0.8 * len(dataset))
    test_size = len(dataset) - train_size
    train_dataset, test_dataset = torch.utils.data.random_split(dataset, [train_size, test_size])

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    return train_loader, test_loader

def train_model(train_loader, model, criterion, optimizer, num_epochs, device):
    model.to(device)
    
    for epoch in range(num_epochs):
        model.train()
        running_loss = 0.0
        
        pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{num_epochs}")
        for images, masks in pbar:
            images = images.to(device)
            masks = masks.to(device)
            
            if masks.dim() == 4:
                masks = masks.squeeze(1)
            
            optimizer.zero_grad()
            
            try:
                outputs = model(images)
                
                if outputs.size()[2:] != masks.size()[1:]:
                    outputs = F.interpolate(outputs, size=masks.size()[1:], mode='bilinear', align_corners=True)
                
                loss = criterion(outputs, masks)
                loss.backward()
                optimizer.step()
                
                running_loss += loss.item()
                pbar.set_postfix({'loss': f'{loss.item():.4f}'})
                
            except Exception as e:
                print(f"Error in batch: {e}")
                continue
        
        epoch_loss = running_loss / len(train_loader)
        print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {epoch_loss:.4f}')

def test_model(test_loader, model, device):
    model.eval()
    total_iou = 0.0
    num_images = 0

    with torch.no_grad():
        for images, masks in tqdm(test_loader, desc="Testing"):
            images = images.to(device)
            masks = masks.to(device)
            
            if masks.dim() == 4:
                masks = masks.squeeze(1)
            
            outputs = model(images)
            
            # 确保输出和目标的大小匹配
            if outputs.size()[2:] != masks.size()[1:]:
                outputs = F.interpolate(outputs, size=masks.size()[1:], mode='bilinear', align_corners=True)
            
            predictions = torch.argmax(outputs, dim=1)
            
            # 计算IoU
            for pred, mask in zip(predictions, masks):
                intersection = torch.logical_and(pred, mask).sum()
                union = torch.logical_or(pred, mask).sum()
                iou = (intersection / union).item() if union > 0 else 0
                total_iou += iou
                num_images += 1

    mean_iou = total_iou / num_images
    print(f'Mean IoU: {mean_iou:.4f}')

    
def segnet_train(input_file, dataset_type, input_size, annotations_file, num_classes, num_epochs, learning_rate, batch_size, result_dir, model_name, device):
    # 确保输入尺寸适合模型架构
    if input_size % 32 != 0:
        input_size = ((input_size // 32) + 1) * 32
        print(f"调整输入图像尺寸为: {input_size}x{input_size}")
    
    # 准备数据
    train_loader, test_loader = prepare_data(input_file, dataset_type, batch_size, input_size, annotations_file)
    
    # 初始化模型
    model = SegNet(num_classes)
    
    # 损失函数
    criterion = nn.CrossEntropyLoss()
    
    # 优化器
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    
    try:
        # 训练模型
        train_model(train_loader, model, criterion, optimizer, num_epochs, device)
        
        # 测试模型
        test_model(test_loader, model, device)
        
        # 保存模型
        os.makedirs(result_dir, exist_ok=True)
        model_path = os.path.join(result_dir, f"{model_name}.pth")
        torch.save(model.state_dict(), model_path)
        print(f'训练完成，模型保存到 {model_path}')
        
    except Exception as e:
        print(f"训练过程中发生错误: {e}")

if __name__ == "__main__":
    # 设置参数
    data_dir = 'E:/data/VOCdevkit/VOC2007'
    input_file = data_dir  # 使用主目录，因为我们需要同时访问图像和掩码
    annotations_file = os.path.join(data_dir, "Annotations")
    input_size = 224  # 输入图像大小
    dataset_type = "voc"
    num_classes = 21  # 20类 + 背景
    epochs = 1
    learning_rate = 0.001
    batch_size = 4
    result_dir = 'E:/data/VOCdevkit/model'
    model_name = 'fcn_segmentation'
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # 设置随机种子以确保可重复性
    torch.manual_seed(42)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(42)
    
    segnet_train(input_file, dataset_type, input_size, annotations_file, 
              num_classes, epochs, learning_rate, batch_size, 
              result_dir, model_name, device)
