# BiLSTM推理镜像 - 基于优化版基础镜像
# 包含模型文件和推理服务，可直接部署
# 基于优化的BiLSTMPredictor.py

ARG BASE_IMAGE=bilstm-inference:base-latest
FROM ${BASE_IMAGE}

# 镜像标签
LABEL maintainer="AI Algorithm Team" \
      version="2.0.0" \
      description="BiLSTM inference image - optimized with models" \
      purpose="ready-to-deploy-seldon-service" \
      base_image="${BASE_IMAGE}"

# 切换到root用户以进行文件操作
USER root

# 复制启动脚本
COPY start.py /app/

# 复制模型文件（直接内嵌到镜像中）
COPY models/ /app/models/

# 验证模型文件并设置权限
RUN echo "=== 检查模型文件 ===" && \
    ls -la /app/models/ && \
    find /app/models/ -name "*.pth" -o -name "*.pkl" -o -name "*.json" | head -10 && \
    echo "=== 设置权限 ===" && \
    chown -R aiuser:aiuser /app && \
    chmod +x /app/start.py

# 切换回非root用户
USER aiuser

# 设置环境变量（针对Seldon部署）
ENV MODEL_LIBRARY_DIR=/app/models
ENV MODEL_DIR=/app
ENV SELDON_MODE=true
ENV PYTHONPATH=/app:$PYTHONPATH

# 健康检查（使用优化后的BiLSTMPredictor）
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python -c "from BiLSTMPredictor import health_status; result = health_status(); exit(0 if result.get('status') == 'healthy' else 1)"

# 暴露端口
EXPOSE 5000 9000

# 默认启动命令
# Seldon Core模式启动
CMD ["python", "/app/start.py"] 