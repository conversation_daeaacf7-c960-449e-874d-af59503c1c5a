2025-05-21 16:26:27,300 - GCN_TF_Local - INFO - 日志将记录到控制台和文件: results\results_gcn_tf_local\gcn_tf_local_training_20250521_162627.log
2025-05-21 16:26:27,301 - GCN_TF_Local - INFO - GCN TensorFlow 本地模式训练脚本已初始化。
2025-05-21 16:26:27,301 - GCN_TF_Local - INFO - TensorFlow 版本: 2.13.0
2025-05-21 16:26:27,301 - GCN_TF_Local - INFO - 参数: {'input_file': 'E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl', 'result_dir': 'results\\results_gcn_tf_local', 'model_name': 'GCN_TF_Local_Model', 'label_column': 'Label', 'data_format': 'pkl', 'hidden_size': 64, 'num_layers': 2, 'dropout_rate': 0.5, 'num_epochs': 3, 'learning_rate': 0.005, 'optimizer': 'adam', 'loss_function': 'sparsecategoricalcrossentropy', 'edge_strategy': 'fully_connected', 'test_split_size': 0.2, 'random_state': 42, 'normalize': True, 'scaler_type': 'standard', 'handle_imbalance': False, 'stratify_split': True, 'numeric_impute_strategy': 'mean', 'categorical_impute_strategy': 'most_frequent', 'use_cv': False, 'cv_folds': 5, 'log_level': 'INFO', 'force_cpu': False}
2025-05-21 16:26:27,303 - GCN_TF_Local - INFO - 未找到GPU或强制使用CPU。
2025-05-21 16:26:27,303 - GCN_TF_Local - INFO - 从 E:/data/网络攻防博弈平台数据集/data2/TrafficLabelling_/Wednesday-workingHours.pcap_ISCX.pkl (格式: pkl) 加载数据
2025-05-21 16:26:27,704 - GCN_TF_Local - INFO - 数据加载为 DataFrame，形状: (692703, 85)
2025-05-21 16:26:27,913 - GCN_TF_Local - INFO - 特征形状: (692703, 84), 标签形状: (692703,)
2025-05-21 16:26:27,943 - GCN_TF_Local - INFO - 标签分布: {'BENIGN': 440031, 'DoS Hulk': 231073, 'DoS GoldenEye': 10293, 'DoS slowloris': 5796, 'DoS Slowhttptest': 5499, 'Heartbleed': 11}
2025-05-21 16:26:27,975 - GCN_TF_Local - INFO - 选择标准训练/测试分割模式。
2025-05-21 16:26:28,506 - GCN_TF_Local - INFO - 已将特征中的无限值替换为NaN。
2025-05-21 16:26:28,620 - GCN_TF_Local - INFO - 处理特征中的缺失值。
2025-05-21 16:26:30,743 - GCN_TF_Local - INFO - 使用 'mean' 填充了数值型缺失值。
2025-05-21 16:26:31,233 - GCN_TF_Local - INFO - 使用 'most_frequent' 填充了类别型缺失值。
2025-05-21 16:26:31,336 - GCN_TF_Local - INFO - 标签已编码。6 个类别: ['BENIGN', 'DoS GoldenEye', 'DoS Hulk', 'DoS Slowhttptest', 'DoS slowloris', 'Heartbleed']
2025-05-21 16:26:31,435 - GCN_TF_Local - INFO - 编码类别特征: ['Flow ID', 'Source IP', 'Destination IP', 'Timestamp']
2025-05-21 16:26:35,374 - GCN_TF_Local - INFO - 使用 standard 缩放器标准化特征。
2025-05-21 16:26:37,643 - GCN_TF_Local - INFO - 数据分割: X_train (554162, 84), y_train (554162,), X_test (138541, 84), y_test (138541,)
2025-05-21 16:26:37,691 - GCN_TF_Local - INFO - 训练/测试分割的预处理信息已保存: results\results_gcn_tf_local\GCN_TF_Local_Model_preprocessing_info_tf.json
2025-05-21 16:26:37,692 - GCN_TF_Local - INFO - 预处理后数据 (训练/测试分割): 输入大小=84, 类别数=6
2025-05-21 16:26:37,692 - GCN_TF_Local - WARNING - 节点数 (554162) 超过阈值 (20000)，全连接策略将使用等效单位矩阵进行归一化 (仅自环)。
2025-05-21 16:26:37,740 - GCN_TF_Local - INFO - 请求为 554162 个节点创建基于标识的邻接信息。
2025-05-21 16:26:37,741 - GCN_TF_Local - INFO - normalize_adjacency_matrix_tf_local: Identity requested. Returning placeholder and True flag.
2025-05-21 16:26:37,742 - GCN_TF_Local - INFO - 创建图数据字典: 特征 (554162, 84), 处理后的邻接张量 ((1, 1)), 是否为单位邻接: True, 标签 (554162,)
2025-05-21 16:26:37,742 - GCN_TF_Local - INFO - 训练图数据准备完成. 节点数: 554162, 边策略: fully_connected
2025-05-21 16:26:37,742 - GCN_TF_Local - WARNING - 节点数 (138541) 超过阈值 (20000)，全连接策略将使用等效单位矩阵进行归一化 (仅自环)。
2025-05-21 16:26:37,754 - GCN_TF_Local - INFO - 请求为 138541 个节点创建基于标识的邻接信息。
2025-05-21 16:26:37,755 - GCN_TF_Local - INFO - normalize_adjacency_matrix_tf_local: Identity requested. Returning placeholder and True flag.
2025-05-21 16:26:37,755 - GCN_TF_Local - INFO - 创建图数据字典: 特征 (138541, 84), 处理后的邻接张量 ((1, 1)), 是否为单位邻接: True, 标签 (138541,)
2025-05-21 16:26:37,755 - GCN_TF_Local - INFO - 测试图数据准备完成. 节点数: 138541, 边策略: fully_connected
2025-05-21 16:26:37,786 - GCN_TF_Local - CRITICAL - 发生意外严重错误: call() missing 2 required positional arguments: 'adj_tensor_for_layers' and 'is_adj_identity_flag_for_layers'
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 1160, in gcn_train_tf_local_mode
    _ = model([train_graph_dict['x'], train_graph_dict['adj_tensor'], train_graph_dict['is_adj_identity']], training=False)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 96, in error_handler
    return fn(*args, **kwargs)
TypeError: call() missing 2 required positional arguments: 'adj_tensor_for_layers' and 'is_adj_identity_flag_for_layers'
2025-05-21 16:26:37,791 - GCN_TF_Local - CRITICAL - 主程序中发生严重错误: call() missing 2 required positional arguments: 'adj_tensor_for_layers' and 'is_adj_identity_flag_for_layers'
Traceback (most recent call last):
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 1281, in <module>
    gcn_train_tf_local_mode(parsed_args)
  File "E:\work\增值项目\网络攻击博弈平台\2025.3\bypt\desin\图神经网络\GCN_tf_local_mode.py", line 1160, in gcn_train_tf_local_mode
    _ = model([train_graph_dict['x'], train_graph_dict['adj_tensor'], train_graph_dict['is_adj_identity']], training=False)
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 70, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "c:\ProgramData\Anaconda3\envs\bypt\lib\site-packages\keras\src\utils\traceback_utils.py", line 96, in error_handler
    return fn(*args, **kwargs)
TypeError: call() missing 2 required positional arguments: 'adj_tensor_for_layers' and 'is_adj_identity_flag_for_layers'
